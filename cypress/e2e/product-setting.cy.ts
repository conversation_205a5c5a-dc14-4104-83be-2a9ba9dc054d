const login_url = 'http://fe.rocketbird.cn:5173/login';
const product_setting_url = 'http://fe.rocketbird.cn:5173/marketing/group-online';

const login = () => {
  cy.visit(login_url);

  cy.get('input[placeholder="请输入账号"]').type('admin');
  cy.get('input[placeholder="请输入密码"]').type('rockbird2018');

  cy.get('button[type="submit"]').click();

  cy.wait(4000);
  cy.visit(product_setting_url, {
    onLoad(win) {
      (win as any).IS_BRAND_SITE = true; // Ensure we have permission to view the page
    },
  });
};

describe('ProductSetting Component', () => {
  beforeEach(() => {
    login();
  });

  describe('Search Functionality', () => {
    it('should filter results', () => {
      // Open the store dropdown
      cy.get('form').find('.arco-form-item').eq(0).click();
      // Select the first store option
      cy.wait(2000);
      cy.get('.arco-trigger-popup').eq(0).find('li.arco-select-option').first().click();
      // Click search button
      cy.wait(2000);
      cy.get('button').contains('搜索').click();
      cy.wait(4000);

      // Open the category dropdown
      cy.get('form').find('.arco-form-item').eq(1).click();
      // Select the first category option
      cy.wait(2000);
      cy.get('.arco-trigger-popup').eq(1).find('li.arco-select-option').eq(4).click();
      // Click search button
      cy.wait(2000);
      cy.get('button').contains('搜索').click();
      cy.wait(4000);

      // Type a product name in the input
      cy.get('form').find('.arco-form-item').eq(2).find('input').type('等级');
      // Click search button
      cy.wait(2000);
      cy.get('button').contains('搜索').click();
      cy.wait(4000);
    });

    // it('should combine multiple search criteria', () => {
    //   // Open the store dropdown and select first option
    //   cy.get('form').find('.arco-form-item').eq(0).click();
    //   cy.get('.arco-select-dropdown-option').first().click();

    //   // Open the category dropdown and select "商品" option
    //   cy.get('form').find('.arco-form-item').eq(1).click();
    //   cy.get('.arco-select-dropdown-option').contains('商品').click();

    //   // Type a product name
    //   cy.get('input[placeholder="请输入"]').type('test product');

    //   // Click search button
    //   cy.get('button').contains('搜索').click();

    //   // Verify the table updates
    //   cy.get('.arco-table').should('be.visible');
    // });
  });

  // describe('Table Operations', () => {
  //   it('should display product data in the table', () => {
  //     // Verify table columns exist
  //     cy.get('.arco-table-th').should('contain', '类型');
  //     cy.get('.arco-table-th').should('contain', '商品');
  //     cy.get('.arco-table-th').should('contain', '门店');
  //     cy.get('.arco-table-th').should('contain', '美团');
  //     cy.get('.arco-table-th').should('contain', '大众点评');
  //     cy.get('.arco-table-th').should('contain', '抖音');
  //     cy.get('.arco-table-th').should('contain', '操作');
  //   });

  //   it('should select rows with checkboxes', () => {
  //     // Wait for table to load
  //     cy.get('.arco-table-tr').should('be.visible');

  //     // Select the first row if available
  //     cy.get('.arco-table-tr').first().find('.arco-checkbox').click();

  //     // Verify the selection is reflected in the UI
  //     cy.get('span').contains('已选择').should('be.visible');
  //   });

  //   it('should navigate to edit page when clicking edit button', () => {
  //     // Stub the router push method
  //     cy.window().then((win) => {
  //       cy.stub(win.history, 'pushState').as('routerPush');
  //     });

  //     // Find and click the first edit button if available
  //     cy.get('.arco-table-tr').first().find('button').contains('编辑').click();

  //     // Verify the router was called with the correct path
  //     cy.get('@routerPush').should('be.called');
  //   });

  //   it('should show delete confirmation when clicking delete button', () => {
  //     // Find and click the first delete button if available
  //     cy.get('.arco-table-tr').first().find('button').contains('删除').click();

  //     // Verify the confirmation modal appears
  //     cy.get('.arco-modal-title').contains('提示').should('be.visible');
  //     cy.get('.arco-modal-content').should('contain', '确认删除');

  //     // Close the modal by clicking cancel
  //     cy.get('.arco-modal-footer').find('button').first().click();
  //   });
  // });

  // describe('Button Actions', () => {
  //   it('should navigate to add page when clicking new relation button', () => {
  //     // Stub the router push method
  //     cy.window().then((win) => {
  //       cy.stub(win.history, 'pushState').as('routerPush');
  //     });

  //     // Click the "新增关联" button
  //     cy.get('button').contains('新增关联').click();

  //     // Verify the router was called with the correct path
  //     cy.get('@routerPush').should('be.called');
  //   });

  //   it('should show warning when attempting batch delete without selection', () => {
  //     // Click the "批量删除" button without selecting any rows
  //     cy.get('button').contains('批量删除').click();

  //     // Verify warning message appears
  //     cy.get('.arco-message').should('contain', '请选择要删除的记录');
  //   });

  //   it('should show confirmation when attempting batch delete with selection', () => {
  //     // Select the first row if available
  //     cy.get('.arco-table-tr').first().find('.arco-checkbox').click();

  //     // Click the "批量删除" button
  //     cy.get('button').contains('批量删除').click();

  //     // Verify the confirmation modal appears
  //     cy.get('.arco-modal-title').contains('提示').should('be.visible');
  //     cy.get('.arco-modal-content').should('contain', '确认删除');

  //     // Close the modal by clicking cancel
  //     cy.get('.arco-modal-footer').find('button').first().click();
  //   });

  //   it('should trigger export function when clicking export button', () => {
  //     // Create a stub for the export function
  //     cy.window().then((win) => {
  //       cy.stub(win, 'open').as('exportFunction');
  //     });

  //     // Click the "导出" button
  //     cy.get('button').contains('导出').click();

  //     // Verify the export function was triggered (this is an approximation as we can't fully test the Excel export)
  //     cy.get('.arco-message').should('exist');
  //   });
  // });
});
