const login_url = 'http://fe.rocketbird.cn:5173/login';
const group_online_url = 'http://fe.rocketbird.cn:5173/marketing/group-online';

const login = (isBrandSite: boolean) => {
  cy.visit(login_url);

  cy.get('input[placeholder="请输入账号"]').type('admin');
  cy.get('input[placeholder="请输入密码"]').type('rockbird2018');

  cy.get('button[type="submit"]').click();

  cy.wait(4000);
  cy.visit(group_online_url, {
    onLoad(win) {
      (win as any).IS_BRAND_SITE = isBrandSite;
    },
  });
};

describe('Permission-Based Rendering', () => {
  it('should show empty state when user does not have brand site permission', () => {
    // Mock window.IS_BRAND_SITE to be false
    login(false);

    // Verify empty state is shown
    cy.get('.empty-container').should('be.visible');
  });

  it('should show tabs when user has brand site permission', () => {
    // Mock window.IS_BRAND_SITE to be true
    login(true);

    // Verify tabs are visible
    cy.get('.arco-tabs').should('be.visible');
  });
});

describe('Tab Switching', () => {
  beforeEach(() => {
    // Mock window.IS_BRAND_SITE to be true for all tests in this block
    login(true);
  });

  it('should display product-setting by default', () => {
    // Verify the first tab is active by default
    cy.get('.arco-tabs-nav-tab-list .arco-tabs-tab').first().should('have.class', 'arco-tabs-tab-active');
  });

  it('should switch to store-setting when clicking the second tab', () => {
    // Click on the second tab
    cy.get('.arco-tabs-nav-tab-list .arco-tabs-tab').eq(1).click();

    // Verify the second tab becomes active
    cy.get('.arco-tabs-nav-tab-list .arco-tabs-tab').eq(1).should('have.class', 'arco-tabs-tab-active');
  });
});

// Add an empty export to make this file a module
export {};
