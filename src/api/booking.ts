import request from '@/request/index';

export function getSchedule(data: any) {
  return request({
    url: '/Web/Space/restList',
    options: {
      method: 'POST',
      data,
    },
  });
}

export function getStadiumList(data: any) {
  return request({
    url: '/Web/SpaceOrderLong/getAllSpace',
    options: {
      method: 'POST',
      params: data,
    },
  });
}

// export function getUnavailableList(data: any) {
//   return request({
//     url: '/Web/Space/getUnavailableList',
//     options: {
//       method: 'GET',
//       params: data,
//     },
//   });
// }

export function checkLongTermRentalStatus() {
  return request({
    url: '/Web/SpaceOrderLong/checkSpaceLongLimit',
    options: {
      method: 'POST',
    },
  });
}

export function saveLongTermRental(data: any) {
  return request({
    url: '/Web/SpaceOrderLong/createLongOrder',
    options: {
      method: 'POST',
      data,
    },
  });
}

export function getLongTermRental(data: any) {
  return request({
    url: '/Web/SpaceOrderLong/getLongOrderDetail',
    options: {
      method: 'POST',
      data,
    },
  });
}

export function cancelLongTermRental(data: any) {
  return request({
    url: '/Web/SpaceOrderLong/refundSpaceLongItems',
    options: {
      method: 'POST',
      data,
    },
  });
}

export function saveBookingList(data: any) {
  return request({
    url: '/Web/SpaceOrderLong/submitLongOrderItems',
    options: {
      method: 'POST',
      data,
    },
  });
}

export function getBookingList(data: any) {
  return request({
    url: '/Web/SpaceOrderLong/getLongOrderSonLists',
    options: {
      method: 'POST',
      data,
    },
  });
}

export function getStadium(data: any) {
  return request({
    url: '/Web/Space/getEditInfo',
    options: {
      method: 'GET',
      params: data,
    },
  });
}

export function saveStadium(data: any) {
  return request({
    url: '/Web/Space/save',
    options: {
      method: 'POST',
      data,
    },
  });
}

export function getStadiumCategoryList() {
  return request({
    url: '/Web/Space/getTypes',
    options: {
      method: 'GET',
    },
  });
}

export function getAllStrategyList() {
  return request({
    url: '/Web/Space/getListSchedulePriceTemplate',
    options: {
      method: 'POST',
      data: {
        page_no: 1,
        page_size: 1000,
      },
    },
  });
}

export function getStrategyList(data: any) {
  return request({
    url: '/Web/Space/getListSchedulePriceTemplate',
    options: {
      method: 'POST',
      data,
    },
    immediate: false,
  });
}

export function getStrategy(data: any) {
  return request({
    url: '/Web/Space/getOneSchedulePriceTemplate',
    options: {
      method: 'GET',
      params: data,
    },
  });
}

export function saveStrategy(data: any) {
  const url = data.id ? '/Web/Space/editSchedulePriceTemplate' : '/Web/Space/addSchedulePriceTemplate';
  return request({
    url,
    options: {
      method: 'POST',
      data,
    },
  });
}

export function deleteStrategy(data: any) {
  return request({
    url: '/Web/Space/deleteSchedulePriceTemplate',
    options: {
      method: 'POST',
      data,
    },
  });
}

export default getSchedule;
