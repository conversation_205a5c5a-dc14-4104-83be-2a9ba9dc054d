import request from '@/request/index';

const { IS_BRAND_SITE } = window;
const urlPrefix = IS_BRAND_SITE ? 'merchant' : 'web';
// 新入会会员月报
export function joinBusReport() {
  return request({
    url: `${urlPrefix}/ironReport/joinBusReport`,
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function webJoinBusReport() {
  return request({
    url: `${urlPrefix}/ironReport/joinChannelReport`,
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 会员转化
export function webJoinChannelReport() {
  return request({
    url: `${urlPrefix}/ironReport/joinChannelReport`,
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 卡课分析
export function joinCardReport() {
  return request({
    url: 'merchant/ironReport/joinCardReport',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function webJoinCardReport() {
  return request({
    url: `${urlPrefix}/ironReport/joinCardReport`,
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
