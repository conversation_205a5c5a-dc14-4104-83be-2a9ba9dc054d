import request from '@/request/index';

export function getPayedList(data: any) {
  return request({
    url: 'Web/Pay/getPayedList',
    options: {
      method: 'post',
      data,
    },
  });
}
export function cancelPay() {
  return request({
    url: 'Web/Pay/cancelPay',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function pay() {
  return request({
    url: 'Web/Pay/pay',
    options: {
      method: 'post',
      showError: false,
    },
    immediate: false,
  });
}
export function checkPayStat(data: any) {
  return request({
    url: 'Web/Pay/checkPayStat',
    options: {
      method: 'post',
      data,
      showError: false,
    },
  });
}
export function getDevice(data: any) {
  return request({
    url: 'Web/Pay/getDevice',
    options: {
      method: 'post',
      data,
    },
  });
}

export default { getPayedList };
