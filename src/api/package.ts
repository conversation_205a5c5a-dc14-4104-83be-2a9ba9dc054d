import request from '@/request/index';

export function getSaleSetting() {
  return request({
    url: 'Web/package/getSaleSetting',
    options: {
      method: 'post',
    },
  });
}

export function saleSetting() {
  return request({
    url: 'Web/package/saleSetting',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

export function getPackageList() {
  return request({
    url: 'Web/package/getPackageList',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

export function checkAuthPackage() {
  return request({
    url: '/Web/package/checkAuthPackage',
    options: {
      method: 'get',
    },
  });
}

export function swicthSaleStatus() {
  return request({
    url: '/Web/package/swicthSaleStatus',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function swicthMemberStatus() {
  return request({
    url: '/Web/package/swicthMemberStatus',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function delPackage(data?: any) {
  return request({
    url: '/Web/package/delPackage',
    options: {
      method: 'post',
      data,
    },
  });
}
