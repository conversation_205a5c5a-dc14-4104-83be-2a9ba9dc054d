import request from '@/request/index';
// 团课预约列表
export function pcClassMarkList() {
  return request({
    url: 'Web/ClassMark/pc_class_mark_list',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function getUserMarkCards() {
  return request({
    url: 'Web/ClassMark/get_user_mark_cards',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function classMarkDetail() {
  return request({
    url: 'Web/ClassMark/class_mark_detail',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 取消团课预约
export function cancelClassMark() {
  return request({
    url: 'Web/ClassMark/cancel_class_mark',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 批量取消
export function batchCancelClassMark() {
  return request({
    url: 'Web/ClassMark/batch_cancel_class_mark',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 编辑预约
export function updateMark() {
  return request({
    url: 'Web/ClassMark/update_mark',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function addMark() {
  return request({
    url: 'Web/ClassMark/add_mark',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 获取团课教练签到列表
export function getCoachSignList(data: any) {
  return request({
    url: 'Web/CoachSign/get_coach_sign_list',
    options: {
      method: 'post',
      data,
    },
    immediate: false,
  });
}

// 获取操作记录列表
export function getRecordList(data: any) {
  return request({
    url: 'Web/CoachSign/coach_sign_operating_record_list',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

// 获取实际到场会员列表
export function getArrivalList(data: any) {
  return request({
    url: 'Web/ClassMark/getCourseScheduleUserList',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

// 获取教练列表
export function getCoachList(data: any) {
  return request({
    url: 'Web/CoachSign/get_bus_coach_list',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

// 获取课程列表
export function getCourseList(data: any) {
  return request({
    url: 'Web/CoachSign/get_bus_class_list',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

// 教练签到
export function coachSign(data: any) {
  return request({
    url: 'Web/CoachSign/add_coach_sign',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

// 教练取消签到
export function cancelCoachSign(data: any) {
  return request({
    url: 'Web/CoachSign/update_coach_sign',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

// 检查是否超过限制
export function checkLimitCard(data: any) {
  return request({
    url: 'Web/ClassMark/check_limit_card',
    options: {
      method: 'post',
      showError: false,
      data,
    },
  });
}

// 获取团课订阅列表
export function pcClassSubscriptionList() {
  return request({
    url: '/Web/ClassMark/class_mark_wait_list',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export default { pcClassMarkList };
