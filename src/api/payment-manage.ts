import request from '@/request/index';

export function getBusPayType() {
  return request({
    url: 'Web/PaymentManage/getBusPayType',
    options: { method: 'POST' },
    immediate: false,
  });
}

export function editPayType() {
  return request({
    url: 'Web/PaymentManage/editPayType',
    options: { method: 'POST' },
    immediate: false,
  });
}

export function delPayType() {
  return request({
    url: 'Web/PaymentManage/delPayType',
    options: { method: 'POST' },
    immediate: false,
  });
}

export function addBusPayType() {
  return request({
    url: 'Web/PaymentManage/addBusPayType',
    options: { method: 'POST' },
    immediate: false,
  });
}

export function switchPayType() {
  return request({
    url: 'Web/PaymentManage/switchPayType',
    options: { method: 'POST' },
    immediate: false,
  });
}

export function getPaymentOption() {
  return request({
    url: 'Web/PaymentManage/getPaymentOption',
    options: { method: 'POST' },
  });
}

export function getBusAllPayType() {
  return request({
    url: 'Web/PaymentManage/getBusAllPayType',
    options: { method: 'POST' },
  });
}

export function setPaymentOption() {
  return request({
    url: 'Web/PaymentManage/setPaymentOption',
    options: { method: 'POST' },
    immediate: false,
  });
}

export default {};
