import request from '@/request/index';

export function delRule() {
  return request({
    url: 'Web/CardRule/del',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function getRuleList() {
  return request({
    url: 'Web/CardRule/getList',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function getCardCoupon(data: any) {
  return request({
    url: 'Web/CardRule/getCardCoupon',
    options: {
      method: 'post',
      data,
    },
  });
}

export function saveCardRule() {
  return request({
    url: 'Web/CardRule/save',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function getCardRuleDetail(data: any) {
  return request({
    url: 'Web/CardRule/getDetail',
    options: {
      method: 'post',
      data,
    },
  });
}

export function sanVerifyList() {
  return request({
    url: 'Web/CardRule/sanVerifyList',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function verification() {
  return request({
    url: 'Web/CardRule/verification',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function cancelVerification() {
  return request({
    url: 'Web/CardRule/cancelVerification',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export default getRuleList;
