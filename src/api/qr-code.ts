import request from '@/request/index';

export function getQrCodeMember() {
  return request({
    url: 'Merchant/QrCode/member',
    options: { method: 'GET', responseType: 'blob' },
    immediate: false,
  });
}

export function getIvepAndroid() {
  return request({
    url: 'Merchant/QrCode/ivepAndroid',
    options: { method: 'GET', responseType: 'blob' },
    immediate: false,
  });
}

export default {};
