import request from '@/request/index';

// 获取场馆会员黑名单
export function getBlacklist() {
  return request({
    url: 'Web/UserBlacklist/lists',
    options: {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
    },
    immediate: false,
  });
}

// 获取商家会员黑名单
export function getMerchantBlacklist() {
  return request({
    url: 'Merchant/UserBlacklist/lists',
    options: {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
    },
    immediate: false,
  });
}
interface DeleteBlackData {
  bus_id: string;
  id: string;
}
// 删除会员黑名单
export function deleteBlack(data: DeleteBlackData) {
  return request({
    url: 'Web/UserBlacklist/delete',
    options: {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      data,
    },
  });
}
export function deleteMerchantBlack(data: DeleteBlackData) {
  return request({
    url: 'Merchant/UserBlacklist/delete',
    options: {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      data,
    },
  });
}

// 添加黑名单
export function addBlack() {
  return request({
    url: 'Web/UserBlacklist/add',
    options: {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
    },
    immediate: false,
  });
}

export function addMerchantBlack() {
  return request({
    url: 'Merchant/UserBlacklist/add',
    options: {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
    },
    immediate: false,
  });
}
// 编辑黑名单
export function editBlack() {
  return request({
    url: 'Web/UserBlacklist/edit',
    options: {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
    },
    immediate: false,
  });
}

export function editMerchantBlack() {
  return request({
    url: 'Merchant/UserBlacklist/edit',
    options: {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
    },
    immediate: false,
  });
}
// 获取黑名单详情
export function getDetail(data: any) {
  return request({
    url: 'Web/UserBlacklist/getDetail',
    options: {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      data,
    },
  });
}

export function getMerchantDetail(data: any) {
  return request({
    url: 'Merchant/UserBlacklist/getDetail',
    options: {
      method: 'post',
      headers: { 'Content-Type': 'application/json' },
      data,
    },
  });
}
