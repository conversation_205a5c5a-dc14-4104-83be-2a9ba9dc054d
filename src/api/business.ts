import request from '@/request/index';

// 当前账号下有权限的所有场馆信息等（包括过期和试用场馆）
export function getBusinessBusList(data?: any) {
  return request({
    url: '/Web/Business/get_bus_list',
    options: {
      method: 'post',
      data,
    },
  });
}
// 系统设置设置项的权限
export function checkAdminBusSetting(data?: any) {
  return request({
    url: 'Web/business/check_admin_bus_setting',
    options: {
      method: 'post',
      data,
    },
  });
}

// 获取场馆私教或者泳教设置
export function getBusCoachSetting(data?: any) {
  return request({
    url: 'Web/Business/get_bus_coach_setting',
    options: {
      method: 'post',
      data,
    },
  });
}

// 设置场馆泳教设置
export function updateBusSwimCoachSetting() {
  return request({
    url: 'Web/Business/update_bus_swim_coach_setting',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 设置场馆私教设置
export function updateBusCoachSetting() {
  return request({
    url: 'Web/Business/update_bus_coach_setting',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 划拨合同券
export function transferContractNumber() {
  return request({
    url: 'Web/Business/transfer_contract_number',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 场馆组列表
export function getLevelList() {
  return request({
    url: 'Web/business/get_level_list',
    options: {
      method: 'get',
    },
  });
}

export default getBusinessBusList;
