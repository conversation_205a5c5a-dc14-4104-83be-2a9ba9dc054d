import request from '@/request/index';

export function getAccountDetails() {
  const { IS_BRAND_SITE } = window;
  return request({
    url: `/${IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/getAccountDetails`,
    options: {
      method: 'post',
    },
  });
}

export function getFinanceCheckDays() {
  return request({
    url: 'web/admin/check_days',
    options: {
      method: 'post',
    },
  });
}

export function changePassword() {
  return request({
    url: 'Web/Admin/change_password',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

export function checkPasswordSmsCode(data) {
  return request({
    url: 'Web/Admin/check_password_sms_code',
    options: {
      method: 'POST',
      data,
    },
  });
}
export function updateAdminPassword() {
  return request({
    url: 'Web/Admin/update_admin_password',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 该账号列表获取接口，不受是否有账号权限控制
export function getAdminListApprove() {
  return request({
    url: 'Web/Admin/get_admin_list_approve',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 账号列表获取
export function getAdminList() {
  return request({
    url: 'Web/Admin/get_admin_list',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 商家账号列表获取
export function getMerchantAdminList() {
  return request({
    url: 'Merchant/Admin/get_admin_list',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 更新账号状态
export function updateAdminStatus() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/update_admin_status`,
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 删除账号
export function deleteAdmin() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/delete_admin`,
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 添加账号
export function addAdmin() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/add_admin`,
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 更新账号
export function updateAdmin() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/update_admin`,
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 查看是否重复
export function checkAdminUsername(data?: any) {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/check_admin_username`,
    options: {
      method: 'POST',
      data,
    },
  });
}

// 获取账号信息
export function getAdminInfo(data?: any) {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Admin/get_admin_info`,
    options: {
      method: 'POST',
      data,
    },
  });
}
export function sendSmsToPassword(data?: any) {
  return request({
    url: 'Web/Admin/send_sms_to_password',
    options: {
      method: 'POST',
      data,
    },
  });
}

export default getAccountDetails;
