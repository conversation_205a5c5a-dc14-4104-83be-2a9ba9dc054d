import request from '@/request/index';

export function getOpenClassList() {
  return request({
    url: 'Web/OpenClass/get_open_class_list',
    options: { method: 'POST' },
    immediate: false,
  });
}

export function getOpenClassInfo(data: any) {
  return request({
    url: 'Web/OpenClass/get_open_class_info',
    options: { method: 'POST', data },
  });
}

// 获取商家下课程列表 筛选用 可传bus_id获取场馆下课程列表
export function getOpenClassAll(data?: any) {
  return request({
    url: 'Web/OpenClass/get_open_class_all',
    options: { method: 'POST', data },
  });
}

// 设置课程收费方案
export function setClassChargePlan() {
  return request({
    url: 'Web/OpenClass/set_class_charge_plan',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 删除课程收费方案
export function delClassChargePlan() {
  return request({
    url: 'Web/OpenClass/del_class_charge_plan',
    options: { method: 'POST' },
    immediate: false,
  });
}

export function openclassSupportBus(data?: any) {
  return request({
    url: 'Web/OpenClass/openclass_support_bus',
    options: { method: 'POST', data },
  });
}

export function addOpenClass() {
  return request({
    url: 'Web/OpenClass/add_open_class',
    options: { method: 'POST' },
    immediate: false,
  });
}

export function updateOpenClass() {
  return request({
    url: 'Web/OpenClass/update_open_class',
    options: { method: 'POST' },
    immediate: false,
  });
}
export function supportLineUp() {
  return request({
    url: 'Web/OpenClass/support_line_up',
    options: { method: 'POST', showError: false },
  });
}

export function hiddenOpenClass() {
  return request({
    url: 'Web/OpenClass/hidden_open_class',
    options: { method: 'POST' },
    immediate: false,
  });
}

export default { getOpenClassList };
