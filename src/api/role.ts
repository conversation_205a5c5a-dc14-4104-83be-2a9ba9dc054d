import request from '@/request/index';

// 角色列表
export function getRoleList() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/get_role_list`,
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}
// 角色列表
export function updateRoleAssignStatus() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/update_role_assign_status`,
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 删除角色
export function delRole() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/del_role`,
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

// 获取角色绑定信息
export function getAccount(data?: any) {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/get_account`,
    options: {
      method: 'POST',
      data,
    },
  });
}

// 更新角色绑定信息
export function accountBindRole() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/account_bind_role`,
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

export function getRoleAccess(data?: any) {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/get_role_access`,
    options: {
      method: 'POST',
      data,
    },
  });
}

export function getAdminAccess(data?: any) {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/get_admin_access`,
    options: {
      method: 'POST',
      data,
    },
  });
}

export function addRole() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/add_role`,
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

export function updateRole() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/update_role`,
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}

export function getRoles() {
  return request({
    url: `${window.IS_BRAND_SITE ? 'Merchant' : 'Web'}/Role/get_roles`,
    options: {
      method: 'POST',
    },
  });
}

export default getRoleList;
