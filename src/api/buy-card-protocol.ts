import request from '@/request/index';

// 获取购卡协议
export function getProtocolContent() {
  return request({
    url: 'Web/BuycardProtocol/get_protocol_content',
    options: {
      method: 'get',
    },
  });
}

// 设置购卡协议
export function updateProtocolContent() {
  return request({
    url: 'Web/BuycardProtocol/update_protocol_content',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
