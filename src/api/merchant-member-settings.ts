import request from '@/request/index';

// 保存协议
export function saveProtocol(data: any) {
  return request({
    url: '/Web/AlipayMerchantCard/agreementUpsert',
    options: {
      method: 'post',
      data,
    },
  });
}

// 获取协议
export function getProtocol() {
  return request({
    url: 'Web/AlipayMerchantCard/agreementInfo',
    options: {
      method: 'get',
    },
  });
}

// 保存设置
export function saveSetting(data: any) {
  return request({
    url: 'Web/AlipayMerchantCard/storeConfigUpdate ',
    options: {
      method: 'post',
      data,
    },
  });
}

// 获取设置
export function getSetting() {
  return request({
    url: 'Web/AlipayMerchantCard/storeConfig ',
    options: {
      method: 'get',
    },
  });
}
