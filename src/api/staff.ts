import request from '@/request/index';

export const getCoachGroup = (data?: any) => {
  return request({
    url: '/Web/CoachGroup/get_coach_group',
    options: {
      method: 'GET',
      params: data,
    },
    immediate: true,
  });
};

export const getCoachSpecial = (data: any) => {
  return request({
    url: '/Web/Coach/get_coach_spec',
    options: {
      method: 'GET',
      params: data,
    },
    immediate: true,
  });
};

export const saveCoachSpecial = (data: any) => {
  return request({
    url: '/Web/Coach/add_coach_spec',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const removeCoachSpecial = (data: any) => {
  return request({
    url: '/Web/Coach/del_coach_spec',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const getCoachPosition = (data?: any) => {
  return request({
    url: '/Web/Coach/get_coach_posi',
    options: {
      method: 'GET',
      params: data,
    },
    immediate: true,
  });
};

export const addCoachPosition = (data: any) => {
  return request({
    url: '/Web/Coach/add_coach_posi',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const updateCoachPosition = (data: any) => {
  return request({
    url: '/Web/Coach/update_coach_posi',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const removeCoachPosition = (data: any) => {
  return request({
    url: '/Web/Coach/del_coach_posi',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const getCardGroup = (data: any) => {
  return request({
    url: '/Web/Card/get_bus_card_group',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const getCardList = (data: any) => {
  return request({
    url: '/Web/Coach/get_bus_card',
    options: {
      method: 'GET',
      params: data,
    },
    immediate: true,
  });
};

export const getCoach = (data: any) => {
  return request({
    url: '/Web/Coach/get_coach',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const addCoach = (data: any) => {
  return request({
    url: '/Web/Coach/add_coach',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const updateCoach = (data: any) => {
  return request({
    url: '/Web/Coach/update_coach_info',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const getCoachList = (data: any) => {
  return request({
    url: '/Web/Coach/get_bus_coach_list',
    options: {
      method: 'POST',
      data,
    },
    immediate: false,
  });
};

export const removeCoach = (data: any) => {
  return request({
    url: '/Web/Coach/del_bus_coach',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const addCoachGroup = (data: any) => {
  return request({
    url: '/Web/CoachGroup/add_coach_group',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const updateCoachGroup = (data: any) => {
  return request({
    url: '/Web/CoachGroup/update_coach_group',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const removeCoachGroup = (data: any) => {
  return request({
    url: '/Web/CoachGroup/del_coach_group',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const unbind = (data: any) => {
  return request({
    url: '/Web/Coach/unbind_property',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const bind = (data: any) => {
  return request({
    url: '/Web/Coach/bind_property',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const clearFingerPrint = (data: any) => {
  return request({
    url: '/Web/Member/cancel_finger_relation',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const setAllowedCourses = (data: any) => {
  return request({
    url: '/Web/Coach/batch_set_permitted_class',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const setAuthorityForMember = (data: any) => {
  return request({
    url: '/Web/Member/set_upload_face_power',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
};

export const getAuthorityForMember = (data: any) => {
  return request({
    url: '/Web/Member/get_upload_face_power',
    options: {
      method: 'GET',
      params: data,
    },
    immediate: true,
  });
};
