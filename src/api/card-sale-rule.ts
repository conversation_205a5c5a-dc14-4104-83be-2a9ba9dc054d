import request from '@/request/index';

export function getCardRuleAuth() {
  return request({
    url: '/Web/CardSaleRule/set_rule',
    options: {
      method: 'get',
      showError: false,
    },
  });
}

export function getRange() {
  return request({
    url: '/Web/CardSaleRule/getRange',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function getRuleList(data?: any) {
  return request({
    url: '/Web/CardSaleRule/getRuleList',
    options: {
      method: 'post',
      data,
    },
  });
}

export default getCardRuleAuth;
