import request from '@/request/index';

// 获取储值卡设置
export function getSetting() {
  return request({
    url: 'Web/Commodity/get_setting',
    options: {
      method: 'post',
    },
  });
}

// 更新储值卡设置
export function saveSetting() {
  return request({
    url: 'Web/Commodity/save_setting',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function getUserDebitCard(data: any) {
  return request({
    url: 'Web/Commodity/get_user_debit_card',
    options: {
      method: 'post',
      data,
    },
  });
}
