import request from '@/request/index';

export function addCardGroup(data?: any) {
  return request({
    url: 'Web/CardGroup/addCardGroup',
    options: {
      method: 'post',
      data,
    },
  });
}

export function deleteCardGroup(data?: any) {
  return request({
    url: 'Web/CardGroup/deleteCardGroup',
    options: {
      method: 'post',
      data,
    },
  });
}

export function getCardGroupList(data?: any) {
  return request({
    url: 'Web/CardGroup/getCardGroupList',
    options: {
      method: 'post',
      data,
    },
  });
}

export default addCardGroup;
