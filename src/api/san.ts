import request from '@/request/index';

// 获取散场票设置
export function getSanSetting() {
  return request({
    url: 'Web/San/get_san_setting',
    options: {
      method: 'post',
    },
  });
}

// 设置散场票设置
export function updateSanSetting() {
  return request({
    url: 'Web/San/update_san_setting',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 散场票信息检测
export function checkStatusByConsumesn(data) {
  return request({
    url: 'Web/San/checkStatusByConsumesn',
    options: {
      method: 'post',
      data,
      showError: false,
    },
  });
}

// 移动端核销权限
export function verificationWap() {
  return request({
    url: 'Web/San/verificationWap',
    options: {
      method: 'GET',
    },
  });
}
