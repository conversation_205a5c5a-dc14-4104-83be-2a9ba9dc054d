import request from '@/request/index';

// 规则列表
export function getRuleList(data: any) {
  return request({
    url: 'Web/BusinessFit/commissionRulesList',
    options: {
      method: 'post',
      data,
    },
    immediate: false,
  });
}

// 获取所有提成规则
export function getAllRule(params?: any) {
  return request({
    url: 'Web/BusinessFit/commissionRulesList',
    options: {
      method: 'get',
      params: { page: 1, page_size: 9999, ...params },
    },
  });
}

// 保存规则
export function saveRule() {
  return request({
    url: 'Web/BusinessFit/iOrUCommissionRules',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 删除规则
export function removeRule(data: any) {
  return request({
    url: 'Web/BusinessFit/delCommissionRules',
    options: {
      method: 'post',
      data,
    },
  });
}

// 获取规则详情
export function getRule(params: any) {
  return request({
    url: 'Web/BusinessFit/commissionRulesDetail',
    options: {
      method: 'get',
      params,
    },
  });
}

// 提成汇总列表
export function getSummary(data: any) {
  return request({
    url: 'Web/BusinessFit/commissionSummary',
    options: {
      method: 'post',
      data,
    },
    immediate: false,
  });
}

// 获取执行规则
export function getExecuteRule(params?: any) {
  return request({
    url: 'Web/BusinessFit/commissionExecDetail',
    options: {
      method: 'get',
      params,
    },
  });
}

// 保存执行规则
export function saveExecuteRule(data: any) {
  return request({
    url: 'Web/BusinessFit/iOrUCommissionExecByRule',
    options: {
      method: 'post',
      data,
    },
  });
}
