import request from '@/request/index';

export function getMerchantCardList() {
  return request({
    url: '/Merchant/CardClass/get_card_list',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function getMerchantCardAuth() {
  return request({
    url: '/Merchant/CardClass/card_auth',
    options: {
      method: 'get',
    },
  });
}

export function getMerchantUniversalCardAuth() {
  return request({
    url: '/Merchant/CardClass/universal_card_auth',
    options: {
      method: 'get',
    },
  });
}

export function getMerchantExperienceCardAuth() {
  return request({
    url: '/Merchant/CardClass/experience_card_auth',
    options: {
      method: 'get',
    },
  });
}
export function getMerchantSwimCard() {
  return request({
    url: '/Merchant/CardClass/swim_card',
    options: {
      method: 'get',
    },
  });
}
export function getMerchantMultiPtCardAuth() {
  return request({
    url: '/Merchant/CardClass/multi_pt_card_auth',
    options: {
      method: 'get',
    },
  });
}

export function checMerchantAuthPackage() {
  return request({
    url: '/Merchant/CardClass/checkAuthPackage',
    options: {
      method: 'get',
    },
  });
}

export function merchantCardClassGroupList(showError = true) {
  return request({
    url: 'Merchant/CardClass/card_class_group_list',
    options: {
      method: 'post',
      showError,
    },
    immediate: false,
  });
}

export function merchantCardClassGroupAdd() {
  return request({
    url: 'Merchant/CardClass/card_class_group_add',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function delMerchantCardClassGroup(data?: any) {
  return request({
    url: 'Merchant/CardClass/card_class_group_del',
    options: {
      method: 'post',
      data,
    },
  });
}

// 设置课程分组
export function setMerchantCardClassGroup() {
  return request({
    url: 'Merchant/CardClass/card_class_group_set',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 授课方式是否可编辑
export function checkMerchantUpdateUserNo() {
  return request({
    url: 'Merchant/CardClass/check_update_user_no',
    options: {
      method: 'post',
      showError: false,
    },
  });
}

export default getMerchantCardAuth;
