import request from '@/request/index';
// 添加课程分组
export function addCardClassGroup() {
  return request({
    url: 'Web/CardClassGroup/add',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 设置课程分组
export function setCardClassGroup() {
  return request({
    url: 'Web/CardClassGroup/set',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function delCardClassGroup(data?: any) {
  return request({
    url: 'Web/CardClassGroup/del',
    options: {
      method: 'post',
      data,
    },
  });
}

export function getCardClassGroup(showError = true) {
  return request({
    url: 'Web/CardClassGroup/list',
    options: {
      method: 'post',
      showError,
    },
    immediate: false,
  });
}

export function getCardClassGroupCardList() {
  return request({
    url: 'Web/CardClassGroup/getCardClassGroupCardList',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function getGroupCardList(data?: any) {
  return request({
    url: 'Web/CardClassGroup/get_group_card_list',
    options: {
      method: 'post',
      data,
    },
  });
}

export default { getCardClassGroup };
