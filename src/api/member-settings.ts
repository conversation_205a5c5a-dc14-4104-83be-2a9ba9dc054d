import request from '@/request/index';

// 保存协议
export function saveProtocol(data: any) {
  return request({
    url: '/Web/BusinessFit/agreementUpsert',
    options: {
      method: 'post',
      data,
    },
  });
}

// 获取协议
export function getProtocol() {
  return request({
    url: 'Web/BusinessFit/agreementInfo',
    options: {
      method: 'get',
    },
  });
}

// 保存设置
export function saveSetting(data: any) {
  return request({
    url: 'Web/BusinessFit/storeConfigUpdate ',
    options: {
      method: 'post',
      data,
    },
  });
}

// 获取设置
export function getSetting() {
  return request({
    url: 'Web/BusinessFit/storeConfig ',
    options: {
      method: 'get',
    },
  });
}
