import request from '@/request/index';

// 收银流水汇总表
export function getReceiveLogAll() {
  return request({
    url: 'merchant/statistics/receiveLogConsolidated',
    options: { method: 'POST' },
    immediate: false,
  });
}
// 收银流水表
export function getReceiveLog() {
  return request({
    url: 'merchant/statistics/ReceiveLogList',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 业务流水汇总表
export function getBusinessLogAll() {
  return request({
    url: 'merchant/statistics/financialFlowNewConsolidated',
    options: { method: 'POST' },
    immediate: false,
  });
}
// 业务流水表
export function getBusinessLog() {
  return request({
    url: 'merchant/statistics/getFinancialFlowNew',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 合同订单汇总表
export function getOrderLogAll() {
  return request({
    url: 'merchant/statistics/cardOrderSumList',
    options: { method: 'POST' },
    immediate: false,
  });
}
// 合同流水表
export function getOrderLog() {
  return request({
    url: 'merchant/statistics/cardOrderList',
    options: { method: 'POST' },
    immediate: false,
  });
}
// 门店合同流水表
export function cardOrderList() {
  return request({
    url: 'Web/Statistics/cardOrderList',
    options: { method: 'POST' },
    immediate: false,
  });
}
// 合同流水数据详情
export function getOrderLogInfo() {
  return request({
    url: 'web/Statistics/statistics_card_order_info',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 流水汇总表
export function getFlowLogAll() {
  return request({
    url: 'merchant/Statistics/getSummaryList',
    options: { method: 'POST' },
    immediate: false,
  });
}
// bus_id、type
export function getSummaryGroup() {
  return request({
    url: 'Web/Summary/getGroupList',
    options: { method: 'POST' },
    immediate: false,
  });
}
export function getSummaryGroupList() {
  return request({
    url: 'Web/Summary/getSummaryGroupList',
    options: { method: 'POST' },
    immediate: false,
  });
}
export function putSummaryGroup() {
  return request({
    url: 'Web/Summary/groupOperate',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 报表菜单导航
export function getMenu() {
  return request({
    url: 'merchant/statistics/getStatisticsMenulist',
    options: { method: 'GET' },
    immediate: false,
  });
}

// 客流汇总表
export function getPassengerLogAll() {
  return request({
    url: '/merchant/statistics/getFlowList',
    options: { method: 'POST' },
    immediate: false,
  });
}
// 团课时汇总表
export function getOpenClassLogAll() {
  return request({
    url: '/merchant/statistics/getOpenClassSumList',
    options: { method: 'POST' },
    immediate: false,
  });
}
// 散场票汇总表
export function getTicketLogAll() {
  return request({
    url: '/merchant/statistics/getSanLogSumList',
    options: { method: 'POST' },
    immediate: false,
  });
}
// 场地收益汇总表
export function getSpaceIncomeLogAll() {
  return request({
    url: '/merchant/statistics/getIncomeSumList',
    options: { method: 'POST' },
    immediate: false,
  });
}
// 跨店销售对账表
export function getReconciliationLog() {
  return request({
    url: '/merchant/statistics/cardOrderDebtList',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 训练次数统计
export function getUserSignStatistics() {
  return request({
    url: '/Web/Statistics/user_sign_statistics',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 商家概况 - 总场馆数量
export function getAllBusCount() {
  return request({
    url: '/merchant/statistics/getAllBusCount',
    options: { method: 'get' },
    immediate: false,
  });
}

// 商家概况 - 总客流
export function allPassengerFlow() {
  return request({
    url: '/merchant/statistics/allPassengerFlow',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 商家概况 - 总客流排行
export function allPassengerFlowList() {
  return request({
    url: '/merchant/statistics/allPassengerFlowList',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 商家概况 - 总客流折线图
export function allPassengerFlowPage() {
  return request({
    url: '/merchant/statistics/allPassengerFlowPage',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 商家概况 - 总流水
export function allFinclFlow() {
  return request({
    url: '/merchant/statistics/allFinclFlow',
    options: { method: 'POST' },
    immediate: false,
  });
}
// 商家概况 - 总流水排行
export function ranckFinclFlow() {
  return request({
    url: '/merchant/statistics/ranckFinclFlow',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 商家概况 - 总流水折线图
export function allFinclFlowPage() {
  return request({
    url: '/merchant/statistics/allFinclFlowPage',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 商家概况 - 总业绩
export function allCardOrderCount() {
  return request({
    url: '/merchant/statistics/allCardOrderCount',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 商家概况 - 总业绩排行
export function ranckCardOrder() {
  return request({
    url: '/merchant/statistics/ranckCardOrder',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 商家概况 - 总业绩折线
export function cardOrderPage() {
  return request({
    url: '/merchant/statistics/cardOrderPage',
    options: { method: 'POST' },
    immediate: false,
  });
}

// 线下签署
export function orderSign() {
  return request({
    url: 'Web/Statistics/order_sign',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
// 取消线下签署
export function cancelOrderSign() {
  return request({
    url: 'Web/Statistics/cancel_order_sign',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
// 获取业务流水
export function getFinancialFlow() {
  return request({
    url: 'Web/statistics/getFinancialFlowNew',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 业务流水导出
export function exportFlow() {
  return request({
    url: 'Web/statistics/exportFlow',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
// 上海交大业务流水导出
export function tableTwo() {
  return request({
    url: 'Web/Statistics/tableTwo',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export default {};
