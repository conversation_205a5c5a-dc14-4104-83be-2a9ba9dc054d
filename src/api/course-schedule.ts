import request from '@/request/index';
// 获取团课课程排课
export function getClassList() {
  return request({
    url: 'Web/CourseSchedule/get_class_list',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function delScheduleOnce() {
  return request({
    url: 'Web/CourseSchedule/del_schedule_once',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function batchDelSchedules() {
  return request({
    url: 'Web/CourseSchedule/batch_del_schedules',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function delSchedules() {
  return request({
    url: 'Web/CourseSchedule/del_schedules',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function scheduleCopyThisWeek() {
  return request({
    url: 'Web/CourseSchedule/schedule_copy_this_week',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function getAddCourseScheduleData() {
  return request({
    url: 'Web/CourseSchedule/get_add_course_schedule_data',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function addCourseSchedule() {
  return request({
    url: 'Web/CourseSchedule/add_course_schedule',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function getCourseSchedule() {
  return request({
    url: 'Web/CourseSchedule/get_course_schedule',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function updateCourseSchedule() {
  return request({
    url: 'Web/CourseSchedule/update_course_schedule',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function scheduleCopyOnce() {
  return request({
    url: 'Web/CourseSchedule/schedule_copy_once',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export default { getClassList };
