import request from '@/request/index';

export function getAllPlanList(params: any) {
  return request({
    url: '/Web/PtChargePlan/get_pt_charge_plan_all_list',
    options: {
      method: 'GET',
      params,
    },
    immediate: true,
  });
}

export function getPlanList(params: any) {
  return request({
    url: '/Web/PtChargePlan/get_pt_charge_plan_list',
    options: {
      method: 'GET',
      params,
    },
    immediate: false,
  });
}

export function getPlanInfo(params: any) {
  return request({
    url: '/Web/PtChargePlan/get_pt_charge_plan_info',
    options: {
      method: 'GET',
      params,
    },
    immediate: true,
  });
}

export function addPlan(data: any) {
  return request({
    url: '/Web/PtChargePlan/add_pt_charge_plan',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
}

export function updatePlan(data: any) {
  return request({
    url: '/Web/PtChargePlan/edit_pt_charge_plan',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
}

export function removePlan(data: any) {
  return request({
    url: '/Web/PtChargePlan/del_pt_charge_plan',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
}

export function getCourseTree(params: any) {
  return request({
    url: '/Web/PtChargePlan/get_pt_charge_plan_detail_list',
    options: {
      method: 'GET',
      params,
    },
    immediate: true,
  });
}

export function getCourseList(params: any) {
  return request({
    url: '/Web/PtChargePlan/get_pt_all_card',
    options: {
      method: 'GET',
      params,
    },
    immediate: true,
  });
}

export function setCardPrice(data: any) {
  return request({
    url: '/Web/PtChargePlan/batch_update_price',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
}

export function setCoach(data: any) {
  return request({
    url: '/Web/PtChargePlan/update_coach_pt_charge_plan',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
}

export function getInformation(data: any) {
  return request({
    url: '/Web/PtChargePlan/get_pt_refund_info',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
}

export function refundPt(data: any) {
  return request({
    url: '/Web/PtSchedule/cancel_pt_schedule',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
}

export function refundSwim(data: any) {
  return request({
    url: '/Web/PtSchedule/cancel_swim_schedule',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
}

export function getCoachList(params: any) {
  return request({
    url: '/Web/PtChargePlan/get_private_coach',
    options: {
      method: 'GET',
      params,
    },
    immediate: true,
  });
}

export function getCardsForAll(params: any = {}) {
  return request({
    url: '/Web/Activity/get_cards_for_all',
    options: {
      method: 'GET',
      params,
    },
    immediate: true,
  });
}

export function getSpaceListByBusId(data: any) {
  return request({
    url: '/Web/Space/getSpaceNewNumber',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
}

export function saveSpacePrice(data: any) {
  return request({
    url: '/web/Space/SpaceMemberPriceSave',
    options: {
      method: 'POST',
      data,
    },
    immediate: true,
  });
}

export function getAuthority() {
  return request({
    url: '/Web/Admin/check_plan_auth',
    options: {
      method: 'GET',
    },
    immediate: true,
  });
}

export default {};
