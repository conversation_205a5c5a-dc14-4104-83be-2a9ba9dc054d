import request from '@/request/index';

export function wxinit() {
  return request({
    url: 'Web/Wap/wxinit',
    options: {
      method: 'GET',
    },
  });
}
export function wapIndex() {
  return request({
    url: 'Web/Wap/index',
    options: {
      method: 'GET',
    },
  });
}
export function entryVerification(data) {
  return request({
    url: 'Web/Wap/entryVerification',
    options: {
      method: 'POST',
      data,
    },
    immediate: false,
  });
}
export function verificationList(data) {
  return request({
    url: 'Web/Wap/verificationList',
    options: {
      method: 'POST',
      data,
    },
  });
}

export default wxinit;
