import request from '@/request/index';
import { AnyObject } from '@/types/global';

export interface MerchantMemberListData {
  // 所属场馆的bus_id
  belong_bus_id?: string;
  // 入会开始时间
  buy_card_begin_time?: string;
  // 入会结束时间
  buy_card_end_time?: string;
  // 满足的条件类型
  condition_type?: string;
  // 当然分页条数，不传默认10
  limit?: string;
  // 当前查询页数，不传默认1
  page?: string;
  // 标签id，多个用逗号连接
  product_tag?: string;
  // 姓名/电话/身份证
  search?: string;
  // 注册开始时间
  ub_create_begin_time?: string;
  // 注册结束时间
  ub_create_end_time?: string;
  // 会员类别
  user_category?: string;
  // 会员等级
  user_level?: string;
}

export interface MemberListType {
  // 会员头像
  avatar: string;
  // 场馆列表
  bus_list: string[];
  // 入会时间
  first_buy_card_t: string;
  // 聚合数据来源场馆id
  from_bus_id: number;
  // 商家会员id
  merchant_user_id: number;
  // 商家id
  merchants_id: number;
  // 会员手机号
  phone: string;
  // 购买过的产品
  tag_list: string[];
  tag_list_custom: AnyObject[];
  // 注册时间
  ub_create_time: string;
  // 会员等级
  user_level: string;
  // 会员真实姓名
  username: string;
  // 当前有效产品
  valid_tag_list: string[];
  valid_tag_list_custom: AnyObject[];
}
export interface MerchantMemberListRes {
  // 总条数
  count: number;
  // 数据列表
  list: MemberListType[];
  // 最大页数
  max_page: number;
  // 当前页
  page: number;
}

// 商家端会员列表
export function getMerchantMemberList() {
  return request<MerchantMemberListRes>({
    url: '/Merchant/MemberList/getList',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 当前商家下的所有场馆列表
export function getAdminBusList(data?: any) {
  return request({
    url: '/Web/MemberList/getAdminBusList',
    options: {
      method: 'post',
      data,
    },
  });
}

// 会员列表下拉搜索项配置
export function getMemberSelectConfig() {
  return request({
    url: '/Merchant/MemberList/getSelectConfig',
    options: {
      method: 'post',
    },
  });
}
