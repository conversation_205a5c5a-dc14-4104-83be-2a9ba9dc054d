import request from '@/request/index';

export function getProductSettingList(data: any) {
  return request({
    url: '/Merchant/CardRule/getList',
    options: {
      method: 'post',
      data,
    },
    immediate: false,
  });
}

export function getStoreSettingList(data: any) {
  return request({
    url: '/Merchant/CardRule/getThirdPlatformShopId',
    options: {
      method: 'post',
      data,
    },
    immediate: false,
  });
}

export function removeProductSetting(data: any) {
  return request({
    url: '/Web/CardRule/del',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

export function updateStoreSetting(data: any) {
  return request({
    url: '/Merchant/CardRule/updateThirdPlatformShopId',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

export function getModifyAuthority() {
  return request({
    url: '/Merchant/CardRule/card_rule_auth',
    options: {
      method: 'get',
    },
    immediate: true,
  });
}
