import request from '@/request/index';

export function getMerchantsBusList() {
  return request({
    url: '/Web/Card/get_merchants_bus_list',
    options: {
      method: 'post',
    },
  });
}

export function getCardList(data?: any, immediate = false) {
  return request({
    url: '/Web/Card/get_card_list',
    options: {
      method: 'post',
      data,
    },
    immediate,
  });
}

export function getCardAuth() {
  return request({
    url: '/Web/Card/card_auth',
    options: {
      method: 'get',
    },
  });
}

export function getUniversalCardAuth() {
  return request({
    url: '/Web/Card/universal_card_auth',
    options: {
      method: 'get',
    },
  });
}

export function getExperienceCardAuth() {
  return request({
    url: '/Web/Card/experience_card_auth',
    options: {
      method: 'get',
    },
  });
}
export function getSwimCard() {
  return request({
    url: '/Web/Card/swim_card',
    options: {
      method: 'get',
    },
  });
}
export function getMultiPtCardAuth() {
  return request({
    url: '/Web/Card/multi_pt_card_auth',
    options: {
      method: 'get',
    },
  });
}
export function getCardClass(data?: any) {
  return request({
    url: '/Web/Card/get_card_class',
    options: {
      method: 'post',
      data,
    },
  });
}
export function setCardSaleStatus() {
  return request({
    url: '/Web/Card/set_sale_status',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function setCardStatus() {
  return request({
    url: '/Web/Card/set_card_status',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function checkSurportOnline() {
  return request({
    url: '/Web/Card/check_surport_online',
    options: {
      method: 'get',
    },
  });
}

export function deleteCards(ids) {
  return request({
    url: `/Web/Card/delete_cards/ids/${ids}`,
    options: {
      method: 'get',
    },
  });
}

export function getCardDetail(busId, cardId) {
  return request({
    url: `/Web/Card/get_card_detail/busid/${busId}/id/${cardId}`,
    options: {
      method: 'get',
    },
  });
}

export function addCard() {
  return request({
    url: '/Web/Card/add_card',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function updateCard() {
  return request({
    url: '/Web/Card/update_card',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function allStoredValueCardSet() {
  return request({
    url: '/Web/Card/allStoredValueCardSet',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function checkUpdateUserNo() {
  return request({
    url: 'Web/Card/check_update_user_no',
    options: {
      method: 'post',
      showError: false,
    },
  });
}
// 获取商家下的卡种列表
export function getMerchantsCard() {
  return request({
    url: 'Web/Card/get_merchants_card',
    options: {
      method: 'post',
    },
  });
}

export default getMerchantsBusList;
