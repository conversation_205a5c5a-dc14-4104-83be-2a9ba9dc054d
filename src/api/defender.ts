import request from '@/request/index';

export function getDefenderList() {
  return request({
    url: '/web/AntiTailgating/getAntiTailgatingCheckList',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function setDefenderAlarm(data: any) {
  return request({
    url: '/web/AntiTailgating/enableAlarmNotify',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

export function getAlarmConfig(data: any) {
  return request({
    url: '/web/AntiTailgating/getConfigureAlarm',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

export function saveAlarmConfig(data: any) {
  return request({
    url: '/web/AntiTailgating/configureAlarm',
    options: {
      method: 'post',
      data,
    },
    immediate: true,
  });
}

export function getDeviceLogList() {
  return request({
    url: '/web/AntiTailgating/getTailgatingLogList',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
