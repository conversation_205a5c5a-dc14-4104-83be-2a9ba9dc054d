import request from '@/request/index';

// 获取订场设置
export function getSetting() {
  return request({
    url: 'Web/Space/getSetting',
    options: {
      method: 'post',
    },
  });
}

// 设置订场设置
export function saveSetting() {
  return request({
    url: 'Web/Space/saveSetting',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function getTypes() {
  return request({
    url: 'Web/Space/getTypes',
    options: {
      method: 'post',
    },
  });
}

export function getHolidayList() {
  return request({
    url: 'Web/Space/getHolidayList',
    options: {
      method: 'post',
      pageKey: {
        page: 'page',
        size: 'per_page',
      },
    },
    immediate: false,
  });
}

export function deleteHolidayList() {
  return request({
    url: 'Web/Space/deleteHolidayList',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
