import request from '@/request/index';

// 获取场馆会员黑名单
export function getBlackCardList() {
  return request({
    url: '/Web/MemberCard/card_disable_list',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 获取场馆会员的卡数据
export function getUserCardList() {
  return request({
    url: '/Web/MemberCard/card_user_list_by_card_disable',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 禁用会员卡
export function disabledUserCard() {
  return request({
    url: '/Web/MemberCard/card_disable',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 恢复/批量恢复会员卡
export function restoreUserCard() {
  return request({
    url: '/Web/MemberCard/card_disable_return',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
