import request from '@/request/index';

export function couponList() {
  return request({
    url: 'Web/Coupon/coupon_list',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function setCouponStatus() {
  return request({
    url: 'Web/Coupon/set_coupon_status',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function delCoupon() {
  return request({
    url: 'Web/Coupon/del_coupon',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function getBusCoupon(data: any) {
  return request({
    url: 'Web/Coupon/get_bus_coupon',
    options: {
      method: 'post',
      data,
    },
  });
}
export function getGrantList() {
  return request({
    url: 'Web/Coupon/get_grant_list',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function getReceiveList() {
  return request({
    url: 'Web/Coupon/get_receive_list',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function cardList() {
  return request({
    url: 'Web/Coupon/card_list',
    options: {
      method: 'post',
    },
  });
}

export function openClassCoupon() {
  return request({
    url: 'Web/Coupon/open_class_coupon',
    options: {
      method: 'post',
      showError: false,
    },
  });
}

export function addCoupon() {
  return request({
    url: 'Web/Coupon/add_coupon',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export default couponList;
