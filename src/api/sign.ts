import request from '@/request/index';

// 团课签到
export function userSign() {
  return request({
    url: 'Web/Sign/user_sign',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
// 批量签到
export function batchUserSign() {
  return request({
    url: 'Web/Sign/batch_user_sign',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
// 搜索后获取除用户基本信息外的其它信息
export function userAttribute(data: any) {
  return request({
    url: 'Web/Sign/user_attribute',
    options: {
      method: 'post',
      data,
    },
  });
}
