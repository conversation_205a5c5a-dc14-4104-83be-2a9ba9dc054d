import request from '@/request/index';

// 英派斯设备列表
export function getBusDeviceList() {
  return request({
    url: 'web/YpsConfig/getBusDeviceList',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

// 英派斯设备添加
export function addYpsDevice() {
  return request({
    url: 'web/YpsConfig/addYpsDevice',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function UpdateYpsDevice() {
  return request({
    url: 'web/YpsConfig/UpdateYpsDevice',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function getYpsDeviceInfo(data) {
  return request({
    url: 'web/YpsConfig/getYpsDeviceInfo',
    options: {
      method: 'post',
      data,
    },
  });
}

export function openOrStop() {
  return request({
    url: 'web/YpsConfig/OpenOrStop',
    options: {
      method: 'POST',
    },
    immediate: false,
  });
}
