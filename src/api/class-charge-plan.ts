import request from '@/request/index';

export function getClassChargePlanList() {
  return request({
    url: 'Web/ClassChargePlan/get_class_charge_plan_list',
    options: {
      method: 'get',
    },
    immediate: false,
  });
}

export function delClassChargePlan() {
  return request({
    url: 'Web/ClassChargePlan/del_class_charge_plan',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function addClassChargePlan() {
  return request({
    url: 'Web/ClassChargePlan/add_class_charge_plan',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}
export function editClassChargePlan() {
  return request({
    url: 'Web/ClassChargePlan/edit_class_charge_plan',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function getClassChargePlanAllList() {
  return request({
    url: 'Web/ClassChargePlan/get_class_charge_plan_all_list',
    options: {
      method: 'post',
    },
  });
}

export function wxPayMarkClass() {
  return request({
    url: 'Web/ClassChargePlan/wx_pay_mark_class',
    options: {
      method: 'post',
      showError: false,
    },
  });
}

export function getClassChargePlanInfo(data: any, immediate = false) {
  return request({
    url: 'Web/ClassChargePlan/get_class_charge_plan_info',
    options: {
      method: 'post',
      data,
    },
    immediate,
  });
}

export default { getClassChargePlanList };
