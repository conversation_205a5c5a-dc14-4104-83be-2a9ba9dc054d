import request from '@/request/index';

// 获取三方平台设置
export function getThirdPlatformShopId() {
  return request({
    url: 'Web/SanGroup/getThirdPlatformShopId',
    options: {
      method: 'post',
    },
  });
}

// 设置三方平台设置
export function updateThirdPlatformShopId() {
  return request({
    url: 'Web/SanGroup/updateThirdPlatformShopId',
    options: {
      method: 'post',
    },
    immediate: false,
  });
}

export function sanGroupAuth(params?: any) {
  return request({
    url: 'Web/SanGroup/auth',
    options: {
      method: 'get',
      params: { redirect_url: window.location.href, ...params },
    },
  });
}

export function getThirdSanList(params?: any) {
  return request({
    url: 'Web/SanGroup/getThirdSanList',
    options: {
      method: 'get',
      params,
    },
    immediate: false,
  });
}

export function getVoucher(data?: any) {
  return request({
    url: 'Web/SanGroup/getVoucher',
    options: {
      method: 'post',
      data,
      showError: false,
    },
  });
}

export function getNeedAuthShops(params?: any) {
  return request({
    url: 'Web/sanGroup/getNeedAuthShops',
    options: {
      method: 'get',
      params,
    },
  });
}

export function authBatch(data) {
  return request({
    url: 'Web/sanGroup/authBatch',
    options: {
      method: 'post',
      data,
    },
  });
}
