/**
 * qiankun应用间通信处理
 */
import { initGlobalState, MicroAppStateActions } from 'qiankun';
import { RouteLocationNormalized } from 'vue-router';
import { AdminInfoState } from '@/store/modules/admin-info/types';
import { BusInfoState } from '@/store/modules/bus-info/types';

interface qiankunGlobalState {
  adminInfo: AdminInfoState;
  busInfo: BusInfoState;
  subRouteInfo: RouteLocationNormalized;
}
const initialState: qiankunGlobalState = {
  // 子应用的route信息
  subRouteInfo: {},
  // 场馆相关信息
  busInfo: {},
  // 账号信息
  adminInfo: {},
};
const qiankunActions: MicroAppStateActions = initGlobalState(initialState);

export default qiankunActions;
