import { loadMicroApp } from 'qiankun';

export const microAppConfig = [
  {
    name: 'mainsiteV2',
    entry: import.meta.env.PROD ? '/child/v2/' : '//localhost:8089',
    container: '#v2container',
    activeRule: '/v2',
  },
];

//  判断当前路由是否是微应用
export function isMicroApp(path: string) {
  return !!microAppConfig.some((item) => {
    return path.startsWith(item.activeRule);
  });
}

// 查找当前匹配微应用配置
export function findMicroAppByPath(path: string) {
  return microAppConfig.find((item) => {
    return path.startsWith(item.activeRule);
  });
}

// 手动加载微应用
export function loadMicroByPath(path: string) {
  const config = findMicroAppByPath(path);
  loadMicroApp({ ...config });
}
