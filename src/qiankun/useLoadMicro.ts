import type { RouteLocationNormalized } from 'vue-router';
import { loadMicroApp } from 'qiankun';
import { findMicroAppByPath } from '@/qiankun/config';
import { listenerRouteChange } from '@/utils/route-listener';
import qiankunActions from '@/qiankun/actions';
import { useAppStore } from '@/store';
import loadTMap from '@/request/requestTMap';

export default function useLoadMicro() {
  let watchQiankunStateFlag = false;
  function watchQiankunStateChange() {
    if (watchQiankunStateFlag) {
      return;
    }
    watchQiankunStateFlag = true;
    const appStore = useAppStore();
    qiankunActions.onGlobalStateChange((state) => {
      const subMeta = state.subRouteInfo?.meta;
      if (subMeta && Object.keys(subMeta).length) {
        appStore.updateSettings({
          navbar: !subMeta.hideHeader,
          menu: !subMeta.hideSidebar,
        });
      }

      if (subMeta && subMeta.showMap) {
        if (window?.TMap) {
          console.log('已加载地图!');
        } else {
          console.log('赶快加载地图!');
          loadTMap();
        }
      }
    });
  }
  function watchRouterToLoadMicro() {
    // 缓存应用实例
    const microList = new Map([]);
    // 当前应用配置
    let current;
    listenerRouteChange((route: RouteLocationNormalized) => {
      const conf = findMicroAppByPath(route.path);
      // 子应用跳转
      if (conf) {
        // 未切换子应用
        if (current && current.activeRule === conf.activeRule) {
          return;
        }
        const cacheMicro = microList.get(conf.activeRule);
        // 已缓存应用
        if (cacheMicro) {
          return;
        }
        // 未缓存应用
        const micro = loadMicroApp({ ...conf, route });
        microList.set(conf.activeRule, micro);
        current = conf;
      }
    });
  }

  return {
    watchRouterToLoadMicro,
    watchQiankunStateChange,
  };
}
