import { createApp } from 'vue';
// import { registerMicroApps } from 'qiankun';
import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import { isBrandSite } from '@/config/url';
// import { microAppConfig } from '@/qiankun/config';
import globalComponents from '@/components';
import MyIcon from '@/components/menu/icon.vue';
// import initIconPark from '@/utils/icon-park';
import router from './router';
import store from './store';
import directive from './directive';
import App from './App.vue';
// Styles are imported via arco-plugin. See config/plugin/arcoStyleImport.ts in the directory for details
// 样式通过 arco-plugin 插件导入。详见目录文件 config/plugin/arcoStyleImport.ts
// https://arco.design/docs/designlab/use-theme-package
import '@/assets/style/global.less';

// 注入全局window变量 IS_BRAND_SITE 用于判断是否在品牌端
isBrandSite();
const app = createApp(App);

app.use(ArcoVue, {});
app.use(ArcoVueIcon);
// initIconPark(app);

app.use(router);
app.use(store);
app.use(globalComponents);
app.use(directive);

/**
 * 全局挂载组件 MyIcon 2023/09/19
 * 后续其他页面无需引入直接使用  <my-icon icon='' size='' />
 */
app.component('MyIcon', MyIcon);

app.mount('#mainApp');

// registerMicroApps(microAppConfig);
