/* 主题相关 */

/* 颜色 */
@theme-primary: #FF2351; // 品牌色
@theme-warning: #FF6323; // 警示色
@theme-success: #45BF55; // 成功色
@theme-link: #03B6FF; // 链接色
@theme-cyan: #34D1C9; // 青色
@theme-yellow: #FFD123; // 黄色
@theme-purple: #6323FF; // 紫色
@theme-pinkipurple: #D123FF; // 粉紫
@theme-blue: #2351FF; // 深蓝
@theme-magenta: #FF23BF; // 洋红
@theme-lime: #BFFF23; // 黄绿

@theme-primary-light: #FFE9EE;
@theme-warning-light: #FFF0E9;
@theme-success-light: #E8F7EA;
@theme-link-light: #D3F2FF;
@theme-cyan-light: #D9F6F4;
@theme-yellow-light: #FFF3C8;
@theme-purple-light: #F3EEFF;
@theme-pinkipurple-light: #FBECFF;
@theme-blue-light: #EDF1FF;
@theme-magenta-light: #FFE9F9;
@theme-lime-light: #EFFFC8;

/* 文字 */
@theme-text-color-1: #1D2129; // 强调/正文
@theme-text-color-2: #4E5969; // 次强调/正文
@theme-text-color-3: #AEB8C5; // 次要信息
@theme-text-color-4: #8A96A8; // 置灰信息
@theme-text-color-while: #FFFFFF; // 纯白文字

/* 线条 */
@theme-line-color-1: #F2F3F5; // 浅
@theme-line-color-2: #E5E6EB; // 一般
@theme-line-color-3: #C9CDD4; // 深/悬浮
@theme-line-color-4: #86909C; // 重/按钮描边

/* 填充 */
@theme-line-color-while: #FFFFFF; // 纯白填充
@theme-line-color-1: #E2E5E9; // 浅/禁用
@theme-line-color-2: #F7FAFF; // 常规/白底悬浮
@theme-line-color-3: #F5F8FE; // 常规/白底悬浮2
@theme-line-color-4: #E5E6EB; // 深/灰底悬浮
@theme-line-color-5: #C9CDD4; // 重/特殊场景

