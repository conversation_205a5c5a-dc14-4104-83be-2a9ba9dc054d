@import './theme.less';

* {
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  background-color: var(--color-bg-1);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

a,
article,
aside,
audio ,
b,
blockquote,
body,
button,
canvas,
dd,
div,
dl,
dt,
em,
embed,
footer,
form,
h1,
h2,
h3,
h4,h5,
h6,
header,
hgroup,
html,
i,
img,
label,
legend,
li,
menu,
nav,
ol,
output,
p,
pre,
section,
span,
strong,
summary,
table,
tbody,
td,
tfoot,
th,
thead,
tr,
ul,
video {
  margin: 0;
  padding: 0;
  // border: 0;
  // font-size: 100%;
  // font: inherit;
  // vertical-align: baseline;
}

em, i {
  font-style: normal;
}

ul, li {
  list-style: none;
  list-style-type: none;
}

img {
  vertical-align: middle;
}

a {
  text-decoration: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

button, input, a, div, img {
  outline: 0;
}

#mainApp {
  height: 100%;
}

//scroll
::-webkit-scrollbar-track {
  border-radius: 3px;
  background-color: #fff;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #ccc;
}

// base-box
.base-box {
  padding: 0 20px 20px 20px;
}

.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgba(253, 254, 255, 0.6) -6.04%,
    rgba(244, 247, 252, 0.6) 85.2%
  ) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  .content-panel {
    display: flex;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    width: 164px;
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    margin: 0;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}

.general-card {
  border-radius: 4px !important;
  border: none !important;
  &.arco-card > .arco-card-header {
    height: auto;
    padding: 20px;
    border: none;
  }
  &.arco-card > .arco-card-body {
    padding: 20px;
  }

  .arco-row {

    .arco-form {
      .arco-form-item {
        margin-bottom: 12px;
      }
    }
  }
}

// form
.general-form {
  &.arco-form {
    width: 800px;
  }
}

.split-line {
  border-color: rgb(var(--gray-2));
}

.arco-table-cell {
  .circle {
    display: inline-block;
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: rgb(var(--blue-6));
    &.pass {
      background-color: rgb(var(--green-6));
    }
  }
}
.disabled-row.arco-table-tr td {
  color: #ccc !important;
}

// 财务-收入/支出/合计
.finance-statistics {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: auto;
  min-width: 145px;
  min-height: 82px;
  padding: 10px 16px;
  background: #F5F8FE;
  border-radius: 4px;

  .arco-statistic-title {
    white-space: nowrap;
    line-height: 20px;
    font-weight: bold;
  }

  .arco-statistic-content {
    .arco-statistic-value {
      line-height: 34px;
      font-weight: 600;
      font-size: 26px;
    }

    .arco-statistic-prefix {
      margin-right: 8px;
      vertical-align: text-top;
    }

    .arco-statistic-value-decimal {
      font-size: 26px;
    }
  }
}

.finance-income {
  background: @theme-warning-light;
}

.finance-expenditure {
  background: @theme-cyan-light;
}

.finance-amountTo {
  background: @theme-primary-light;
}

// 模态框高度超过一屏时出现滚动条
.arco-modal-body {
  max-height: calc(100vh - 200px);
  overflow: auto;
  .general-form {
    &.arco-form {
      width: 100%;
    }
  }
}

// 通用竖向tab
.base-tabs-warp {
  height: calc(100% - 57px);

  .arco-tabs {
    width: 100%;
    height: 100%;

    .arco-tabs-nav {
      margin-right: 20px;
      width: 170px;
      &::before {
        width: 0;
      }
    }

    .arco-tabs-tab {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 0;
      margin-bottom: 10px;
      width: 156px;
      height: 74px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #e2e5e9;
      text-align: center;
      &.arco-tabs-tab-active {
        box-shadow: 0px 4px 6px 0px rgba(255, 35, 81, 0.15);
        background-color: #ffedf1;
        border: 2px solid #ff2351;
      }
    }

    .arco-tabs-content {
      padding: 20px;
      background-color: #fff;
      border: 0;
    }
    .arco-tabs-content-list,
    .arco-tabs-pane {
      height: 100%;
    }
  }
}
