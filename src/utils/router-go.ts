import router from '@/router';

const subPrefix = '/v2';
// 会员详情
export function goSubDetail(userId: string, busId?: string, isRouterPush = true) {
  const path = `${subPrefix}/member/detail/${userId}/${busId || ''}`;
  if (isRouterPush) {
    router.push({
      path: `${subPrefix}/member/detail/${userId}/${busId || ''}`,
    });
  }
  return path;
}
// 会员列表
export function goSubMemberList(query?: { curMenu?: string; search?: string }) {
  router.push({
    path: `${subPrefix}/member/`,
    query,
  });
}

// 散场票收入报表
export function goTicketIncome(query?: { busId?: string; beginDate?: string; endDate?: string }) {
  router.push({
    path: `${subPrefix}/stat/menus/spaceIncome`,
    query,
  });
}
// 场地收入报表
export function goSpaceIncome(params?: { bus_id?: string; start?: string; end?: string }) {
  router.push({
    path: `${subPrefix}/stat/menus/spaceAnalysisDetail`,
    state: params,
  });
}
// 使用帮助
export function goHelpPage(query?: { active?: number }) {
  router.push({
    path: `${subPrefix}/signin/manual`,
    query,
  });
}

// 会员卡管理
export function goCardManage() {
  router.push({
    path: `${subPrefix}/management/card`,
  });
}

// 电子合同签署流程
export function goElectronic() {
  router.push({
    path: `${subPrefix}/paraset/electronic`,
  });
}

// 生成课表
export function goSchedulePreview(state?: { selectedBusId?: string; scheduleList?: string; weekDateString?: string }) {
  router.push({
    path: `${subPrefix}/course/schedulePreview`,
    state,
  });
}

// 购卡规则设置
export function goCardRules() {
  router.push({
    path: `${subPrefix}/management/card/cardRules`,
  });
}

// 节假日调价记录编辑
export function goHolidaySetEdit(id?: string) {
  router.push({
    path: `${subPrefix}/stadium/holydaySettings${id ? `/${id}` : ''}`,
  });
}

// 票务核销
export function goTicketVerify(state?: { consume_sn?: string; platform_id?: string }) {
  window.open(
    `${subPrefix}/signin/nonMember?consume_sn=${state?.consume_sn}&platform_id=${state?.platform_id}`,
    '_blank'
  );
}
