type TargetContext = '_self' | '_parent' | '_blank' | '_top';

export const openWindow = (url: string, opts?: { target?: TargetContext; [key: string]: any }) => {
  const { target = '_blank', ...others } = opts || {};
  window.open(
    url,
    target,
    Object.entries(others)
      .reduce((preValue: string[], curValue) => {
        const [key, value] = curValue;
        return [...preValue, `${key}=${value}`];
      }, [])
      .join(',')
  );
};

export const regexUrl = new RegExp(
  '^(?!mailto:)(?:(?:http|https|ftp)://)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$',
  'i'
);
/* eslint-disable */
export function uuid() {
  let d = Date.now();
  if (
    typeof performance !== 'undefined' &&
    typeof performance.now === 'function'
  ) {
    d += performance.now(); // use high-precision timer if available
  }
  return 'xxxxxxxx-xxxx-8xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
}

// url为assets/img文件夹下的图片路径
export function getAssetsImg(url: string) {
  return new URL(`../assets/img/${url}`, import.meta.url).href;
}

/**
 * var tree = [{"id":1,"pid":"-1","son":[{"id":11,"pid":"1","son":[]},{"id":12,"pid":"1","son":[]}]}];
treeToList(tree,'son') //[{"id":1,"pid":"-1"},{"id":11,"pid":"1"},{"id":12,"pid":"1"}]
 */
export function treeToList(treeNodes: [], key: string) {
  let NodeList: Array<Object> = [];
  function appendChildren(nodes: []) {
    for (let i = 0; i < nodes.length; i++) {
      let node: any = nodes[i];
      NodeList.push(node);
      //判断是否有子节点
      if (node[key] && node[key].length > 0) {
        //所有子节点
        appendChildren(node[key]);
      }
      if (node.hasOwnProperty(key))
        delete node[key];
    }
  }
  appendChildren(treeNodes);
  return NodeList;
}

/**
 * check a string contain Chinese characters
 */
export function countChineseCharacters(str) {
  const chineseReg = /[\u4e00-\u9fa5]/g;
  const chineseMatches = str.match(chineseReg);
  return chineseMatches ? chineseMatches.length : 0;
}
export function isChinese(val) {
  const str = val || '';
  if (str.length >= 3 || countChineseCharacters(str) >= 1) {
    return true;
  }
  return false;
}

// HTML转义
export function unescapeHTML(a) {
  a = `${a}`;
  return a
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
export default null;
