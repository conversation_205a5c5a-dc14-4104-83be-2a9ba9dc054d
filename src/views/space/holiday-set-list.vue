<template>
  <div class="base-box">
    <Breadcrumb />
    <a-alert type="warning" style="margin-bottom: 16px">
      在系统设置>订场设置中，可设置国家法定节假日自动执行节日价格，无需手动配置节假日调价方案
    </a-alert>
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="name" label="日期">
                  <a-date-picker v-model="searchParam.start_date" format="YYYY-MM-DD" type="date" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="goHolidaySetEdit()">新增</a-button>
            <a-button :loading="isBatchCancelLoading" @click="batchDel">批量删除</a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel ref="exportExcel">
            <template #default="{ handleExport }">
              <a-button @click="handleClickExport(handleExport as Callback<ExportData>)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-model:selectedKeys="selectedIds" v-on="tableEvent">
        <template #columns>
          <a-table-column title="操作时间" data-index="update_time" />
          <a-table-column title="日期" data-index="rangeTime" />
          <a-table-column title="场地" data-index="space_name" />
          <a-table-column title="操作人" data-index="operator_name" />
          <a-table-column title="状态" data-index="status" />
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space v-if="record.status === '执行中'">
                <a-link @click="goHolidaySetEdit(record.id)">编辑</a-link>
                <a-link status="danger" :loading="record.isDelLoading" @click="delPlan(record)">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { getHolidayList, deleteHolidayList } from '@/api/space';
  import { getOpenClassAll } from '@/api/open-class';
  import useTableProps from '@/hooks/table-props';
  import { goHolidaySetEdit } from '@/utils/router-go';
  import ExportExcel from '@/components/exportExcel.vue';
  import { Callback, ExportData } from '@/types/global';

  const selectedIds = ref([]);
  const classList = ref([]);
  const { tableProps, dataPath, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    getHolidayList,
    (list) => {
      return list.map((item) => {
        return {
          ...item,
          disabled: item.status !== '执行中',
          rangeTime: `${item.start_time} ~ ${item.end_time}`,
        };
      });
    }
  );

  tableProps.value['row-selection'] = {
    type: 'checkbox',
    showCheckedAll: true,
  };
  // const route = useRoute();
  dataPath.value = {
    list: 'data',
    count: 'total',
  };
  // 设置除分页外的其它属性值
  setSearchParam({
    start_date: '',
  });

  function getOpenClassList() {
    getOpenClassAll().then((res) => {
      classList.value = res.data.value.list;
    });
  }
  getOpenClassList();
  loadTableList();

  function delPlan(record: Record<string, any>) {
    const { isLoading, execute: executeDel } = deleteHolidayList();
    record.isDelLoading = isLoading;
    Modal.confirm({
      title: '提示',
      content: '确认删除？',
      onOk: () => {
        executeDel({ data: { ids: [record.id] } }).then((res: any) => {
          loadTableList();
          Message.success(res.response.value.errormsg);
        });
      },
    });
  }

  // 批量删除
  const { isLoading: isBatchCancelLoading, execute: executeBatchDel } = deleteHolidayList();
  function batchDel() {
    if (!selectedIds.value.length) {
      Message.error('请先勾选需要删除的项');
      return;
    }
    Modal.confirm({
      title: '提示',
      content: '确认批量删除？',
      onOk: () => {
        executeBatchDel({ data: { ids: selectedIds.value } }).then((res: any) => {
          selectedIds.value = [];
          loadTableList();
          Message.success(res.response.value.errormsg);
        });
      },
    });
  }

  const handleClickExport = (cb: Callback<ExportData>) => {
    const columns = [
      {
        title: '操作时间',
        dataIndex: 'update_time',
      },
      {
        title: '日期',
        dataIndex: 'rangeTime',
      },
      {
        title: '场地',
        dataIndex: 'space_name',
      },
      {
        title: '操作人',
        dataIndex: 'operator_name',
      },
      {
        title: '状态',
        dataIndex: 'status',
      },
    ];
    loadTableList(true).then((list) => {
      cb({
        filename: '临时调价记录',
        columns,
        data: list,
      });
    });
  };
</script>
