const pathStat = '/v2/stat/menus/'; // 原门店端页面路径
const pathFinance = '/v2/finance/'; // 原门店端页面路径
const newPath = '/stat/'; // 商家端页面路径

export const IMG: Record<string, any> = {
  业绩类报表: '',
  会员类报表: '',
  私教类报表: '',
  会籍类报表: '',
  泳教类报表: '',
  场馆汇总类报表: '',
  订场类报表: '',
  团课类报表: '',
};

type MenuStyle = { iconColor: string; icon: string; bgColor: string };
export const STYLE: Array<MenuStyle> = [
  { iconColor: '#3491FA', icon: '', bgColor: '#D3F2FF' },
  { iconColor: '#6323FF', icon: '', bgColor: '#FAE9FF' },
  { iconColor: '#00B42A', icon: '', bgColor: '#E8F7EA' },
  { iconColor: '#FF7D00', icon: '', bgColor: '#FFE0D3' },
  // { iconColor: '#FF2351', icon: '', bgColor: '#FFE0D3' },
  { iconColor: '#14C9C9', icon: '', bgColor: '#D9F6F4' },
  { iconColor: '#FADC19', icon: '', bgColor: '#FFF3C8' },
  { iconColor: '#D123FF', icon: '', bgColor: '#FFE9EE' },
  { iconColor: '#9FDB1D', icon: '', bgColor: '#FFF3C8' },
  { iconColor: '#F5319D', icon: '', bgColor: '#FFE9F9' },
  { iconColor: '#2351FF', icon: '', bgColor: '#D3F2FF' },
  { iconColor: '#00B42A', icon: '', bgColor: '#E8F7EA' },
];

// 此处顺序影响报表导航展示顺序
export const MENU_CLASS: string[] = [
  '6_bus_statistics',
  '1_achievements',
  '2_members',
  '3_coaches',
  '4_membership',
  '5_swimming',
  '8_open_class',
  '7_booking_space',
];

export const MENU_CLASS_OBJ: Record<string, any> = {
  '1_achievements': '业绩类报表',
  '2_members': '会员类报表',
  '4_membership': '会籍类报表',
  '3_coaches': '私教类报表',
  '5_swimming': '泳教类报表',
  '6_bus_statistics': '场馆汇总类报表',
  '7_booking_space': '订场类报表',
  '8_open_class': '团课类报表',
};

// 后端返回英文名和前端显示中文菜单名一一对应
export const MENU_CHART: Record<string, any> = {
  '1_achievements': {
    get_bus_income_details: '收入分析',
    cardOrderList: '订单报表',
    get_salary_list: '薪资报表',
    financial_allocation2: '收入分摊',
    deposit_card_statistics: '储值卡使用统计',
    deposit_card_statistics_detail: '储值卡消费品类详情',
    new_card_analysis: '卡课分析',
  },
  '2_members': {
    user_classify: '会员分析',
    customer_behave_analyze_data: '会员到场分析',
    pt_class_analyze_data: '私教会员分析',
    bus_daily_purchase_card_statistics_data: '购续会员卡情况',
    bus_daily_purchase_pt_statistics_data: '购续私教情况',
    user_sign_statistics_data: '训练次数统计',
    new_join_report: '新入会会员月报',
    new_transfer_report: '会员转化分析',
  },
  '3_coaches': {
    // get_class_statistics: '上课统计',
    new_pt_statistics: '私教课时统计',
    coach_statistics_detail_data: '私教业绩统计',
    coach_brief_review: '私教业绩概况',
    getPrivateCoachAchievementMonthTotalData: '私教业绩月报',
    coach_assessment: '私教课评价报表',
    // coach_assigning_task: '私教分配任务完成情况',
    coach_class_statistics_data: '私教课时概况',
    team_class_private_statistics_data: '私教班课时统计',
  },
  '4_membership': {
    membership_statistics_data: '会籍业绩概况',
    getMemberShipAchievementMonthTotalData: '会籍业绩月报',
    ms_brief_review: '会籍工作概况',
    ms_statistics: '会籍业绩统计',
    membership_assigning_task_data: '会籍分配任务完成情况',
  },
  '5_swimming': {
    swimming_class_period_statistics: '泳教课时统计',
    swimming_achievement_statistics: '泳教业绩统计',
    swimming_brief_review: '泳教业绩概况',
    getSwimCoachAchievementMonthTotalData: '泳教业绩月报',
    swimming_class_comment: '泳教课评价报表',
    swimming_class_statistics_data: '泳教课时概况',
    team_class_swim_statistics_data: '泳教班课时统计',
  },
  '6_bus_statistics': {
    bus_statistics_data: '总业绩汇总',
    bus_statistics_detail_ms_data: '会籍业绩汇总',
    bus_statistics_detail_pt_data: '私教业绩汇总',
    bus_statistics_detail_swim_data: '泳教业绩汇总',
    // bus_fincl_flow_data: '业务流水汇总',
    bus_statistics_class_pt_data: '私教课时汇总',
    bus_statistics_class_swim_data: '泳教课时汇总',
    bus_statistics_income_sum: '客流汇总',
    bus_statistics_open_class_sum: '团课课时汇总',
    bus_statistics_san_log_sum: '散场票汇总',
    bus_statistics_sapce_sum: '场地收益汇总',
  },
  '7_booking_space': {
    space_income_statistics: '场地收益概况',
    space_income_list: '场地收益明细',
    san_log_statistics: '散场票收入报表',
  },
  '8_open_class': {
    open_class_comment_statistics_data: '团课评价报表',
    new_public_class_statistics: '操课课时统计',
    new_class_statistics: '团课课时统计',
    course_effect_statistics: '课程效果统计',
    classroom_statistics: '教室使用统计',
    openclass_user_month_all_statistics: '会员上课统计',
    openclass_sign_lists: '会员上课明细',
  },
};

// 最终要将后端返回的数据对应转化为类似如下格式 (menus.menu)
export const MENU: Record<string, any> = {
  业绩类报表: {
    收入分析: {
      name: '收入分析',
      path: `${pathStat}revenueStatis`,
    },
    订单报表: {
      name: '订单报表',
      path: `${pathFinance}orderList`,
    },
    薪资报表: {
      name: '薪资报表',
      path: `${pathFinance}salaryList`,
    },
    收入分摊: {
      name: '收入分摊',
      path: `${pathStat}financialAllocation2`,
    },
    储值卡使用统计: {
      name: '储值卡使用统计',
      path: `${pathStat}depositCard`,
    },
    储值卡消费品类详情: {
      name: '储值卡消费品类详情',
      path: `${pathStat}depositCardDetail`,
    },
    卡课分析: {
      name: '卡课分析',
      path: `${newPath}table-nav/card-report-brand`,
    },
  },
  会员类报表: {
    会员分析: {
      name: '会员分析',
      path: `${pathStat}analysis`,
    },
    会员到场分析: {
      name: '会员到场分析',
      path: `${pathStat}coachingBehavior`,
    },
    私教会员分析: {
      name: '私教会员分析',
      path: `${pathStat}ptClassInfo`,
    },
    购续会员卡情况: {
      name: '购续会员卡情况',
      path: `${pathStat}buyAndRenewalBehavior`,
    },
    购续私教情况: {
      name: '购续私教情况',
      path: `${pathStat}privateSold`,
    },
    训练次数统计: {
      name: '训练次数统计',
      path: `${newPath}table-nav/sign-list`,
    },
    新入会会员月报: {
      name: '新入会会员月报',
      path: `${newPath}table-nav/new-member-brand`,
    },
    会员转化分析: {
      name: '会员转化分析',
      path: `${newPath}table-nav/channel-report-brand`,
    },
  },
  私教类报表: {
    // 上课统计: {
    //   name: '上课统计',
    //   path: `${pathStat}#/web/coach/coachStatis`
    // },
    私教课时统计: {
      name: '私教课时统计',
      path: `${pathStat}classStat4Coach`,
      routeName: '私教课时',
      query: {
        type: 2,
      },
    },
    私教课时概况: {
      name: '私教课时概况',
      path: `${pathStat}coachCourseOverview`,
    },
    私教班课时统计: {
      name: '私教班课时统计',
      path: `${pathStat}privateTeamClass`,
    },
    // 私教课时统计: {
    //   name: '私教课时统计',
    //   path: `${pathStat}classStat`,
    //   routeName: '上课统计',
    //   params: {
    //     type: 2
    //   }
    // },
    私教业绩统计: {
      name: '私教业绩统计',
      path: `${pathStat}coach`,
    },
    私教业绩概况: {
      name: '私教业绩概况',
      path: `${pathStat}coachbriefReview`,
    },
    私教业绩月报: {
      name: '私教业绩月报',
      path: `${pathStat}ptMonthTotal`,
    },
    私教课评价报表: {
      name: '私教课评价报表',
      path: `${pathStat}comment4coachTable`,
    },
    // 私教分配任务完成情况: {
    //   name: '私教分配任务完成情况',
    //   path: `${pathStat}mission`,
    //   routeName: '分配任务完成情况',
    //   query: {
    //     type: 'coach'
    //   }
    // }
  },
  泳教类报表: {
    泳教课时统计: {
      name: '泳教课时统计',
      path: `${pathStat}classStat4Swim`,
      routeName: '泳教课时',
      query: {
        type: 2,
      },
    },
    泳教课时概况: {
      name: '泳教课时概况',
      path: `${pathStat}swimCourseOverview`,
    },
    泳教班课时统计: {
      name: '泳教班课时统计',
      path: `${pathStat}swimTeamClass`,
    },
    泳教业绩统计: {
      name: '泳教业绩统计',
      path: `${pathStat}swim`,
    },
    泳教业绩概况: {
      name: '泳教业绩概况',
      path: `${pathStat}swimBriefReview`,
    },
    泳教业绩月报: {
      name: '泳教业绩月报',
      path: `${pathStat}swimMonthTotal`,
    },
    泳教课评价报表: {
      name: '泳教课评价报表',
      path: `${pathStat}comment4swimTable`,
    },
  },
  会籍类报表: {
    会籍业绩概况: {
      name: '会籍业绩概况',
      path: `${pathStat}msbriefReviewNew`,
    },
    会籍工作概况: {
      name: '会籍工作概况',
      path: `${pathStat}msbriefReview`,
    },
    会籍业绩月报: {
      name: '会籍业绩月报',
      path: `${pathStat}monthTotal`,
    },
    会籍业绩统计: {
      name: '会籍业绩统计',
      path: `${pathStat}membership`,
    },
    会籍分配任务完成情况: {
      name: '会籍分配任务完成情况',
      path: `${pathStat}mission`,
      routeName: '分配任务完成情况',
      query: {
        type: 'membership',
      },
    },
  },
  场馆汇总类报表: {
    总业绩汇总: {
      name: '总业绩汇总',
      path: `${pathStat}totalPerformanceSummary`,
    },
    会籍业绩汇总: {
      name: '会籍业绩汇总',
      path: `${pathStat}membershipPerformanceSummary`,
    },
    私教业绩汇总: {
      name: '私教业绩汇总',
      path: `${pathStat}coachPerformanceSummary`,
    },
    泳教业绩汇总: {
      name: '泳教业绩汇总',
      path: `${pathStat}swimPerformanceSummary`,
    },
    // 业务流水汇总: { // 已转移到财务菜单下
    //   name: '业务流水汇总',
    //   path: `${pathStat}businessFlowSummary`,
    // },
    私教课时汇总: {
      name: '私教课时汇总',
      path: `${pathStat}coachCourseSummary`,
    },
    泳教课时汇总: {
      name: '泳教课时汇总',
      path: `${pathStat}swimCourseSummary`,
    },
    客流汇总: {
      name: '客流汇总',
      path: `${newPath}table-nav/passenger-flow-log-all`,
      // routerName: 'PassengerFlowLogAll'
    },
    团课课时汇总: {
      name: '团课课时汇总',
      path: `${newPath}table-nav/open_class-expend-log-all`,
    },
    散场票汇总: {
      name: '散场票汇总',
      path: `${newPath}table-nav/ticket-log-all`,
    },
    场地收益汇总: {
      name: '场地收益汇总',
      path: `${newPath}table-nav/space-income-log-all`,
    },
  },
  订场类报表: {
    场地收益概况: {
      name: '场地收益概况',
      path: `${pathStat}spaceAnalysis`,
    },
    场地收益明细: {
      name: '场地收益明细',
      path: `${pathStat}spaceAnalysisDetail`,
    },
    散场票收入报表: {
      name: '散场票收入报表',
      path: `${pathStat}spaceIncome`,
    },
  },
  团课类报表: {
    操课课时统计: {
      name: '操课课时统计',
      path: `${pathStat}classStat`,
      routeName: '上课统计',
      query: {
        type: 0,
      },
    },
    团课课时统计: {
      name: '团课课时统计',
      path: `${pathStat}classStat`,
      routeName: '上课统计',
      query: {
        type: 1,
      },
    },
    团课评价报表: {
      name: '团课评价报表',
      path: `${pathStat}commentNewClassTable`,
    },
    课程效果统计: {
      name: '课程效果统计',
      path: `${pathStat}classEffectStat`,
    },
    教室使用统计: {
      name: '教室使用统计',
      path: `${pathStat}classroomUseStat`,
    },
    会员上课统计: {
      name: '会员上课统计',
      path: `${pathStat}memberClassStat`,
    },
    会员上课明细: {
      name: '会员上课明细',
      path: `${pathStat}memberAttendClassDetail`,
    },
  },
};

export default {};
