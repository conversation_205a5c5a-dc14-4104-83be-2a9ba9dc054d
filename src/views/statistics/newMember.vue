<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col v-if="IS_BRAND_SITE" :span="8">
                <a-form-item field="bus_id" label="场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" placeholder="所属场馆" allow-search />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="rangeValue" label="统计月份">
                  <a-range-picker
                    v-model="rangeValue"
                    mode="month"
                    value-format="YYYY-MM"
                    :allow-clear="false"
                    :disabled-date="getDisabledDate"
                    :placeholder="['开始日期', '结束日期']"
                    @select="onSelect"
                    @popup-visible-change="onPopupVisibleChange" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>

        <a-divider style="height: 32px" direction="vertical" />

        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space></a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button @click="handleClickExport(handleExport as Callback<ExportData>)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>

      <a-table v-bind="tableProps" ref="tableRef" v-on="tableEvent">
        <template #columns>
          <a-table-column title="统计月份" data-index="date_month" />
          <a-table-column title="入会渠道" data-index="join_channel" />
          <a-table-column title="购买人数" data-index="member_num_total" />
          <a-table-column title="总单数" data-index="order_total" />
          <a-table-column title="总业绩" data-index="amount_total" align="right" />
          <a-table-column title="新入会会员人数" data-index="first_num_total" />
          <a-table-column title="新入会会员人数占比" data-index="first_percent" />
          <a-table-column title="新入会会员首单业绩" data-index="first_amount_total" align="right" />
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { webJoinBusReport } from '@/api/ironReport';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';

  import useTableProps from '@/hooks/table-props';
  import { useBusInfoStore } from '@/store';
  import { Callback, ExportData } from '@/types/global';

  import ExportExcel from '@/components/exportExcel.vue';

  const busInfo = useBusInfoStore();
  const { IS_BRAND_SITE } = window;
  const { isLoading, tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    webJoinBusReport,
    (list) => {
      return list.map((item) => {
        item.first_percent = `${item.first_percent}%`;
        return item;
      });
    }
  );

  const route = useRoute();
  const busId = computed(() => {
    return route.query.bus_id || busInfo.bus_id;
  });
  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: busId.value,
    begin_time: route.query.begin_time || dayjs().subtract(1, 'month').format('YYYY-MM'),
    end_time: route.query.end_time || dayjs().subtract(1, 'month').format('YYYY-MM'),
  });

  const rangeValue = computed({
    get() {
      if (searchParam.end_time) {
        return [searchParam.begin_time, searchParam.end_time];
      }
      return [];
    },
    set(val) {
      setSearchParam({
        begin_time: val[0],
        end_time: val[1],
      });
    },
  });

  // 支持查询起止月份，最长区间12个月。 起止：2025年1月 ~ 当月。
  const dates = ref([]);
  function onSelect(valueString, value) {
    dates.value = value;
  }
  function onPopupVisibleChange(visible) {
    if (!visible) {
      dates.value = [];
    }
  }
  const getDisabledDate = (current: Date) => {
    const startYear = 2025;
    const startMonth = 0;
    const startDate = dayjs(new Date(startYear, startMonth));

    const today = dayjs();
    const maxDate = today.endOf('month');

    let tooLate = false;
    let tooEarly = false;
    if (dates.value && (dates.value[0] || dates.value[1])) {
      const selDay = dates.value[0] || dates.value[1];
      const selectValueStart = dayjs(selDay);
      tooLate = selDay && selectValueStart.diff(current, 'month') >= 12;
      tooEarly = selDay && dayjs(current).diff(selectValueStart, 'month') >= 12;
    }
    return current < startDate.toDate() || current > maxDate.toDate() || tooLate || tooEarly;
  };

  const tableRef = ref(null);
  const handleClickExport = (cb: Callback<ExportData>) => {
    loadTableList(true).then((list) => {
      cb({
        filename: `新入会会员月报-${rangeValue.value[0]}-${rangeValue.value[1]}`,
        columns: [
          {
            title: '统计月份',
            dataIndex: 'date_month',
          },
          {
            title: '入会渠道',
            dataIndex: 'join_channel',
          },
          {
            title: '购买人数',
            dataIndex: 'member_num_total',
          },
          {
            title: '总单数',
            dataIndex: 'order_total',
          },
          {
            title: '总业绩',
            dataIndex: 'amount_total',
          },
          {
            title: '新入会会员人数',
            dataIndex: 'first_num_total',
          },
          {
            title: '新入会会员人数占比',
            dataIndex: 'first_percent',
          },
          {
            title: '新入会会员首单业绩',
            dataIndex: 'first_amount_total',
          },
        ],
        data: list,
      });
    });
  };

  loadTableList();
</script>
