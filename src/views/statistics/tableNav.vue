<template>
  <div>
    <div class="base-box">
      <Breadcrumb />
      <div class="base-tabs-warp">
        <a-tabs v-model="current" position="left" type="card-gutter" lazy-load>
          <a-tab-pane v-for="(item, index) in menus" :key="index" :title="item.className">
            <ul class="menu-wrap">
              <li
                v-for="(menu, subIndex) in item.menu"
                :key="subIndex"
                class="menu-link"
                :style="{ 'background-color': menu.bgColor }"
                @click="handleNavTo(menu)">
                <!-- <div class="icon-box"></div> -->
                <div class="menu-name">{{ menu.name }}</div>
              </li>
            </ul>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { getMenu } from '@/api/statistics';
  import { STYLE, MENU_CLASS, MENU_CLASS_OBJ, MENU_CHART, MENU } from './tableNav';

  defineOptions({
    name: 'TableNav',
  });

  const current = ref(0);

  const router = useRouter();

  const handleNavTo = (menu) => {
    router.push({
      path: `${menu.path}`,
      query: menu.query,
    });
  };

  const menus = ref<Record<string, any>[]>([]);
  const { isLoading, execute } = getMenu();
  async function getNav() {
    const { data: resData } = await execute();
    const obj: Record<string, any> = {};
    const menusData: Record<string, any> = {};
    // const resDataKeys = Object.keys(resData.value);

    // 处理成前端需要的顺序
    const menuSet = new Set(MENU_CLASS);
    menuSet.forEach((key) => {
      if (resData.value[key]) {
        obj[key] = resData.value[key].map((menu: string) => {
          return MENU_CHART[key] && MENU_CHART[key][menu];
        });
      }
    });

    const objKeys = Object.keys(obj);
    for (let index = 0; index < objKeys.length; index += 1) {
      const key = objKeys[index];
      menusData[MENU_CLASS_OBJ[key]] = {};
      obj[key].forEach((menu: string) => {
        if (menu) {
          menusData[MENU_CLASS_OBJ[key]][menu] = MENU[MENU_CLASS_OBJ[key]][menu];
        }
      });
    }

    // console.log('menusData: ', menusData);
    menus.value = Object.keys(menusData).map((key) => {
      return {
        className: key,
        menu: Object.keys(menusData[key]).map((item, index) => ({
          ...menusData[key][item],
          bgColor: STYLE[index % (STYLE.length + 1)].bgColor,
        })),
      };
    });
  }
  getNav();
</script>

<style lang="less" scoped>
  .base-box {
    padding: 0 20px 15px 20px;
    height: 100%;

    .base-tabs-warp {
      :deep(.arco-tabs) {
        .arco-tabs-content {
          padding: 112px 0 112px 42px;
          background-color: #fff;
          border: 0;
        }
      }
    }

    .menu-wrap {
      display: flex;
      flex-wrap: wrap;
      .menu-link {
        margin-right: 20px;
        margin-bottom: 20px;
        width: 210px;
        height: 130px;
        line-height: 130px;
        border-radius: 6px 6px 6px 6px;
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
      }

      .icon-box {
        margin: 30px auto 12px;
        width: 36px;
        height: 36px;
        background: #ffffff;
        border-radius: 50%;
      }

      .menu-name {
        text-align: center;
      }
    }
  }
</style>
