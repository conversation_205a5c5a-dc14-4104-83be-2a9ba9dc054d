<template>
  <Chart style="width: 100%; height: 380px" :option="chartOption" />
</template>

<script lang="ts" setup>
  import { LineSeriesOption } from 'echarts';
  import useChartOption from '@/hooks/chart-option';

  const props = withDefaults(
    defineProps<{
      data: any[];
    }>(),
    {
      data: () => [],
    }
  );
  const xAxis = ref<string[]>([]);
  const legendData = ref<any[]>([]);
  const seriesData = ref<LineSeriesOption[]>([]);
  const COLORS = ['#2B5AED', '#FFB800', '#00B96B', '#FF6B00', '#07ABF2'];
  watch(
    () => props.data,
    (val) => {
      if (val && val.length > 0) {
        val = val.sort((a, b) => {
          return a.date_month.localeCompare(b.date_month);
        });
        legendData.value = [
          '新增潜客人数',
          '新入会会员人数',
          '新入会会员-7日购买人数',
          '新入会会员-15日购买人数',
          '新入会会员-30日购买人数',
        ];
        xAxis.value = val.map((item) => item.date_month);
        seriesData.value = [
          {
            name: '新增潜客人数',
            type: 'line',
            data: val.map((item) => parseInt(item.customer_num_total || 0, 10)),
          },
          {
            name: '新入会会员人数',
            type: 'line',
            data: val.map((item) => parseInt(item.first_num_total || 0, 10)),
          },
          {
            name: '新入会会员-7日购买人数',
            type: 'line',
            data: val.map((item) => parseInt(item.seven_member_num || 0, 10)),
          },
          {
            name: '新入会会员-15日购买人数',
            type: 'line',
            data: val.map((item) => parseInt(item.fifteen_member_num || 0, 10)),
          },
          {
            name: '新入会会员-30日购买人数',
            type: 'line',
            data: val.map((item) => parseInt(item.thirty_member_num || 0, 10)),
          },
        ];
      } else {
        legendData.value = [];
        xAxis.value = [];
        seriesData.value = [];
      }
    },
    {
      immediate: true,
      deep: true,
    }
  );
  const { chartOption } = useChartOption(() => {
    return {
      color: COLORS,
      title: {
        text: '场馆会员转化趋势图',
        textStyle: {
          fontSize: 16,
        },
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: legendData.value,
        right: 0,
        padding: [
          5, // 上
          5, // 右
          5, // 下
          140, // 左
        ],
        itemWidth: 8,
        itemHeight: 8,
      },
      xAxis: {
        type: 'category',
        data: xAxis.value,
      },
      yAxis: {
        type: 'value',
      },
      series: seriesData.value,
    };
  });
</script>

<style scoped lang="less"></style>
