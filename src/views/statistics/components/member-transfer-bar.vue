<template>
  <Chart style="width: 100%; height: 380px" :option="chartOption" />
</template>

<script lang="ts" setup>
  import { LineSeriesOption } from 'echarts';
  import useChartOption from '@/hooks/chart-option';

  const props = withDefaults(
    defineProps<{
      data: any[];
    }>(),
    {
      data: () => [],
    }
  );
  const xAxis = ref<string[]>([]);
  const legendData = ref<any[]>([]);
  const seriesData = ref<LineSeriesOption[]>([]);
  const COLORS = ['#09CA96', '#FFCE22', '#016FDE'];

  watch(
    () => props.data,
    (val) => {
      if (val && val.length > 0) {
        val = val.sort((a, b) => {
          return a.date_month.localeCompare(b.date_month);
        });
        legendData.value = ['新入会会员-7日转化率', '新入会会员-15日转化率', '新入会会员-30日转化率'];
        xAxis.value = val.map((item) => item.date_month);
        seriesData.value = [
          {
            name: '新入会会员-7日转化率',
            type: 'bar',
            data: val.map((item) => parseFloat(item.seven_transfer_percent || 0)),
          },
          {
            name: '新入会会员-15日转化率',
            type: 'bar',
            data: val.map((item) => parseFloat(item.fifteen_transfer_percent || 0)),
          },
          {
            name: '新入会会员-30日转化率',
            type: 'bar',
            data: val.map((item) => parseFloat(item.thirth_transfer_percent || 0)),
          },
        ];
      } else {
        legendData.value = [];
        xAxis.value = [];
        seriesData.value = [];
      }
    },
    {
      immediate: true,
      deep: true,
    }
  );
  const { chartOption } = useChartOption(() => {
    return {
      color: COLORS,
      title: {
        text: '场馆会员转化率',
        textStyle: {
          fontSize: 16,
        },
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: legendData.value,
        right: 0,
        padding: [
          5, // 上
          5, // 右
          5, // 下
          80, // 左
        ],
        itemWidth: 8,
        itemHeight: 8,
      },
      xAxis: {
        type: 'category',
        data: xAxis.value,
      },
      yAxis: {
        type: 'value',
      },
      series: seriesData.value,
    };
  });
</script>

<style scoped lang="less"></style>
