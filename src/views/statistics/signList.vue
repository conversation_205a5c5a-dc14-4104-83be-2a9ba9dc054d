<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="bus_id" label="所属场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" placeholder="所属场馆" allow-search />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="会员名称">
                  <a-input
                    v-model="searchParam.search"
                    placeholder="会员名称"
                    allow-clear
                    @press-enter="handleSearch"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="type" label="统计方式">
                  <a-select v-model="searchParam.type" allow-search placeholder="统计方式">
                    <a-option :value="1">按会员统计</a-option>
                    <a-option :value="2">按会员卡统计</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="rangeValue" label="日期区间">
                  <a-range-picker
                    v-model="rangeValue"
                    value-format="YYYY-MM-DD"
                    :allow-clear="false"
                    :placeholder="['开始日期', '结束日期']"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="card_id" label="会员卡">
                  <a-select
                    v-model="searchParam.card_id"
                    allow-search
                    allow-clear
                    :loading="isCardLoading"
                    :options="cardList"
                    :virtual-list-props="{ height: 200 }"
                    :threshold="200"
                    placeholder="会员卡"
                    @popup-visible-change="popupVisibleChange"
                  ></a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="duration" label="单次在场时长>=">
                  <a-input-number
                    v-model="searchParam.duration"
                    :min="0"
                    :max="10080"
                    :precision="0"
                    hide-button
                    placeholder="单次在场时长"
                  >
                    <template #suffix>分钟</template>
                  </a-input-number>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="det_bus_id" label="训练数量">
                  <a-input-group>
                    <a-select v-model="searchParam.search_type" :trigger-props="{ autoFitPopupMinWidth: true }">
                      <a-option :value="1"> 训练次数 </a-option>
                      <a-option :value="2"> 训练天数 </a-option>
                    </a-select>
                    <a-input-number v-model="searchParam.s_num" :min="0" :max="100000" :precision="0" hide-button>
                      <template #suffix>{{ searchParam.search_type === 1 ? '次' : '天' }}</template>
                    </a-input-number>
                    ~
                    <a-input-number v-model="searchParam.e_num" :min="1" :max="1000000" :precision="0" hide-button>
                      <template #suffix>{{ searchParam.search_type === 1 ? '次' : '天' }}</template>
                    </a-input-number>
                  </a-input-group>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>

        <a-divider style="height: 120px" direction="vertical" />

        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-divider style="margin-top: 0" />

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space> </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button :loading="isLoading" @click="handleClickExport(handleExport as Callback<ExportData>)">
                导出
              </a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>

      <a-table v-bind="tableProps" ref="tableRef" v-on="tableEvent">
        <template #columns>
          <a-table-column title="会员" data-index="username">
            <!-- <template #cell="{ record }">
              <a-link
                :href="goSubDetail(record.user_id, searchParam.bus_id, false)"
                @click.prevent="goSubDetail(record.user_id, searchParam.bus_id)"
              >
                {{ record.username }}
              </a-link>
            </template> -->
          </a-table-column>
          <a-table-column v-if="searchParam.type === 2" title="训练用卡" data-index="card_name" />
          <a-table-column title="训练次数" data-index="num" />
          <a-table-column title="训练天数" data-index="day" />
          <a-table-column title="平均时长" data-index="ave_duration">
            <template #title>
              平均时长
              <a-tooltip content="根据会员签到时间和离场时间计算，无离场时间计为60分钟。">
                <icon-question-circle style="color: #ff6323" />
              </a-tooltip>
            </template>
          </a-table-column>
          <a-table-column title="跟进会籍" data-index="name" :width="240" />
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { getUserSignStatistics } from '@/api/statistics';
  import { getMemberListCards } from '@/api/member';

  import useTableProps from '@/hooks/table-props';
  import { useBusInfoStore } from '@/store';
  import { goSubDetail } from '@/utils/router-go';
  import { AnyObject, Callback, ExportData } from '@/types/global';

  import ExportExcel from '@/components/exportExcel.vue';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import List from '../admin/list.vue';

  const busInfo = useBusInfoStore();
  const { isLoading, tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getUserSignStatistics);

  const today = ref(new Date());
  const begin = ref(new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000 - 30));
  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: busInfo.bus_id,
    search: '',
    duration: null,
    s_date: dayjs(begin.value).format('YYYY-MM-DD'),
    e_date: dayjs(today.value).format('YYYY-MM-DD'),
    s_num: null, // 训练查询类型 数值
    e_num: null,
    type: 1, // 统计类型 1按会员统计 2按会员卡统计
    search_type: 1, // 训练查询类型 1训练次数 2训练天数
  });

  const rangeValue = computed({
    get() {
      if (searchParam.e_date) {
        return [searchParam.s_date, searchParam.e_date];
      }
      return [];
    },
    set(val) {
      setSearchParam({
        s_date: val[0],
        e_date: val[1],
      });
    },
  });

  const cardList = ref<any[]>([]);
  const { isLoading: isCardLoading, execute: executeCard } = getMemberListCards();
  // 获取会员卡列表
  async function getCardList() {
    const { data } = await executeCard({
      params: { belong_bus_id: searchParam.bus_id, member_list: '1' },
    });
    const list = data.value?.card_list || [];
    list.forEach((v: any) => {
      v.value = v.card_id;
      v.label = v.card_name;
    });
    cardList.value = list;
    return Promise.resolve(data.value);
  }

  const needRefresh = ref(true);
  function popupVisibleChange(visible: boolean) {
    if (visible && needRefresh.value) {
      getCardList().then(() => {
        needRefresh.value = false;
      });
    }
  }
  watch(
    () => searchParam.bus_id,
    ([newValue], [oldValue]) => {
      needRefresh.value = true;
    }
  );

  const route = useRoute();
  const tableRef = ref(null);
  const handleClickExport = (cb: Callback<ExportData>) => {
    loadTableList(true).then((list) => {
      list.forEach((v: any) => {
        v.name = v.name || '';
      });

      cb({
        filename: `
          ${(route.matched[route.matched.length - 1].meta.locale as string | undefined) || '训练次数统计'}
          (${searchParam.s_date}~${searchParam.e_date})
        `,
        columns: [
          {
            title: '会员',
            dataIndex: 'username',
          },
          ...(searchParam.type === 2
            ? [
                {
                  title: '训练用卡',
                  dataIndex: 'card_name',
                },
              ]
            : []),
          {
            title: '训练次数',
            dataIndex: 'num',
          },
          {
            title: '训练天数',
            dataIndex: 'day',
          },
          {
            title: '平均时长',
            dataIndex: 'ave_duration',
          },
          {
            title: '跟进会籍',
            dataIndex: 'name',
          },
        ],
        data: list,
      });
    });
  };

  loadTableList();
</script>
