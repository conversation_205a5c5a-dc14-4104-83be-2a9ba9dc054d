<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col v-if="IS_BRAND_SITE" :span="8">
                <a-form-item field="bus_id" label="场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" placeholder="所属场馆" allow-search />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="rangeValue" label="统计月份">
                  <a-range-picker
                    v-model="rangeValue"
                    mode="month"
                    value-format="YYYY-MM"
                    :allow-clear="false"
                    :disabled-date="getDisabledDate"
                    :placeholder="['开始日期', '结束日期']"
                    @select="onSelect"
                    @popup-visible-change="onPopupVisibleChange" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>

        <a-divider style="height: 32px" direction="vertical" />

        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearchAction">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0; margin-bottom: 26px" />
      <a-row :gutter="16">
        <a-col :span="12">
          <MemberTransferLine :data="chartData" />
        </a-col>
        <a-col :span="12">
          <MemberTransferBar :data="chartData" />
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space></a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button @click="handleClickExport(handleExport as Callback<ExportData>)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>

      <a-table v-bind="tableProps" ref="tableRef" v-on="tableEvent">
        <template #columns>
          <a-table-column title="统计月份" data-index="date_month" :width="120" />
          <a-table-column title="入会渠道" data-index="join_channel" :width="100" tooltip ellipsis />
          <a-table-column title="新入会会员人数" data-index="first_num_total" :width="130" />
          <a-table-column title="新入会会员首单金额" data-index="first_amount_total" :width="170" />
          <a-table-column title="新入会会员-7日" align="center">
            <a-table-column title="购买人数" data-index="seven_member_num" :width="120" />
            <a-table-column title="购买业绩" data-index="seven_amount_num" :width="120" />
            <a-table-column title="购买转化率" data-index="seven_transfer_percent" :width="120" />
          </a-table-column>
          <a-table-column title="新入会会员-15日" align="center">
            <a-table-column title="购买人数" data-index="fifteen_member_num" :width="120" />
            <a-table-column title="购买业绩" data-index="fifteen_amount_num" :width="120" />
            <a-table-column title="购买转化率" data-index="fifteen_transfer_percent" :width="120" />
          </a-table-column>
          <a-table-column title="新入会会员-30日" align="center">
            <a-table-column title="购买人数" data-index="thirty_member_num" :width="120" />
            <a-table-column title="购买业绩" data-index="thirty_amount_num" :width="120" />
            <a-table-column title="购买转化率" data-index="thirth_transfer_percent" :width="120" />
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { webJoinChannelReport, joinBusReport } from '@/api/ironReport';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';

  import useTableProps from '@/hooks/table-props';
  import { useBusInfoStore } from '@/store';
  import { Callback, ExportData } from '@/types/global';

  import ExportExcel from '@/components/exportExcel.vue';
  import MemberTransferLine from './components/member-transfer-line.vue';
  import MemberTransferBar from './components/member-transfer-bar.vue';

  const busInfo = useBusInfoStore();
  const { IS_BRAND_SITE } = window;
  const { isLoading, tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    webJoinChannelReport,
    (list) => {
      return list.map((item) => {
        return {
          ...item,
          seven_transfer_percent: `${item.seven_transfer_percent}%`,
          fifteen_transfer_percent: `${item.fifteen_transfer_percent}%`,
          thirth_transfer_percent: `${item.thirth_transfer_percent}%`,
        };
      });
    }
  );

  const route = useRoute();
  const busId = computed(() => {
    return route.query.bus_id || busInfo.bus_id;
  });
  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: busId.value,
    begin_time: route.query.begin_time || dayjs().subtract(1, 'month').format('YYYY-MM'),
    end_time: route.query.end_time || dayjs().subtract(1, 'month').format('YYYY-MM'),
  });

  const rangeValue = computed({
    get() {
      if (searchParam.end_time) {
        return [searchParam.begin_time, searchParam.end_time];
      }
      return [];
    },
    set(val) {
      setSearchParam({
        begin_time: val[0],
        end_time: val[1],
      });
    },
  });

  // 支持查询起止月份，最长区间12个月。 起止：2025年1月 ~ 当月。
  const dates = ref([]);
  function onSelect(valueString, value) {
    dates.value = value;
  }
  function onPopupVisibleChange(visible) {
    if (!visible) {
      dates.value = [];
    }
  }
  const getDisabledDate = (current: Date) => {
    const startYear = 2025;
    const startMonth = 0;
    const startDate = dayjs(new Date(startYear, startMonth));

    const today = dayjs();
    const maxDate = today.endOf('month');

    let tooLate = false;
    let tooEarly = false;
    if (dates.value && (dates.value[0] || dates.value[1])) {
      const selDay = dates.value[0] || dates.value[1];
      const selectValueStart = dayjs(selDay);
      tooLate = selDay && selectValueStart.diff(current, 'month') >= 12;
      tooEarly = selDay && dayjs(current).diff(selectValueStart, 'month') >= 12;
    }
    return current < startDate.toDate() || current > maxDate.toDate() || tooLate || tooEarly;
  };

  const tableRef = ref(null);
  const handleClickExport = (cb: Callback<ExportData>) => {
    loadTableList(true).then((list) => {
      cb({
        filename: `新入会会员月报-${rangeValue.value[0]}-${rangeValue.value[1]}`,
        columns: [
          { title: '统计月份', dataIndex: 'date_month' },
          { title: '入会渠道', dataIndex: 'join_channel' },
          { title: '新入会会员人数', dataIndex: 'first_num_total' },
          { title: '新入会会员首单金额', dataIndex: 'first_amount_total' },
          { title: '新入会会员-7日购买人数', dataIndex: 'seven_member_num' },
          { title: '新入会会员-7日购买业绩', dataIndex: 'seven_amount_num' },
          { title: '新入会会员-7日购买转化率', dataIndex: 'seven_transfer_percent' },
          { title: '新入会会员-15日购买人数', dataIndex: 'fifteen_member_num' },
          { title: '新入会会员-15日购买业绩', dataIndex: 'fifteen_amount_num' },
          { title: '新入会会员-15日购买转化率', dataIndex: 'fifteen_transfer_percent' },
          { title: '新入会会员-30日购买人数', dataIndex: 'thirty_member_num' },
          { title: '新入会会员-30日购买业绩', dataIndex: 'thirty_amount_num' },
          { title: '新入会会员-30日购买转化率', dataIndex: 'thirth_transfer_percent' },
        ],
        data: list,
      });
    });
  };

  const chartData = ref([]);
  function getChartData() {
    const { execute: getJoinChannelReport } = joinBusReport();
    getJoinChannelReport({
      data: {
        bus_id: searchParam.bus_id,
        begin_time: searchParam.begin_time,
        end_time: searchParam.end_time,
        page_size: 12,
        page_no: 1,
      },
    }).then((res) => {
      chartData.value = res.data.value.list;
    });
  }

  function handleSearchAction() {
    handleSearch();
    getChartData();
  }
  getChartData();
  loadTableList();
</script>
