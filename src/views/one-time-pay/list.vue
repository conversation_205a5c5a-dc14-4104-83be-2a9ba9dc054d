<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="title" label="方案名称">
                  <a-input v-model="searchParam.name" placeholder="请输入" allow-clear @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="title" label="教练">
                  <SalesSelect
                    v-model="searchParam.coach_id"
                    :belong-bus-id="searchParam.bus_id"
                    label-in-value
                    :is-membership="false"
                    is-pt-coach
                    is-swim-coach
                    show-coach-type
                    placeholder="请选择"></SalesSelect>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="$router.push('/one-time-pay/save')">新增方案</a-button>
            <a-button type="outline" @click="handleSetPriceModal">设置持卡价</a-button>
            <a-button @click="handleBatchRemove">批量删除</a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-bind="tableProps"
        v-model:selectedKeys="selectedKeys"
        row-key="id"
        :row-selection="{
          type: 'checkbox',
          showCheckedAll: true,
          onlyCurrent: false,
        }"
        v-on="tableEvent">
        <template #columns>
          <a-table-column title="方案名称" data-index="name" />
          <a-table-column title="方案类型" data-index="type">
            <!-- 使用类型，1标签，2卡种 -->
            <template #cell="{ record }">
              <span v-if="record.type === 1">私教方案</span>
              <span v-else-if="record.type === 2">泳教方案</span>
              <span v-else>-</span>
            </template>
          </a-table-column>
          <a-table-column title="教练" data-index="use_coach">
            <template #cell="{ record }">
              <!-- if more than 30 characters, it will be truncated -->
              <a-tooltip
                v-if="record.use_coach.length > 30"
                :content="record.use_coach"
                :content-style="{ maxWidth: '800px' }">
                <span>{{ record.use_coach.substring(0, 30) }}...</span>
              </a-tooltip>
              <span v-else-if="record.use_coach">{{ record.use_coach }}</span>
              <span v-else>-</span>
            </template>
          </a-table-column>
          <a-table-column title="操作" :width="200">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="$router.push(`/one-time-pay/save/${record.id}`)">编辑</a-link>
                <a-link @click="handleSetCoachModal(record)">设置教练</a-link>
                <a-link status="danger" @click="handleRemove(record)">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:visible="isSetPriceModal"
      title="设置持卡价"
      :width="800"
      :mask-closable="false"
      @before-ok="handleSetPriceSubmit">
      <a-alert type="warning" style="margin-bottom: 16px">
        将卡种批量设置到现有方案的课程组中，若课程组中已有对应卡种，新的持卡价将覆盖原有价格
      </a-alert>
      <a-form ref="setPriceRef" :rules="setPriceRules" :model="setPriceForm" auto-label-width>
        <a-form-item label="卡种名称" field="card_ids">
          <a-tree-select
            v-model="setPriceForm.card_ids"
            :tree-props="{
              virtualListProps: {
                height: 200,
              },
            }"
            :allow-search="true"
            :allow-clear="true"
            :tree-checkable="true"
            tree-checked-strategy="child"
            :data="cardTree"
            :max-tag-count="5"
            :filter-tree-node="filterTreeNode"
            placeholder="请选择">
            <template #label="{ data }">
              <a-tooltip v-if="data.label.length > 20" :content="data.label">
                <span>{{ data.label.substring(0, 20) }}...</span>
              </a-tooltip>
              <span v-else>{{ data.label }}</span>
            </template>
            <template #tree-slot-title="{ title }">
              <a-tooltip v-if="title.length > 20" :content="title">
                <span>{{ title.substring(0, 20) }}...</span>
              </a-tooltip>
              <span v-else>{{ title }}</span>
            </template>
          </a-tree-select>
        </a-form-item>
        <a-form-item label="" field="course_price_list">
          <course-price
            v-model:course-price-list="setPriceForm.course_price_list"
            :course-list="courseList"></course-price>
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal
      v-model:visible="isSetCoachModal"
      title="设置教练"
      :width="600"
      :mask-closable="false"
      @before-ok="handleSetCoachSubmit">
      <a-alert type="warning" style="margin-bottom: 16px">本方案将替换教练已关联的方案</a-alert>
      <a-form :model="setCoachForm" auto-label-width>
        <a-form-item label="名称">
          <a-tag>{{ setCoachForm.name }}</a-tag>
        </a-form-item>
        <a-form-item label="教练" field="coach_ids">
          <a-select v-model="setCoachForm.coach_ids" multiple allow-search allow-clear :max-tag-count="3">
            <a-option v-for="coach in coachList" :key="coach.coach_id" :value="coach.coach_id">
              <span>{{ coach.coach_name }}</span>
              <span style="float: right; color: #ccc">{{ coach.is_swim ? '泳教' : '私教' }}</span>
            </a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { useBusInfoStore } from '@/store';
  import {
    getPlanList,
    removePlan,
    setCoach,
    setCardPrice,
    getCourseTree,
    getCoachList,
    getCardsForAll,
  } from '@/api/one-time-pay';
  import useTableProps from '@/hooks/table-props';
  import SalesSelect from '@/components/membership/salesSelect.vue';
  import CoursePrice from './components/CoursePrice.vue';

  defineOptions({
    name: 'OneTimePayList',
  });

  const props = defineProps({
    cardId: {
      type: String,
      default: '',
    },
  });

  const busInfo = useBusInfoStore();

  // variable
  const selectedKeys = ref([]);
  const courseList = ref<any>([]);

  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getPlanList);

  setSearchParam({
    name: '',
    coach_id: '',
    bus_id: busInfo.bus_id,
  });

  const pullCourseList = async () => {
    const { response }: any = await getCourseTree({
      bus_id: busInfo.bus_id,
    });

    if (response.value.errorcode === 0) {
      courseList.value = response.value.data;
    }
  };

  const handleRemove = (record: any) => {
    Modal.confirm({
      title: `确定删除方案 "${record.name}" 吗？`,
      content: '删除方案，用户将不能再按此方案约课，但已经完成预约的课程不受影响，确定删除？',
      okText: '删除',
      cancelText: '取消',
      escToClose: false,
      maskClosable: false,
      onOk: async () => {
        const { response }: any = await removePlan({ id: [record.id] });
        if (response.value.errorcode === 0) {
          Message.success('删除成功');
          loadTableList();
          pullCourseList();
        }
      },
    });
  };

  const handleBatchRemove = () => {
    if (selectedKeys.value.length === 0) {
      Message.warning('请选择需要删除的方案');
      return;
    }

    Modal.confirm({
      title: '删除单节付费课方案',
      content: '删除方案，用户将不能再按此方案约课，但已经完成预约的课程不受影响，确定删除？',
      onOk: async () => {
        const { response }: any = await removePlan({ id: selectedKeys.value });
        if (response.value.errorcode === 0) {
          Message.success('删除成功');
          selectedKeys.value = [];
          loadTableList();
          pullCourseList();
        }
      },
    });
  };

  // cards
  let cardList: any[] = [];
  const cardTree = ref<any[]>([]);
  const initCardTree = async () => {
    try {
      const { response } = await getCardsForAll({
        bus_id: busInfo.bus_id,
      });
      cardList = response.value?.data;
      const array = cardList.map((item: any) => {
        return {
          title: item.card_name,
          value: item.card_id,
          key: item.card_id,
          card_type_id: item.card_type_id,
          is_pt_time_limit_card: item.is_pt_time_limit_card,
        };
      });
      const getCardByType = (type: string) => {
        return array.filter((item: any) => item.card_type_id === type);
      };
      cardTree.value = [
        {
          title: '全部卡种',
          value: 'all',
          key: 'all',
          children: [
            {
              title: '期限卡',
              value: 'term',
              key: 'term',
              children: getCardByType('1'),
            },
            {
              title: '次卡',
              value: 'times',
              key: 'times',
              children: getCardByType('2'),
            },
            {
              title: '储值卡',
              value: 'value',
              key: 'value',
              children: getCardByType('3'),
            },
            {
              title: '私教课',
              value: 'private',
              key: 'private',
              children: getCardByType('4'),
            },
            {
              title: '泳教课',
              value: 'swim',
              key: 'swim',
              children: getCardByType('5'),
            },
          ].filter((item) => item.children.length > 0),
        },
      ];
    } catch (error) {
      console.error(error);
      cardTree.value = [];
    }
  };
  const filterTreeNode = (searchValue: string, nodeData: any) => {
    return nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  };

  // set price modal
  const isSetPriceModal = ref(false);
  const setPriceForm = ref({
    card_ids: [] as any[],
    course_price_list: [] as any[],
  });
  const setPriceRules = {
    card_ids: [{ required: true, message: '请选择卡种' }],
  };
  const setPriceRef = ref();
  const handleSetPriceModal = () => {
    setPriceForm.value = {
      card_ids: [],
      course_price_list: [
        {
          course_ids: [],
          price: undefined,
        },
      ],
    };
    isSetPriceModal.value = true;
  };
  const handleSetPriceSubmit = async (done: any) => {
    const valid = await setPriceRef.value.validate();
    if (valid) {
      done(false);
      return;
    }

    const detail = {
      charge_plan: cardList
        .filter((card) => setPriceForm.value.card_ids.includes(card.card_id))
        .map((card) => {
          return {
            id: card.card_id,
            name: card.card_name,
            card_type_id: card.card_type_id,
          };
        }),
      pt_card_list: setPriceForm.value.course_price_list.map((item: any) => {
        return {
          list: courseList.value.pt_charge_plan_detail
            .filter((course: any) => item.course_ids.includes(course.id))
            .map((course: any) => {
              return {
                sort: course.sort,
                pt_charge_plan_id: course.pt_charge_plan_id,
              };
            }),
          price: item.price,
        };
      }),
    };

    const { response }: any = await setCardPrice({
      bus_id: busInfo.bus_id,
      detail: JSON.stringify(detail),
    });

    if (response.value.errorcode === 0) {
      Message.success('设置成功');
      loadTableList();
      isSetPriceModal.value = false;
    }
  };

  // set coach modal
  const isSetCoachModal = ref(false);
  const setCoachForm = ref({
    id: '',
    name: '',
    coach_ids: [],
  });
  const coachList = ref<any[]>([]);
  const handleSetCoachModal = (record: any) => {
    getCoachList({
      bus_id: busInfo.bus_id,
      type: record.type,
    }).then((res) => {
      coachList.value = res.data.value.list;
    });
    setCoachForm.value.id = record.id;
    setCoachForm.value.name = record.name;
    setCoachForm.value.coach_ids = record.use_coach_id;
    isSetCoachModal.value = true;
  };
  const handleSetCoachSubmit = async (done: any) => {
    const { response }: any = await setCoach({
      id: setCoachForm.value.id,
      coach_id: setCoachForm.value.coach_ids.filter((item) => item),
    });
    if (response.value.errorcode === 0) {
      Message.success('设置成功');
      loadTableList();
      isSetCoachModal.value = false;
      done(true);
    }
  };

  // created
  loadTableList();
  initCardTree();
  pullCourseList().then(() => {
    if (props.cardId) {
      setPriceForm.value = {
        card_ids: [props.cardId],
        course_price_list: [
          {
            course_ids: [],
            price: undefined,
          },
        ],
      };
      isSetPriceModal.value = true;
    }
  });
</script>

<style lang="less" scoped>
  :deep(.arco-checkbox-label) {
    width: 100%;
  }
  :deep(.arco-select-option-checkbox) {
    width: 100%;
  }
</style>
