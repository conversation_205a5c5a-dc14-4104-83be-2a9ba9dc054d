<template>
  <!-- <a-card style="width: 100%"> -->
  <div style="width: 100%">
    <a-alert v-if="props.tip" type="warning" style="margin-bottom: 10px">{{ props.tip }}</a-alert>
    <!-- <a-divider v-if="props.tip" /> -->
    <a-card style="margin-bottom: 10px" :body-style="{ padding: '8px' }">
      <div v-for="(item, index) in list" :key="index" style="margin-bottom: 10px">
        <a-row>
          <a-col :span="3" style="text-align: right">
            <!-- <a-tag style="margin-right: 10px">适用卡种 {{ index + 1 }}</a-tag> -->
            <span style="font-size: 12px; margin-right: 10px; line-height: 32px">适用卡种 {{ index + 1 }}</span>
          </a-col>
          <a-col :span="19">
            <a-skeleton v-if="loading" animation>
              <a-skeleton-line />
            </a-skeleton>
            <a-form-item v-else :no-style="!item?.cardError" hide-label :field="`list[${index}].card_ids`">
              <a-tree-select
                v-model="item.card_ids"
                :tree-props="{
                  virtualListProps: {
                    height: 200,
                  },
                }"
                :allow-search="true"
                :allow-clear="true"
                :tree-checkable="true"
                tree-checked-strategy="child"
                :data="item.cardTree"
                :filter-tree-node="filterTreeNode"
                :max-tag-count="5"
                @change="handleChange">
                <template #label="{ data }">
                  <a-tooltip v-if="data.label.length > 20" :content="data.label">
                    <span>{{ data.label.substring(0, 20) }}...</span>
                  </a-tooltip>
                  <span v-else>{{ data.label }}</span>
                </template>
                <template #tree-slot-title="{ title }">
                  <a-tooltip v-if="title.length > 20" :content="title">
                    <span>{{ title.substring(0, 20) }}...</span>
                  </a-tooltip>
                  <span v-else>{{ title }}</span>
                </template>
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="2" style="text-align: right">
            <a-button type="text" @click="handleDelete(index)">
              <icon-delete />
            </a-button>
          </a-col>
        </a-row>
        <a-row style="margin-top: 10px">
          <a-col :span="3" style="text-align: right">
            <!-- <a-tag style="margin-right: 10px">定价</a-tag> -->
            <span style="font-size: 12px; margin-right: 10px; line-height: 32px">优惠价</span>
          </a-col>
          <a-col :span="19">
            <a-skeleton v-if="loading" animation>
              <a-skeleton-line />
            </a-skeleton>
            <a-form-item v-else :no-style="!item?.priceError" hide-label :field="`list[${index}].price`">
              <a-input-number
                v-model="item.price"
                :min="0"
                :max="99999"
                :step="0.01"
                :precision="2"
                @change="emit('update:cardPriceList', list)">
                <template #suffix>
                  <span>元</span>
                </template>
              </a-input-number>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- <a-divider /> -->
      </div>
      <a-empty v-if="list.length === 0" />
    </a-card>
    <a-button type="outline" :disabled="list.length >= props.max" @click="handleAdd">
      <template #icon>
        <icon-plus />
      </template>
      新增卡种定价
    </a-button>
  </div>
  <!-- </a-card> -->
</template>

<script setup lang="ts">
  import _ from 'lodash';
  import { CardPrice } from '../types';

  const props = defineProps({
    tip: {
      type: String,
      default: '',
    },
    max: {
      type: Number,
      default: Infinity,
    },
    cardPriceList: {
      type: Array as PropType<CardPrice[]>,
      default: () => [],
    },
    cardList: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:cardPriceList']);

  // cards
  const getCardTree = (cardList: any[]) => {
    let cardTree: any[] = [];

    try {
      const array = cardList.map((item: any) => {
        return {
          title: item.card_name,
          value: item.card_id,
          key: item.card_id,
          card_type_id: item.card_type_id,
          is_pt_time_limit_card: item.is_pt_time_limit_card,
          disabled: item.disabled,
        };
      });
      const getCardByType = (type: string) => {
        return array.filter((item: any) => item.card_type_id === type);
      };
      cardTree = [
        {
          title: '全部卡种',
          value: 'all',
          key: 'all',
          children: [
            {
              title: '期限卡',
              value: 'term',
              key: 'term',
              children: getCardByType('1'),
            },
            {
              title: '次卡',
              value: 'times',
              key: 'times',
              children: getCardByType('2'),
            },
            {
              title: '储值卡',
              value: 'value',
              key: 'value',
              children: getCardByType('3'),
            },
            {
              title: '私教课',
              value: 'private',
              key: 'private',
              children: getCardByType('4'),
            },
            {
              title: '泳教课',
              value: 'swim',
              key: 'swim',
              children: getCardByType('5'),
            },
          ].filter((item) => item.children.length > 0),
        },
      ];
    } catch (error) {
      console.error(error);
    }

    return cardTree;
  };
  const filterTreeNode = (searchValue: string, nodeData: any) => {
    return nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  };

  // variables
  const list = ref<CardPrice[]>([]);
  const setDisable = () => {
    const card_ids = list.value.map((item) => item.card_ids).flat();

    list.value.forEach((item, index) => {
      const own_card_ids = list.value[index].card_ids;
      const disable_card_ids = card_ids.filter((id) => !own_card_ids.includes(id));
      const cardList = _.cloneDeep(props.cardList);

      cardList.forEach((card: any) => {
        if (disable_card_ids.includes(card.card_id)) {
          card.disabled = true;
        } else {
          card.disabled = false;
        }
      });

      item.cardTree = getCardTree(cardList);
    });
  };
  const handleAdd = () => {
    if (list.value.length >= props.max) {
      return;
    }

    list.value.push({
      card_ids: [],
      price: undefined,
      cardTree: [],
      cardError: false,
      priceError: false,
    });

    nextTick(() => {
      setDisable();
      emit('update:cardPriceList', list.value);
    });
  };
  const handleDelete = (index: number) => {
    list.value.splice(index, 1);

    nextTick(() => {
      setDisable();
      emit('update:cardPriceList', list.value);
    });
  };
  const handleChange = () => {
    nextTick(() => {
      if (list.value.length > 1) {
        setDisable();
      }
      // emit('update:cardPriceList', list.value);
    });
  };

  // validator
  // const cardValidator = (index: number) => {
  //   return (value: any, callback: any) => {
  //     const item = list.value[index];
  //     if (item.card_ids.length === 0) {
  //       item.cardError = true;
  //       callback('请选择卡种');
  //     } else {
  //       item.cardError = false;
  //       callback();
  //     }
  //   };
  // };
  // const priceValidator = (index: number) => {
  //   return (value: any, callback: any) => {
  //     const item = list.value[index];
  //     if (item.price === undefined || item.price === null) {
  //       item.priceError = true;
  //       callback('请填写定价');
  //     } else {
  //       item.priceError = false;
  //       callback();
  //     }
  //   };
  // };

  // watch
  watch(
    () => props.cardPriceList,
    () => {
      list.value = props.cardPriceList;

      nextTick(() => {
        setDisable();
      });
    },
    { immediate: true, deep: false }
  );
  watch(
    () => props.cardList,
    () => {
      nextTick(() => {
        setDisable();
      });
    },
    { immediate: true, deep: false }
  );
</script>

<style scoped></style>
