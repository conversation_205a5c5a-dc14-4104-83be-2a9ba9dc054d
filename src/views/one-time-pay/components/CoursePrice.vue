<template>
  <!-- <a-card style="width: 100%"> -->
  <div style="width: 100%">
    <a-alert v-if="props.tip" type="warning">{{ props.tip }}</a-alert>
    <a-divider v-if="props.tip" />
    <!-- <div v-for="(item, index) in list" :key="index"> -->
    <a-card v-for="(item, index) in list" :key="index" style="margin-bottom: 10px" :body-style="{ padding: '8px' }">
      <a-row>
        <a-col :span="3" style="text-align: right">
          <!-- <a-tag style="margin-right: 10px">课程选择</a-tag> -->
          <span style="font-size: 12px; margin-right: 10px; line-height: 32px">课程选择</span>
        </a-col>
        <a-col :span="19">
          <a-skeleton v-if="loading" animation>
            <a-skeleton-line />
          </a-skeleton>
          <a-form-item
            v-else
            hide-label
            :field="`list[${index}].course_ids`"
            :rules="[{ validator: courseValidator(index) }]">
            <a-tree-select
              v-model="item.course_ids"
              :tree-props="{
                virtualListProps: {
                  height: 200,
                },
              }"
              :allow-search="true"
              :allow-clear="true"
              :tree-checkable="true"
              tree-checked-strategy="child"
              :data="item.courseTree"
              :max-tag-count="2"
              :filter-tree-node="filterTreeNode"
              @change="handleChange">
              <template #label="{ data }">
                <a-tooltip v-if="data.label.length > 20" :content="data.label">
                  <span>{{ data.label.substring(0, 20) }}...</span>
                </a-tooltip>
                <span v-else>{{ data.label }}</span>
              </template>
              <template #tree-slot-title="{ title }">
                <a-tooltip v-if="title.length > 20" :content="title">
                  <span>{{ title.substring(0, 20) }}...</span>
                </a-tooltip>
                <span v-else>{{ title }}</span>
              </template>
            </a-tree-select>
          </a-form-item>
        </a-col>
        <a-col v-if="list.length > 1" :span="2">
          <a-button type="text" @click="handleDelete(index)">
            <icon-delete />
          </a-button>
        </a-col>
      </a-row>
      <a-row style="margin-top: 10px">
        <a-col :span="3" style="text-align: right">
          <!-- <a-tag style="margin-right: 10px">持卡价格</a-tag> -->
          <span style="font-size: 12px; margin-right: 10px; line-height: 32px">持卡价格</span>
        </a-col>
        <a-col :span="19">
          <a-skeleton v-if="loading" animation>
            <a-skeleton-line />
          </a-skeleton>
          <a-form-item
            v-else
            hide-label
            :field="`list[${index}].price`"
            :rules="[{ validator: priceValidator(index) }]">
            <a-input-number
              v-model="item.price"
              :min="0"
              :max="99999"
              :step="0.01"
              :precision="2"
              @change="emit('update:coursePriceList', list)">
              <template #suffix>
                <span>元</span>
              </template>
            </a-input-number>
          </a-form-item>
        </a-col>
      </a-row>
      <!-- <a-divider /> -->
    </a-card>
    <!-- </div> -->
    <a-empty v-if="list.length === 0" />
    <a-button type="outline" :disabled="list.length >= props.max" @click="handleAdd">
      <template #icon>
        <icon-plus />
      </template>
      新增课程
    </a-button>
  </div>
  <!-- </a-card> -->
</template>

<script setup lang="ts">
  import _ from 'lodash';
  import { CoursePrice } from '../types';

  const props = defineProps({
    tip: {
      type: String,
      default: '',
    },
    max: {
      type: Number,
      default: Infinity,
    },
    coursePriceList: {
      type: Array as PropType<CoursePrice[]>,
      default: () => [],
    },
    courseList: {
      type: Object,
      default: () => {
        return {};
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:coursePriceList']);

  // course tree
  const getCourseTree = (courseList: any) => {
    let courseTree: any[] = [];
    const parentList = courseList.pt_charge_plan;
    const childrenList = courseList.pt_charge_plan_detail;

    if (Array.isArray(parentList) && Array.isArray(childrenList)) {
      courseTree = parentList.map((parent: any) => {
        return {
          key: parent.id,
          title: parent.name,
          children: childrenList
            .filter((child: any) => child.pt_charge_plan_id === parent.id)
            .map((child: any) => {
              return {
                key: child.id,
                title: child.name,
                full_title: `${parent.name} / ${child.name}`,
                pt_charge_plan_detail_id: child.pt_charge_plan_id,
                sort: child.sort,
                disabled: child.disabled,
              };
            }),
        };
      });
    }

    return courseTree;
  };
  const filterTreeNode = (searchValue: string, nodeData: any) => {
    return nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  };

  // variables
  const list = ref<CoursePrice[]>([]);
  const setDisable = () => {
    const course_ids = list.value.map((item) => item.course_ids).flat();

    list.value.forEach((item, index) => {
      const own_course_ids = list.value[index].course_ids;
      const disable_course_ids = course_ids.filter((id) => !own_course_ids.includes(id));
      const courseList = _.cloneDeep(props.courseList);

      courseList.pt_charge_plan_detail.forEach((course: any) => {
        if (disable_course_ids.includes(course.id)) {
          course.disabled = true;
        } else {
          course.disabled = false;
        }
      });

      item.courseTree = getCourseTree(courseList);
    });
  };

  const handleAdd = () => {
    if (list.value.length >= props.max) {
      return;
    }
    list.value.push({
      course_ids: [],
      price: undefined,
      courseTree: [],
    });

    nextTick(() => {
      setDisable();
      emit('update:coursePriceList', list.value);
    });
  };

  const handleDelete = (index: number) => {
    list.value.splice(index, 1);

    nextTick(() => {
      setDisable();
      emit('update:coursePriceList', list.value);
    });
  };

  const handleChange = () => {
    nextTick(() => {
      if (list.value.length > 1) {
        setDisable();
      }
      emit('update:coursePriceList', list.value);
    });
  };

  // validator
  const courseValidator = (index: number) => {
    return (value: any, callback: any) => {
      const item = list.value[index];
      if (item.course_ids.length === 0) {
        callback('请选择课程');
      } else {
        callback();
      }
    };
  };
  const priceValidator = (index: number) => {
    return (value: any, callback: any) => {
      const item = list.value[index];
      if (item.price === undefined || item.price === null) {
        callback('请填写定价');
      } else {
        callback();
      }
    };
  };

  // watch
  watch(
    () => props.coursePriceList,
    () => {
      list.value = props.coursePriceList;

      nextTick(() => {
        setDisable();
      });
    },
    { immediate: true, deep: false }
  );
  watch(
    () => props.courseList,
    () => {
      nextTick(() => {
        setDisable();
      });
    },
    { immediate: true, deep: false }
  );
</script>

<style scoped></style>
