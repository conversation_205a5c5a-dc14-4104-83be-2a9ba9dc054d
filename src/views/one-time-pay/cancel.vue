<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :rules="formRules" :model="formData" class="general-form" auto-label-width>
        <a-form-item field="name" label="客户名称">{{ formData?.username }}</a-form-item>
        <a-form-item field="phone" label="联系方式">{{ formData?.phone }}</a-form-item>
        <a-form-item field="course" label="课程名称">{{ formData?.class_name }}</a-form-item>
        <a-form-item field="coach" label="教练名称">{{ formData?.coach_name }}</a-form-item>
        <a-form-item field="reservation_date" label="预约时间">{{ formData?.create_time }}</a-form-item>
        <a-form-item field="sign_date" label="上课时间">{{ formData?.course_time }}</a-form-item>
        <a-form-item field="price" label="实付金额">{{ formData?.income_amount || 0 }}</a-form-item>
        <a-form-item field="return_rate" label="应退比例">{{ formData?.refund_rate }}%</a-form-item>
        <a-form-item field="amount" label="退款金额">
          <a-input-number v-model="formData.refund_amount" :min="0" :max="Infinity" :precision="2" :step="0.01">
            <template #suffix>
              <span>元</span>
            </template>
          </a-input-number>
        </a-form-item>
        <a-form-item field="rule" label="退款规则">
          <a-space direction="vertical">
            <!-- <a-tag>{{ tagFirst }}</a-tag> -->
            <div style="font-size: 12px; line-height: 16px; margin-top: 8px" v-html="tagFirst"></div>
            <!-- <a-tag v-for="(item, index) of tagList" :key="index">{{ item }}</a-tag> -->
            <div
              v-for="(item, index) of tagList"
              :key="index"
              style="font-size: 12px; line-height: 16px"
              v-html="item"></div>
          </a-space>
        </a-form-item>
        <a-form-item v-if="formData.pay_type">
          <PayTypeList
            v-model="formData.pay_type"
            :amount="formData.refund_amount"
            :bus-id="formData.bus_id"
            :user-id="formData.user_id"
            :describe="`取消预约[${formData.class_name}]`"
            show-sqb
            :sqb-option="{ describe: `取消预约[${formData.class_name}]`, serviceType: 2, isEqual: false }"
            show-card-pay
            is-lock-card
            is-refund
            :is-refund-need-s-q-b="isRefundNeedSQB"
            :is-max-amount="formData.from === 2" />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  // import _ from 'lodash';
  import PayTypeList from '@/components/form/PayTypeList.vue';
  import { getInformation, refundPt, refundSwim } from '@/api/one-time-pay';

  defineOptions({
    name: 'OneTimePayCancel',
  });

  const router = useRouter();

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
    direction: {
      type: String,
      default: '',
    },
  });

  // form
  const isLoading = ref(false);
  const isRefundNeedSQB = ref(false);

  const formRef = ref();
  const formData = ref({
    refund_amount: 0,
    refund_rate: 0,
    pay_type: null,
  });
  const formRules = {};
  const tagFirst = ref('');
  const tagList = ref([]);

  // event
  const getInfo = () => {
    return getInformation({
      id: props.id,
    }).then((res) => {
      formData.value = {
        ...res.data.value,
        refund_amount: Number(Number(res.data.value.refund_amount || 0).toFixed(2)),
        refund_rate: (Number(res.data.value.refund_rate) * 100).toFixed(2),
        is_swim: Number(res.data.value.card_type_id) === 5,
        from: Number(res.data.value.from || 1),
      };

      // 退款类型中是否包收钱吧
      isRefundNeedSQB.value = formData.value.pay_type.findIndex((item) => item.pay_type === 20) > -1;

      if (Array.isArray(formData.value.cancel_pt_refund_ladder) && formData.value.cancel_pt_refund_ladder.length) {
        tagFirst.value = `<span>开课前 <strong style="color: #ff2351">${formData.value.cancel_pt_refund_ladder[0].start}</strong> 分钟内不允许取消</span>`;

        formData.value.cancel_pt_refund_ladder.forEach((item) => {
          tagList.value.push(
            `<span>开课前 <strong style="color: #ff2351">${item.start}</strong> 分钟 ~ ${
              !item.end
                ? '<strong style="color: #ff2351">无限制</strong>'
                : ` <strong style="color: #ff2351">${item.end}</strong> 分钟`
            }，退款 <strong style="color: #ff2351">${(item.rate * 100).toFixed(2)}%</strong></span>`
          );
        });
      }
    });
  };
  const handleSubmit = async () => {
    const valid = await formRef.value.validate();
    if (!valid) {
      isLoading.value = true;

      const params = {
        id: props.id,
        amount: formData.value.refund_amount,
        new_pay_type: formData.value.pay_type,
      };

      let res = null;
      if (formData.value.is_swim) {
        res = await refundSwim(params).catch(() => {
          isLoading.value = false;
        });
      } else {
        res = await refundPt(params).catch(() => {
          isLoading.value = false;
        });
      }

      if (res && res.response.value.errorcode === 0) {
        Message.success('保存成功');
        isLoading.value = false;
        setTimeout(() => {
          if (props.direction === 'pt') {
            if (formData.value.card_type_id === '4') {
              router.push(`/v2/Web/PtSchedule/pt_schedule_list?date=${formData.value.begin_time}`);
            } else {
              router.push(`/v2/Web/PtSchedule/swim_schedule_list?date=${formData.value.begin_time}`);
            }
          } else {
            router.back();
          }
        }, 1000);
      }
    }
  };

  // created
  getInfo();
</script>

<style lang="less" scoped></style>
