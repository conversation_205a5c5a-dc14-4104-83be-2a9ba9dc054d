<template>
  <div class="base-box">
    <Breadcrumb />

    <a-card class="general-card">
      <a-form ref="formRef" :rules="formRules" :model="formData" class="general-form" auto-label-width>
        <a-form-item field="name" label="方案名称">
          <a-input v-model="formData.name" />
          <a-button type="outline" style="margin-left: 20px" @click="handleCopyModalShow">复制现有方案</a-button>
        </a-form-item>
        <a-form-item field="type" label="方案类型">
          <a-radio-group v-model="formData.type" :disabled="!!id" @change="handleTypeChange">
            <a-radio :value="1">私教方案</a-radio>
            <a-radio :value="2">泳教方案</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- <a-divider>课程设置</a-divider> -->
        <!-- <a-card -->
        <div v-for="(cp, index) in list" :key="cp.id" style="margin-bottom: 20px">
          <a-form-item
            label="课程名称"
            :field="`list[${index}].course_ids`"
            :rules="[{ validator: courseValidator(index) }]">
            <a-select
              v-model="list[index].course_ids"
              multiple
              allow-search
              :max-tag-count="3"
              @change="handleCourseChange(index)">
              <a-option
                v-for="course in list[index].courseList"
                :key="course.id"
                :value="course.id"
                :disabled="course.disabled">
                <span>{{ course.name }}</span>
                <span style="float: right; color: #ccc">
                  &nbsp;{{ course.class_duration }}分钟 {{ course.single_price }}元/节
                </span>
              </a-option>
            </a-select>
            <a-button v-if="list.length > 1" type="text" style="margin-left: 10px" @click="handleRemoveItem(index)">
              <template #icon>
                <icon-delete />
              </template>
            </a-button>
          </a-form-item>
          <a-form-item label="持卡价格" validate-status="">
            <card-price
              v-model:card-price-list="list[index].card_price_list"
              :loading="isLoading"
              :card-list="cardList"
              :max="10"
              tip="优惠价格条件：卡种 有效 且 未耗尽；若选储值卡 则需选作支付方式。"></card-price>
          </a-form-item>

          <!-- <a-button v-if="list.length > 1" type="dashed" @click="handleRemoveItem(index)">
            <template #icon>
              <icon-delete />
            </template>
            删除
          </a-button> -->
          <!-- <a-alert v-if="errorTip && errorIndex === index" type="error" style="margin-top: 10px">
            {{ errorTip }}
          </a-alert> -->
          <!-- </a-card> -->
        </div>

        <a-skeleton v-if="isLoading && list.length === 0" animation>
          <div style="width: 100%; height: 40px; text-align: center">
            <icon-loading style="font-size: 20px; color: rgb(var(--primary-6))" />
          </div>
          <a-skeleton-line :rows="6" :line-height="60" />
        </a-skeleton>

        <a-divider>
          <a-button type="outline" @click="handleAddItem">新增课程</a-button>
        </a-divider>

        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <a-modal v-model:visible="isShowCopyModal" title="方案选择" width="600px" @ok="handleCopy">
      <a-form ref="copyFormRef" :rules="copyFormRules" :model="copyFormData" auto-label-width>
        <a-form-item field="id" label="请选择方案">
          <a-select v-model="copyFormData.id" allow-search>
            <a-option v-for="item in planList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
  import { Message } from '@arco-design/web-vue';
  import _ from 'lodash';
  import { useRouter } from 'vue-router';
  import { useBusInfoStore } from '@/store';
  import { getPlanInfo, addPlan, updatePlan, getCourseList, getAllPlanList, getCardsForAll } from '@/api/one-time-pay';
  import CardPrice from './components/CardPrice.vue';

  defineOptions({
    name: 'OneTimePaySave',
  });

  const busInfo = useBusInfoStore();
  const router = useRouter();

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
  });

  // form
  const formRef = ref();
  const formData = ref({
    name: '',
    type: 1,
    detail: [],
  });
  const formRules = {
    name: {
      required: true,
      message: '请输入方案名称',
    },
    type: {
      required: true,
      message: '请选择方案类型',
    },
  };
  const copyFormRef = ref();
  const copyFormData = ref({
    id: '',
  });
  const copyFormRules = {
    id: {
      required: true,
      message: '请选择方案',
    },
  };
  const isLoading = ref(false);
  // const errorIndex = ref(-1);
  // const errorTip = ref('');

  const list = ref([]);
  const courseList = ref([]);

  // validate
  const validateAllCourse = () => {
    // errorTip.value = '';
    if (list.value.length === 0) {
      // errorTip.value = '请添加课程设置!';
      // Message.error(errorTip.value);
      return false;
    }
    try {
      list.value.forEach((item) => {
        // errorIndex.value = index;
        if (item.course_ids.length === 0) {
          throw new Error('请选择课程!');
        }
        // if (item.card_price_list.length === 0) {
        //   throw new Error('请添加持卡价格!');
        // }

        // item.card_price_list.forEach((cp) => {
        //   if (cp.card_ids.length === 0) {
        //     throw new Error('请选择适用卡种!');
        //   }
        //   if (cp.price === null || cp.price === undefined) {
        //     throw new Error('请填写定价!');
        //   }
        // });
      });
    } catch (error) {
      // errorTip.value = error.message;
      // Message.error(errorTip.value);
      return false;
    }

    return true;
  };
  const courseValidator = (index) => {
    return (value, callback) => {
      const item = list.value[index];
      if (item.course_ids.length === 0) {
        callback('请选择课程');
      } else {
        callback();
      }
    };
  };

  // course list
  const pullCourseList = () => {
    return getCourseList({
      bus_id: busInfo.bus_id,
      type: formData.value.type + 3,
    }).then((res) => {
      courseList.value = res.data.value;
      return res;
    });
  };
  const handleCourseChange = (activeIndex) => {
    const course_ids = list.value.map((item) => item.course_ids).flat();

    list.value.forEach((item, disableIndex) => {
      const own_course_ids = list.value[disableIndex].course_ids;
      // from course_ids remove own_course_ids
      const new_course_ids = course_ids.filter((i) => !own_course_ids.includes(i));
      if (activeIndex !== disableIndex) {
        item.courseList.forEach((course) => {
          if (new_course_ids.includes(course.id)) {
            course.disabled = true;
          } else {
            course.disabled = false;
          }
        });
      }
    });
  };

  // list
  const handleAddItem = () => {
    const course_ids = list.value.map((item) => item.course_ids).flat();
    const newCourseList = _.cloneDeep(courseList.value);

    newCourseList.forEach((item) => {
      if (course_ids.includes(item.id)) {
        item.disabled = true;
      } else {
        item.disabled = false;
      }
    });

    list.value.push({
      course_ids: [],
      card_price_list: [
        {
          card_ids: [],
          price: undefined,
        },
      ],
      courseList: newCourseList,
    });

    // errorIndex.value = -1;
    // errorTip.value = '';
  };
  const handleRemoveItem = (index) => {
    list.value[index].course_ids = [];
    handleCourseChange(-1);

    list.value.splice(index, 1);

    // errorIndex.value = -1;
    // errorTip.value = '';
  };

  // type change
  const handleTypeChange = () => {
    Message.loading({
      content: '课程选择将被重置!',
      duration: 500,
    });

    pullCourseList().then(() => {
      list.value.forEach((item) => {
        item.course_ids = [];
        item.courseList = _.cloneDeep(courseList.value);
      });
    });
  };

  // card list
  const cardList = ref([]);
  const pullCardList = () => {
    getCardsForAll().then((res) => {
      cardList.value = res.data.value;
    });
  };

  // plan list
  const planList = ref([]);
  const pullPlanList = () => {
    let type = '';
    if (props.id) {
      type = formData.value.type;
    }
    return getAllPlanList({
      type,
    }).then((res) => {
      planList.value = res.data.value.list;
    });
  };

  // request
  const handleSubmit = async () => {
    const valid = await formRef.value.validate();
    if (!valid) {
      isLoading.value = true;

      if (!validateAllCourse()) {
        isLoading.value = false;
        return;
      }

      const detail = list.value.map((item) => {
        return {
          pt_card_list: courseList.value
            .filter((i) => item.course_ids.includes(i.id))
            .map((i) => {
              return {
                id: i.id,
                name: i.name,
              };
            }),
          charge_plan: item.card_price_list.map((j) => {
            return {
              list: cardList.value
                .filter((k) => j.card_ids.includes(k.card_id))
                .map((k) => {
                  return {
                    id: k.card_id,
                    name: k.card_name,
                    card_type_id: k.card_type_id,
                  };
                }),
              price: j.price || 0,
            };
          }),
        };
      });
      formData.value.detail = JSON.stringify(detail);

      // submit
      let res = null;
      if (props.id) {
        res = await updatePlan({
          id: props.id,
          ...formData.value,
        }).catch(() => {
          isLoading.value = false;
        });
      } else {
        res = await addPlan(formData.value).catch(() => {
          isLoading.value = false;
        });
      }

      if (res.response.value.errorcode === 0) {
        Message.success('保存成功');
        isLoading.value = false;
        router.back();
      } else {
        isLoading.value = false;
      }
    }
  };
  const pullInfo = (id) => {
    isLoading.value = true;

    return getPlanInfo({
      id: id || props.id,
    }).then((res) => {
      if (!formData.value.name) {
        formData.value.name = res.data.value.name;
      }
      formData.value.type = Number(res.data.value.type);
      list.value = [];

      pullCourseList().then(() => {
        const all_course_ids = courseList.value.map((item) => item.id);
        const all_card_ids = cardList.value.map((item) => item.card_id);
        if (Array.isArray(res.data.value.detail)) {
          list.value = res.data.value.detail.map((item) => {
            return {
              course_ids: item.pt_card_list.filter((i) => all_course_ids.includes(i.id)).map((i) => i.id),
              card_price_list: item.charge_plan.map((j) => {
                return {
                  card_ids: j.list.filter((k) => all_card_ids.includes(k.id)).map((k) => k.id),
                  price: j.price,
                };
              }),
              courseList: _.cloneDeep(courseList.value),
            };
          });

          // disabled each other
          for (let i = 0; i < list.value.length; i += 1) {
            handleCourseChange(i);
          }
        }
      });

      return res;
    });
  };

  // modal
  const isShowCopyModal = ref(false);
  const handleCopyModalShow = () => {
    // pullPlanList();
    copyFormData.value.id = '';
    isShowCopyModal.value = true;
  };
  const handleCopy = async () => {
    const valid = await copyFormRef.value.validate();
    if (!valid) {
      isLoading.value = true;

      await pullInfo(copyFormData.value.id);

      Message.success('复制成功');
      isLoading.value = false;
      isShowCopyModal.value = false;
    }
  };

  // created
  pullCardList();
  // pullPlanList();
  if (props.id) {
    pullInfo().then(() => {
      pullPlanList().then(() => {
        // setTimeout(() => {
        isLoading.value = false;
        // }, 500);
      });
    });
  } else {
    isLoading.value = true;
    pullPlanList();
    pullCourseList().then(() => {
      handleAddItem();
      // setTimeout(() => {
      isLoading.value = false;
      // }, 500);
    });
  }
</script>

<style lang="less" scoped>
  // .error-card {
  //   animation: blink 2s infinite;
  // }

  // @keyframes blink {
  //   0% {
  //     background-color: white;
  //     border-color: #ff2351;
  //   }

  //   70% {
  //     background-color: #ffe9ee;
  //     border-color: #ffe9ee;
  //   }

  //   100% {
  //     background-color: white;
  //     border-color: #ff2351;
  //   }
  // }

  :deep(.arco-checkbox-label) {
    width: 100%;
  }
  :deep(.arco-select-option-checkbox) {
    width: 100%;
  }
</style>
