<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-tabs v-if="roleEditAuth" lazy-load>
        <a-tab-pane key="0" title="账号">
          <AccountList />
        </a-tab-pane>
        <a-tab-pane key="1" title="角色">
          <RoleList />
        </a-tab-pane>
      </a-tabs>
      <AccountList v-else />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { useAdminInfoStore } from '@/store';
  import RoleList from './components/role-list.vue';
  import AccountList from './components/account-list.vue';

  const adminInfo = useAdminInfoStore();
  const { role_edit: roleEditAuth } = storeToRefs(adminInfo);
  defineOptions({
    name: 'AdminList',
  });
</script>
