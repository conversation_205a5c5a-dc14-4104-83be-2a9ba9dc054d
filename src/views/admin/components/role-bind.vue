<template>
  <a-modal v-model:visible="isShowModal" title="角色绑定" :width="720" :on-before-ok="hansleConfirm">
    <a-space direction="vertical" style="width: 100%">
      <p class="title-content">
        请选择以下哪些账号要与
        <span class="cw" :title="data.name">{{ data.name }}</span>
        <span class="bind">角色绑定</span>
      </p>
      <div style="border-bottom: 1px solid #e9e9e9; padding-bottom: 6px; margin-bottom: 6px">
        <a-checkbox :model-value="checkedAll" :indeterminate="indeterminate" @change="handleChangeAll">全选</a-checkbox>
      </div>
      <a-checkbox-group v-model="checkAllGroup" class="check-list" @change="handleChange">
        <a-grid style="width: 100%" :cols="3" :col-gap="24" :row-gap="16">
          <a-grid-item v-for="item in accountList" :key="item.id">
            <a-checkbox :value="item.id">{{ item.username }}</a-checkbox>
          </a-grid-item>
        </a-grid>
      </a-checkbox-group>
    </a-space>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getAccount, accountBindRole } from '@/api/role';

  const props = defineProps<{
    modelValue?: boolean;
    data: Record<string, any>;
  }>();

  const emits = defineEmits(['update:modelValue', 'onSuccess']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const indeterminate = ref(false);
  const checkedAll = ref(false);
  const checkAllGroup = ref([]);
  const accountList = ref([]);

  const handleChangeAll = (value) => {
    indeterminate.value = false;
    if (value) {
      checkedAll.value = true;
      checkAllGroup.value = accountList.value.map((item) => item.id);
    } else {
      checkedAll.value = false;
      checkAllGroup.value = [];
    }
  };

  const handleChange = (values) => {
    if (values.length === 3) {
      checkedAll.value = true;
      indeterminate.value = false;
    } else if (values.length === 0) {
      checkedAll.value = false;
      indeterminate.value = false;
    } else {
      checkedAll.value = false;
      indeterminate.value = true;
    }
  };

  function getList() {
    getAccount({ role_id: props.data.role_id }).then((res) => {
      const list = res.data.value;
      accountList.value = list;
      list.forEach((item) => {
        if (item.is_bind === 1) {
          checkAllGroup.value.push(item.id);
        }
      });
    });
  }
  watch(
    () => isShowModal.value,
    (newValue) => {
      console.log(newValue);
      if (newValue) {
        getList();
      } else {
        checkAllGroup.value = [];
        accountList.value = [];
        indeterminate.value = true;
        checkedAll.value = false;
      }
    }
  );
  const { execute: setInfo } = accountBindRole();
  async function hansleConfirm() {
    const postData = {
      role_id: props.data.role_id,
      admin_id: checkAllGroup.value,
    };

    try {
      await setInfo({ data: postData });
      Message.success('设置成功！');
      emits('onSuccess');
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }
</script>

<style lang="less" scoped>
  .title-content {
    text-align: center;
    font-size: 14px;
    color: #999999;
    margin-bottom: 15px;
  }

  .check-list {
    width: 100%;
    max-height: 300px;
    overflow-y: scroll;
  }
</style>
