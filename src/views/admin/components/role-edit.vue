<template>
  <a-modal
    v-model:visible="isShowModal"
    class="base-modal"
    :title="+postData.role_id ? '编辑角色' : '添加角色'"
    :width="1180"
    :on-before-ok="hansleConfirm">
    <a-form ref="roleForm" class="base-set-form" :model="postData" auto-label-width>
      <a-form-item
        label="角色名"
        field="name"
        :rules="{ required: true, message: '请填写角色名' }"
        :content-flex="false">
        <a-input v-model="postData.name" placeholder="请填写角色名" />
        <a-checkbox
          v-model="postData.is_bus_role"
          style="margin-top: 8px"
          :disabled="!isAdminAuth && props.data.is_bus_role === '1'">
          角色在系统更新时自动得到新功能赋权（建议管理员拥有）
        </a-checkbox>
      </a-form-item>
      <a-form-item label="财务流水查询范围" field="check_days" :rules="{ required: true, message: '请填写' }">
        <a-input-number v-model="postData.check_days" :min="0" :max="360" :precision="0" placeholder="请填写" />
        <template #extra>0~360,0表示不限制</template>
      </a-form-item>
    </a-form>
    <div ref="editTable" class="edit-table">
      <template v-for="(table, key) in tableData">
        <table v-if="table.show" :key="key" class="table tableoption" style="table-layout: fixed; width: 100%">
          <thead>
            <tr>
              <th colspan="2" style="width: 400px">
                <div>
                  <a-switch
                    v-model="table.open"
                    :disabled="!isAdminAuth && props.data.is_bus_role === '1'"
                    @change="handleToggledOpen(key, $event)" />
                  <p class="title">{{ table.title }}</p>
                </div>
              </th>
              <th style="display: none"></th>
              <th>
                <div class="collapse-btn" @click="handleToggledCollapse(key)">
                  <p>
                    <span>{{ table.collapse ? '收起' : '展开' }}</span>
                    <icon-down :rotate="table.collapse ? 180 : 0" />
                  </p>
                </div>
              </th>
            </tr>
            <tr v-show="table.collapse">
              <th>一级菜单</th>
              <th>二级菜单</th>
              <th>功能权限</th>
            </tr>
          </thead>
          <tbody v-show="table.collapse" class="tbody">
            <template v-for="item in table.list">
              <tr v-for="(subItem, subIndex) in item.son" :key="subItem.id">
                <td v-if="subIndex == 0" :rowspan="item.son.length">
                  <a-switch
                    v-model="item.is_default"
                    :disabled="!isAdminAuth && props.data.is_bus_role === '1'"
                    @change="changeTopSwitch(item)"></a-switch>
                  <p>{{ item.name }}</p>
                </td>
                <td>
                  <div v-if="item.son.length > 1">
                    <a-switch
                      v-model="subItem.is_default"
                      :disabled="!item.is_default || (!isAdminAuth && props.data.is_bus_role === '1')"
                      @on-change="changeSwitch(subItem)"></a-switch>
                    <p style="white-space: nowrap">{{ subItem.title }}</p>
                  </div>
                </td>
                <td>
                  <a-checkbox
                    v-if="subItem.son && subItem.son.length > 1"
                    v-model="subItem.is_checkall"
                    :disabled="
                      !item.is_default || !subItem.is_default || (!isAdminAuth && props.data.is_bus_role === '1')
                    "
                    @change="changeCheckedAll(subItem)">
                    全选
                  </a-checkbox>
                  <a-checkbox
                    v-for="subSubItem in subItem.son"
                    :key="subSubItem.id"
                    v-model="subSubItem.is_default"
                    :disabled="
                      !item.is_default || !subItem.is_default || (!isAdminAuth && props.data.is_bus_role === '1')
                    "
                    @change="changeChecked(subItem)">
                    {{ subSubItem.title }}
                  </a-checkbox>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </template>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getRoleAccess, getAdminAccess, addRole, updateRole } from '@/api/role';
  import { useAdminInfoStore } from '@/store';

  const props = defineProps<{
    modelValue?: boolean;
    data: Record<string, any>;
  }>();

  const emits = defineEmits(['update:modelValue', 'onSuccess']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const adminInfo = useAdminInfoStore();
  const { is_admin: isAdminAuth } = storeToRefs(adminInfo);
  const postData = reactive({
    is_bus_role: false, // 与props.data中同样字段的含义不同 is_bus_role.is_bus_role 0 普通员工 1 初始管理员（自动得到新功能赋权） 2 一般管理员
    role_id: '0',
    name: '',
    check_days: 0,
    node_id: [],
  });
  const initTableData = {
    merchant: {
      title: '商家后台功能权限',
      show: false,
      open: false,
      collapse: false,
      list: [],
    },
    bus: {
      title: '门店功能权限',
      show: false,
      open: false,
      collapse: false,
      list: [],
    },
  };
  const tableData = reactive({ ...initTableData });

  function getNodeIds(list) {
    const nodeIds = [];
    list.forEach((item) => {
      if (item.son) {
        item.son.forEach((subitem) => {
          if (subitem.is_default) {
            nodeIds.push(subitem.id);
            if (subitem.son) {
              subitem.son.forEach((subSubItem) => {
                if (subSubItem.is_default) {
                  nodeIds.push(subSubItem.id);
                }
              });
            }
          }
        });
      }
    });
    return nodeIds;
  }

  function initAuthList(res) {
    const { web_node_list = [], merchant_node_list = [] } = res;

    Object.entries({
      bus: web_node_list,
      merchant: merchant_node_list,
    }).forEach(([key, list]) => {
      for (let i = 0; i < list.length; i += 1) {
        list[i].is_default = false;
        for (let j = 0; j < list[i].son.length; j += 1) {
          list[i].son[j].is_default = Number(list[i].son[j].is_default) === 1;
          let findNotDefaultIndex = -1;
          if (list[i].son[j].son) {
            list[i].son[j].son.forEach((v, index) => {
              v.is_default = Number(v.is_default) === 1;
              if (!v.is_default) {
                findNotDefaultIndex = index;
              }
            });
          }
          list[i].son[j].is_checkall = findNotDefaultIndex === -1;
          if (list[i].son[j].is_default) {
            list[i].is_default = true;
          }
        }
      }
      const open = !!getNodeIds(list).length;
      tableData[key].open = open;
      tableData[key].collapse = open;
      tableData[key].show = list.length;
      tableData[key].list = list;
    });
  }

  function getAuthList() {
    if (props.data.role_id) {
      getRoleAccess({ role_id: props.data.role_id }).then((res) => {
        initAuthList(res.data.value);
      });
    } else {
      getAdminAccess().then((res) => {
        initAuthList(res.data.value);
      });
    }
  }
  const editTable = ref();
  const roleForm = ref();
  watch(
    () => isShowModal.value,
    (newValue) => {
      if (newValue) {
        postData.is_bus_role = !!(props.data.is_bus_role === '1' || props.data.is_bus_role === '2');
        postData.role_id = props.data.role_id ? props.data.role_id : '';
        postData.name = props.data.name ? props.data.name : '';
        postData.check_days = typeof props.data.check_days === 'undefined' ? 0 : +props.data.check_days;
        getAuthList();
      } else {
        if (editTable.value) {
          editTable.value.scrollTop = 0;
        }
        Object.assign(tableData, initTableData);
        roleForm.value.resetFields();
      }
    }
  );

  async function hansleConfirm() {
    const { execute: setInfo } = props.data.role_id ? updateRole() : addRole();
    try {
      const errors = await roleForm.value.validate();
      if (errors) return false;
      // 开启了对应商家/门店权限，才传
      const nodes = [];
      const { merchant, bus } = tableData;
      if (merchant.open) nodes.push(...merchant.list);
      if (bus.open) nodes.push(...bus.list);
      await setInfo({
        data: {
          ...postData,
          node_id: getNodeIds(nodes),
          is_bus_role: postData.is_bus_role ? 2 : 0,
        },
      });
      Message.success('设置成功！');
      emits('onSuccess');
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  function changeCheckedAll(item) {
    if (item.son) {
      item.son.forEach((arr) => {
        arr.is_default = !!item.is_checkall;
      });
    }
  }
  // 切换是否开启 商家/门店功能权限
  function handleToggledOpen(key, value) {
    tableData[key].collapse = value;
  }

  // 切换展开/收起
  function handleToggledCollapse(key) {
    const item = tableData[key];
    item.collapse = !item.collapse;
  }

  function changeSwitch(item) {
    if (!item.is_default) {
      item?.son?.forEach((arr) => {
        arr.is_default = false;
      });
      item.is_checkall = false;
    }
  }

  function changeTopSwitch(item) {
    if (!item.is_default) {
      item?.son?.forEach((arr) => {
        arr.is_default = false;
        changeSwitch(arr);
      });
    } else if (item.is_default && item?.son?.length === 1) {
      item.son[0].is_default = true;
    }
  }

  function changeChecked(parentItem) {
    const findNotDefaultIndex = parentItem?.son?.findIndex((v) => !v.is_default);
    parentItem.is_checkall = findNotDefaultIndex === -1;
  }
</script>

<style lang="less" scoped>
  .edit-table {
    width: 100%;
    margin-top: 30px;
    .arco-switch {
      float: right;
      margin-left: 30px;
      margin-top: 8px;
    }
  }
  .edit-table thead tr th {
    text-align: center;
    background: #fff;
    .arco-switch {
      margin-top: 0;
    }
  }
  .table {
    margin: 0;
    margin-bottom: 50px;
    border-spacing: 0;
    border-collapse: collapse;
  }
  .table > thead > tr > th {
    padding: 8px 12px;
    border: 1px solid #cccccc;
    border-bottom: none;
    border-right: none;
    line-height: 24px;

    .title {
      font-weight: bold;
      font-size: 15px;
    }

    .collapse-btn {
      margin-left: auto;
      width: 55px;
      text-align: center;
      cursor: pointer;
    }
  }
  .tbody {
    border-top: 1px solid #cccccc;
  }
  .table > thead > tr {
    border: 1px solid #cccccc;
    line-height: 40px;
    height: 40px;
  }
  .table tbody tr {
    line-height: 40px;
    height: 40px;
  }
  .table tbody tr td {
    border: 1px solid #cccccc;
    vertical-align: middle;
    padding: 8px 12px;
    text-align: center;
    color: #333;
    font-size: 14px;
    font-weight: 400;
    &:last-child {
      text-align: left;
    }
    .arco-checkbox-wrapper {
      margin-right: 20px;
      color: #333;
      font-size: 14px;
      font-weight: 400;
    }
  }
  .table th:nth-child(1) {
    width: 177px;
  }
  .table th:nth-child(2) {
    width: 230px;
  }
</style>
