<template>
  <div class="base-box">
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="search" label="姓名/用户名">
                  <a-input
                    v-model="searchParam.search"
                    placeholder="姓名/用户名"
                    allow-clear
                    @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="belong_bus_id" label="场馆/区域">
                  <admin-region
                    v-model="searchParam.region_bus"
                    :should-default="1"
                    :multiple="false"
                    placeholder="请选择"
                    @change="loadTableList()" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="$router.push('/admin/add')">添加</a-button>
            <a-button :loading="deleteLoading" @click="handleDelete()">批量删除</a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-bind="tableProps"
        v-model:selectedKeys="selectedIds"
        v-on="tableEvent"
        @selection-change="handleSelectionChange">
        <template #columns>
          <a-table-column title="用户名" data-index="username" />
          <a-table-column title="姓名" data-index="realname" />
          <a-table-column title="管理门店" data-index="busList">
            <template #cell="{ record }">
              <a-link @click="handleBusListModal(record.busList)">{{ record.busCount }}</a-link>
            </template>
          </a-table-column>
          <a-table-column title="角色" data-index="role_name" ellipsis tooltip />
          <a-table-column title="最近登录时间" data-index="last_login_time" />
          <a-table-column title="禁用/启用">
            <template #cell="{ record }">
              <a-switch
                :loading="record.loading"
                :model-value="record.status"
                :disabled="record.edit === 0"
                checked-value="1"
                unchecked-value="0"
                :title="record.edit === 0 ? '权限不足，不可修改' : ''"
                @change="handleStatusChange($event, record)" />
            </template>
          </a-table-column>
          <a-table-column title="BOSS端" data-index="is_open_boss">
            <template #cell="{ record }">
              <icon-mobile v-if="record.is_open_boss === '1'" :size="30" style="color: #45bf55" />
              <icon-mobile v-else :size="30" style="color: #8a96a8" />
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="handleDetail(record)">编辑</a-link>
                <a-link @click="handleReset(record)">修改密码</a-link>
                <a-link status="danger" :disabled="deleteLoading" @click="handleDelete([record.admin_id])">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <bus-list-modal v-model="isShowBusList" :table-data="curBusList" name-key="name" />
    <ResetModal v-model="isShowReset" :account-data="curData" />
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { getAdminList, getMerchantAdminList, updateAdminStatus, deleteAdmin } from '@/api/admin';
  import busListModal from '@/components/bus-list-modal/index.vue';
  import AdminRegion from '@/components/form/adminRegion.vue';
  import useTableProps from '@/hooks/table-props';
  import ResetModal from '../../account/components/reset-modal.vue';

  const { IS_BRAND_SITE } = window;
  const router = useRouter();
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    IS_BRAND_SITE ? getMerchantAdminList : getAdminList
  );
  tableProps.value['row-key'] = 'admin_id';
  tableProps.value['row-selection'] = {
    type: 'checkbox',
    showCheckedAll: true,
  };
  const selectedIds = ref([]);
  const selectedNames = ref([]);
  function handleSelectionChange(rowKeys: string | number[]) {
    tableProps.value.data.forEach((item) => {
      const hasName = selectedNames.value.find((name) => name === item.username);
      const hasCheck = rowKeys.includes(item.admin_id);
      if (hasName && !hasCheck) {
        selectedNames.value = selectedNames.value.filter((name) => name !== item.username);
      } else if (!hasName && hasCheck) {
        selectedNames.value.push(item.username);
      }
    });
  }
  // 设置除分页外的其它属性值
  setSearchParam({
    search: '',
    region_bus: '',
  });

  const isShowBusList = ref(false);
  const curBusList = ref<string[] | Record<string, any>[]>([]);
  function handleBusListModal(list = []) {
    curBusList.value = list;
    isShowBusList.value = true;
  }
  async function handleStatusChange(val: string, info) {
    const { status, admin_id } = info;
    const { isLoading, execute: setInfo } = updateAdminStatus();
    info.loading = isLoading;
    try {
      const newStatus = status === '1' ? '0' : '1';
      await setInfo({ data: { status, admin_id } });
      info.status = newStatus;
      Message.success('设置成功！');
    } catch (error) {
      console.log(error);
      Message.error('设置失败！');
    }
  }
  // 跳转详情
  const handleDetail = (record: Record<string, any>) => {
    const { admin_id } = record;
    router.push({
      path: '/admin/add',
      query: {
        id: admin_id,
      },
    });
  };
  // 重置密码
  const isShowReset = ref(false);
  const curData = ref({});
  const handleReset = (record: Record<string, any>) => {
    isShowReset.value = true;
    curData.value = record;
  };
  // 删除确认
  const { isLoading: deleteLoading, execute: deleteAdminExecute } = deleteAdmin();
  async function handleDeletePost(adminIds: string[]) {
    const { response } = await deleteAdminExecute({
      data: {
        admin_ids: adminIds,
      },
    });
    if (response) {
      Message.success('删除成功');
      selectedIds.value = [];
      loadTableList();
    }
  }
  const handleDelete = (adminIds: string[] | undefined) => {
    let content = '确定要删除该账号么？';
    if (!adminIds) {
      if (selectedIds.value.length < 1) {
        Message.error('请先勾选需要删除的账号!');
        return;
      }
      content = `您确定要删除已选中的账号"${selectedNames.value.join(',')}"吗？`;
      adminIds = selectedIds.value;
    }
    Modal.confirm({
      title: '提示',
      content,
      onOk: () => {
        handleDeletePost(adminIds);
      },
    });
  };
</script>
