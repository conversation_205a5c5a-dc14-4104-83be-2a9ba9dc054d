<template>
  <div class="base-box">
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="name" label="角色名">
                  <a-input v-model="searchParam.name" placeholder="角色名" allow-clear @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="handleAddRole">添加</a-button>
            <a-button :loading="deleteLoading" @click="handleDelete()">批量删除</a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        v-bind="tableProps"
        v-model:selectedKeys="selectedIds"
        v-on="tableEvent"
        @selection-change="handleSelectionChange">
        <template #columns>
          <a-table-column title="角色名" data-index="username">
            <template #cell="{ record }">
              <span>
                <span>{{ record.name }}</span>
                <sup
                  v-if="record.is_bus_role == 1 || record.is_bus_role == 2"
                  style="font-size: 12px; color: #5cb85c; margin-left: 6px"
                  title="商家管理员">
                  管
                </sup>
              </span>
            </template>
          </a-table-column>
          <a-table-column title="授权账号" data-index="username" ellipsis tooltip />
          <a-table-column title="角色状态">
            <template #cell="{ record }">
              <a-switch
                :loading="record.loading"
                :model-value="record.assign_status"
                checked-value="1"
                unchecked-value="0"
                @change="handleStatusChange($event, record)" />
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="editRole(record)">编辑</a-link>
                <a-link @click="bindRole(record)">绑定</a-link>
                <a-link status="danger" :disabled="record.is_bus_role === '1'" @click="handleDelete([record.role_id])">
                  删除
                </a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <role-bind v-model="showRoleBind" :data="selItem" @on-success="loadTableList" />
    <role-edit v-model="showRoleEdit" :data="selItem" @on-success="loadTableList" />
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { getRoleList, updateRoleAssignStatus, delRole } from '@/api/role';
  import useTableProps from '@/hooks/table-props';
  import RoleBind from './role-bind.vue';
  import RoleEdit from './role-edit.vue';

  const router = useRouter();
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getRoleList);
  // 设置除分页外的其它属性值
  setSearchParam({
    name: '',
  });
  tableProps.value['row-key'] = 'role_id';
  tableProps.value['row-selection'] = {
    type: 'checkbox',
    showCheckedAll: true,
  };
  const selectedIds = ref([]);
  const selectedNames = ref([]);
  function handleSelectionChange(rowKeys: string | number[]) {
    tableProps.value.data.forEach((item) => {
      const hasName = selectedNames.value.find((name) => name === item.name);
      const hasCheck = rowKeys.includes(item.role_id);
      if (hasName && !hasCheck) {
        selectedNames.value = selectedNames.value.filter((name) => name !== item.name);
      } else if (!hasName && hasCheck) {
        selectedNames.value.push(item.name);
      }
    });
  }
  loadTableList();

  const showRoleEdit = ref(false);
  const showRoleBind = ref(false);
  const selItem = ref({});
  function bindRole(item) {
    showRoleBind.value = true;
    selItem.value = item;
  }

  function editRole(item) {
    showRoleEdit.value = true;
    selItem.value = item;
  }
  function handleAddRole() {
    showRoleEdit.value = true;
    selItem.value = {};
  }

  async function handleStatusChange(val: string, info) {
    const { assign_status, role_id } = info;
    const { isLoading, execute: setInfo } = updateRoleAssignStatus();
    info.loading = isLoading;
    try {
      const newStatus = assign_status === '1' ? '0' : '1';
      await setInfo({ data: { assign_status: newStatus, role_id } });
      info.assign_status = newStatus;
      Message.success('设置成功！');
    } catch (error) {
      console.log(error);
      Message.error('设置失败！');
    }
  }
  // 删除确认
  const { isLoading: deleteLoading, execute: deleteRoleExecute } = delRole();
  async function handleDeletePost(adminIds: string[]) {
    const { response } = await deleteRoleExecute({
      data: {
        role_id: adminIds,
      },
    });
    if (response) {
      Message.success('删除成功');
      selectedIds.value = [];
      loadTableList();
    }
  }
  const handleDelete = (adminIds: string[] | undefined) => {
    let content = '确定要删除该角色么？';
    if (!adminIds) {
      if (selectedIds.value.length < 1) {
        Message.error('请先勾选需要删除的角色!');
        return;
      }
      content = `您确定要删除已选中的角色"${selectedNames.value.join(',')}"吗？`;
      adminIds = selectedIds.value;
    }
    Modal.confirm({
      title: '提示',
      content,
      onOk: () => {
        handleDeletePost(adminIds);
      },
    });
  };
</script>
