<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
        <a-form-item field="realname" label="真实姓名" :rules="{ required: true, message: '请填写真实姓名' }">
          <a-input v-model="formData.realname" />
        </a-form-item>
        <a-form-item
          label="手机号"
          field="phone"
          :rules="{ required: true, pattern: /^1\d{10}$/, message: '手机号码错误', trigger: 'change' }">
          <a-input v-model="formData.phone" />
        </a-form-item>
        <a-form-item
          v-if="!id"
          label="登录账号"
          field="username"
          :rules="{ required: true, validator: checkUsername, trigger: 'blur' }">
          <a-input v-model="formData.username" />
        </a-form-item>
        <a-form-item v-else label="登录账号" field="username">
          <a-input v-model="formData.username" disabled />
        </a-form-item>
        <a-form-item
          v-if="!id"
          label="密码"
          field="password"
          :rules="{
            required: id ? false : true,
            min: 6,
            max: 16,
            message: '请正确填写密码（长度6-16）',
          }">
          <a-input v-model="formData.password" type="password" />
        </a-form-item>
        <a-form-item
          v-if="!id"
          label="重复密码"
          field="repassword"
          :rules="{ required: id ? false : true, validator: validatePass, trigger: 'blur' }">
          <a-input v-model="formData.repassword" type="password" />
        </a-form-item>
        <a-form-item label="管辖范围" field="admin_type">
          <a-radio-group v-model="formData.admin_type">
            <a-radio v-if="adminType === '1'" value="1">商家管理(所有场馆)</a-radio>
            <a-radio value="0">指定管辖范围</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-show="formData.admin_type === '0'"
          field="admin_type"
          label="管辖范围"
          :rules="{ required: true, message: '请选择管辖范围' }">
          <AdminRegion :id="id || ''" v-model="formData.region_bus" :should-default="id ? 0 : 1" />
        </a-form-item>
        <a-form-item
          label="角色"
          field="role_ids"
          :rules="{ required: true, message: '请选择角色' }"
          :content-flex="false">
          <a-checkbox-group v-model="formData.role_ids">
            <a-grid :cols="3" :row-gap="16">
              <a-grid-item v-for="item in roleList" :key="item.role_id">
                <a-checkbox :value="item.role_id">{{ item.name }}</a-checkbox>
              </a-grid-item>
            </a-grid>
          </a-checkbox-group>
          <div v-if="roleEditAuth" style="margin-top: 16px">
            <a-button type="outline" @click="showRoleAdd = true">创建新角色</a-button>
          </div>
        </a-form-item>
        <a-form-item label="是否开通BOSS端" field="is_open_boss">
          <a-radio-group v-model="formData.is_open_boss">
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
    <role-edit v-model="showRoleAdd" :data="{}" @on-success="getRoleList" />
  </div>
</template>

<script lang="ts" setup>
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { getRoles } from '@/api/role';
  import { checkAdminUsername, getAdminInfo, addAdmin, updateAdmin } from '@/api/admin';
  import { useBusInfoStore, useAdminInfoStore } from '@/store';
  import AdminRegion from '@/components/form/adminRegion.vue';
  import RoleEdit from './components/role-edit.vue';

  const route = useRoute();
  const { id } = route.query;
  const busInfo = useBusInfoStore();
  const adminInfo = useAdminInfoStore();
  const { admin_type: adminType } = storeToRefs(busInfo);
  const { role_edit: roleEditAuth } = storeToRefs(adminInfo);
  const formRef = ref<FormInstance>();
  const formData = reactive({
    username: '',
    realname: '',
    password: '',
    repassword: '',
    phone: '',
    admin_type: '0',
    role_ids: [],
    region_bus: [],
    is_open_boss: '0',
  });
  const showRoleAdd = ref(false);
  const roleList = ref([]);
  function getRoleList() {
    getRoles().then((res) => {
      roleList.value = res.data.value;
    });
  }

  function checkUsername(value, callback) {
    if (id) {
      callback();
      return true;
    }
    if (!value) {
      callback('请输入登录账号');
      return false;
    }
    return checkAdminUsername({ username: value }).then((res) => {
      const { status } = res.data.value;
      if (status === 40020) {
        callback('该登录账号已被使用');
      } else if (status === 0) {
        callback();
      }
    });
  }
  function validatePass(value, callback) {
    if (formData.password && value === '') {
      callback('请再次输入密码');
    } else if (formData.password && value !== formData.password) {
      callback('两次输入密码不一致!');
    } else {
      callback();
    }
  }

  function getInfo() {
    getAdminInfo({ admin_id: id }).then((res) => {
      const { info } = res.data.value;
      Object.assign(formData, info);
    });
  }
  const { isLoading, execute: executeUpdate } = id ? updateAdmin() : addAdmin();
  const router = useRouter();
  async function handleSubmit() {
    const errors = await formRef.value?.validate();
    if (!errors) {
      executeUpdate({
        data: {
          ...formData,
          admin_id: id || '',
        },
      }).then(() => {
        Message.success('操作成功');
        router.back();
      });
    }
  }

  onMounted(() => {
    if (id) {
      getInfo();
      getRoleList();
    } else {
      getRoleList();
    }
  });
</script>
