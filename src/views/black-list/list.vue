<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="search" label="会员">
                  <a-input v-model="searchParam.username" placeholder="姓名" allow-clear @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="belong_bus_id" label="场馆">
                  <admin-region v-model="searchParam.bus_or_region_id" :multiple="false" placeholder="请选择" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="$router.push('/admin/black-list-add')">添加</a-button>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column v-if="IS_BRAND_SITE" title="场馆" data-index="bus_name" />
          <a-table-column title="会员姓名" data-index="username" />
          <a-table-column title="限制场馆" data-index="rule_names" />
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="handleChange(record)">编辑</a-link>
                <a-link status="danger" @click="handleDelete(record)">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { getBlacklist, getMerchantBlacklist, deleteBlack, deleteMerchantBlack } from '@/api/user-black-list';
  import AdminRegion from '@/components/form/adminRegion.vue';
  import useTableProps from '@/hooks/table-props';
  import { useBusInfoStore } from '@/store';

  defineOptions({
    name: 'MerchantBlackList',
  });
  const { IS_BRAND_SITE } = window;
  const router = useRouter();
  const busInfo = useBusInfoStore();
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    IS_BRAND_SITE ? getMerchantBlacklist : getBlacklist
  );
  // 设置除分页外的其它属性值
  setSearchParam({
    username: '',
    bus_or_region_id: '',
    bus_id: IS_BRAND_SITE ? '' : busInfo.bus_id,
    admin_id: busInfo.admin_id,
  });
  onActivated(() => {
    loadTableList();
  });
  // 跳转详情
  const handleChange = (record: Record<string, any>) => {
    const { id, bus_id } = record;
    router.push({
      path: '/admin/black-list-edit',
      query: {
        id,
        bus_id,
      },
    });
  };
  // 删除确认
  async function handleDeletePost(record: Record<string, any>) {
    const postInfo = {
      id: record.id,
      bus_id: record.bus_id,
    };
    const { response } = IS_BRAND_SITE ? await deleteMerchantBlack(postInfo) : await deleteBlack(postInfo);
    if (response) {
      Message.success('删除成功');
      loadTableList();
    }
  }
  const handleDelete = (record: Record<string, any>) => {
    Modal.confirm({
      title: '确定删除吗？',
      content: `确定删除会员黑名单-${record.username}吗`,
      okText: '删除',
      onOk: () => {
        handleDeletePost(record);
      },
    });
  };
</script>
