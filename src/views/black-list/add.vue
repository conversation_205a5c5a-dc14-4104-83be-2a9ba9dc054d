<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
        <a-form-item
          v-if="IS_BRAND_SITE"
          field="bus_id"
          label="场馆"
          :rules="[
            {
              required: true,
              message: '请选择场馆',
            },
          ]">
          <BusSelectAdmin v-model="formData.bus_id" :disabled="!!queryId" @change="handleBusChange" />
        </a-form-item>

        <a-form-item
          field="user_id"
          label="会员姓名"
          :rules="[
            {
              required: true,
              message: '请选择会员',
            },
          ]">
          <span v-if="queryId">{{ userObject?.username }}</span>
          <user-search
            v-else
            v-model="formData.user_id"
            :is-get-more-on-change="true"
            :from="4"
            :bus-id="formData.bus_id"
            @change="handleUserChange"
          />
        </a-form-item>
        <a-form-item v-if="userObject" label="">
          <user-info :user-info="userObject" />
        </a-form-item>

        <a-form-item
          field="bus_or_region_id"
          label="限制场馆"
          :rules="[
            {
              required: true,
              message: '请选择限制场馆',
            },
          ]">
          <admin-region v-model="formData.bus_or_region_id" @change="handleRegionChange" />
        </a-form-item>

        <a-form-item v-if="formData.rule.length" :content-flex="false">
          <a-row v-for="(item, index) in formData.rule" :key="index" class="limit-content">
            <a-col flex="80px">{{ item.name }}</a-col>
            <a-col flex="auto">
              <a-form-item :field="`rule[${index}].end_time`" class="end_time" label="限制期限">
                <a-date-picker
                  v-model="item.end_time"
                  style="width: 100%; min-width: 340px"
                  type="date"
                  placeholder="请选择限制结束日期" />
                <template #extra>
                  <div>不选表示无限期</div>
                </template>
              </a-form-item>
              <a-form-item
                :field="`rule[${index}].member_rule`"
                class="content"
                label="会员端限制"
                :rules="[
                  {
                    required: true,
                    message: '请选择会员端限制',
                  },
                ]">
                <a-checkbox-group v-model="item.member_rule">
                  <a-checkbox value="1">禁止签到</a-checkbox>
                  <a-checkbox value="2">禁止购卡购课</a-checkbox>
                  <a-checkbox value="3">禁止约团课</a-checkbox>
                  <a-checkbox value="4">禁止约私/泳教</a-checkbox>
                  <a-checkbox value="5">禁止购票</a-checkbox>
                  <a-checkbox value="6">禁止订场</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
              <a-form-item :field="`rule[${index}].device_limit`" class="content special" label="硬件限制">
                <a-checkbox v-model="item.device_limit">禁止签到</a-checkbox>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>

        <a-form-item field="remark" label="备注">
          <a-textarea v-model="formData.remark" placeholder="请输入备注" allow-clear />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { useBusInfoStore } from '@/store';
  import AdminRegion from '@/components/form/adminRegion.vue';
  import UserSearch from '@/components/user/user-search.vue';
  import UserInfo from '@/components/user/user-info.vue';
  import {
    addBlack,
    getDetail,
    editBlack,
    addMerchantBlack,
    getMerchantDetail,
    editMerchantBlack,
  } from '@/api/user-black-list';

  interface RuleType {
    bus_id?: string; // 场馆id
    device_limit?: boolean; // 硬件限制
    district_id?: string; // 区域id
    end_time: string; // 限制结束时间
    id?: string; // 规则id
    member_rule?: string[]; // 会员端限制规则 1:禁止签到 2:禁止购卡购课
    type: string; // 限制类型 1区域 2场馆
    unionid: string; // 树Id(前端使用)
    [key: string]: any;
  }

  const { IS_BRAND_SITE } = window;
  const busInfo = useBusInfoStore();
  const route = useRoute();
  const router = useRouter();
  const userObject = ref();
  const { id: queryId, bus_id } = route.query;
  const formRef = ref<FormInstance>();
  const formData = reactive({
    id: (queryId || '') as string,
    bus_id: (bus_id || busInfo.bus_id || '') as string,
    admin_id: busInfo.admin_id,
    bus_or_region_id: [] as string[],
    rule: [] as RuleType[],
    remark: '',
    user_id: '',
  });

  async function fethDetail() {
    const { data } = IS_BRAND_SITE
      ? await getMerchantDetail({ id: queryId, bus_id })
      : await getDetail({ id: queryId, bus_id });
    formData.rule = data.value.rule.map((item: Record<string, any>) => {
      formData.bus_or_region_id.push(item.unionid);
      return {
        ...item,
        end_time: item.end_time === 0 ? '' : dayjs(item.end_time).format('YYYY-MM-DD'),
      };
    });
    userObject.value = data.value.User;
    formData.user_id = data.value.User.user_id;
    formData.id = data.value.id;
    formData.remark = data.value.remark;
  }
  if (queryId) {
    fethDetail();
  }
  // rule根据选择的区域初始化
  function handleRegionChange(infoArr: Record<string, any>[]) {
    const list: RuleType[] = [];
    infoArr.forEach((item) => {
      const { type, unionId, name, id } = item;
      const oldInfo = formData.rule.find((info) => info.unionid === unionId);
      if (oldInfo) {
        list.push(oldInfo);
      } else {
        list.push({
          bus_id: type !== 1 ? id : '',
          district_id: type === 1 ? id : '',
          unionid: unionId,
          device_limit: true,
          end_time: '',
          member_rule: ['1', '2', '3', '4', '5', '6'],
          name,
          type,
        });
      }
    });
    formData.rule = list;
  }
  const editApi = IS_BRAND_SITE ? editMerchantBlack() : editBlack();
  const addApi = IS_BRAND_SITE ? addMerchantBlack() : addBlack();
  const { isLoading, execute: addData } = queryId ? editApi : addApi;
  const handleSubmit = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      const postData = {
        ...formData,
        rule: formData.rule.map((item) => {
          return {
            ...item,
            end_time: item.end_time || 0,
          };
        }),
      };
      await addData({
        data: postData,
      });
      Message.success({
        content: '操作成功！',
      });
      router.back();
    }
  };

  // 选中用户变化
  function handleUserChange(info: Record<string, any> | undefined) {
    formData.user_id = info?.user_id || '';
    userObject.value = info || null;
  }

  function handleBusChange(busId: string) {
    handleUserChange(undefined);
  }
</script>
