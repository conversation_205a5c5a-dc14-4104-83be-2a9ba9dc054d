<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="rule_type" label="类型">
                  <a-select v-model="searchParam.rule_type" placeholder="请选择" allow-clear allow-search>
                    <a-option v-for="item in MEIPING_EXCHANGE_TYPE" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="name" label="商品">
                  <a-input v-model="searchParam.name" placeholder="关键词" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="$router.push('/admin/sale-add')">新增关联</a-button>
            <a-button :loading="isBatchCancelLoading" @click="batchDel">批量删除</a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel ref="exportExcel">
            <template #default="{ handleExport }">
              <a-button @click="handleClickExport(handleExport as Callback<ExportData>)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-model:selectedKeys="selectedIds" v-on="tableEvent">
        <template #columns>
          <a-table-column title="类型" data-index="ruleTypeName" />
          <a-table-column title="商品" data-index="name" />
          <a-table-column title="美团" data-index="meituan_deal_id">
            <template #cell="{ record }">
              <a-link v-if="record.is_show_meituan === '1'">关联id {{ record.meituan_deal_id }}</a-link>
              <div v-else>未开通</div>
            </template>
          </a-table-column>
          <a-table-column title="大众点评" data-index="dianping_deal_id">
            <template #cell="{ record }">
              <a-link v-if="record.is_show_dianping === '1'">关联id {{ record.dianping_deal_id }}</a-link>
              <div v-else>未开通</div>
            </template>
          </a-table-column>
          <a-table-column title="抖音" data-index="tiktok_deal_id">
            <template #cell="{ record }">
              <a-link v-if="record.is_show_tiktok === '1'">关联id {{ record.tiktok_deal_id }}</a-link>
              <div v-else>未开通</div>
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="$router.push('/admin/sale-add/' + record.id)">编辑</a-link>
                <a-link status="danger" :loading="record.isDelLoading" @click="delPlan(record)">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { getRuleList, delRule } from '@/api/card-rule';
  import { MEIPING_EXCHANGE_TYPE } from '@/store/constants';
  import useTableProps from '@/hooks/table-props';
  import ExportExcel from '@/components/exportExcel.vue';
  import { Callback, ExportData } from '@/types/global';

  const selectedIds = ref([]);
  function getNameByType(type: number) {
    const item = MEIPING_EXCHANGE_TYPE.find((i) => +i.value === type);
    return item?.label;
  }
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    getRuleList,
    (list: any[]) => {
      return list.map((item: any) => {
        return {
          ...item,
          ruleTypeName: getNameByType(item.rule_type),
        };
      });
    }
  );

  tableProps.value['row-selection'] = {
    type: 'checkbox',
    showCheckedAll: true,
  };
  const route = useRoute();
  // 设置除分页外的其它属性值
  setSearchParam({
    rule_type: '',
    name: '',
  });

  loadTableList();

  function delPlan(record: Record<string, any>) {
    const { isLoading, execute: executeDel } = delRule();
    record.isDelLoading = isLoading;
    Modal.confirm({
      title: '删除',
      content: '确定要删除?',
      onOk: () => {
        executeDel({ data: { ids: [record.id] } }).then((res) => {
          loadTableList();
          Message.success(res.response.value.errormsg);
        });
      },
    });
  }

  // 批量删除
  const { isLoading: isBatchCancelLoading, execute: executeBatchDel } = delRule();
  function batchDel() {
    if (!selectedIds.value.length) {
      Message.error('请先勾选需要删除的商品');
      return;
    }
    Modal.confirm({
      title: '删除',
      content: '确定要批量删除?',
      onOk: () => {
        executeBatchDel({ data: { ids: selectedIds.value } }).then((res) => {
          selectedIds.value = [];
          loadTableList();
          Message.success(res.response.value.errormsg);
        });
      },
    });
  }

  const handleClickExport = (cb: Callback<ExportData>) => {
    const columns = [
      {
        title: '类型',
        dataIndex: 'ruleTypeName',
      },
      {
        title: '商品',
        dataIndex: 'name',
      },
      {
        title: '美团',
        dataIndex: 'meituan_deal_id',
      },
      {
        title: '大众点评',
        dataIndex: 'dianping_deal_id',
      },
      {
        title: '抖音',
        dataIndex: 'tiktok_deal_id',
      },
    ];
    loadTableList(true).then((list) => {
      cb({
        filename: '美评抖音售卖',
        columns,
        data: list,
      });
    });
  };
</script>
