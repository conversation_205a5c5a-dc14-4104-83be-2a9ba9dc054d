<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="10">
                <a-form-item field="start_time" label="时间">
                  <a-range-picker
                    v-model="rangeValue"
                    :allow-clear="false"
                    style="width: 100%"
                    @change="handleTimeChange" />
                </a-form-item>
              </a-col>
              <a-col :span="7">
                <a-form-item field="platform_id" label="来源" allow-clear>
                  <a-select v-model="searchParam.platform_id" placeholder="请选择" allow-search allow-clear>
                    <a-option v-for="item in THIRD_PLATFORM_NAME" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="7">
                <a-form-item field="rule_type" label="类型">
                  <a-select v-model="searchParam.rule_type" placeholder="请选择" allow-search allow-clear>
                    <a-option v-for="item in MEIPING_EXCHANGE_TYPE" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="10">
                <a-form-item field="code" label="券码/订单">
                  <a-input v-model="searchParam.code" placeholder="请输入" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="7">
                <a-form-item field="is_cancel" label="状态">
                  <a-select v-model="searchParam.is_cancel" placeholder="请选择" allow-clear>
                    <a-option value="0">已核销</a-option>
                    <a-option value="1">已取消</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="showVerifyModal = true">新增核销</a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportButton :data="exportPostParams" url="/Web/CardRule/sanVerifyList" />
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-model:selectedKeys="selectedIds" :row-class="getRowClassName" v-on="tableEvent">
        <template #columns>
          <a-table-column title="时间" data-index="create_time" />
          <a-table-column title="来源" data-index="platformName" />
          <a-table-column title="券码/订单" data-index="code" />
          <a-table-column title="类型" data-index="ruleTypeName" />
          <a-table-column title="团购商品" data-index="code_name" />
          <a-table-column title="对应系统商品" data-index="name" />
          <a-table-column title="核销人信息" data-index="username">
            <template #cell="{ record }">
              <span v-if="!record.user_id">{{ record.username }}</span>
              <a-link v-else :href="goSubDetail(record.user_id, '', false)" target="_blank">
                {{ record.username }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="核销方式" data-index="consumeTypeName" />
          <a-table-column title="核销账号" data-index="admin_id" />
          <a-table-column title="状态" data-index="cancelStatus" />
          <a-table-column title="取消时间" data-index="cancel_time" />
          <a-table-column title="取消账号" data-index="cancel_admin_id" />

          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link
                  status="danger"
                  :loading="record.isDelLoading"
                  :disabled="record.disabled"
                  @click="delPlan(record)">
                  取消核销
                </a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <VerifyModal v-model="showVerifyModal" @success="loadTableList" />
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { Modal, Message } from '@arco-design/web-vue';
  import { MEIPING_EXCHANGE_TYPE, THIRD_PLATFORM_NAME } from '@/store/constants';
  import { sanVerifyList, cancelVerification } from '@/api/card-rule';
  import useTableProps from '@/hooks/table-props';
  import ExportButton from '@/components/form/export-button.vue';
  import { goSubDetail } from '@/utils/router-go';
  import VerifyModal from './components/verify-modal.vue';

  defineOptions({
    name: 'VerifyList',
  });
  const selectedIds = ref([]);
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    sanVerifyList,
    (list) => {
      return list.map((item: any) => {
        return {
          ...item,
          platformName: THIRD_PLATFORM_NAME.find((i: any) => i.value === item.platform_id)?.label,
          ruleTypeName: MEIPING_EXCHANGE_TYPE.find((i: any) => i.value === item.rule_type)?.label,
          cancelStatus: item.is_cancel === '1' ? '已取消' : '已核销',
          disabled: item.is_cancel === '1',
          consumeTypeName: ['', '前台核销', '会员端核销', '抖音小程序核销'][Number(item.consume_type)],
        };
      });
    }
  );

  const rangeValue = ref<string[]>([dayjs().add(-30, 'd').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]);
  // 设置除分页外的其它属性值
  setSearchParam({
    rule_type: '',
    start_time: rangeValue.value[0],
    end_time: rangeValue.value[1],
    code: '',
    is_cancel: '',
    platform_id: '',
  });
  const exportPostParams = computed(() => {
    return {
      ...searchParam,
      is_export: 1,
      current: 1,
      pageSize: tableProps.value.pagination.total,
    };
  });

  const showVerifyModal = ref(false);
  onActivated(() => {
    loadTableList();
  });

  function handleTimeChange(params: any) {
    const [beginTime, endTime] = params || ['', ''];
    searchParam.start_time = beginTime;
    searchParam.end_time = endTime;
  }

  const CANCEL_VERIFY_MESSAGE = {
    card: '会籍卡、私/泳教课需要自行手动处理，系统无法自动回收，确定取消?',
    expireCard: '将收回已发放体验卡/课，确定取消？',
    coupon: '将回未使用收折扣券，已使用折扣券无法回收,确定取消？',
  };
  function delPlan(record: Record<string, any>) {
    const { isLoading, execute: executeDel } = cancelVerification();
    let messageType = '';
    if ([1, 6, 7, 8].includes(Number(record.rule_type))) {
      messageType = 'card';
    } else if (Number(record.rule_type) === 5) {
      messageType = 'coupon';
    } else {
      messageType = 'expireCard';
    }

    record.isDelLoading = isLoading;
    Modal.confirm({
      title: '取消核验',
      content: CANCEL_VERIFY_MESSAGE[messageType],
      onOk: () => {
        executeDel({ data: { verify_log_id: record.id } }).then((res: any) => {
          loadTableList();
          Message.success(res.response.value.errormsg);
        });
      },
    });
  }
  const getRowClassName = (record: any) => {
    if (record.disabled) {
      return 'disabled-row';
    }
    return '';
  };
</script>
