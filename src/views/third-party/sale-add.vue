<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
        <a-alert type="warning" style="margin-bottom: 16px">
          会籍卡，私泳教课，套餐包是否要合同签署可以在 “门店端-设置-合同签署” 线上团购配置进行设置。
        </a-alert>
        <a-form-item field="bus_id" label="门店" :rules="{ required: true, message: '请选择' }">
          <BusSelectAdmin v-model="formData.bus_id" />
        </a-form-item>
        <a-form-item label="类型" field="rule_type" :rules="{ required: true, message: '请选择' }">
          <a-select v-model="formData.rule_type" placeholder="请选择" allow-search @change="changeType">
            <a-option v-for="item in MEIPING_EXCHANGE_TYPE" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="formData.rule_type !== '5' && formData.rule_type !== '8'"
          label="商品"
          field="card_id"
          :rules="{ required: true, message: '请选择' }">
          <a-select v-model="formData.card_id" placeholder="请选择" :loading="isGoodsLoading" allow-search>
            <a-option v-for="item in goodsList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-else-if="formData.rule_type === '5'"
          label="商品"
          field="coupon_id"
          :rules="{ required: true, message: '请选择' }">
          <a-select v-model="formData.coupon_id" placeholder="请选择" :loading="isGoodsLoading" allow-search>
            <a-option v-for="item in goodsList" :key="item.coupon_id" :value="item.coupon_id">
              {{ item.coupon_name }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-else-if="formData.rule_type === '8'"
          label="商品"
          field="package_id"
          :rules="{ required: true, message: '请选择' }">
          <a-select v-model="formData.package_id" placeholder="请选择" :loading="isGoodsLoading" allow-search>
            <a-option v-for="item in goodsList" :key="item.id" :value="item.id">
              {{ item.name }}
            </a-option>
          </a-select>
        </a-form-item>

        <!-- <a-form-item
          v-if="['6', '7', '1', '2', '3', '4'].includes(formData.rule_type) && product?.universal_card == 0"
          label="使用门店"
          field="use_pod"
          :rules="{ required: true, message: '请选择' }">
          <a-radio-group v-model="formData.use_pod">
            <a-radio value="0">当前核销门店</a-radio>
            <a-radio value="1" disabled>卡课发放/使用场馆中任一场馆</a-radio>
          </a-radio-group>
        </a-form-item> -->

        <template v-if="['6', '7'].includes(formData.rule_type)">
          <a-form-item label="节数" field="num" :rules="{ required: true, message: '请选择' }">
            <a-input-number v-model="formData.num" :min="1" :max="1000000" :step="1" :precision="0" />
          </a-form-item>
          <a-form-item label="有效期(天)" field="days" :rules="{ required: true, message: '请选择' }">
            <a-input-number v-model="formData.days" :min="1" :max="1000000" :step="1" :precision="0" />
          </a-form-item>
        </template>

        <a-form-item label="核销限制" field="verify_limit" :rules="{ required: true, message: '请选择' }">
          <a-radio-group v-model="formData.verify_limit">
            <a-radio value="1">仅首次购买可核销</a-radio>
            <a-radio value="0">可重复购买核销</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="美团售卖" field="is_show_meituan">
          <a-switch
            v-model="formData.is_show_meituan"
            checked-value="1"
            unchecked-value="0"
            @change="handleTiktokChange('meituan', $event)" />
          <div v-if="formData.meituan_deal_id" class="relative" @click="handleTiktokChange('meituan', '1')">
            关联商品ID {{ formData.meituan_deal_id }}
            <icon-edit class="mei-edit" />
          </div>
        </a-form-item>
        <a-form-item label="大众点评售卖" field="is_show_dianping">
          <a-switch
            v-model="formData.is_show_dianping"
            checked-value="1"
            unchecked-value="0"
            @change="handleTiktokChange('dianping', $event)" />
          <div v-if="formData.dianping_deal_id" class="relative" @click="handleTiktokChange('dianping', '1')">
            关联商品ID {{ formData.dianping_deal_id }}
            <icon-edit class="mei-edit" />
          </div>
        </a-form-item>
        <a-form-item label="抖音售卖" field="is_show_tiktok">
          <a-switch
            v-model="formData.is_show_tiktok"
            checked-value="1"
            unchecked-value="0"
            @change="handleTiktokChange('tiktok', $event)" />
          <div v-if="formData.tiktok_deal_id" class="relative" @click="handleTiktokChange('tiktok', '1')">
            关联商品ID {{ formData.tiktok_deal_id }}
            <icon-edit class="mei-edit" />
          </div>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" :loading="isLoading" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
    <MeipingGoodsModal
      v-if="showCouponModel"
      v-model="showCouponModel"
      :platform-id="platformId"
      :bus-id="formData.bus_id"
      :selected-id="Number(formData[`${platformName}_deal_id`])"
      @confirm="handleConfirmCoupon"
      @cancel="handleCloseCoupon"
      @auth="handleNeedAuth" />
    <MeituanAuth v-model="showMeituanAuth" :meituan-auth-info="meituanAuthInfo" />
  </div>
</template>

<script lang="ts" setup>
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { MEIPING_EXCHANGE_TYPE } from '@/store/constants';
  import { getCardCoupon, saveCardRule, getCardRuleDetail } from '@/api/card-rule';
  import { sanGroupAuth } from '@/api/san-group';
  import { useBusInfoStore } from '@/store';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import MeipingGoodsModal from './components/meiping-goods-modal.vue';
  import MeituanAuth from './components/meituan-auth.vue';

  const IS_BRAND_SITE = (window as any).IS_BRAND_SITE ?? false;

  const busInfo = useBusInfoStore();
  const formRef = ref<FormInstance>();
  const router = useRouter();
  const route = useRoute();
  const { id } = route.params;
  const formData = reactive({
    bus_id: IS_BRAND_SITE ? '' : busInfo.bus_id,
    card_id: '',
    coupon_id: '',
    package_id: '',
    dianping_deal_id: '',
    id: id || '',
    is_show_dianping: '0',
    is_show_meituan: '0',
    is_show_tiktok: '0',
    meituan_deal_id: '',
    tiktok_deal_id: '',
    rule_type: '',
    use_pod: '0',
    num: undefined,
    days: undefined,
    verify_limit: '0',
  });

  watch(
    () => formData.bus_id,
    (newVal, oldVal) => {
      if (newVal !== oldVal) {
        formData.rule_type = '';
        formData.card_id = '';
        formData.coupon_id = '';
        formData.package_id = '';
        formData.num = undefined;
        formData.days = undefined;
      }
    }
  );

  const goodsList = ref<any>([]);
  const isGoodsLoading = ref(false);
  function getGoodsByType(rule_type: string) {
    isGoodsLoading.value = true;
    return getCardCoupon({ rule_type, bus_id: formData.bus_id })
      .then((res) => {
        goodsList.value = res.data.value;
        isGoodsLoading.value = false;
        return res;
      })
      .catch(() => {
        isGoodsLoading.value = false;
        return null;
      });
  }
  function changeType(rule_type: any) {
    formData.card_id = '';
    formData.coupon_id = '';
    formData.package_id = '';
    getGoodsByType(rule_type);
  }

  const product = computed(() => {
    if (formData.rule_type !== '5' && formData.rule_type !== '8') {
      return goodsList.value.find((item: any) => item.id === formData.card_id);
    }
    if (formData.rule_type === '5') {
      return goodsList.value.find((item: any) => item.coupon_id === formData.coupon_id);
    }
    if (formData.rule_type === '8') {
      return goodsList.value.find((item: any) => item.package_id === formData.package_id);
    }
    return {};
  });

  const platformId = ref(1);
  const platformName = ref('美团');
  const meituanAuthInfo = ref({});
  const showMeituanAuth = ref(false);
  function getSaleAuthority() {
    const defaultResponse = {
      is_auth: false,
      url: '',
    };
    if (platformId.value === 3) {
      return defaultResponse;
    }
    return sanGroupAuth({ platform_id: platformId.value, bus_id: formData.bus_id })
      .then((res) => {
        const response = res.response.value;
        if (response.errorcode === 0) {
          return response.data;
        }
        return defaultResponse;
      })
      .catch(() => {
        return defaultResponse;
      });
  }

  function getPlatformName(name: string) {
    if (name === 'dianping') {
      return 2;
    }
    if (name === 'tiktok') {
      return 3;
    }
    return 1;
  }

  const showCouponModel = ref(false);
  async function handleTiktokChange(name, val) {
    platformId.value = getPlatformName(name);
    platformName.value = name;
    if (val === '1') {
      const res = await getSaleAuthority();
      if (!res.is_auth && res.url) {
        // window.open(res.url, '_blank');
        meituanAuthInfo.value = { ...res, platform_id: platformId.value, bus_id: formData.bus_id };
        showMeituanAuth.value = true;
        formData[`is_show_${name}`] = '0';
      } else {
        showCouponModel.value = true;
      }
    } else {
      formData[`${name}_deal_id`] = '';
    }
  }
  function handleConfirmCoupon(deal_id) {
    formData[`${platformName.value}_deal_id`] = deal_id;
  }
  function handleCloseCoupon() {
    if (!formData[`${platformName.value}_deal_id`]) {
      formData[`is_show_${platformName.value}`] = '0';
    }
  }
  async function handleNeedAuth() {
    const res = await getSaleAuthority();
    if (!res.is_auth && res.url) {
      // window.open(res.url, '_blank');
      meituanAuthInfo.value = { ...res, platform_id: platformId.value, bus_id: formData.bus_id };
      showMeituanAuth.value = true;
    }
    showCouponModel.value = false;
    handleCloseCoupon();
  }
  const { isLoading, execute } = saveCardRule();
  async function handleSubmit() {
    const errors = await formRef.value?.validate();
    if (!errors) {
      execute({
        data: formData,
      }).then(() => {
        Message.success('操作成功');
        router.back();
      });
    }
  }
  function getInfo() {
    getCardRuleDetail({
      id,
    }).then((res) => {
      const resData = res.data.value;
      formData.bus_id = String(resData.bus_id);

      resData.days = resData.days === null ? undefined : Number(resData.days);
      resData.num = resData.num === null ? undefined : Number(resData.num);
      resData.rule_type = String(resData.rule_type);

      getGoodsByType(resData.rule_type).then(() => {
        Object.keys(formData).forEach((key) => {
          formData[key] = resData[key];
        });
      });
    });
  }
  onMounted(() => {
    if (id) {
      getInfo();
    }
  });
</script>

<style lang="less" scoped>
  .relative {
    font-size: 14px;
    margin-left: 16px;
  }
  .mei-edit {
    margin-right: 8px;
    color: #52a4ea;
    font-size: 16px;
    cursor: pointer;
  }
</style>
