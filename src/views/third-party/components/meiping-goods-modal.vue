<template>
  <a-modal
    v-model:visible="isShowModal"
    class="meiping-goods-modal"
    :width="1020"
    :on-before-ok="handleOk"
    unmount-on-close
    :body-style="{ padding: '8px' }"
    @cancel="handleCancel">
    <!-- #title -->
    <template #title>
      <div style="text-align: center">
        <p style="font-size: 16px">请选择团购套餐</p>
        <p style="font-size: 12px; margin: 4px 0">需在第三方平台提前创建对应团购套餐</p>
      </div>
    </template>
    <a-table
      v-if="platformId != 3"
      v-bind="tableProps"
      v-model:selectedKeys="selectedIds"
      :row-class="getRowClass"
      v-on="tableEvent">
      <template #columns>
        <a-table-column v-if="platformId !== 3" title="套餐id" data-index="deal_id">
          <template #cell="{ record }">
            <div>
              <a-tooltip v-if="record.not_belong_to">
                <template #content>所选商品不属于当前门店</template>
                <span>{{ record.deal_id }}</span>
              </a-tooltip>
              <span v-else>{{ record.deal_id }}</span>
            </div>
          </template>
        </a-table-column>
        <a-table-column v-if="platformId !== 3" title="团购id" data-index="group_id">
          <template #cell="{ record }">
            <div>
              <a-tooltip v-if="record.not_belong_to">
                <template #content>所选商品不属于当前门店</template>
                <span>{{ record.group_id }}</span>
              </a-tooltip>
              <span v-else>{{ record.group_id }}</span>
            </div>
          </template>
        </a-table-column>
        <a-table-column v-if="platformId === 3" title="商品id" data-index="sku_id">
          <template #cell="{ record }">
            <div>
              <a-tooltip v-if="record.not_belong_to">
                <template #content>所选商品不属于当前门店</template>
                <span style="text-decoration: underline">{{ record.sku_id }}</span>
              </a-tooltip>
              <span v-else>{{ record.sku_id }}</span>
            </div>
          </template>
        </a-table-column>
        <a-table-column title="套餈名祢" data-index="title" :width="160" ellipsis tooltip />
        <a-table-column title="套餐价格" :width="100" data-index="price" />
        <a-table-column title="团购券服务开始时间" :width="160" data-index="begin_time" />
        <a-table-column title="团购券服务结束时间" :width="160" data-index="end_time" />
        <a-table-column title="售卖状态" data-index="status" />
      </template>
    </a-table>
    <div v-else class="tiktok-box">
      <a-table
        v-model:selectedKeys="selectedIds"
        :data="tiktokData"
        :pagination="false"
        :row-key="'deal_id'"
        :row-selection="{ type: 'radio' }"
        :row-class="getRowClass">
        <template #columns>
          <a-table-column title="商品id" data-index="sku_id">
            <template #cell="{ record }">
              <div>
                <a-tooltip v-if="record.not_belong_to">
                  <template #content>所选商品不属于当前门店</template>
                  <span style="text-decoration: underline">{{ record.sku_id }}</span>
                </a-tooltip>
                <span v-else>{{ record.sku_id }}</span>
              </div>
            </template>
          </a-table-column>
          <a-table-column title="套餈名祢" data-index="title" :width="160" ellipsis tooltip />
          <a-table-column title="套餐价格" :width="100" data-index="price" />
          <a-table-column title="团购券服务开始时间" :width="160" data-index="begin_time" />
          <a-table-column title="团购券服务结束时间" :width="160" data-index="end_time" />
          <a-table-column title="售卖状态" data-index="status" />
        </template>
      </a-table>
      <div v-if="hasMoreTable" style="text-align: center; margin-top: 16px">
        <a-button type="primary" @click="loadMoreTable">加载更多</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import useTableProps from '@/hooks/table-props';
  import { getThirdSanList } from '@/api/san-group';

  interface TableRecord {
    not_belong_to: boolean;
    deal_id: number;
    _disabled?: boolean;
    [key: string]: any;
  }

  const props = defineProps<{
    modelValue?: boolean;
    platformId?: number;
    selectedId?: number;
    busId?: number | string;
  }>();
  const emits = defineEmits(['update:modelValue', 'confirm', 'cancel', 'auth']);

  const hasMoreTable = ref(true);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const selectedIds = ref<number[]>([]);
  watchEffect(() => {
    if (props.selectedId) {
      selectedIds.value = [props.selectedId];
    }
  });
  const { tableProps, tableEvent, setSearchParam, loadTableList } = useTableProps(
    getThirdSanList,
    (list) => {
      return list.map((item) => {
        return {
          ...item,
          deal_id: Number(item.deal_id), // 后端返回的 deal_id 可能为字符串
          // eslint-disable-next-line no-underscore-dangle
          disabled: Number(item.deal_id) === props.selectedId ? false : item._disabled,
          not_belong_to: Boolean(item.not_belong_to),
        };
      });
    },
    (res) => {
      hasMoreTable.value = res.data.value?.has_more || false;
      // errormsg:"因shopId非对应商户下门店需重新授权"
      if (res.errorcode === 4010141) {
        emits('auth');
      }
    }
  );

  tableProps.value['row-key'] = 'deal_id';
  tableProps.value['row-selection'] = { type: 'radio' };
  tableProps.value.pagination.pageSize = 50;
  tableProps.value.pagination.showPageSize = false;

  const tiktokData = ref([]);
  const setTiktokData = () => {
    if (props.platformId === 3) {
      tiktokData.value = tiktokData.value.concat(tableProps.value.data);
    }
  };

  watch(
    () => isShowModal.value,
    (val) => {
      if (val) {
        setSearchParam({
          platform_id: props.platformId,
          bus_id: props.busId,
        });
        loadTableList().then(setTiktokData);
      } else {
        selectedIds.value = [];
      }
    },
    { immediate: true }
  );
  const handleOk = async () => {
    emits('confirm', selectedIds.value[0]);
    return true;
  };
  function handleCancel() {
    emits('cancel');
  }

  const getRowClass = (record: TableRecord) => {
    return record.disabled ? 'not-belong-row' : '';
  };

  function loadMoreTable() {
    tableProps.value.pagination.current += 1;
    loadTableList().then(setTiktokData);
  }
</script>

<style lang="less">
  .meiping-goods-modal {
    .arco-table-tr.not-belong-row {
      background-color: var(--color-fill-2) !important;

      .arco-table-td {
        color: var(--color-text-3) !important;
        background-color: var(--color-fill-2) !important;
      }

      &:hover {
        .arco-table-td {
          background-color: var(--color-fill-2) !important;
        }
      }
    }
  }
</style>
