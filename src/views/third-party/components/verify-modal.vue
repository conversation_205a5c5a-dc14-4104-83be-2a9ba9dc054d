<template>
  <a-modal v-model:visible="isShowModal" title="团购核销" :width="720" :on-before-ok="handleConfirm">
    <a-form ref="formRef" :model="postData" auto-label-width>
      <a-form-item field="buy_channel" label="团购渠道" :content-flex="false">
        <a-radio-group v-model="postData.buy_channel" style="width: 100%; display: flex" @change="handleChannelChange">
          <a-radio
            v-for="(item, index) in THIRD_PLATFORM_NAME"
            :key="item.value"
            :value="item.value"
            :style="{ flex: 1, marginRight: index === THIRD_PLATFORM_NAME.length - 1 ? '0' : '20px' }">
            <template #radio="{ checked }">
              <div class="verify-radio-item" :class="{ checked }">
                <img :src="item.img" />
                <div>{{ item.label }}</div>
              </div>
            </template>
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        field="consume_sn"
        label="券码编号"
        :content-flex="false"
        :validate-status="consumeSnStatus"
        :help="consumeSnHelp"
        :rules="{ required: true, message: '请输入' }">
        <a-input
          ref="codeInputRef"
          v-model="postData.consume_sn"
          placeholder="请输入"
          allow-clear
          @input="handleSNChange" />
        <a-spin
          v-if="postData.consume_sn && !consumeSnHelp"
          :loading="isConsumeSnLoading"
          style="width: 100%; margin-top: 16px">
          <a-list v-if="postData.group_title">
            <a-list-item>
              <a-list-item-meta :title="goodsInfo.group_title">
                <template #avatar>
                  <a-avatar shape="square">
                    <img alt="PLATFORM" :src="getPlatformImg(goodsInfo.platform_id)" />
                  </a-avatar>
                </template>
                <template #description>
                  <p>{{ goodsInfo.consume_sn }}</p>
                  <p>{{ goodsInfo.end_time }}</p>
                  <p>{{ goodsInfo.group_price }}元</p>
                  <p>对应系统:{{ goodsInfo.qn_title }}</p>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </a-list>
        </a-spin>
      </a-form-item>

      <!-- 如果兑换的产品 属于 会籍卡、私/泳教课 时 ，新增“开卡激活”的选项，默认勾选中，如果取消选项，则表示核销下卡后卡是未激活状态 需要手动激活 -->
      <a-form-item v-if="[1, 6, 7, 8].includes(Number(goodsInfo.rule_type))" field="active_type" label="开卡激活">
        <a-switch v-model="postData.active_type" :checked-value="1" :unchecked-value="0" checked-text="立即开卡" />
      </a-form-item>

      <a-form-item v-if="isShowModal" field="user_id" label="核销人电话" :rules="{ required: true, message: '请输入' }">
        <UserSearch
          v-model="postData.user_id"
          :has-self-phone="true"
          placeholder="姓名/电话"
          url="Web/SpaceOrder/searchUserList"
          @change="handleUserChange" />
      </a-form-item>
      <a-form-item field="username" label="核销人姓名" :rules="{ required: true, message: '请输入' }">
        <a-input v-model="postData.username" :disabled="disabledName" placeholder="请输入" />
      </a-form-item>
    </a-form>
    <MeituanAuth v-model="showMeituanAuth" :meituan-auth-info="meituanAuthInfo" />
  </a-modal>
</template>

<script lang="ts" setup>
  import debounce from 'lodash/debounce';
  import { Message, Modal } from '@arco-design/web-vue';
  import { THIRD_PLATFORM_NAME } from '@/store/constants';
  import { getVoucher, sanGroupAuth } from '@/api/san-group';
  import { verification } from '@/api/card-rule';
  import UserSearch from '@/components/user/user-search.vue';
  import { goTicketVerify } from '@/utils/router-go';
  import MeituanAuth from './meituan-auth.vue';

  const props = defineProps<{
    modelValue?: boolean;
  }>();
  const codeInputRef = ref();
  const INIT_DATA = {
    consume_sn: '',
    user_id: '',
    username: '',
    phone: '',
    card_rule_id: '',
    buy_channel: '1',
    group_price: '',
    group_title: '',
    active_type: 1,
  };
  const postData = reactive({ ...INIT_DATA });
  const goodsInfo = ref<Record<string, any>>({});
  const meituanAuthInfo = ref({});
  const showMeituanAuth = ref(false);

  const emits = defineEmits(['update:modelValue', 'success']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  function getPlatformImg(platformId: string) {
    return THIRD_PLATFORM_NAME.find((item) => item.value === platformId)?.img;
  }
  const disabledName = computed(() => {
    return postData.user_id !== 'self_id';
  });

  function openWindowByResult(result: any) {
    if (!result.is_auth && result.url) {
      // window.open(result.url, '_blank');
      meituanAuthInfo.value = { ...result, platform_id: postData.buy_channel };
      showMeituanAuth.value = true;
    }
  }
  function getSaleAuthority(): Promise<any> {
    if (postData.buy_channel === '3') {
      return Promise.resolve();
    }
    const defaultResponse = {
      is_auth: false,
      url: '',
    };
    return sanGroupAuth({ platform_id: postData.buy_channel })
      .then((res: any) => {
        const response = res.response.value;
        if (response.errorcode === 0) {
          openWindowByResult(response.data);
          return response.data;
        }
        openWindowByResult(defaultResponse);
        return defaultResponse;
      })
      .catch(() => {
        openWindowByResult(defaultResponse);
        return defaultResponse;
      });
  }

  function resetGoodsInfo() {
    goodsInfo.value = {
      group_price: '',
      group_title: '',
      card_rule_id: '',
    };
    postData.card_rule_id = '';
    postData.group_price = '';
    postData.group_title = '';
  }

  const consumeSnHelp = ref('');
  const isConsumeSnLoading = ref(false);
  function handleChannelChange() {
    if (isConsumeSnLoading.value) {
      Message.error('验证中，请稍后再试');
      return;
    }
    postData.consume_sn = '';
    consumeSnHelp.value = '';
    getSaleAuthority();
  }
  const consumeSnStatus = computed(() => {
    if (consumeSnHelp.value) {
      return 'error';
    }
    return undefined;
  });
  const handleSNChange = debounce(async (search: string) => {
    postData.consume_sn = search.replace(/\s+/g, '');
    if (!postData.consume_sn) {
      resetGoodsInfo();
      consumeSnHelp.value = '请输入';
      return;
    }
    consumeSnHelp.value = '';
    isConsumeSnLoading.value = true;
    getVoucher({
      platform_id: postData.buy_channel,
      code: postData.consume_sn,
      type: 0,
    })
      .then((res) => {
        const resData = res.data.value;
        // 散场票
        if (resData.type === '1') {
          resetGoodsInfo();
          isConsumeSnLoading.value = false;
          Modal.confirm({
            title: '操作提示',
            content: '券码类型属于散场票，将前往“票务核销”进行操作',
            okText: '确定前往',
            cancelText: '取消',
            onOk: () => {
              isShowModal.value = false;
              goTicketVerify({ consume_sn: postData.consume_sn, platform_id: postData.buy_channel });
              postData.consume_sn = '';
            },
            onCancel: () => {
              postData.consume_sn = '';
            },
          });
          return;
        }
        goodsInfo.value = res.data.value;
        postData.group_price = goodsInfo.value.group_price;
        postData.group_title = goodsInfo.value.group_title;
        postData.card_rule_id = goodsInfo.value.card_rule_id;
        isConsumeSnLoading.value = false;
      })
      .catch((err) => {
        resetGoodsInfo();
        consumeSnHelp.value = err.errormsg;
        isConsumeSnLoading.value = false;

        // mock start
        // goodsInfo.value = {
        //   group_price: '502.58',
        //   group_title: 'Handcrafted Fresh Hat',
        //   qn_title: 'Rustic Metal Mouse',
        //   end_time: '2023-10-31 23:59:59',
        //   universal_card: 0,
        //   rule_type: 8,
        //   use_pod: 1,
        //   activation_restriction: 47,
        //   platform_id: '1',
        // };
        // postData.group_price = goodsInfo.value.group_price;
        // postData.group_title = goodsInfo.value.group_title;
        // postData.card_rule_id = goodsInfo.value.card_rule_id;
        // isConsumeSnLoading.value = false;
        // mock end
      });
  }, 400);
  // 选中用户变化
  function handleUserChange(info: Record<string, any> | undefined) {
    postData.user_id = info?.user_id || '';
    postData.phone = info?.phone || '';
    postData.username = info?.username || '';
  }

  const formRef = ref();
  const { execute: verificationCode } = verification();
  async function handleConfirm() {
    try {
      const errors = await formRef.value.validate();
      if (errors || consumeSnHelp.value) return false;
      await verificationCode({ data: postData });
      Message.success('核销成功！');
      formRef.value.resetFields();
      emits('success');
      return true;
    } catch (err) {
      console.error(err);
      return false;
    }
  }
  const keyEnterStr = ref('');

  function handleVerificationKeyDown(e: any) {
    const event = window.event || e;
    const { key } = event;
    if (event.target.localName !== 'input' && event.target.localName !== 'textarea') {
      if (key === 'Enter') {
        codeInputRef.value.focus();
        postData.consume_sn = keyEnterStr.value;
        handleSNChange(keyEnterStr.value);
        keyEnterStr.value = '';
      } else if (/^[0-9a-zA-Z\-_@./#]+$/.test(key)) {
        // 数字、链接中的字符或特殊符号（抖音扫码是链接）
        keyEnterStr.value += key;
      }
    }
  }
  watch(
    () => isShowModal.value,
    (val) => {
      if (val) {
        getSaleAuthority();
        document.addEventListener('keydown', handleVerificationKeyDown);
      } else {
        resetGoodsInfo();
        Object.assign(postData, INIT_DATA);
        consumeSnHelp.value = '';
        document.removeEventListener('keydown', handleVerificationKeyDown);
      }
    }
  );
  onUnmounted(() => {
    document.removeEventListener('keydown', handleVerificationKeyDown);
  });
</script>

<style lang="less" scoped>
  .verify-radio-item {
    width: 100%;
    display: flex;
    align-items: center;
    border: 1px solid @theme-line-color-4;
    padding: 8px;
    border-radius: 4px;
    img {
      width: 40px;
      margin-right: 8px;
    }
    &.checked {
      border-color: @theme-primary;
    }
  }
</style>
