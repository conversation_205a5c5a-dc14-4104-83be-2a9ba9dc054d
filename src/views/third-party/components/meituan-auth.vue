<template>
  <a-modal
    v-model:visible="showModal"
    :ok-text="formData.is_auth_all ? '批量授权' : '单店授权'"
    title="美团门店授权"
    :width="720"
    :on-before-ok="hansleConfirm">
    <div style="margin-bottom: 20px">是否对当前门店进行"美团到店综合业务授权"</div>
    <a-form ref="formRef" :model="formData" auto-label-width>
      <a-form-item field="is_auth_all" label="批量授权" :content-flex="false">
        <a-checkbox v-model="formData.is_auth_all" style="margin-top: 8px"></a-checkbox>
        <template #extra>
          <div>对以下{{ nameList.length }}家门店进行"美团到店综合业务授权"</div>
          <div>
            <span v-for="(item, index) in nameList" v-show="index < 4 || isShowMore" :key="index">
              {{ item.name }}{{ index === nameList.length - 1 ? '' : '、' }}
            </span>
            <a v-if="nameList.length > 4 && !isShowMore" class="show-more" @click="isShowMore = !isShowMore">
              ...【展示全部】
            </a>
          </div>
        </template>
      </a-form-item>
      <a-form-item
        v-if="formData.is_auth_all"
        field="account"
        label="美团开店宝账号"
        :content-flex="false"
        :rules="{ required: true, message: '请输入' }">
        <a-input v-model="formData.account" clearable placeholder="美团开店宝账号" />
        <template #extra>
          <p>商家打开开店宝时，通过账号密码方式登录的账号。注意，不是手机号</p>
        </template>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { Message } from '@arco-design/web-vue';
  import { getNeedAuthShops, authBatch } from '@/api/san-group';

  const props = defineProps({
    modelValue: {
      type: Boolean,
    },
    meituanAuthInfo: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:modelValue', 'success']);

  const formData = reactive({
    account: '',
    is_auth_all: true,
  });
  const nameList = ref([]);
  const isShowMore = ref(false);
  const showModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  async function getNames() {
    const { data } = await getNeedAuthShops({
      platform_id: props.meituanAuthInfo.platform_id,
      bus_id: props.meituanAuthInfo.bus_id,
    });
    nameList.value = data.value;
  }
  watch(
    () => showModal.value,
    (val) => {
      if (val) {
        getNames();
      }
    }
  );

  const formRef = ref();
  async function hansleConfirm() {
    if (formData.is_auth_all) {
      try {
        const errors = await formRef.value.validate();
        if (errors) return false;
        await authBatch({
          ...formData,
          platform_id: props.meituanAuthInfo.platform_id,
          bus_id: props.meituanAuthInfo.bus_id,
        });
        Message.success('操作成功，后台处理中！');
        return true;
      } catch (err) {
        console.error(err);
        return false;
      }
    } else {
      window.open(props.meituanAuthInfo.url, '_blank');
      return true;
    }
  }
</script>

<style lang="less" scoped></style>
