<template>
  <div class="base-box">
    <Breadcrumb />

    <!-- Filter/Search Bar -->
    <a-card class="general-card">
      <a-form
        :model="searchParam"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width>
        <a-row :gutter="16">
          <a-col :flex="1">
            <a-form-item label="日期">
              <a-range-picker v-model="rangeValue" style="width: 300px" @change="onDateRangeChange" />
            </a-form-item>
          </a-col>
          <a-col :flex="1">
            <a-form-item label="状态">
              <a-select v-model="searchParam.device_status" style="width: 300px" allow-clear>
                <a-option value="1">在线</a-option>
                <a-option value="0">离线</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-divider style="height: 32px" direction="vertical" />
          <a-col :flex="'86px'" style="text-align: right">
            <a-form-item>
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <icon-search />
                </template>
                搜索
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- Table -->
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column title="场馆名称" data-index="bus_name" />
          <a-table-column title="设备名称" data-index="device_name" />
          <a-table-column title="设备SN" data-index="device_id" />
          <a-table-column title="检查时间" data-index="check_time" />
          <a-table-column title="设备状态" data-index="device_status">
            <template #cell="{ record }">
              <a-tag :color="record.device_status === '1' ? 'green' : 'red'">
                {{ record.device_status === '1' ? '在线' : '离线' }}
              </a-tag>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import dayjs from 'dayjs';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import useTableProps from '@/hooks/table-props';
  import { getDeviceLogList } from '@/api/defender';

  defineOptions({
    name: 'DefenderRecord',
  });

  const route = useRoute();

  // Device info from route query
  const deviceInfo = reactive({
    device_id: '',
    device_status: '',
  });

  // Date range picker
  // const rangeValue = ref([dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]);
  // Default to today
  const rangeValue = ref([dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]);

  // Table setup
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    getDeviceLogList,
    (list) => {
      return list.map((item) => {
        return {
          ...item,
        };
      });
    }
  );

  // Initialize search parameters
  setSearchParam({
    begin_time: rangeValue.value[0],
    end_time: rangeValue.value[1],
    device_id: deviceInfo.device_id,
    device_status: '',
    page_no: 1,
    page_size: 10,
  });

  // Date range change handler
  const onDateRangeChange = (dates: any) => {
    if (dates && Array.isArray(dates) && dates.length === 2) {
      const [beginDate, endDate] = dates;
      searchParam.begin_time = beginDate;
      searchParam.end_time = endDate;
    }
  };

  // Initialize component
  onMounted(() => {
    // Get device info from route query
    const { query } = route;
    if (query) {
      deviceInfo.device_id = (query.device_id as string) || '';
      deviceInfo.device_status = (query.device_status as string) || '';

      // Set device_id for API call
      searchParam.device_id = deviceInfo.device_id;
    }

    // Load initial data
    loadTableList();
  });
</script>

<style scoped></style>
