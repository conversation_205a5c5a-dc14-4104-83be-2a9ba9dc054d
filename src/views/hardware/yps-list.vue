<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="bus_id" label="场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="device_type" label="器械类型">
                  <a-select v-model="searchParam.device_type" placeholder="请选择" allow-search allow-clear>
                    <a-option value="2">力量器械</a-option>
                    <a-option value="1">有氧器械</a-option>
                    <a-option value="0">其它</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="device_brand" label="器械品牌">
                  <a-input
                    v-model="searchParam.device_brand"
                    placeholder="请输入"
                    allow-clear
                    @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="device_name" label="器械名称">
                  <a-input
                    v-model="searchParam.device_name"
                    placeholder="请输入"
                    allow-clear
                    @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="device_sn" label="SN码">
                  <a-input
                    v-model="searchParam.device_sn"
                    placeholder="请输入"
                    allow-clear
                    @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="$router.push('/hardware/yps-device')">添加器械</a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column title="场馆名称" data-index="bus_name" />
          <a-table-column title="设备类型" data-index="device_type_name" />
          <a-table-column title="器械名称" data-index="device_name" ellipsis tooltip />
          <a-table-column title="品牌" data-index="device_brand" />
          <a-table-column title="型号" data-index="device_model" />
          <a-table-column title="SN码" data-index="device_sn" />
          <a-table-column title="添加日期" data-index="create_time" :width="160" />
          <a-table-column title="备注" data-index="remark" ellipsis tooltip />
          <a-table-column title="启停用" data-index="start">
            <template #cell="{ record }">
              <a-switch
                :model-value="record.start"
                :loading="record.loading"
                checked-value="1"
                unchecked-value="0"
                @change="
                  (e) => {
                    handleStatusChange(e, record);
                  }
                " />
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="handleChange(record)">编辑</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { Modal, Message } from '@arco-design/web-vue';
  import { getBusDeviceList, openOrStop } from '@/api/yps-config';
  import { useBusInfoStore } from '@/store';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import useTableProps from '@/hooks/table-props';

  defineOptions({
    name: 'OpenClass',
  });
  const router = useRouter();
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    getBusDeviceList,
    (list) => {
      return list.map((item) => {
        return {
          ...item,
          create_time: dayjs(item.create_time * 1000).format('YYYY-MM-DD HH:mm'),
          device_type_name: item.device_type === '1' ? '有氧器械' : item.device_type === '2' ? '力量器械' : '其它',
        };
      });
    }
  );
  const busInfo = useBusInfoStore();
  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: busInfo.bus_id,
    device_type: '',
    device_name: '',
    device_brand: '',
    device_sn: '',
  });
  loadTableList();
  // 跳转详情
  const handleChange = (record: Record<string, any>) => {
    router.push(`/hardware/yps-device/${record.id}`);
  };

  async function handleStatusChange(val: string, info) {
    const { start, device_sn } = info;
    const { isLoading, execute: setInfo } = openOrStop();
    info.loading = isLoading;
    try {
      const newStatus = start === '1' ? '0' : '1';
      await setInfo({ data: { start: newStatus, device_sn } });
      info.start = newStatus;
      Message.success('设置成功！');
    } catch (error) {
      console.log(error);
      Message.error('设置失败！');
    }
  }
</script>
