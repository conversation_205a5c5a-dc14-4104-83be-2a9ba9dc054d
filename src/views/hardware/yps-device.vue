<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
        <a-form-item label="设备类型" field="device_type" :rules="{ required: true, message: '请选择' }">
          <a-select v-model="formData.device_type" placeholder="请选择" :disabled="!!id">
            <a-option value="2">力量器械</a-option>
            <a-option value="1">有氧器械</a-option>
            <a-option value="0">其它</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="设备名称" field="device_name" :rules="{ required: true, message: '请输入' }">
          <a-input v-model="formData.device_name" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="设备品牌" field="device_brand" :rules="{ required: true, message: '请输入' }">
          <a-input v-model="formData.device_brand" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="设备型号" field="device_model">
          <a-input v-model="formData.device_model" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="MAC地址" field="device_mac" :rules="{ required: true, message: '请输入' }" :disabled="!!id">
          <a-input v-model="formData.device_mac" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="SN码" field="device_sn" :rules="{ required: true, message: '请输入' }" :disabled="!!id">
          <a-input v-model="formData.device_sn" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="备注" field="remark">
          <a-textarea v-model="formData.remark" placeholder="请输入备注" allow-clear />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" :loading="isLoading" @click="handleSubmit"> 提交 </a-button>
            <a-button type="secondary" @click="$router.back()"> 取消 </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { getYpsDeviceInfo, addYpsDevice, UpdateYpsDevice } from '@/api/yps-config';

  const route = useRoute();
  const { id } = route.params;
  const formRef = ref<FormInstance>();
  const formData = reactive({
    id: id || '',
    device_type: '',
    device_name: '',
    device_brand: '',
    device_model: '',
    device_sn: '',
    device_mac: '',
    remark: '',
  });

  const { isLoading, execute: executeUpdate } = id ? UpdateYpsDevice() : addYpsDevice();
  const router = useRouter();
  async function handleSubmit() {
    const errors = await formRef.value?.validate();
    if (!errors) {
      executeUpdate({
        data: formData,
      }).then(() => {
        Message.success('操作成功');
        router.back();
      });
    } else {
      Message.error('请完善表单信息');
    }
  }

  function getInfo() {
    getYpsDeviceInfo({ id }).then((res) => {
      const info = res.data.value;
      Object.assign(formData, info);
    });
  }

  onMounted(() => {
    if (id) {
      getInfo();
    }
  });
</script>

<style lang="less" scoped></style>
