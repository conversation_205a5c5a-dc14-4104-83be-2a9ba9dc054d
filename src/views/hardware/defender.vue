<template>
  <div class="base-box">
    <Breadcrumb />
    <!-- Filter/Search Bar -->
    <a-card class="general-card">
      <a-form
        :model="searchParam"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width>
        <a-row :gutter="16">
          <a-col :flex="1">
            <a-form-item label="场馆">
              <BusSelectAdmin v-model="searchParam.bus_id" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :flex="1">
            <a-form-item label="设备SN">
              <a-input v-model="searchParam.device_id" placeholder="请输入" style="width: 300px" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :flex="1">
            <a-form-item label="状态">
              <a-select v-model="searchParam.device_status" style="width: 300px" allow-clear>
                <a-option value="1">在线</a-option>
                <a-option value="0">离线</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-divider style="height: 32px" direction="vertical" />
          <a-col :flex="'86px'" style="text-align: right">
            <a-form-item>
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <IconSearch />
                </template>
                搜索
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 报警配置 Button -->
        <a-divider style="margin-top: 0" />
        <a-row style="margin-bottom: 16px">
          <a-col :span="12">
            <a-button type="primary" status="danger" @click="handleAlarmConfig">报警配置</a-button>
          </a-col>
        </a-row>
      </a-form>

      <!-- Table -->
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column title="场馆名称" data-index="bus_name" />
          <a-table-column title="设备名称" data-index="device_name" />
          <a-table-column title="设备SN" data-index="device_id" />
          <a-table-column title="检查时间" data-index="check_time" />
          <a-table-column title="设备状态" data-index="device_status">
            <!-- 状态: 0离线 1在线 -->
            <template #cell="{ record }">
              <a-tag :color="Number(record.device_status) === 1 ? 'green' : 'red'">
                {{ Number(record.device_status) === 1 ? '在线' : '离线' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="报警通知" data-index="alarm_notify">
            <template #cell="{ record }">
              <a-switch
                v-model="record.alarm_notify"
                checked-value="1"
                unchecked-value="0"
                @change="onAlarmNotifyChange(record, $event)" />
            </template>
          </a-table-column>
          <a-table-column title="设备日志">
            <template #cell="{ record }">
              <a-link @click="onViewLog(record)">查看</a-link>
            </template>
          </a-table-column>
        </template>
      </a-table>

      <!-- 报警配置 Dialog -->
      <a-modal
        v-model:visible="showAlarmConfig"
        title="报警配置"
        :footer="false"
        width="800px"
        :closable="true"
        @cancel="showAlarmConfig = false">
        <div style="margin: 32px 0 48px 0">
          <a-row style="margin-bottom: 18px; align-items: center">
            <a-col :span="12" style="text-align: right; font-size: 18px; font-weight: 500">
              企微群机器人Webhook地址：
            </a-col>
            <a-col :span="12">
              <a-switch v-model="alarmConfig.wechat.enabled" style="margin-right: 8px" />
            </a-col>
          </a-row>
          <a-row v-if="alarmConfig.wechat.enabled" style="margin-bottom: 18px; align-items: center">
            <a-col :span="24">
              <a-input v-model="alarmConfig.wechat.webhook" placeholder="请输入Webhook地址" />
            </a-col>
          </a-row>
          <a-row style="margin-bottom: 18px; align-items: center">
            <a-col :span="12" style="text-align: right; font-size: 18px; font-weight: 500">
              钉钉群机器人Webhook地址：
            </a-col>
            <a-col :span="12">
              <a-switch v-model="alarmConfig.dingtalk.enabled" style="margin-right: 8px" />
            </a-col>
          </a-row>
          <a-row v-if="alarmConfig.dingtalk.enabled" style="margin-bottom: 18px; align-items: center">
            <a-col :span="24">
              <a-input v-model="alarmConfig.dingtalk.webhook" placeholder="请输入Webhook地址" />
            </a-col>
          </a-row>
          <div style="display: flex; justify-content: center; gap: 32px; margin-top: 48px">
            <a-button @click="showAlarmConfig = false">取消</a-button>
            <a-button type="primary" status="danger" @click="onAlarmConfigConfirm">确认</a-button>
          </div>
        </div>
      </a-modal>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import { useRouter } from 'vue-router';
  import { useBusInfoStore } from '@/store';
  import useTableProps from '@/hooks/table-props';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { getDefenderList, setDefenderAlarm, getAlarmConfig, saveAlarmConfig } from '@/api/defender';

  defineOptions({
    name: 'Defender',
  });

  const busInfo = useBusInfoStore();
  const router = useRouter();

  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    getDefenderList,
    (list) => {
      return list.map((item) => {
        return {
          ...item,
        };
      });
    }
  );

  setSearchParam({
    bus_id: busInfo.busId,
    device_id: '',
    device_status: '',
    page_no: 1,
    page_size: 10,
  });

  loadTableList();

  const onAlarmNotifyChange = (record: any, value: any) => {
    setDefenderAlarm({
      device_id: record.device_id,
      alarm_notify: value,
    })
      .then(() => {
        busInfo.$message.success('报警通知设置成功');
      })
      .catch(() => {
        busInfo.$message.error('报警通知设置失败');
        // Revert the change if failed
      })
      .finally(() => {
        loadTableList(); // Reload the table to reflect changes
      });
  };

  const onViewLog = (record: any) => {
    router.push({
      name: 'DefenderRecord',
      query: {
        device_id: record.device_id,
        // device_status: record.device_status,
      },
    });
  };

  const showAlarmConfig = ref(false);
  const alarmConfig = reactive({
    wechat: { enabled: false, webhook: '' },
    dingtalk: { enabled: false, webhook: '' },
  });

  const handleAlarmConfig = () => {
    getAlarmConfig({
      bus_id: busInfo.busId,
    }).then((res: any) => {
      alarmConfig.wechat.enabled = !!res.data.value.wx_robot;
      alarmConfig.wechat.webhook = res.data.value.wx_robot || '';
      alarmConfig.dingtalk.enabled = !!res.data.value.dd_robot;
      alarmConfig.dingtalk.webhook = res.data.value.dd_robot || '';
      showAlarmConfig.value = true;
    });
  };

  const onAlarmConfigConfirm = () => {
    saveAlarmConfig({
      bus_id: busInfo.busId,
      wx_robot: alarmConfig.wechat.enabled ? alarmConfig.wechat.webhook : '',
      dd_robot: alarmConfig.dingtalk.enabled ? alarmConfig.dingtalk.webhook : '',
    })
      .then(() => {
        Message.success('报警配置保存成功');
        showAlarmConfig.value = false;
      })
      .catch(() => {
        Message.error('报警配置保存失败');
      })
      .finally(() => {
        loadTableList(); // Reload the table to reflect changes
      });
  };
</script>

<style scoped></style>
