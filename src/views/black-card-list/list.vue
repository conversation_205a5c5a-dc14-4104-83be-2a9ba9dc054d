<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="search" label="会员">
                  <a-input
                    v-model="searchParam.search"
                    placeholder="姓名/电话"
                    allow-clear
                    @press-enter="handleSearchThing" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="status" label="状态">
                  <a-select v-model="searchParam.status" allow-clear placeholder="请选择">
                    <a-option :value="1">禁用</a-option>
                    <a-option :value="2">已恢复正常</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" :loading="isListLoading" @click="handleSearchThing">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="24" style="display: flex; align-items: center; justify-content: space-between">
          <div>
            <a-button type="primary" @click="handleOpenModal">新增禁用</a-button>
            <a-button
              type="primary"
              style="margin-left: 12px"
              :loading="isRestoreLoading"
              @click="handleConfirmRestore">
              批量恢复
            </a-button>
            <a-button type="primary" style="margin-left: 12px" @click="handleOpenImport">导入禁用名单</a-button>
          </div>
          <div>
            <a-button
              style="margin-left: 12px"
              class="arco-btn-link"
              :loading="isExportLoading"
              @click="handleClickExport">
              导出
            </a-button>
          </div>
        </a-col>
      </a-row>
      <a-table
        v-bind="tableProps"
        v-model:selectedKeys="selectedKeys"
        row-key="card_disable_id"
        :row-selection="rowSelection"
        v-on="tableEvent">
        <template #columns>
          <a-table-column title="会员姓名" data-index="username" />
          <a-table-column title="卡课名称" data-index="card_name" />
          <a-table-column title="状态">
            <template #cell="{ record }">
              <a-space>
                {{ record.return_time ? '已恢复正常' : '已禁用' }}
              </a-space>
            </template>
          </a-table-column>
          <a-table-column title="操作账号" data-index="admin_name" />
          <a-table-column title="操作时间" data-index="create_time" />
          <a-table-column title="恢复操作账号" data-index="return_admin_name" />
          <a-table-column title="恢复时间" data-index="return_time" />
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link v-if="!record.return_time" @click="handleConfirmSingleRestore(record)">恢复正常</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <!-- 新增禁用对话窗 -->
    <a-modal
      :visible="visible"
      ok-text="禁用"
      cancel-text="取消"
      unmount-on-close
      mask-closable
      :ok-loading="isDisabledLoading"
      @ok="handleOk"
      @cancel="handleCancel">
      <template #title>新增禁用</template>
      <div>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 4 }"
            :wrapper-col-props="{ span: 20 }"
            label-align="left">
            <a-row :gutter="24">
              <a-col>
                <a-form-item field="search" label="会员">
                  <UserSearchAll v-model="formData.user_id" :bus-id="formData.bus_id" @change="handleUserChange" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col>
                <a-form-item field="search" label="会员卡">
                  <div>
                    <div>仅可对正常、过期、用完状态卡进行操作</div>
                    <div class="user-card-list">
                      <a-checkbox-group v-model="blackCardList">
                        <div v-for="item in cardList" :key="item.card_user_id" class="card-content">
                          <a-checkbox :value="item.card_user_id">
                            <template #checkbox="{ checked }">
                              <a-space
                                align="start"
                                class="custom-checkbox-card"
                                :class="{ 'custom-checkbox-card-checked': checked }">
                                <div className="custom-checkbox-card-mask">
                                  <div className="custom-checkbox-card-mask-dot" />
                                </div>
                                <div style="flex: 1">
                                  <div className="custom-checkbox-card-title">
                                    <div>{{ item.cardname || '' }}</div>
                                    <div>{{ item.status || '' }}</div>
                                  </div>
                                  <div className="custom-checkbox-card-detail">
                                    <div>{{ item.validity || '-' }}</div>
                                    <div>
                                      <span>{{ item.overplus || '0' }}</span>
                                      <span
                                        v-if="
                                          item.is_pt_time_limit_card != 1 &&
                                          (item.card_type_id == 2 || item.card_type_id == 4 || item.card_type_id == 5)
                                        ">
                                        {{ '/' + item.all_num + ' 次' }}
                                      </span>
                                      <span v-else-if="item.is_pt_time_limit_card == 1 || item.card_type_id == 1">
                                        {{ '/' + item.all_days + ' 天' }}
                                      </span>
                                      <span v-else-if="item.card_type_id == 3">{{ '/' + item.all_num + ' 元' }}</span>
                                    </div>
                                  </div>
                                </div>
                              </a-space>
                            </template>
                          </a-checkbox>
                        </div>
                      </a-checkbox-group>
                    </div>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
      </div>
    </a-modal>
    <!-- 导入禁用名单对话窗 -->
    <a-modal
      :visible="importVisible"
      unmount-on-close
      mask-closable
      :ok-loading="isDisabledLoading"
      @cancel="handleCancelImport">
      <template #title>导入禁用名单</template>
      <div class="up-excel">
        <div class="btn-down">
          <a-button class="arco-btn-link" @click="handleDownload">
            <template #icon>
              <icon-download />
            </template>
            下载模板
          </a-button>
        </div>
        <a-upload
          id="uploader"
          ref="upload"
          accept=".xls, .xlsx"
          :show-file-list="false"
          :with-credentials="true"
          :disabled="importDisabled"
          :action="getBaseUrl() + '/Web/MemberCard/import_card_disable'"
          :data="importData"
          :on-before-upload="handleBeforeUpload"
          @success="handleSuccess"
          @error="handleError"></a-upload>
      </div>
      <template #footer><div></div></template>
    </a-modal>
    <!-- 导入二次弹窗 -->
    <a-modal :visible="errorModal" width="500px" unmount-on-close mask-closable @cancel="handleImportCancel">
      <template #title>提示</template>
      <div>数据验证失败, 请下载错误文件</div>
      <template #footer>
        <a-button style="margin-right: 12px" @click="handleImportCancel">取消</a-button>
        <ExportExcel ref="exportExcel">
          <template #default="{ handleExport }">
            <a-button @click="handleModalExport(handleExport as Callback<ExportData>)">确定</a-button>
          </template>
        </ExportExcel>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { getBaseUrl } from '@/config/url';
  import { useBusInfoStore } from '@/store';
  import useTableProps from '@/hooks/table-props';
  import { Callback, ExportData } from '@/types/global';
  import ExportExcel from '@/components/exportExcel.vue';
  import UserSearchAll from '@/components/user/user-search-all.vue';
  import { getBlackCardList, getUserCardList, disabledUserCard, restoreUserCard } from '@/api/black-card-list';

  defineOptions({
    name: 'MerchantBlackCardList',
  });

  // 路由对象
  const router = useRouter();
  // 场馆信息
  const busInfo = useBusInfoStore();

  // 表格相关变量及方法
  const {
    isLoading: isListLoading,
    tableProps,
    tableEvent,
    searchParam,
    handleSearch,
    setSearchParam,
    loadTableList,
  } = useTableProps(getBlackCardList);
  // 设置除分页外的其它属性值
  setSearchParam({
    search: '',
    status: '',
    _export: 0,
  });

  // 列表-多选-选中后的变量
  const selectedKeys = ref([]);
  // 列表请求
  loadTableList().then(() => {
    selectedKeys.value = [];
  });
  // 列表搜索
  const handleSearchThing = () => {
    selectedKeys.value = [];
    handleSearch();
  };

  // 配置table
  const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
  });

  // 新增禁用-弹窗变量
  const visible = ref(false);
  // 打开新增禁用弹窗
  const handleOpenModal = () => {
    visible.value = true;
  };
  // interface-表单
  interface formDataType {
    user_id?: string; // 用户id
    bus_id: string; // 场馆id
  }
  // 新增禁用-表单
  const formData: formDataType = reactive({
    user_id: '',
    bus_id: busInfo.bus_id,
  });

  // 新增禁用-搜索会员-选择会员-会员卡列表
  const cardList = ref<any>([]);
  // 新增禁用-搜索会员-选择会员-会员卡列表-选择想禁用的会员卡
  const blackCardList = ref<any>([]);
  // 新增禁用-搜索会员-选择会员-获取会员卡列表
  const { execute: getCard } = getUserCardList();
  // 新增禁用-搜索会员-选择会员
  const handleUserChange = async (info: Record<string, any> | undefined) => {
    formData.user_id = info?.user_id || '';
    blackCardList.value = [];
    // 获取选择会员的正常卡数据
    if (info?.user_id) {
      const { data } = await getCard({
        data: { user_id: info?.user_id },
      });
      cardList.value = data.value;
    }
  };
  // 取消禁用
  const handleCancel = () => {
    formData.user_id = '';
    blackCardList.value = [];
    cardList.value = [];
    visible.value = false;
  };
  // 禁用
  const { isLoading: isDisabledLoading, execute: disabledCard } = disabledUserCard();
  // 禁用事件
  const handleDisable = async () => {
    const card_user_list = {};
    cardList.value.forEach((item: any) => {
      if (blackCardList.value.indexOf(item.card_user_id) !== -1) {
        card_user_list[blackCardList.value.indexOf(item.card_user_id)] = {
          user_id: formData.user_id,
          card_user_id: item.card_user_id,
        };
      }
    });
    const { response }: any = await disabledCard({
      data: { card_user_list },
    });
    if (response.value.errorcode === 0) {
      Message.success({
        content: response.value.errormsg,
      });
      handleCancel();
      loadTableList().then(() => {
        selectedKeys.value = [];
      });
    } else {
      Message.error({
        content: response.value.errormsg,
      });
    }
  };
  // 确认禁用-弹出确认窗口
  const handleOk = () => {
    if (!formData.user_id) {
      Message.error({
        content: '请检索并选择会员',
      });
      return;
    }
    if (!blackCardList.value.length) {
      Message.error({
        content: '请选择要禁用的会员卡',
      });
      return;
    }

    Modal.confirm({
      title: '提示',
      content: '确定禁用吗？',
      okText: '禁用',
      cancelText: '取消',
      onOk: () => {
        handleDisable();
      },
    });
  };

  // 导出
  const { isLoading: isExportLoading, execute: exportData } = getBlackCardList();
  const handleClickExport = async () => {
    const params = {
      search: searchParam.search,
      status: searchParam.status,
      page: 1,
      page_size: tableProps.value.pagination.total,
      _export: 1,
    };
    const { response }: any = await exportData({ data: { ...params } });
    if (response.value?.errorcode === 0) {
      Message.success('导出任务运行中，请稍后到消息中心下载!');
    } else {
      Message.error(response.value?.errormsg);
    }
  };

  // 恢复
  const { isLoading: isRestoreLoading, execute: restoreCard } = restoreUserCard();
  // 批量恢复事件
  const handleRestore = async () => {
    if (selectedKeys.value.length > 0) {
      const { response }: any = await restoreCard({
        data: { card_disable_ids: selectedKeys.value },
      });
      if (response.value.errorcode === 0) {
        Message.success({
          content: response.value.errormsg,
        });
      } else {
        Message.error({
          content: response.value.errormsg,
        });
      }
      selectedKeys.value = [];
      loadTableList().then(() => {
        selectedKeys.value = [];
      });
    }
  };
  // 确认是否恢复
  const handleConfirmRestore = async () => {
    if (!(selectedKeys.value.length > 0)) {
      Message.error({
        content: '请选择要恢复的会员卡',
      });
    } else {
      Modal.confirm({
        title: '提示',
        content: '确定恢复吗？',
        okText: '恢复',
        cancelText: '取消',
        onOk: () => {
          handleRestore();
        },
      });
    }
  };
  // 恢复事件
  const handleSingleRestore = async (record: any) => {
    if (record && record.card_disable_id) {
      const { response }: any = await restoreCard({
        data: { card_disable_ids: [record.card_disable_id] },
      });
      if (response.value.errorcode === 0) {
        Message.success({
          content: response.value.errormsg,
        });
      } else {
        Message.error({
          content: response.value.errormsg,
        });
      }
      loadTableList().then(() => {
        selectedKeys.value = [];
      });
    }
  };
  // 确认是否恢复
  const handleConfirmSingleRestore = async (record: any) => {
    Modal.confirm({
      title: '提示',
      content: '确定恢复吗？',
      okText: '恢复',
      cancelText: '取消',
      onOk: () => {
        handleSingleRestore(record);
      },
    });
  };

  // 上传数据-控制弹窗显示变量
  const importVisible = ref(false);
  // 上传数据-data
  const importData: any = ref({});
  // 上传数据-上传按钮禁用状态
  const importDisabled: any = ref(false);

  // 下载错误数据-控制弹窗显示变量
  const errorModal: any = ref(false);
  // 下载错误数据-错误数据变量
  const fileData = ref([]);
  // 下载错误数据-导出ref
  const exportExcel = ref<HTMLInputElement | null>(null);

  // 导入禁用名单-打开
  const handleOpenImport = () => {
    importVisible.value = true;
  };
  // 导入禁用名单-取消
  const handleCancelImport = () => {
    importVisible.value = false;
  };

  // 下载模板
  function handleDownload() {
    window.open(`${getBaseUrl()}/Web/Excel/getTpl?type=cardDisable`);
  }
  // 上传文件前
  function handleBeforeUpload(file: any) {
    importDisabled.value = true;
    if (file.name.indexOf('.xlsx') === -1 && file.name.indexOf('.xls') === -1) {
      Message.error({
        content: '请上传xls/xlsx文件!',
      });
      importDisabled.value = false;
      return new Promise((resolve) => {
        resolve(false);
      });
    }
    if (file.size > 100 * 1024) {
      Message.error({
        content: '文件大于100M!无法上传!',
      });
      importDisabled.value = false;
      return new Promise((resolve) => {
        resolve(false);
      });
    }
    return new Promise((resolve) => {
      resolve(true);
    });
  }
  // 上传成功/失败后处理
  const handleUpExcel = () => {
    loadTableList().then(() => {
      importVisible.value = false;
      selectedKeys.value = [];
    });
  };
  // 上传成功
  function handleSuccess(res: any) {
    handleUpExcel();
    importDisabled.value = false;
    const { response } = res;
    if (response.errorcode === 0) {
      if (response.data && response.data.err_data && response.data.err_data.length > 0) {
        // 处理错误数据
        const fileList = response.data.err_data;
        fileList.forEach((item: any, index: number) => {
          item.index = index + 1;
        });
        fileData.value = fileList;
        errorModal.value = true;
      } else {
        Message.success({
          content: response.errormsg,
        });
      }
    } else {
      Message.error({
        content: response.errormsg,
      });
    }
  }
  // 上传异常
  function handleError(res: any) {
    handleUpExcel();
    importDisabled.value = false;
    const { response } = res;
    if (response.errorcode === 0) {
      Message.success({
        content: response.errormsg,
      });
    } else {
      Message.error({
        content: response.errormsg,
      });
    }
  }
  // 下载错误数据的弹窗-取消
  function handleImportCancel() {
    errorModal.value = false;
    fileData.value = [];
  }
  // 下载错误数据的弹窗-确定-下载数据
  const handleModalExport = (cb: Callback<ExportData>) => {
    const list = fileData.value;
    cb({
      filename: '会员卡禁用上传错误文件',
      columns: [
        { title: '序号', dataIndex: 'index' },
        { title: '会员名称', dataIndex: 'username' },
        { title: '手机号', dataIndex: 'phone' },
        { title: '会员卡名称', dataIndex: 'card_name' },
        { title: '实体卡号', dataIndex: 'card_sn' },
        { title: '错误信息', dataIndex: 'error_msg' },
      ],
      data: list,
    });
    handleImportCancel();
  };
</script>

<style scoped lang="less">
  .card-content + .card-content {
    margin-top: 12px;
  }

  .custom-checkbox-card {
    padding: 10px 16px;
    border: 1px solid var(--color-border-2);
    border-radius: 4px;
    width: 405px;
    box-sizing: border-box;

    :deep(.arco-space-item:nth-child(2)) {
      flex: 1;
    }
  }

  .custom-checkbox-card-mask {
    height: 14px;
    width: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    border: 1px solid var(--color-border-2);
    box-sizing: border-box;
  }

  .custom-checkbox-card-mask-dot {
    width: 8px;
    height: 8px;
    border-radius: 2px;
  }

  .custom-checkbox-card-title {
    color: var(--color-text-1);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
  }

  .custom-checkbox-card-detail {
    color: var(--color-text-3);
    font-size: 14px;
    font-weight: normal;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
  }

  .custom-checkbox-card:hover,
  .custom-checkbox-card-checked,
  .custom-checkbox-card:hover .custom-checkbox-card-mask,
  .custom-checkbox-card-checked .custom-checkbox-card-mask {
    border-color: rgb(var(--primary-6));
  }

  .custom-checkbox-card-checked {
    background-color: var(--color-primary-light-1);
  }

  .custom-checkbox-card:hover .custom-checkbox-card-title,
  .custom-checkbox-card-checked .custom-checkbox-card-title,
  .custom-checkbox-card:hover .custom-checkbox-card-detail,
  .custom-checkbox-card-checked .custom-checkbox-card-detail {
    color: rgb(var(--primary-6));
  }

  .custom-checkbox-card-checked .custom-checkbox-card-mask-dot {
    background-color: rgb(var(--primary-6));
  }

  .user-card-list {
    max-height: 370px;
    overflow-y: auto;
    overflow-x: hidden;
    margin-top: 12px;
  }

  .arco-btn-link {
    &.arco-btn,
    :deep(.arco-btn) {
      color: @theme-text-color-while;
      background-color: @theme-link;
      &:hover {
        opacity: 0.85;
      }
    }
  }

  .up-excel {
    width: 100%;
    min-height: 90px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .btn-down {
    margin-bottom: 16px;
  }
  .btn-upload {
    margin: 0 auto;
  }
  .arco-btn-link {
    &.arco-btn,
    :deep(.arco-btn) {
      color: @theme-text-color-while;
      background-color: @theme-link;
      &:hover {
        opacity: 0.85;
      }
    }
  }
</style>
