<template>
  <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
    <div v-if="!isCodeVerify">
      <a-form-item field="username" placeholder="暂无手机号" label="手机号码">
        <a-input v-model="formData.phone" type="text" disabled />
      </a-form-item>
      <a-form-item
        field="code"
        label="验证码"
        :rules="[
          {
            required: true,
            message: '请输入验证码',
          },
        ]">
        <a-input v-model="formData.code" type="text" placeholder="短信验证码">
          <template #suffix>
            <a-link :disabled="codeDisabled" @click="getCode">{{ codeText }}</a-link>
          </template>
        </a-input>
      </a-form-item>
    </div>
    <div v-else>
      <a-form-item field="username" label="用户名">
        {{ formData.username }}
      </a-form-item>
      <a-form-item
        field="password"
        label="新密码"
        :content-flex="false"
        :rules="[
          {
            required: true,
            minLength: 6,
            maxLength: 16,
            message: '请正确填写密码（长度6-16）',
          },
        ]">
        <a-input v-model="formData.password" type="password" placeholder="请填写密码" />
        <div v-if="formData.password">
          <LevelTag :password="formData.password" />
        </div>
      </a-form-item>
      <a-form-item
        field="repassword"
        label="确认密码"
        :rules="[
          {
            required: true,
            validator: validatePass,
          },
        ]"
        :validate-trigger="['blur']">
        <a-input v-model="formData.repassword" type="password" placeholder="请填写重复密码" />
      </a-form-item>
    </div>
    <a-form-item>
      <a-space v-if="!isCodeVerify">
        <a-button type="primary" @click="handleNext">下一步</a-button>
        <a-button type="secondary" @click="handleCancel">取消</a-button>
      </a-space>
      <a-space v-else>
        <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
        <a-button type="secondary" @click="handleCancel">取消</a-button>
      </a-space>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { useBusInfoStore, useAdminInfoStore } from '@/store';
  import { sendSmsToPassword, changePassword, updateAdminPassword, checkPasswordSmsCode } from '@/api/admin';
  import { getBaseUrl } from '@/config/url';
  import LevelTag from './level-tag.vue';
  import useTimeInterval from './hooks/useTimeInterval';

  const router = useRouter();
  const formRef = ref<FormInstance>();
  const props = defineProps({
    accountData: Object,
    from: String,
  });

  const busInfo = useBusInfoStore();
  const adminInfo = useAdminInfoStore();
  const { setTimeInterval, disButton, codeText, codeDisabled } = useTimeInterval();

  const formData = reactive({
    type: 1,
    admin_id: busInfo.admin_id,
    username: busInfo.admin_name,
    code: '',
    phone: adminInfo.phone,
    password: '',
    repassword: '',
  });

  watch(
    () => props.accountData,
    (val) => {
      if (val) {
        formData.phone = val.phone;
        formData.admin_id = val.admin_id;
        formData.username = val.username;
      }
    },
    { immediate: true }
  );

  const isCodeVerify = ref(false);
  const sendSmsSuccess = ref(false);
  function getCode() {
    setTimeInterval();
    sendSmsToPassword({
      admin_id: formData.admin_id,
      phone: formData.phone,
    })
      .then(() => {
        sendSmsSuccess.value = true;
        Message.success({
          content: '发送成功！',
        });
      })
      .catch(() => {
        disButton();
      });
  }

  // 重复密码校验
  const validatePass = (value: string, callback: (arg0?: string) => void) => {
    if (formData.password && !value) {
      callback('请再次输入密码');
    } else if (formData.password && value !== formData.password) {
      callback('两次输入密码不一致!');
    } else {
      callback();
    }
  };

  async function handleNext() {
    if (!sendSmsSuccess.value) return;
    const res = await formRef.value?.validate();
    if (!res) {
      await checkPasswordSmsCode({
        phone: formData.phone,
        admin_id: formData.admin_id,
        code: formData.code,
      });
      isCodeVerify.value = true;
    }
  }
  // 提交修改
  const { isLoading, execute: executeUpdate } = props.from === 'modal' ? updateAdminPassword() : changePassword();
  const url = `${getBaseUrl()}/Web/Public/logout`;
  const closeModal = inject('closeModal');
  // 提交修改
  const handleSubmit = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      const postData = {
        ...formData,
      };
      const { response }: { response: { value: any } } = await executeUpdate({
        data: postData,
      });
      if (response.value?.errorcode === 0) {
        Message.success({
          content: '操作成功！',
        });
        if (props.from === 'modal') {
          closeModal();
          // 弹窗中若是修改的当前用户
          if (postData.admin_id === busInfo.admin_id) {
            window.location.href = url;
          }
        } else {
          window.location.href = url;
        }
      } else {
        Message.error(response.value?.errormsg);
      }
    }
  };

  // 取消
  const handleCancel = () => {
    if (closeModal && props.from === 'modal') {
      closeModal();
    } else {
      router.back();
    }
  };
</script>

<style lang="less" scoped></style>
