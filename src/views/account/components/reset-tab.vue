<template>
  <div class="reset-form-wrapper">
    <a-tabs lazy-load>
      <a-tab-pane key="0" title="密码验证">
        <ResetByPwd :from="from" :account-data="accountData" />
      </a-tab-pane>
      <a-tab-pane key="1" title="手机号验证">
        <ResetByPhone :from="from" :account-data="accountData" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
  import ResetByPwd from './reset-by-pwd.vue';
  import ResetByPhone from './reset-by-phone.vue';

  const props = defineProps({
    accountData: Object,
    from: String,
  });
</script>

<style lang="less" scoped></style>
