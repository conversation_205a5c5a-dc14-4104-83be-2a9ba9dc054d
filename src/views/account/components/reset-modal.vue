<template>
  <a-modal
    v-model:visible="isShowModal"
    class="base-modal"
    title="修改密码"
    :width="720"
    :footer="false"
    :unmount-on-close="true">
    <ResetTab :account-data="accountData" from="modal" />
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { useAdminInfoStore } from '@/store';
  import ResetTab from './reset-tab.vue';

  const props = defineProps<{
    modelValue?: boolean;
    accountData: object;
  }>();

  const emits = defineEmits(['update:modelValue', 'onSuccess']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  provide('closeModal', () => {
    isShowModal.value = false;
  });
  const adminInfo = useAdminInfoStore();
  const postData = reactive({
    role_id: '0',
    name: '',
    check_days: 0,
    node_id: [],
  });
</script>

<style lang="less" scoped></style>
