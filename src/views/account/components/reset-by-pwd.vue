<template>
  <div class="reset-form-wrapper">
    <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
      <a-form-item field="username" label="用户名">
        {{ formData.username }}
      </a-form-item>
      <a-form-item
        field="old_password"
        label="原密码"
        :rules="[
          {
            required: true,
            minLength: 6,
            maxLength: 16,
            message: '密码格式不正确',
          },
        ]">
        <a-input v-model="formData.old_password" type="password" placeholder="请填写密码" />
      </a-form-item>
      <a-form-item
        field="password"
        label="新密码"
        :content-flex="false"
        :rules="[
          {
            required: true,
            minLength: 6,
            maxLength: 16,
            message: '请正确填写密码（长度6-16）',
          },
        ]">
        <a-input v-model="formData.password" type="password" placeholder="请填写密码" />
        <div v-if="formData.password">
          <LevelTag :password="formData.password" />
        </div>
      </a-form-item>
      <a-form-item
        field="repassword"
        label="确认密码"
        :rules="[
          {
            required: true,
            validator: validatePass,
          },
        ]"
        :validate-trigger="['blur']">
        <a-input v-model="formData.repassword" type="password" placeholder="请填写重复密码" />
      </a-form-item>
      <a-form-item>
        <a-space>
          <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
          <a-button type="secondary" @click="handleCancel">取消</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getBaseUrl } from '@/config/url';
  import { useBusInfoStore } from '@/store';
  import { changePassword, updateAdminPassword } from '@/api/admin';
  import LevelTag from './level-tag.vue';

  const router = useRouter();
  const busInfo = useBusInfoStore();
  const formRef = ref<FormInstance>();

  const props = defineProps({
    accountData: Object,
    from: String,
  });

  const formData = reactive({
    type: 0,
    admin_id: busInfo.admin_id,
    username: busInfo.admin_name,
    old_password: '',
    password: '',
    repassword: '',
  });
  watch(
    () => props.accountData,
    (val) => {
      if (val) {
        formData.phone = val.phone;
        formData.admin_id = val.admin_id;
        formData.username = val.username;
      }
    },
    { immediate: true }
  );

  // 重复密码校验
  const validatePass = (value: string, callback: (arg0?: string) => void) => {
    if (formData.password && !value) {
      callback('请再次输入密码');
    } else if (formData.password && value !== formData.password) {
      callback('两次输入密码不一致!');
    } else {
      callback();
    }
  };
  const { isLoading, execute: executeUpdate } = props.from === 'modal' ? updateAdminPassword() : changePassword();
  const url = `${getBaseUrl()}/Web/Public/logout`;
  const closeModal = inject('closeModal');
  // 提交修改
  const handleSubmit = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      const postData = {
        ...formData,
      };
      const { response }: { response: { value: any } } = await executeUpdate({
        data: postData,
      });
      if (response.value?.errorcode === 0) {
        Message.success({
          content: '操作成功！',
        });
        if (props.from === 'modal') {
          closeModal();
          // 弹窗中若是修改的当前用户
          if (postData.admin_id === busInfo.admin_id) {
            window.location.href = url;
          }
        } else {
          window.location.href = url;
        }
      } else {
        Message.error(response.value?.errormsg);
      }
    }
  };

  const handleCancel = () => {
    if (closeModal && props.from === 'modal') {
      closeModal();
    } else {
      router.back();
    }
  };
</script>

<style lang="less" scoped></style>
