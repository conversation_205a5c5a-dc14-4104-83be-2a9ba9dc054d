<template>
  <div class="streng-box">
    <div class="schedule">
      <span class="bg-span p-a"></span>
      <span class="bg-span p-b"></span>
      <span class="bg-span p-c"></span>
      <span v-if="strength >= 1" class="low p-a"></span>
      <span v-if="strength >= 2" class="centre p-b"></span>
      <span v-if="strength >= 3" class="tall p-c"></span>
    </div>
    <div class="schedule-text">
      <span class="low">弱</span>
      <span class="centre">中</span>
      <span class="tall">强</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    password: String,
  });
  const strength = computed(() => {
    const val = props.password || '';
    let lv = 0;
    // 验证是否包含字母
    if (val.match(/[a-z]/g)) {
      lv += 1;
    }
    // 数字
    if (val.match(/[0-9]/g)) {
      lv += 1;
    }
    // 是否包含字母，数字，字符
    if (val.match(/(.[^a-z0-9])/g)) {
      lv += 1;
    }
    if (val.length < 6) {
      lv = 0;
    }
    if (lv > 3) {
      lv = 3;
    }
    return lv;
  });
</script>

<style lang="less" scoped>
  .streng-box {
    width: 100%;
    margin-top: 8px;
  }
  .schedule {
    position: relative;
  }
  .schedule span {
    width: 45px;
    height: 8px;
    border-radius: 2px;
  }
  .schedule .bg-span {
    background-color: #ebeef5;
  }
  .schedule .p-a {
    position: absolute;
    top: 0;
    left: 0;
  }
  .schedule .p-b {
    position: absolute;
    top: 0;
    left: 50px;
  }
  .schedule .p-c {
    position: absolute;
    top: 0;
    left: 100px;
  }
  .schedule .low {
    z-index: 9;
    background: #f56c6c;
  }
  .schedule .centre {
    z-index: 9;
    background: #e6a23c;
  }
  .schedule .tall {
    z-index: 9;
    background: #67c23a;
  }

  .schedule-text {
    padding-top: 10px;
    font-size: 13px;
  }
  .schedule-text span {
    width: 45px;
    height: 20px;
    font-weight: 400px;
    display: inline-block;
    line-height: 20px;
    text-align: center;
    margin-right: 5px;
  }

  .schedule-text .low {
    color: #f56c6c;
  }
  .schedule-text .centre {
    color: #e6a23c;
  }
  .schedule-text .tall {
    color: #67c23a;
  }
</style>
