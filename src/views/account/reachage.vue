<template>
  <div class="base-box">
    <a-card class="general-card">
      <div class="base-tabs-warp">
        <a-tabs v-model:activeKey="activeKey" position="left" type="card-gutter" lazy-load>
          <a-tab-pane key="收费标准" title="收费标准">
            <img class="function-img" :src="`${urlPrefix}${renewVersion}-price.png?t=${time}`" alt="" />
          </a-tab-pane>
          <a-tab-pane key="版本功能" title="版本功能">
            <img class="function-img" :src="`${urlPrefix}${renewVersion}-function.png?t=${time}`" alt="" />
          </a-tab-pane>
          <a-tab-pane key="增值服务" title="增值服务">
            <img class="function-img" :src="`${urlPrefix}${renewVersion}-increase.png?t=${time}`" alt="" />
          </a-tab-pane>
        </a-tabs>
        <div class="renew-phone">
          续费专属热线
          <br />
          4001-6072-66
        </div>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { useAdminInfoStore } from '@/store';

  const urlPrefix = 'https://imagecdn.rocketbird.cn/mainsite-fe/pro-price/';
  const activeKey = ref('收费标准');
  const adminInfoStore = useAdminInfoStore();
  const renewVersion = computed(() => {
    return adminInfoStore.renew_version;
  });
  const time = new Date().getTime();
</script>

<style lang="less" scoped>
  .base-box {
    padding: 0 20px 15px 20px;
    width: 1086px;
    margin: 20px auto;
  }
  .base-tabs-warp {
    position: relative;
    :deep(.arco-tabs) {
      .arco-tabs-content {
        padding: 0 20px;
        height: calc(100vh - 160px);
      }
      .arco-tabs-pane {
        overflow-y: scroll;
      }
    }
  }
  .renew-phone {
    position: absolute;
    left: 20px;
    top: 260px;
    font-size: 22px;
    line-height: 1.5;
    color: rgb(var(--primary-6));
    text-align: center;
  }

  .function-img {
    width: 750px;
  }
</style>
