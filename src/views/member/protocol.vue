<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="time_range" label="时间">
                  <a-range-picker v-model="formModel.time_range" style="width: 100%" @change="handleTimeChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="phone" label="会员手机号">
                  <a-input v-model="formModel.phone" placeholder="请输入" allow-clear @press-enter="search" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 33px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-table
        :loading="isLoading"
        :pagination="pagination"
        :data="renderData"
        :bordered="false"
        :size="size"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange">
        <template #columns>
          <a-table-column title="会员姓名" data-index="username" align="center" />
          <a-table-column title="会员手机号" data-index="phone" align="center" />
          <a-table-column title="协议名称" data-index="agreement_name" align="center">
            <template #cell="{ record }">
              <a-link @click="handleViewProtocol(record)">《{{ record.agreement_name }}》</a-link>
            </template>
          </a-table-column>
          <a-table-column title="签署时间" data-index="create_time" align="center" />
        </template>
      </a-table>
    </a-card>

    <!-- Protocol Content Modal -->
    <a-modal
      v-model:visible="protocolModalVisible"
      :title="`《${currentProtocol?.agreement_name}》`"
      width="80%"
      @cancel="handleCloseModal">
      <div v-if="protocolContentLoading" class="loading-container">
        <a-spin :size="32" />
      </div>
      <!-- eslint-disable-next-line vue/no-v-html -->
      <div v-else-if="protocolContent" class="protocol-content" v-html="protocolContent"></div>
      <div v-else class="empty-content">
        <a-empty description="暂无协议内容" />
      </div>
      <template #footer>
        <div style="text-align: center">
          <a-button @click="handleCloseModal">关闭</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import type { CalendarValue } from '@arco-design/web-vue/es/date-picker/interface';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import dayjs from 'dayjs';
  import Breadcrumb from '@/components/breadcrumb/index.vue';
  import { Pagination } from '@/types/global';
  import { getMemberProtocolList, MemberProtocolType } from '@/api/member-protocol';

  defineOptions({
    name: 'MemberProtocol',
  });

  const startOfWeek = dayjs().startOf('week').add(1, 'day'); // 周一 00:00
  // const endOfWeek = dayjs().endOf('week').add(1, 'day'); // 周日 23:59:59

  const generateFormModel = () => {
    return {
      time_range: [startOfWeek.toDate(), dayjs().toDate()] as CalendarValue[],
      phone: '',
      begin_time: startOfWeek.format('YYYY-MM-DD'),
      end_time: dayjs().format('YYYY-MM-DD'),
    };
  };

  const { isLoading, execute: fetchList } = getMemberProtocolList({});
  const renderData = ref<MemberProtocolType[]>([]);
  const formModel = ref(generateFormModel());
  const basePagination: Pagination = {
    current: 1,
    pageSize: 10,
  };
  const size = ref<'medium'>('medium');
  const pagination = reactive({
    ...basePagination,
    showPageSize: true,
    showTotal: true,
  });

  // Protocol modal state
  const protocolModalVisible = ref(false);
  const currentProtocol = ref<MemberProtocolType | null>(null);
  const protocolContent = ref('');
  const protocolContentLoading = ref(false);

  const fetchData = async () => {
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      phone: formModel.value.phone,
      begin_time: formModel.value.begin_time,
      end_time: formModel.value.end_time,
    };

    const { data } = await fetchList({ data: params });
    if (data.value) {
      renderData.value = data.value.list || [];
      pagination.total = Number(data.value.count || 0);
    }
  };

  const handleTimeChange = (value: (CalendarValue | undefined)[] | undefined) => {
    if (value && value.length === 2 && value[0] && value[1]) {
      // Convert CalendarValue to dayjs for formatting
      const startDate = dayjs(value[0] as any);
      const endDate = dayjs(value[1] as any);
      formModel.value.begin_time = startDate.format('YYYY-MM-DD');
      formModel.value.end_time = endDate.format('YYYY-MM-DD');
    } else {
      formModel.value.begin_time = '';
      formModel.value.end_time = '';
    }
  };

  const search = async () => {
    pagination.current = 1;
    await fetchData();
  };

  const onPageChange = (current: number) => {
    pagination.current = current;
    fetchData();
  };

  const onPageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.current = 1;
    fetchData();
  };

  const handleViewProtocol = async (record: MemberProtocolType) => {
    currentProtocol.value = record;
    protocolModalVisible.value = true;
    protocolContentLoading.value = true;
    protocolContent.value = '';

    try {
      if (record.agreement_url) {
        // Fetch content from the agreement URL
        const response = await fetch(record.agreement_url);
        if (response.ok) {
          const content = await response.text();
          protocolContent.value = content;
        } else {
          protocolContent.value = '<p>无法加载协议内容</p>';
        }
      } else {
        protocolContent.value = '<p>暂无协议内容</p>';
      }
    } catch (error) {
      console.error('Error fetching protocol content:', error);
      protocolContent.value = '<p>加载协议内容时出错</p>';
    } finally {
      protocolContentLoading.value = false;
    }
  };

  const handleCloseModal = () => {
    protocolModalVisible.value = false;
    currentProtocol.value = null;
    protocolContent.value = '';
  };

  // Initialize data
  onMounted(() => {
    fetchData();
  });
</script>

<style scoped lang="less">
  .base-box {
    padding: 0 20px 20px 20px;
  }

  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  .protocol-content {
    max-height: 60vh;
    overflow-y: auto;
    padding: 16px;
    border: 1px solid var(--color-border-2);
    border-radius: 6px;
    background-color: var(--color-bg-1);

    :deep(h1),
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
      margin-top: 16px;
      margin-bottom: 8px;
      font-weight: 600;
    }

    :deep(p) {
      margin-bottom: 12px;
      line-height: 1.6;
    }

    :deep(ul),
    :deep(ol) {
      margin-bottom: 12px;
      padding-left: 24px;
    }

    :deep(li) {
      margin-bottom: 4px;
    }

    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 16px;
    }

    :deep(th),
    :deep(td) {
      border: 1px solid var(--color-border-2);
      padding: 8px 12px;
      text-align: left;
    }

    :deep(th) {
      background-color: var(--color-bg-2);
      font-weight: 600;
    }
  }

  .empty-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
</style>
