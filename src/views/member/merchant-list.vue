<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="search" label="会员">
                  <a-input v-model="formModel.search" placeholder="姓名/电话/身份证" @press-enter="search" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="belong_bus_id" label="所属场馆">
                  <busSelectAdmin v-model="formModel.belong_bus_id" placeholder="全部" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="createdTime" label="入会时间">
                  <a-range-picker style="width: 100%" @change="handleCardTimeChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="user_level" label="客户级别">
                  <a-select v-model="formModel.user_level" placeholder="全部" allow-clear allow-search>
                    <a-option v-for="(value, key) in memberSelectList.user_level" :key="key" :value="key">
                      {{ value }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="createdTime" label="注册时间">
                  <a-range-picker style="width: 100%" @change="handleCreatedTimeChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="user_level" label="会员类型">
                  <a-select v-model="formModel.user_category" placeholder="全部" allow-clear allow-search>
                    <a-option v-for="(value, key) in memberSelectList.user_category" :key="key" :value="key">
                      {{ value }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="16">
                <a-form-item
                  field="product_tag"
                  label="买过产品"
                  :label-col-props="{ span: 3 }"
                  :wrapper-col-props="{ span: 21 }">
                  <a-input-group style="width: 100%">
                    <a-select
                      v-model="formModel.condition_type"
                      style="width: 200px"
                      :trigger-props="{ autoFitPopupMinWidth: true }"
                      @change="handleConditionTypeChange">
                      <a-option v-for="(value, key) in conditionTypeList" :key="key" :value="key">
                        {{ value }}
                      </a-option>
                    </a-select>
                    <a-select
                      v-model="formModel.product_tag"
                      :multiple="formModel.condition_type !== '3'"
                      placeholder="全部类型"
                      allow-clear
                      allow-search>
                      <a-option v-for="(value, key) in memberSelectList.product_tag" :key="key" :value="key">
                        {{ value }}
                      </a-option>
                    </a-select>
                  </a-input-group>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 120px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="goSubMemberList()">高级搜索</a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <export-button :data="formModel" url="/Merchant/MemberList/getList" />
        </a-col>
      </a-row>
      <a-table
        :loading="isLoading"
        :pagination="pagination"
        :data="renderData"
        :bordered="false"
        :size="size"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange">
        <template #columns>
          <a-table-column title="头像" data-index="avatar">
            <template #cell="{ record }">
              <a-avatar :image-url="record.avatar"></a-avatar>
            </template>
          </a-table-column>
          <a-table-column title="姓名" data-index="username" align="center">
            <template #cell="{ record }">
              <a-link
                :href="goSubDetail(record.from_user_id, record.from_bus_id, false)"
                @click.prevent="goSubDetail(record.from_user_id, record.from_bus_id)">
                {{ record.username }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="所属场馆" data-index="bus_list">
            <template #cell="{ record }">
              <a-link @click="handleBusListModal(record.bus_list)">{{ record.bus_list.length }}家</a-link>
            </template>
          </a-table-column>
          <a-table-column title="当前有效产品类型" data-index="valid_tag_list">
            <template #cell="{ record }">
              <a-space :size="4">
                <a-tag v-for="(item, index) in record.valid_tag_list_custom" :key="index" :color="item.color">
                  {{ item.name }}
                </a-tag>
              </a-space>
            </template>
          </a-table-column>
          <a-table-column title="购买过产品类型" data-index="tag_list">
            <template #cell="{ record }">
              <a-space :size="4">
                <a-tag v-for="(item, index) in record.tag_list_custom" :key="index" :color="item.color">
                  {{ item.name }}
                </a-tag>
              </a-space>
            </template>
          </a-table-column>
          <a-table-column title="入会时间" data-index="first_buy_card_t" />
          <a-table-column title="客户级别" data-index="user_level" />
          <a-table-column title="注册时间" data-index="ub_create_time" />
        </template>
      </a-table>
    </a-card>
    <bus-list-modal v-model="isShowBusList" :table-data="curBusList" name-key="bus_name" />
  </div>
</template>

<script lang="ts" setup>
  import type { CalendarValue } from '@arco-design/web-vue/es/date-picker/interface';
  import exportButton from '@/components/form/export-button.vue';
  import { getMerchantMemberList, MemberListType, getMemberSelectConfig } from '@/api/member-list';
  import { Pagination } from '@/types/global';
  import busSelectAdmin from '@/components/bus-select/admin.vue';
  import { goSubDetail, goSubMemberList } from '@/utils/router-go';
  import { isArray } from '@/utils/is';
  import busListModal from '@/components/bus-list-modal/index.vue';

  defineOptions({
    name: 'MemberMerchantList',
  });

  const validColors = {
    私教: 'orange',
    会籍: 'green',
    泳教: 'blue',
  };

  const tagColors = {
    私教: 'orange',
    会籍: 'cyan',
    泳教: 'blue',
    散票: 'purple',
    订场: 'red',
    团操: 'arcoblue',
  };

  const generateFormModel = () => {
    return {
      search: '',
      belong_bus_id: '',
      buy_card_begin_time: '',
      buy_card_end_time: '',
      user_level: '',
      ub_create_begin_time: '',
      ub_create_end_time: '',
      user_category: '',
      condition_type: '1',
      product_tag: [] as string | [],
    };
  };
  const { isLoading, execute: fethList } = getMerchantMemberList();
  const renderData = ref<MemberListType[]>([]);
  const formModel = ref(generateFormModel());
  const basePagination: Pagination = {
    current: 1,
    pageSize: 10,
  };
  const size = ref('medium');
  const pagination = reactive({
    ...basePagination,
    showPageSize: true,
    showTotal: true,
  });

  const isShowBusList = ref(false);
  const curBusList = ref<string[] | Record<string, any>[]>([]);
  function handleBusListModal(list = []) {
    curBusList.value = list.map((item, index) => {
      return {
        index,
        bus_name: item,
      };
    });
    isShowBusList.value = true;
  }
  function handleCardTimeChange(params: (CalendarValue | undefined)[] | undefined) {
    const [beginTime, endTime] = params || ['', ''];
    formModel.value.buy_card_begin_time = beginTime as string;
    formModel.value.buy_card_end_time = endTime as string;
  }
  function handleCreatedTimeChange(params: (CalendarValue | undefined)[] | undefined) {
    const [beginTime, endTime] = params || ['', ''];
    formModel.value.ub_create_begin_time = beginTime as string;
    formModel.value.ub_create_end_time = endTime as string;
  }
  const fetchData = async (params?: Record<string, any>) => {
    try {
      const postData = { ...params };
      if (postData && postData.product_tag) {
        postData.product_tag = isArray(postData.product_tag) ? postData.product_tag.join(',') : postData.product_tag;
      }
      const { data } = await fethList({ data: postData });
      const list = data.value?.list || [];

      // 处理tag颜色
      list.forEach((v) => {
        v.valid_tag_list_custom = Array.isArray(v.valid_tag_list)
          ? v.valid_tag_list.map((k) => ({
              name: k,
              color: validColors[k],
            }))
          : [];

        v.tag_list_custom = Array.isArray(v.tag_list)
          ? v.tag_list.map((k) => ({
              name: k,
              color: tagColors[k],
            }))
          : [];
      });

      renderData.value = data.value?.list || [];
      pagination.total = data.value?.count || 0;
      pagination.current = params?.current || 1;
    } catch (err) {
      renderData.value = [];
    }
  };

  const search = () => {
    pagination.current = 1;
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...formModel.value,
    });
  };
  const onPageChange = (current: number) => {
    pagination.current = current;
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const onPageSizeChange = (pageSize: number) => {
    pagination.current = 1;
    pagination.pageSize = pageSize;
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  fetchData(pagination);

  const conditionTypeList = {
    '1': '满足全部条件',
    '2': '满足任一条件',
    '3': '仅满足单一条件（单选）',
  };
  function handleConditionTypeChange(type: any) {
    if (type === '3' && isArray(formModel.value.product_tag)) {
      formModel.value.product_tag = '';
    } else if (type !== '3' && !isArray(formModel.value.product_tag)) {
      formModel.value.product_tag = [];
    }
  }
  const memberSelectList = reactive({
    product_tag: {},
    user_category: {},
    user_level: {},
  });
  async function getSelectList() {
    const { data } = await getMemberSelectConfig();
    memberSelectList.product_tag = data.value?.product_tag || {};
    memberSelectList.user_category = data.value?.user_category || {};
    memberSelectList.user_level = data.value?.user_level || {};
  }
  getSelectList();
</script>

<style scoped lang="less">
  .base-box {
    padding: 0 20px 20px 20px;
  }

  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
</style>
