<template>
  <div class="wrap-box">
    <div v-show="showLoginLoad" class="loading-box">
      <div class="ani-loading">
        <img v-if="loadingImg" :src="loadingImg" alt="勤鸟运动" class="logo" />
        <img v-else alt="勤鸟运动" :src="getAssetsImg('new-logo.png')" class="logo" />
        <div class="dots animate">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
      </div>
    </div>
    <div v-show="!showLoginLoad" :class="`wrap ${loginUrlType == 1 ? 'wrap1' : loginUrlType == 2 ? 'wrap2' : ''}`">
      <div :class="`base-box ${loginUrlType == 1 ? 'container1' : loginUrlType == 2 ? 'container2' : ''}`">
        <img v-if="loadingImg" :src="loadingImg" alt="勤鸟运动" class="logo" />
        <img v-else :src="LOGINLOGOS" alt="勤鸟运动" class="logo" />
        <LoginForm :url-type="loginUrlType" />
        <div>
          <a :href="shortCut">生成桌面图标</a>
          <i>400-160-7266</i>
        </div>
      </div>
    </div>
    <a :href="browserDownloadUrl" download class="suggest-browser">推荐浏览器下载</a>
  </div>
</template>

<script lang="ts" setup>
  import { getBaseUrl, apis } from '@/config/url';
  import { getAssetsImg } from '@/utils';
  import LoginForm from './components/login-form.vue';

  const showLoginLoad = ref(false); // 展示加载动画
  const loadingImg = ref(''); // 展示加载动画
  const hasVerify = ref(false); // 验证码
  const loginUrlType = ref(0); // 登陆来源 0 vip 默认样式 1 pe 球场 2 yoga 瑜伽馆
  const LOGINLOGOS = getAssetsImg('login-logo1.png');
  const shortCut = `${getBaseUrl()}/Web/Public/create_shortcut`; // 生成桌面图标
  const route = useRoute();
  const { host } = window.location;
  const subDomain = host.split('.')[0];
  if (subDomain === apis[1]) {
    loginUrlType.value = 1;
  } else if (subDomain === apis[2]) {
    loginUrlType.value = 2;
  }
  showLoginLoad.value = route.query.loading === '1';
  loadingImg.value = (route.query.img || '') as string;
  if (window.localStorage.errorCount >= 3) {
    hasVerify.value = true;
  }
  if (showLoginLoad.value) {
    setTimeout(() => {
      showLoginLoad.value = false;
    }, 3300);
  }
  const isMacintosh = navigator.userAgent.match(/Macintosh/i) !== null;
  const browserDownloadUrl = isMacintosh
    ? 'https://imagecdn.rocketbird.cn/chrome.dmg'
    : 'https://imagecdn.rocketbird.cn/chrome.exe';
</script>

<style lang="less" scoped>
  .wrap-box {
    width: 100%;
    height: 100%;
    position: relative;
    .suggest-browser {
      position: absolute;
      bottom: 10px;
      right: 10px;
      font-size: 14px;
    }
  }
  .wrap {
    overflow: hidden;
    color: #1b1b1b;
    width: 100%;
    height: 100%;
    background: url('@/assets/img/login-bg.jpg') no-repeat center top / cover;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Microsoft YaHei', sans-serif;
    position: relative;
    .logo {
      width: 105px;
    }
  }

  .wrap1 {
    background: url('@/assets/img/login-bg1.jpg') no-repeat center top / cover;
  }

  .wrap2 {
    background: url('@/assets/img/login-bg2.jpg') no-repeat center top / cover;
  }

  .base-box {
    // min-height: 400px;
    box-sizing: content-box;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 0 50px 5px rgba(0, 0, 0, 0.2);
    width: 418px;
    padding: 30px 75px 80px;
    > div {
      width: 100%;
      padding-top: 24px;
      white-space: nowrap;
      font-size: 12px;
      display: flex;
      justify-content: space-between;
      a {
        color: #1b1b1b;
        text-decoration: none;
      }
      a:hover {
        color: #f63538;
      }
      i {
        font-style: normal;
        float: right;
        font-family: PingFang, sans-serif;
      }
    }
  }
  .container1 {
    width: 530px;
    background: rgba(255, 255, 255, 0.4);
    border: 0px;
    padding: 60px 100px 70px;
  }
  .container2 {
    margin-left: 40%;
    background: rgba(255, 255, 255, 0.7);
    border: 0px;
  }
  .error-message {
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
    font-size: 16px;
    padding: 60px 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 30px;
    text-align: center;
  }

  .loading-box {
    width: 100%;
    height: 100%;
    background: -webkit-linear-gradient(
      45deg,
      #6fc7b5 0%,
      #13bdce 20%,
      #0094d9 40%,
      #5a3694 60%,
      #ee4d74 80%,
      #fff 100%
    );
    background: linear-gradient(45deg, #6fc7b5 0%, #13bdce 20%, #0094d9 40%, #5a3694 60%, #ee4d74 80%, #fff 100%);
    background-size: 600%;
    background-position: 0% 100%;
    -webkit-animation: gradient 7.5s ease-in-out infinite;
    animation: gradient 7.5s ease-in-out infinite;
    position: absolute;
    left: 0;
  }

  .ani-loading {
    width: 100%;
    height: 100%;
  }
  .ani-loading * {
    position: fixed;
    left: 50%;
    top: 50%;
    -webkit-transform: translate3d(-50%, -50%, 0) rotate(0deg);
    transform: translate3d(-50%, -50%, 0) rotate(0deg);
  }
  .ani-loading .logo {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 180px;
  }
  .ani-loading .dots.animate .dot {
    -webkit-animation: ani-loading-block 2.5s ease-in-out 1;
    animation: ani-loading-block 2.5s ease-in-out 1;
  }
  .ani-loading .dots.animate .dot:after {
    -webkit-animation: ani-loading-dot 2.5s ease-in-out 1;
    animation: ani-loading-dot 2.5s ease-in-out 1;
  }
  .ani-loading .dots .dot {
    width: 300px;
    height: 300px;
  }
  .ani-loading .dots .dot:after {
    content: '';
    display: inline-block;
    width: 60px;
    height: 60px;
    background-color: rgba(255, 255, 255);
    opacity: 0;
    border-radius: 50%;
    position: absolute;
    -webkit-transform: scale(0.17);
    transform: scale(0.17);
  }
  .ani-loading .dots .dot:nth-child(1) {
    top: 119px;
    left: -209px;
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
  }
  .ani-loading .dots .dot:nth-child(1):after {
    -webkit-transform-origin: top right;
    transform-origin: top right;
    top: 0;
    right: 0;
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
  }
  .ani-loading .dots .dot:nth-child(2) {
    top: -161px;
    left: -194px;
    -webkit-animation-delay: 0.25s;
    animation-delay: 0.25s;
  }
  .ani-loading .dots .dot:nth-child(2):after {
    -webkit-transform-origin: bottom right;
    transform-origin: bottom right;
    bottom: 0;
    right: 0;
    -webkit-animation-delay: 0.25s;
    animation-delay: 0.25s;
  }
  .ani-loading .dots .dot:nth-child(3) {
    top: -161px;
    left: -101px;
    -webkit-animation-delay: 0.5s;
    animation-delay: 0.5s;
  }
  .ani-loading .dots .dot:nth-child(3):after {
    -webkit-transform-origin: bottom right;
    transform-origin: bottom right;
    bottom: 0;
    right: 0;
    -webkit-animation-delay: 0.5s;
    animation-delay: 0.5s;
  }
  .ani-loading .dots .dot:nth-child(4) {
    top: 116px;
    left: 200px;
    -webkit-animation-delay: 0.75s;
    animation-delay: 0.75s;
  }
  .ani-loading .dots .dot:nth-child(4):after {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    top: 0;
    left: 0;
    -webkit-animation-delay: 0.75s;
    animation-delay: 0.75s;
  }
  .ani-loading .dots .dot:nth-child(5) {
    top: -161px;
    left: 214px;
    -webkit-animation-delay: 1s;
    animation-delay: 1s;
  }
  .ani-loading .dots .dot:nth-child(5):after {
    -webkit-transform-origin: bottom left;
    transform-origin: bottom left;
    bottom: 0;
    left: 0;
    -webkit-animation-delay: 1s;
    animation-delay: 1s;
  }

  @-webkit-keyframes ani-loading-block {
    100% {
      -webkit-transform: translate3d(-50%, -50%, 0) rotate(360deg);
      transform: translate3d(-50%, -50%, 0) rotate(360deg);
    }
  }

  @keyframes ani-loading-block {
    100% {
      -webkit-transform: translate3d(-50%, -50%, 0) rotate(360deg);
      transform: translate3d(-50%, -50%, 0) rotate(360deg);
    }
  }
  @-webkit-keyframes ani-loading-dot {
    50% {
      -webkit-transform: scale(1);
      transform: scale(1);
      opacity: 0.5;
    }
  }
  @keyframes ani-loading-dot {
    50% {
      -webkit-transform: scale(1);
      transform: scale(1);
      opacity: 0.5;
    }
  }
  @-webkit-keyframes gradient {
    50% {
      background-position: 100% 0%;
    }
  }
  @keyframes gradient {
    50% {
      background-position: 100% 0%;
    }
  }
</style>
