<template>
  <div class="login-form-wrapper" :class="`login-form-wrapper-${urlType}`">
    <div v-if="errorMessage" class="login-form-error-msg">{{ errorMessage }}</div>
    <div v-if="hasBreakErr" class="base-box">
      <p class="break-error-message">
        {{ errorMessage }}
        <span>联系电话: 400-160-7266</span>
      </p>
    </div>
    <a-form
      v-else
      ref="loginForm"
      :model="userInfo"
      class="login-form"
      layout="vertical"
      size="large"
      @submit="handleSubmit">
      <a-form-item
        field="username"
        :rules="[{ required: true, message: '用户名不能为空' }]"
        :validate-trigger="['change', 'blur']"
        hide-label>
        <a-input ref="usernameInput" v-model="userInfo.username" size="large" placeholder="请输入账号">
          <template #prefix>
            <icon-user />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item
        field="password"
        :rules="[{ required: true, message: '密码不能为空' }]"
        :validate-trigger="['change', 'blur']"
        hide-label>
        <a-input-password v-model="userInfo.password" placeholder="请输入密码" size="large" allow-clear>
          <template #prefix>
            <icon-lock />
          </template>
        </a-input-password>
      </a-form-item>
      <a-form-item
        v-if="verifyImg"
        field="vCode"
        :rules="[{ required: true, message: '验证码不能为空' }]"
        :validate-trigger="['change', 'blur']"
        hide-label>
        <a-input v-model="userInfo.vCode" placeholder="请输入验证码" size="large" allow-clear>
          <template #append>
            <div style="width: 130px; height: 36px">
              <img :src="verifyImg" height="36" title="换一张" @click="getVerifyImg" />
            </div>
          </template>
        </a-input>
      </a-form-item>
      <a-button type="primary" status="danger" html-type="submit" size="large" long :loading="isLoading">登录</a-button>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import CryptoJS from 'crypto-js';
  import { getBaseUrl } from '@/config/url';
  import checkLogin from '@/api/public';
  import { REDIRECT_ROUTE_NAME } from '@/router/constants';

  defineProps({
    urlType: {
      type: Number,
      default: 0,
    },
  });
  const { isLoading, execute: fetchLoginData } = checkLogin();
  const route = useRoute();
  const router = useRouter();
  const verifyImg = ref('');
  const errorMessage = ref('');
  const hasBreakErr = ref(false); // 过期或其他提示
  function getVerifyImg() {
    const timestamp = new Date().getTime();
    verifyImg.value = `${getBaseUrl()}/Web/Public/captcha?${timestamp}`;
  }
  // 错误次数超过3次,弹出验证码
  if (window.localStorage.errorCount >= 3) {
    getVerifyImg();
  }
  const usernameInput = ref();
  onMounted(() => {
    usernameInput.value?.focus();
  });
  const userInfo = reactive({
    username: '',
    password: '',
    vCode: '',
  });
  function hasErrCode(err: Record<string, any>) {
    switch (err.errorcode) {
      case 43004:
        getVerifyImg();
        break;
      case 40027:
        window.location.href = err.data.url;
        break;
      case 40033:
        errorMessage.value = err.errormsg;
        if (err.data.error_number >= 3) {
          window.localStorage.errorCount = err.data.error_number;
          getVerifyImg();
        }
        break;
      case 49007:
        hasBreakErr.value = true;
        errorMessage.value = '您好，系统服务已经过期，请联系运营人员进行续费';
        break;
      case 40029:
      case 40030:
      case 40031:
      case 40032:
      default:
        errorMessage.value = err.errormsg;
    }
  }
  function encrypt(word: string) {
    const KEY = 'rocketbird@2017!';
    const IV = 'jSsGUiDSEyG33jV6';
    const key = CryptoJS.enc.Utf8.parse(KEY);
    const iv = CryptoJS.enc.Utf8.parse(IV);
    const secret = CryptoJS.enc.Utf8.parse(word);
    const encrypted = CryptoJS.AES.encrypt(secret, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.ZeroPadding,
    });
    return encrypted.toString();
  }
  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    if (!errors) {
      try {
        await fetchLoginData({
          data: {
            ...values,
            password: encrypt(values.password),
            action: route.query.action ?? '',
          },
        });
        window.localStorage.errorCount = 0;
        sessionStorage.setItem('hasPopupNotice', 'false');
        if (route.query.redirect) {
          router.push(route.query.redirect as string);
        } else {
          router.push({
            name: REDIRECT_ROUTE_NAME,
            params: {
              path: '0',
            },
          });
        }
      } catch (err: any) {
        hasErrCode(err);
      }
    }
  };
</script>

<style lang="less" scoped>
  .break-error-message {
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
    font-size: 16px;
    padding: 60px 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 30px;
    text-align: center;
  }
  .login-form-error-msg {
    color: var(--danger-6);
    line-height: 32px;
  }
  .login-form-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    .invalidate {
      outline: none;
      box-shadow: 0 0 0 1px red;
    }
    .verify-code {
      padding-top: 10px;
      span {
        font-size: 14px;
      }
    }
    .verify {
      flex-direction: row;
      align-items: center;
      input {
        width: 220px;
        height: 40px;
        margin-right: 10px;
      }
      img {
        cursor: pointer;
      }
      a {
        text-decoration: underline;
        padding-left: 20px;
      }
      .verify-err-tag {
        right: 205px;
        bottom: 7px;
      }
    }
    .login-word {
      letter-spacing: 1em;
    }
  }

  .login-form-wrapper-1 {
    .name {
      margin-bottom: 50px;
    }
    .password {
      margin-bottom: 20px;
    }
  }

  .login-form-wrapper-2 {
    .ivu-btn-error {
      background: #986f5c;
      border-color: #986f5c;
    }
    .name {
      margin-bottom: 50px;
    }
    .password {
      margin-bottom: 20px;
    }
  }
</style>
