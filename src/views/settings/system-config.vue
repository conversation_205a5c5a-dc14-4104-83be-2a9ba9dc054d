<template>
  <div class="base-box">
    <Breadcrumb />
    <div class="base-tabs-warp">
      <a-tabs v-model:activeKey="activeKey" position="left" type="card-gutter" lazy-load>
        <a-tab-pane key="数据显示设置" title="数据显示设置">
          <show-data-settings />
        </a-tab-pane>
        <a-tab-pane key="卡课售卖设置" title="卡课售卖设置">
          <card-sale-settings />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.set_business_miss_setting" key="团操课设置" title="团操课设置">
          <team-course-settings />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.update_bus_coach_setting" key="私教课设置" title="私教课设置">
          <pt-set />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.update_bus_swim_coach_setting" key="泳教课设置" title="泳教课设置">
          <pt-set is-swim />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.update_sign_setting" key="签到设置" title="签到设置">
          <sign-set />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.set_sources" key="获客来源设置" title="获客来源设置">
          <sources-set />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.set_sources" key="会籍端设置" title="会籍端设置">
          <membership-track />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.coach_setting_bus" key="教练端设置" title="教练端设置">
          <coach-set />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.member_setting" key="会员端设置" title="会员端设置">
          <member-set />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.approve_get_setting_info" key="合同审批设置" title="合同审批设置">
          <contract-approval-set />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.user_suspend_setting" key="请假设置" title="请假设置">
          <leave-set @go-pay-set="goPaySet" />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.commodity_settlement_setting" key="储值卡设置" title="储值卡设置">
          <prepaid-card-settings />
        </a-tab-pane>
        <a-tab-pane key="积分设置" title="积分设置">
          <number-set />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.space_order_settlement_setting" key="订场设置" title="订场设置">
          <space-order-set />
        </a-tab-pane>
        <a-tab-pane
          v-if="settingAuth.WxpaysubmchConf_save_setting_info"
          key="微信支付特约商户配置"
          title="微信支付特约商户配置">
          <wx-pay-set />
        </a-tab-pane>
        <a-tab-pane key="支付方式设置" title="支付方式设置">
          <pay-type-set />
        </a-tab-pane>
        <a-tab-pane key="电子发票设置" title="电子发票设置">
          <electronic-invoice-set />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.san_setting" key="散场票设置" title="散场票设置">
          <san-set />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.third_party_setting" key="三方平台对接设置" title="三方平台对接设置">
          <third-party-set />
        </a-tab-pane>
        <a-tab-pane v-if="settingAuth.contract_setting" key="合同签署设置" title="合同签署设置">
          <contract-sign-set />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { checkAdminBusSetting } from '@/api/business';
  import CardSaleSettings from './components/card-sale-settings.vue';
  import TeamCourseSettings from './components/team-course-settings.vue';
  import PtSet from './components/pt-set.vue';
  import SignSet from './components/sign-set.vue';
  import SourcesSet from './components/sources-set.vue';
  import MembershipTrack from './components/membership-track.vue';
  import CoachSet from './components/coach-set.vue';
  import LeaveSet from './components/leave-set.vue';
  import PrepaidCardSettings from './components/prepaid-card-settings.vue';
  import SpaceOrderSet from './components/space-order-set.vue';
  import NumberSet from './components/number-set.vue';
  import WxPaySet from './components/wx-pay-set.vue';
  import ElectronicInvoiceSet from './components/electronic-invoice-set.vue';
  import SanSet from './components/san-set.vue';
  import ThirdPartySet from './components/third-party-set.vue';
  import PayTypeSet from './components/pay-type-set.vue';
  import ContractSignSet from './components/contract-sign-set.vue';
  import ContractApprovalSet from './components/contract-approval-set.vue';
  import MemberSet from './components/member-set.vue';
  import ShowDataSettings from './components/show-data-settings.vue';

  const activeKey = ref('数据显示设置');
  const settingAuth = reactive<Record<string, boolean>>({});
  function getAuthority() {
    checkAdminBusSetting().then((res) => {
      Object.assign(settingAuth, res.data.value);
    });
  }
  function goPaySet() {
    activeKey.value = '支付方式设置';
  }
  getAuthority();
</script>

<style lang="less">
  .base-set-box {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .base-set-content {
    flex-grow: 1;
    overflow: auto;
  }
  .base-set-btns {
    height: 100px;
    display: flex;
    align-items: center;
    padding-right: 20px;
  }
</style>

<style lang="less" scoped>
  .base-box {
    padding: 0 20px 15px 20px;
    height: 100%;

    .base-tabs-warp {
      height: calc(100vh - 137px);

      :deep(.arco-tabs) {
        .arco-tabs-tab {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 0;
          margin-bottom: 4px;
          width: 156px;
          height: 35px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #e2e5e9;
          text-align: center;
          &.arco-tabs-tab-active {
            box-shadow: 0px 4px 6px 0px rgba(255, 35, 81, 0.15);
            background-color: #ffedf1;
            border: 2px solid #ff2351;
          }
        }
      }
    }
  }
</style>
