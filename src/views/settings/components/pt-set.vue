<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item :label="`预约${isSwimWord}课`">
          <a-select v-model="postData.card_reservation" @change="handleCardReservationChange">
            <a-option :value="1">无有效会籍卡，不能预约{{ isSwimWord }}课</a-option>
            <a-option :value="0">无有效会籍卡，可以预约{{ isSwimWord }}课</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="预约单节付费课">
          <a-radio-group v-model="postData.pt_charge_member_limit">
            <a-radio :value="0">无有效会籍卡不可约</a-radio>
            <a-radio :value="1" :disabled="noneMemberDisabled">无有效会籍卡可约</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="预约教练">
          <a-radio-group v-model="postData.member_reservation_class">
            <a-radio :value="0">所有授课教练</a-radio>
            <a-radio :value="1">仅指定教练</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="最大预约时间">
          <a-row align="center" style="width: 100%">
            <a-col flex="140px">最早可提前预约天数</a-col>
            <a-col flex="auto">
              <a-select v-model="postData.reservation_prior_days" style="width: 100%">
                <a-option v-for="i in 5" :key="i" :value="i">{{ i }}</a-option>
              </a-select>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item label="每天最早可约时间">
          <a-time-picker v-model="postData.schedule_begin_time" style="width: 100%" format="HH:mm"></a-time-picker>
          <template #extra>第二天及以后的课程开始生效，当天课程全天可约</template>
        </a-form-item>
        <a-form-item :content-flex="false" label="预约/取消">
          <a-form-item hide-label>
            <a-col flex="60px">开课前</a-col>
            <a-col flex="auto">
              <a-input-number
                v-model="postData.reservation_prior_minute"
                :min="1"
                :precision="0"
                style="width: 100%"></a-input-number>
            </a-col>
            <a-col flex="100px" align="center">分钟停止预约</a-col>
          </a-form-item>
          <a-form-item hide-label>
            <a-col flex="60px">开课前</a-col>
            <a-col flex="auto">
              <a-input-number
                v-model="postData.stop_prior_minute"
                :min="1"
                :precision="0"
                style="width: 100%"
                @change="changeStopPriorMinute"></a-input-number>
            </a-col>
            <a-col flex="100px" align="center">分钟停止取消</a-col>
          </a-form-item>
        </a-form-item>
        <a-form-item field="cancel_pt_refund_ladder">
          <template #label>
            <a-space>
              <div>退款规则</div>
              <a-tooltip content="退款规则仅适用于“单节付费课”">
                <icon-question-circle />
              </a-tooltip>
            </a-space>
          </template>
          <a-space direction="vertical">
            <a-space v-for="(item, index) in refundLadderList" :key="index">
              <div style="margin-bottom: 20px">开课前</div>
              <a-input-number
                v-model="refundLadderList[index].start"
                :min="index === 0 ? 1 : refundLadderList[index - 1].end + 1"
                :max="index === 0 ? 1 : refundLadderList[index - 1].end + 1"
                :precision="0"
                style="width: 100px; margin-bottom: 20px"
                disabled></a-input-number>
              <div style="margin-bottom: 20px">~</div>
              <a-form-item
                hide-label
                :field="`cancel_pt_refund_ladder[${index}].end`"
                :rules="{ validator: validateRefundLadder(index) }">
                <a-input-number
                  v-model="refundLadderList[index].end"
                  :min="item.start + 1"
                  :max="index === refundLadderList.length - 1 ? Infinity : maxMinutes"
                  :precision="0"
                  style="width: 100px"></a-input-number>
              </a-form-item>
              <div style="margin-bottom: 20px">分钟, 退款比例</div>
              <a-form-item
                hide-label
                :field="`cancel_pt_refund_ladder[${index}].rate`"
                :rules="{ validator: validateRate(index) }">
                <a-input-number
                  v-model="refundLadderList[index].rate"
                  :min="refundLadderList[index - 1]?.rate || 0.01"
                  :max="100"
                  :step="0.01"
                  :precision="2"
                  style="width: 200px">
                  <template #suffix>%</template>
                </a-input-number>
              </a-form-item>
              <a-button
                v-if="index + 1 === refundLadderList.length && index !== 0"
                type="text"
                style="margin-top: -20px"
                @click="handleDeleteRefundLadder(index)">
                <template #icon>
                  <icon-delete style="color: red" />
                </template>
              </a-button>
            </a-space>
            <a-button :disabled="!canAddRefundLadder" type="outline" @click="handleAddRefundLadder">
              <template #icon>
                <icon-plus />
              </template>
              添加退款阶梯
            </a-button>
          </a-space>
        </a-form-item>
        <a-form-item label="会员爽约">
          <a-select v-model="missToll" style="width: 100%" @change="handleMissTollChange">
            <a-option :value="0">爽约不扣除{{ isSwimWord }}费用</a-option>
            <a-option :value="1">爽约扣除{{ isSwimWord }}费用 并将费用计入教练课时</a-option>
            <a-option :value="2">爽约扣除{{ isSwimWord }}费用 但费用不计入教练课时</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="教练上限">
          <a-row align="center" style="width: 100%">
            <a-col flex="140px">每日预约上限节数</a-col>
            <a-col flex="auto">
              <a-input-number
                v-model="postData.coach_reservation_max"
                :min="1"
                :precision="0"
                style="width: 100%"></a-input-number>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item label="课程上限">
          <a-row align="center" style="width: 100%">
            <a-col flex="210px">每种课程同一会员每日可约节数</a-col>
            <a-col flex="auto">
              <a-input-number
                v-model="postData.reservation_class_num"
                :min="1"
                :precision="0"
                style="width: 100%"></a-input-number>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item label="预约间隔">
          <a-radio-group v-model="postData.schedule_time_type">
            <a-radio :value="1">10分钟</a-radio>
            <a-radio :value="2">15分钟</a-radio>
            <a-radio :value="3">20分钟</a-radio>
            <a-radio :value="0">30分钟</a-radio>
            <a-radio :value="4">60分钟</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          label="工作时间"
          field="coach_working_hours"
          :rules="[{ required: true, validator: timeValidator }]">
          <a-space direction="vertical" style="width: 100%" fill>
            <a-row v-for="(item, index) in postData.coach_working_hours" :key="index" align="center">
              <a-col flex="auto">
                <a-time-picker
                  v-model="postData.coach_working_hours[index]"
                  type="time-range"
                  disable-confirm
                  style="width: 100%"
                  :step="{
                    minute: 10,
                  }"
                  format="HH:mm" />
              </a-col>
              <a-col v-if="isEdit && postData.coach_working_hours.length > 1" flex="70px">
                <a-button :style="{ marginLeft: '8px' }" @click="handleDeleteListClick(index)">删除</a-button>
              </a-col>
            </a-row>
            <a-button v-if="isEdit" type="outline" @click="handleAddListClick">+新增工作时间</a-button>
          </a-space>
        </a-form-item>
        <a-form-item label="预约通知">
          <a-switch v-model="postData.user_phone_hide" :checked-value="1" :unchecked-value="0" />
          预约成功信息推送中隐藏会员的电话号码
        </a-form-item>
        <a-form-item label="暂停约课">
          <a-switch v-model="postData.user_suspend_reservation_class" :checked-value="1" :unchecked-value="0" />
          暂停所有教练约课（暂停期间会员无法在会员端预约{{ isSwimWord }}课）
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getBusCoachSetting, updateBusCoachSetting, updateBusSwimCoachSetting } from '@/api/business';

  const props = defineProps({
    isSwim: {
      type: Boolean,
      default: false,
    },
  });
  const isSwimWord = props.isSwim ? '泳教' : '私教';
  const postData = reactive<any>({
    is_swim: props.isSwim ? 1 : 0,
    card_reservation: 0,
    member_reservation_class: 0,
    reservation_prior_days: 3,
    schedule_begin_time: '00:00',
    reservation_prior_minute: 30,
    stop_prior_minute: 30,
    is_miss_toll: 0,
    is_miss_toll_private: 0,
    coach_reservation_max: 5,
    reservation_class_num: 2,
    schedule_time_type: 0,
    user_phone_hide: 1,
    user_suspend_reservation_class: 0,
    coach_working_hours: [],
    pt_charge_member_limit: 0,
    cancel_pt_refund_ladder: [],
  });

  // refund ladder
  const maxMinutes = ref(10082);
  const refundLadderList = ref<any[]>([]);
  const handleAddRefundLadder = () => {
    const last = refundLadderList.value[refundLadderList.value.length - 1];
    if (last.end) {
      refundLadderList.value.push({
        start: last.end + 1,
        end: null,
        rate: null,
      });
    }
  };
  const handleDeleteRefundLadder = (index: number) => {
    refundLadderList.value.splice(index, 1);
  };
  const noneMemberDisabled = ref(false);
  const handleCardReservationChange = (value: any) => {
    // if (!postData.is_swim) {
    if (value === 1) {
      postData.pt_charge_member_limit = 0;
      noneMemberDisabled.value = true;
    } else {
      postData.pt_charge_member_limit = 0;
      noneMemberDisabled.value = false;
    }
    // }
  };
  const canAddRefundLadder = computed(() => {
    if (refundLadderList.value.length) {
      const last = refundLadderList.value[refundLadderList.value.length - 1];
      // const hasEnd = !!last.end;
      const hasRate = !!last.rate;
      const moreThan10080 = last.end >= maxMinutes.value;
      return hasRate && !moreThan10080;
    }
    return false;
  });

  const missToll = ref(0);
  function handleMissTollChange(value: any) {
    if (value === 0) {
      postData.is_miss_toll = 0;
    } else if (value === 1) {
      postData.is_miss_toll = 1;
      postData.is_miss_toll_private = 0;
    } else if (value === 2) {
      postData.is_miss_toll = 1;
      postData.is_miss_toll_private = 1;
    }
  }
  function handleDeleteListClick(index: number) {
    postData.coach_working_hours.splice(index, 1);
  }

  function timeValidator(value: string, callback: (arg0?: string) => void) {
    for (let index = 0; index < postData.coach_working_hours.length; index += 1) {
      const item = postData.coach_working_hours[index];
      if (!item || !item[0] || !item[1]) {
        callback('请选择时间');
        break;
      }
      if (index !== 0 && dayjs(item[0], 'HH:mm').isBefore(dayjs(postData.coach_working_hours[index - 1][1], 'HH:mm'))) {
        callback('开始时间不能早于上一阶段的结束时间!');
        break;
      }
    }
    callback();
  }

  const validateRefundLadder = (index: number) => {
    return (value: any, callback: any) => {
      const len = refundLadderList.value.length;
      const item = refundLadderList.value[index];
      const max = maxMinutes.value - (len - (index + 1)) * 2;

      if (!item.end && index !== len - 1) {
        callback('请输入结束时间');
      }

      if (item.end > max && index !== len - 1) {
        callback(`最大值为${max}分钟`);
      }

      callback();
    };
  };
  const validateRate = (index: number) => {
    return (value: any, callback: any) => {
      const item = refundLadderList.value[index];
      if (!item.rate) {
        callback('请输入退款比例');
      }

      callback();
    };
  };

  function setTimeByIndex(index: number) {
    const hourNow = dayjs().hour();
    const minNow = dayjs().minute();
    const time = `${hourNow}:${minNow}`;
    if (!index) {
      postData.coach_working_hours.push([time, time]);
    } else {
      const lastTime = postData.coach_working_hours[index - 1];
      if (!lastTime || !lastTime[1]) {
        Message.error('请先填写上一个工作时间!');
        return;
      }
      const nextTime = dayjs(lastTime[1], 'HH:mm').add(1, 'h');
      const trueNextTime = nextTime > dayjs('23:59', 'HH:mm') ? '23:59' : nextTime.format('HH:mm');
      postData.coach_working_hours[index] = [lastTime[1], trueNextTime];
    }
  }
  function handleAddListClick() {
    if (postData.coach_working_hours.length < 5) {
      setTimeByIndex(postData.coach_working_hours.length);
    } else {
      Message.error('最多设置5条!');
    }
  }

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = props.isSwim ? updateBusSwimCoachSetting() : updateBusCoachSetting();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      postData.cancel_pt_refund_ladder = refundLadderList.value.map((item) => {
        return {
          start: item.start,
          end: item.end,
          rate: item.rate / 100,
        };
      });
      setInfo({
        data: postData,
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getBusCoachSetting({
      is_swim: props.isSwim ? 1 : 0,
    });
    const resData = res.data.value.info;
    if (resData.is_miss_toll === '0') {
      missToll.value = 0;
    } else if (resData.is_miss_toll === '1') {
      if (resData.is_miss_toll_private === '1') {
        missToll.value = 2;
      } else if (resData.is_miss_toll_private === '0') {
        missToll.value = 1;
      }
    }
    const shouldStringKeys = ['schedule_begin_time'];
    Object.entries(resData).forEach(([key, val]) => {
      if (typeof val === 'string' && val) {
        postData[key] = shouldStringKeys.includes(key) ? val : Number(val);
      }
    });
    postData.coach_working_hours = resData.coach_working_hours?.length ? resData.coach_working_hours : [];

    if (Array.isArray(resData.cancel_pt_refund_ladder) && resData.cancel_pt_refund_ladder.length) {
      refundLadderList.value = resData.cancel_pt_refund_ladder.map((item: any) => {
        return {
          start: item.start ? Number(item.start) : null,
          end: item.end ? Number(item.end) : null,
          rate: item.rate ? Number(item.rate) * 100 : null,
        };
      });
    } else {
      refundLadderList.value = [
        {
          start: postData.stop_prior_minute,
          end: null,
          rate: null,
        },
      ];
    }

    noneMemberDisabled.value = postData.card_reservation === 1;
  }

  const changeStopPriorMinute = () => {
    const first = refundLadderList.value[0];
    if (first) {
      first.start = postData.stop_prior_minute;
    }
  };

  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
