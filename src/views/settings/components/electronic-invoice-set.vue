<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="发票类型">
          <a-radio-group v-model="postData.bill_type">
            <a-radio :value="1">仅可开普票</a-radio>
            <a-radio :value="2">可开专票与普票</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="开票人">
          <a-checkbox v-model="postData.is_edit">允许修改开票人</a-checkbox>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getSetting, addSetting } from '@/api/invoice';

  const postData = reactive({
    bill_type: 2,
    is_edit: false,
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = addSetting();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      setInfo({
        data: {
          bill_type: postData.bill_type,
          is_edit: postData.is_edit ? 1 : 0,
        },
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getSetting();
    const resData = res.data.value;
    postData.bill_type = Number(resData.bill_type);
    postData.is_edit = resData.is_edit === '1';
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
