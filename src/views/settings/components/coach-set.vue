<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="排课有效天数">
          <a-input-number v-model="postData.no_operate_day" :precision="0" style="width: 100%"></a-input-number>
          <template #extra>
            <span>排课后，一直未上课，超过上课时间的天数后自动删除，默认为0天表示不删除。</span>
          </template>
        </a-form-item>
        <a-form-item label="非私教会员跟进">
          <span>跟进有效天数</span>
          <a-col flex="auto" style="margin-left: 16px">
            <a-input-number
              v-model="postData.coach_followup_unconverted"
              :precision="0"
              style="width: 100%"></a-input-number>
          </a-col>
        </a-form-item>
        <a-form-item label="私教会员跟进">
          <span>跟进有效天数</span>
          <a-col flex="auto" style="margin-left: 16px">
            <a-input-number
              v-model="postData.coach_followup_converted"
              :precision="0"
              style="width: 100%"></a-input-number>
          </a-col>
          <template #extra>
            <span>教练多少天未跟进名下非私教会员/ 会员，将调入公海；默认0天，0表示不启用。</span>
          </template>
        </a-form-item>
        <a-form-item label="会员归属">
          <a-select v-model="postData.coach_followup_change" style="width: 100%">
            <a-option :value="1">教练跟进期内，非本人成单，将会员调入公海</a-option>
            <a-option :value="0">教练跟进期内，非本人成单，保持原有跟进教练不变</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="教练端排课">
          <a-radio-group v-model="postData.is_set_coach_reservation_user">
            <a-radio :value="1">教练端允许排课</a-radio>
            <a-radio :value="0">教练不可排课</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="取消预约">
          <a-radio-group v-model="postData.coach_cancel_pt_auth">
            <a-radio :value="0">教练可以取消排课</a-radio>
            <a-radio :value="1">教练不可取消排课</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="教练开单">
          <a-switch v-model="postData.coach_billed" :checked-value="1" :unchecked-value="0" />
        </a-form-item>
        <a-form-item label="开单限制">
          <a-checkbox v-model="postData.can_edit_fields">可修改售价、赠送、有效期等</a-checkbox>
          <a-checkbox v-model="postData.can_edit_create_time">可修改开卡时间</a-checkbox>
        </a-form-item>
        <a-form-item label="开单核查">
          <a-switch v-model="postData.coach_audit" :checked-value="1" :unchecked-value="0" />
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getCoachSettingBus, coachSettingBus } from '@/api/set-client';

  const postData = reactive({
    no_operate_day: 0,
    coach_followup_unconverted: 0,
    coach_followup_converted: 0,
    coach_followup_change: 0,
    coach_cancel_pt_auth: 0,
    is_set_coach_reservation_user: 0,
    coach_billed: 0,
    can_edit_create_time: false,
    can_edit_fields: false,
    coach_audit: 0,
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = coachSettingBus();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      setInfo({
        data: {
          ...postData,
          can_edit_create_time: postData.can_edit_create_time ? 1 : 0,
          can_edit_fields: postData.can_edit_fields ? 1 : 0,
        },
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getCoachSettingBus();
    const resData = res.data.value;
    const booleanKeys = ['can_edit_create_time', 'can_edit_fields'];
    Object.entries(resData).forEach(([key, val]) => {
      if (typeof val === 'string' && val && !booleanKeys.includes(key)) {
        postData[key] = Number(val);
      }
      if (booleanKeys.includes(key)) {
        postData[key] = val === '1';
      }
    });
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
