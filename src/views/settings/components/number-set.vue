<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="积分兑换确认">
          <a-radio-group v-model="postData.point_confirm" @change="changePoint">
            <a-radio :value="1">需要会员短信验证码确认</a-radio>
            <a-radio :value="0">不需要确认</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="积分清除" field="date" :rules="[{ required: true, message: '请选择日期' }]">
          <a-date-picker
            v-model="postData.date"
            :disabled-date="(current) => dayjs(current).isAfter(dayjs())"
            format="YYYY-MM-DD" />
          <template #extra>清除所选日期及以前产生且未消耗的消费积分</template>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" :loading="pointCleanupLoading" @click="clearPoint">清除</a-button>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Modal, Message } from '@arco-design/web-vue';
  import { getPintSetting, upPintSetting } from '@/api/point-setting';
  import { pointCleanup } from '@/api/point';

  const postData = reactive({
    point_confirm: 0,
    date: '',
  });

  const isEdit = ref(false);
  const { execute: setInfo } = upPintSetting();
  const { isLoading: pointCleanupLoading, execute: pointCleanupExecute } = pointCleanup();
  const formRef = ref<FormInstance>();
  const clearPoint = async () => {
    const error = await formRef.value?.validate();
    if (!error) {
      Modal.confirm({
        title: '提示',
        content: '确认清除本门店下的会员积分?',
        onOk: () => {
          pointCleanupExecute({
            data: {
              date: postData.date,
            },
          }).then(() => {
            Message.success('清除成功');
            isEdit.value = false;
          });
        },
      });
    }
  };
  const changePoint = async () => {
    setInfo({
      data: {
        point_confirm: postData.point_confirm,
      },
    }).then(() => {
      isEdit.value = false;
      Message.success('设置成功');
    });
  };
  async function getInfo() {
    const res = await getPintSetting();
    const resData = res.data.value;
    postData.point_confirm = Number(resData.point_confirm);
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
