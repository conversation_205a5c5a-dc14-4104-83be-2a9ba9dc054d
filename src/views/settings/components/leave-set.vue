<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="期限卡请假">
          <a-select v-model="postData.suspend_rule" style="width: 100%">
            <a-option :value="1">有效期内每年更新请假次数/天数</a-option>
            <a-option :value="2">有效期内拥有一个请假的总次数/天数</a-option>
          </a-select>
          <template #extra>
            <span>次卡、储值卡、私教卡、泳教卡执行有效期内拥有一个请假的总次数/天数。</span>
          </template>
        </a-form-item>

        <a-form-item label="请假限制">
          <a-select v-model="postData.suspend_limit" style="width: 100%" @change="handleSuspendLimit">
            <a-option :value="0">权益用完后可以请假</a-option>
            <a-option :value="1">权益用完后不能请假</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="收费方式">
          <a-radio-group v-model="postData.charging_type" :disabled="!postData.suspend_limit">
            <a-radio :value="1">按请假次数收费</a-radio>
            <a-radio :value="2">按请假时间收费</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="postData.charging_type === 1" label="收费金额">
          每次请假金额（元）
          <a-col flex="auto">
            <a-input-number
              v-model="postData.number_price"
              :disabled="!postData.suspend_limit"
              :min="0"
              :precision="2"
              style="width: 100%"></a-input-number>
          </a-col>
        </a-form-item>
        <a-form-item v-if="postData.charging_type === 2" label="收费金额">
          请假
          <a-col flex="auto" style="margin-left: 16px; margin-right: 16px">
            <a-input-number
              v-model="postData.month"
              :min="1"
              :precision="0"
              :disabled="!postData.suspend_limit"
              style="width: 100%"></a-input-number>
          </a-col>
          月，收取金额（元）
          <a-col flex="auto" style="margin-left: 16px">
            <a-input-number
              v-model="postData.price"
              :min="0"
              :precision="2"
              :disabled="!postData.suspend_limit"
              style="width: 100%"></a-input-number>
          </a-col>
        </a-form-item>
        <a-form-item label="会员端请假">
          <a-switch
            v-model="postData.is_open_member_suspend"
            :checked-value="1"
            :unchecked-value="0"
            @change="handleIsOpenMemberSuspend" />
        </a-form-item>
        <a-form-item label="请假方式">
          <a-select
            v-model="postData.member_suspend_rule.suspend_method"
            style="width: 100%"
            :disabled="!postData.is_open_member_suspend">
            <a-option :value="1">以会员请假，可以对明细所有卡种一并请假</a-option>
            <a-option :value="2">以卡种请假，只能对名下某一张卡请假</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="请假时间范围">
          单次请假最长天数
          <a-col flex="auto" style="margin-left: 16px">
            <a-input-number
              v-model="postData.member_suspend_rule.each_longest_day"
              :min="0"
              :precision="0"
              :disabled="!postData.is_open_member_suspend"
              style="width: 100%"></a-input-number>
          </a-col>
          <template #extra>
            <span>0表示不限制时间</span>
          </template>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message, Modal } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { goCardManage } from '@/utils/router-go';
  import { getUserSuspendSetting, updateUserSuspendSetting } from '@/api/suspend-setting';

  const postData = reactive({
    suspend_rule: 1,
    suspend_limit: 0,
    number_price: 220,
    month: 2,
    price: 200,
    charging_type: 1,
    is_open_member_suspend: 0,
    member_suspend_rule: {
      suspend_method: 1,
      each_longest_day: 0,
    },
  });

  const isOpenAppletPay = ref(false);
  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = updateUserSuspendSetting();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      setInfo({
        data: {
          ...postData,
          can_edit_create_time: postData.can_edit_create_time ? 1 : 0,
          can_edit_fields: postData.can_edit_fields ? 1 : 0,
        },
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getUserSuspendSetting();
    const resData = res.data.value.info;
    Object.entries(resData).forEach(([key, val]) => {
      if (typeof val === 'string' && val) {
        postData[key] = Number(val);
      }
    });
    isOpenAppletPay.value = res.data.value.is_open_applet_pay;
    postData.member_suspend_rule.suspend_method = Number(resData.member_suspend_rule.suspend_method);
    postData.member_suspend_rule.each_longest_day = Number(resData.member_suspend_rule.each_longest_day);
    postData.number_price = Number(resData.charging_rule.number_price);
    postData.month = Number(resData.charging_rule.time_price.month) || 1;
    postData.price = Number(resData.charging_rule.time_price.price);
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  const emits = defineEmits(['goPaySet']);
  function handleTips() {
    Modal.confirm({
      title: '提示',
      content: '您设置了请假收费，但检测到您还未设置小程序支付渠道',
      okText: '去配置',
      cancelText: '取消',
      onOk: () => {
        emits('goPaySet');
      },
    });
  }
  function handleSuspendLimit(val) {
    if (val === 1) {
      Modal.confirm({
        title: '提示',
        content: '请先设置会员卡允许请假次数',
        okText: '去设置',
        cancelText: '设置好了',
        onOk: () => {
          goCardManage();
        },
        onCancel: () => {
          if (postData.is_open_member_suspend && !isOpenAppletPay.value) {
            handleTips();
          }
        },
      });
    }
  }

  function handleIsOpenMemberSuspend(val) {
    if (val === 1 && postData.suspend_limit && !isOpenAppletPay.value) {
      handleTips();
    }
  }

  getInfo();
</script>

<style lang="less" scoped></style>
