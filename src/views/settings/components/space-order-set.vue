<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-alert type="warning" style="margin-bottom: 16px">订场设置只针对会员端订场有限制</a-alert>
        <a-form-item label="订场用户" field="user_range">
          <a-radio-group v-model="postData.user_range" style="width: 100%">
            <a-radio :value="1">会员、潜客都可订场</a-radio>
            <a-radio :value="2">仅会员可订场</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="订场同时约课" field="booking_schedule">
          <a-radio-group v-model="postData.booking_schedule">
            <a-radio :value="1">允许</a-radio>
            <a-radio :value="0">不允许</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="支付方式" field="pay_type">
          <a-checkbox-group v-model="postData.pay_type" @change="handlePayTypeChange">
            <a-checkbox :value="1">微信支付</a-checkbox>
            <a-checkbox :value="2">储值卡支付</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="节假日价格执行" field="holiday_mode">
          <a-radio-group v-model="postData.holiday_mode">
            <a-radio :value="0">需手动设置节假日日期</a-radio>
            <a-radio :value="1">自动随国家法定假日日期</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="订场时间" field="booking_time">
          <a-radio-group v-model="postData.booking_time">
            <a-radio :value="2">仅可预约当天</a-radio>
            <a-radio :value="1">可预约未来时间</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="postData.booking_time === 1" label="可提前预约天数" field="booking_future_days">
          <a-input-number v-model="postData.booking_future_days" :min="1" :max="6" :precision="0" />
        </a-form-item>
        <a-form-item label="允许订场时段" field="allow_time">
          每天
          <a-col flex="auto" style="margin: 0 16px">
            <a-time-picker v-model="postData.allow_time" :allow-clear="false" format="HH:mm" style="width: 100%" />
          </a-col>
          后开始进行场地预订
        </a-form-item>
        <a-form-item label="订场次数">
          <a-radio-group v-model="postData.booking_num_set" @change="handleBookingNumSetChange">
            <a-radio :value="0">每日订场次数无限制</a-radio>
            <a-radio :value="1">每日订场次数有限</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="postData.booking_num_set === 1" label="每日限制次数">
          <a-input-number v-model="postData.booking_num" :min="1" :precision="0" />
        </a-form-item>
        <a-form-item label="取消订场时间" :content-flex="false">
          <a-row align="center" :gutter="[8, 16]">
            <a-col flex="80px">预订超过</a-col>
            <a-col flex="auto">
              <a-input-number
                v-model="postData.cancel_booking_after_order"
                :min="1"
                :precision="0"
                style="width: 100%" />
            </a-col>
            <a-col flex="120px">小时不能取消订场</a-col>
          </a-row>
          <a-row align="center" :gutter="[8, 16]">
            <a-col flex="80px">预订起始前</a-col>
            <a-col flex="auto">
              <a-input-number
                v-model="postData.cancel_booking_before_start"
                :min="1"
                :precision="0"
                style="width: 100%"
                @change="handleCancelBookingChange" />
            </a-col>
            <a-col flex="120px">分钟不能取消预订</a-col>
          </a-row>
        </a-form-item>
        <a-form-item label="取消退款" :content-flex="false" :merge-props="false">
          <a-row
            v-for="(item, index) in postData.cancel_booking_refund_ladder"
            :key="index"
            :gutter="8"
            justify="space-between">
            <a-col flex="80px" style="line-height: 32px">预订开始前</a-col>
            <a-col flex="1">
              <a-form-item
                :field="`cancel_booking_refund_ladder[${index}].start`"
                :rules="{ required: true, type: 'number', message: '请填写规则范围' }"
                hide-label>
                <a-input-number
                  v-model="item.start"
                  :min="
                    index === 0
                      ? postData.cancel_booking_before_start || 0
                      : postData.cancel_booking_refund_ladder[index - 1].end + 1
                  "
                  :max="
                    index === 0
                      ? postData.cancel_booking_before_start || 0
                      : postData.cancel_booking_refund_ladder[index - 1].end + 1
                  "
                  disabled
                  :precision="0"
                  placeholder="请填写" />
              </a-form-item>
            </a-col>
            <a-col flex="20px" style="line-height: 32px">~</a-col>
            <a-col flex="1">
              <a-form-item
                :field="`cancel_booking_refund_ladder[${index}].end`"
                :rules="{ required: true, validator: validateEnd(index) }"
                hide-label>
                <a-input-number
                  v-model="item.end"
                  :min="index > 0 ? postData.cancel_booking_refund_ladder[index].start + 1 : 1"
                  :precision="0"
                  placeholder="请填写"
                  @change="handleChangeEnd($event, index)" />
              </a-form-item>
            </a-col>
            <a-col flex="110px" style="line-height: 32px">分钟，退款比例</a-col>
            <a-col flex="1">
              <a-form-item
                :field="`cancel_booking_refund_ladder[${index}].rate`"
                :rules="{ required: true, validator: validatePass(index) }"
                hide-label>
                <a-input-number v-model="item.rate" :min="1" :max="100" :precision="2" placeholder="请填写" />
              </a-form-item>
            </a-col>
            <a-col flex="20px" style="line-height: 32px">%</a-col>
            <a-col flex="100px" style="line-height: 32px">
              <icon-delete
                v-if="index !== 0 && index === postData.cancel_booking_refund_ladder.length - 1"
                style="cursor: pointer; color: #ff2351"
                size="18"
                title="删除"
                @click.prevent="handleDeleteRefund(index)" />
            </a-col>
          </a-row>
          <a-row :gutter="[8, 16]" style="margin-top: 8px">
            <a-button type="outline" @click="handleAddRefund">添加退款阶梯</a-button>
          </a-row>
        </a-form-item>

        <a-form-item label="订场邀约活动" :content-flex="false">
          <a-row align="center" :gutter="[8, 16]">
            仅能邀约预订未来
            <a-col flex="auto" style="margin: 0 16px">
              <a-input-number v-model="postData.able_mark_limit_time" :min="1" :precision="0" style="width: 100%" />
            </a-col>
            小时以后的场地
          </a-row>
          <a-row align="center" :gutter="[8, 16]">
            邀约活动成团截止时间不晚于开场前
            <a-col flex="auto" style="margin: 0 16px">
              <a-input-number v-model="postData.mark_group_cutoff_time" :min="1" :precision="0" style="width: 100%" />
            </a-col>
            小时
          </a-row>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getSetting, saveSetting } from '@/api/space';
  import { checkBusPayConf } from '@/api/group-buy';

  interface RefundLadder {
    start: number;
    end: number | null;
    rate: number | null;
  }

  const postData = reactive({
    user_range: 1,
    booking_schedule: 0,
    pay_type: [2],
    booking_time: 1,
    holiday_mode: 1,
    booking_future_days: 6,
    booking_num: 0,
    booking_num_set: 0,
    cancel_booking_after_order: 2,
    cancel_booking_before_start: 30,
    cancel_booking_refund_ladder: [{ start: 30, end: null, rate: 100 }] as RefundLadder[],
    able_mark_limit_time: 24,
    mark_group_cutoff_time: 8,
    allow_time: '00:00',
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = saveSetting();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      setInfo({
        data: {
          ...postData,
          cancel_booking_refund_ladder: postData.cancel_booking_refund_ladder.map((v) => {
            return {
              start: v.start.toString(),
              end: v.end && v.end.toString(),
              rate: v.rate && (v.rate / 100).toFixed(4),
            };
          }),
        },
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  const isWxAccount = ref(false);
  function getAuthority() {
    return checkBusPayConf({ is_pay_conf: 1 }).then((res) => {
      isWxAccount.value = res.data.value === 1;
      return res;
    });
  }
  async function handlePayTypeChange() {
    if (!postData.pay_type.includes(1)) {
      return;
    }
    try {
      await getAuthority();
    } catch (error) {
      if (!isWxAccount.value) {
        if (postData.pay_type.includes(2)) {
          postData.pay_type = [2];
        } else {
          postData.pay_type = [];
        }
      }
    }
  }

  function handleBookingNumSetChange(val) {
    if (val === 0) {
      postData.booking_num = 0;
    } else if (val === 1) {
      postData.booking_num = 4;
    }
  }

  function handleChangeEnd(val, index) {
    if (postData.cancel_booking_refund_ladder.length > index + 1) {
      postData.cancel_booking_refund_ladder[index + 1].start = val ? val + 1 : null;
    }
  }
  function handleDeleteRefund(index: number) {
    postData.cancel_booking_refund_ladder.splice(index, 1);
  }

  function handleCancelBookingChange(val) {
    nextTick(() => {
      postData.cancel_booking_refund_ladder[0].start = val || 0;
    });
  }

  const validateEnd = (indexNumber) => {
    return (value: any, callback: any, index = indexNumber) => {
      const len = postData.cancel_booking_refund_ladder.length;
      if (index !== len - 1 && !value) {
        callback('请输入');
      } else if (value && value <= postData.cancel_booking_refund_ladder[index].start) {
        callback('应大于开始值');
      } else if (index !== len - 1 && value && value > 10080 - (len - index - 2) * 2) {
        callback(`此阶段最大值为${10080 - (len - index - 2) * 2}`);
      } else {
        callback();
      }
    };
  };
  const validatePass = (indexNumber) => {
    return (value: any, callback: any, index = indexNumber) => {
      if (!value) {
        callback('请输入退款比例');
      } else if (index > 0 && value < postData.cancel_booking_refund_ladder[index - 1].rate) {
        callback('需大于等于上一阶段比例!');
      } else {
        callback();
      }
    };
  };
  function handleAddRefund() {
    const { length } = postData.cancel_booking_refund_ladder;
    if (length >= 20) {
      Message.error('已达最大可添加上限');
      return;
    }
    const lastEnd = postData.cancel_booking_refund_ladder[length - 1].end;
    if (!lastEnd) {
      Message.error('请填写上一阶梯的结束时间');
      return;
    }
    postData.cancel_booking_refund_ladder.push({
      start: lastEnd + 1,
      end: null,
      rate: null,
    });
  }

  async function getInfo() {
    const res = await getSetting();
    const resData = res.data.value;
    if (!resData) {
      return;
    }
    Object.assign(postData, {
      user_range: resData.user_range ? resData.user_range : 1,
      booking_schedule: resData.booking_schedule ? resData.booking_schedule : 0,
      pay_type: resData.pay_type ? resData.pay_type : [2],
      booking_time: resData.booking_time ? resData.booking_time : 1,
      holiday_mode: resData.holiday_mode,
      booking_future_days: resData.booking_future_days ? resData.booking_future_days : 6,
      booking_num: resData.booking_num ? resData.booking_num : 0,
      cancel_booking_after_order: resData.cancel_booking_after_order ? resData.cancel_booking_after_order : 2,
      cancel_booking_before_start: resData.cancel_booking_before_start ? resData.cancel_booking_before_start : 30,
      cancel_booking_refund_ladder: resData.cancel_booking_refund_ladder.length
        ? resData.cancel_booking_refund_ladder.map((v: any) => {
            return {
              start: +v.start,
              end: v.end ? +v.end : null,
              rate: v.rate ? v.rate * 100 : null,
            };
          })
        : [
            {
              start: resData.cancel_booking_before_start ? resData.cancel_booking_before_start : 30,
              end: null,
              rate: 100,
            },
          ],
      able_mark_limit_time: resData.able_mark_limit_time || 24,
      mark_group_cutoff_time: resData.mark_group_cutoff_time || 8,
      allow_time: resData.allow_time,
    });
    if (postData.booking_num === 0) {
      postData.booking_num_set = 0;
    } else {
      postData.booking_num_set = 1;
    }
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
