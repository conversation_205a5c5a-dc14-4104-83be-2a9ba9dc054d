<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="美团ID">
          <a-input v-model="postData[0].shop_id" placeholder="请输入" style="width: 100%"></a-input>
        </a-form-item>
        <a-form-item label="大众点评ID">
          <a-input v-model="postData[1].shop_id" placeholder="请输入" style="width: 100%"></a-input>
        </a-form-item>
        <a-form-item label="抖音ID">
          <a-input v-model="postData[2].shop_id" placeholder="请输入" style="width: 100%"></a-input>
        </a-form-item>
        <a-form-item label="抖音商家ID">
          <a-input v-model="postData[2].account_id" placeholder="请输入" style="width: 100%"></a-input>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import _ from 'lodash';
  import { getThirdPlatformShopId, updateThirdPlatformShopId } from '@/api/san-group';

  const NONE_THIRD_PARTY_SETTINGS = [
    { platform_id: 1, shop_id: '' },
    { platform_id: 2, shop_id: '' },
    { platform_id: 3, shop_id: '', account_id: '' },
  ];
  const postData = ref(_.cloneDeep(NONE_THIRD_PARTY_SETTINGS));

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = updateThirdPlatformShopId();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      setInfo({
        data: {
          list: postData.value,
        },
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getThirdPlatformShopId();
    const resData = res.data.value;
    if (Array.isArray(resData) && resData.length === 3) {
      resData.sort((a, b) => {
        return Number(a.platform_id) - Number(b.platform_id);
      });
      postData.value = resData;
    } else {
      postData.value = _.cloneDeep(NONE_THIRD_PARTY_SETTINGS);
    }
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
