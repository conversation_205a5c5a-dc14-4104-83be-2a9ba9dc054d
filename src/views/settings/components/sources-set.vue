<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-spin style="display: block" :loading="isLoading">
        <a-form
          ref="formRef"
          class="base-set-form"
          :disabled="!isEdit"
          :model="postData"
          :style="{ width: '900px' }"
          auto-label-width>
          <a-form-item v-for="i in 3" :key="i" :label="typeNames[i - 1]" :content-flex="false">
            <a-row :gutter="[16, 16]" style="width: 100%; margin-bottom: 16px">
              <a-col v-for="(item, index) in postData[`type${i}`]" :key="index" :span="8">
                <a-row style="width: 100%" align="center" :gutter="16">
                  <a-col flex="auto">
                    <a-input
                      v-model="postData[`type${i}`][index].name"
                      :disabled="postData[`type${i}`][index].can_deleted === '1'"
                      style="width: 100%"
                      @blur="saveEdit(postData[`type${i}`][index])"
                      @press-enter="saveEdit(postData[`type${i}`][index])" />
                  </a-col>
                  <a-col v-if="isEdit && postData[`type${i}`][index].can_deleted === '0'" flex="16px">
                    <icon-delete style="cursor: pointer" @click="deleteType(i, index)" />
                  </a-col>
                </a-row>
              </a-col>
            </a-row>
            <div v-if="isEdit">
              <a-button type="outline" @click="addType(i)">添加</a-button>
            </div>
          </a-form-item>
        </a-form>
      </a-spin>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { getSources, setSources } from '@/api/source';

  const typeNames = ['获客来源', '会籍成交方式', '私教成交方式'];
  interface arrItem {
    can_deleted: '1' | '0';
    name: string;
    source_id: string;
    type: string;
  }
  const postData = reactive<{
    type1: arrItem[];
    type2: arrItem[];
    type3: arrItem[];
  }>({
    type1: [],
    type2: [],
    type3: [],
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = setSources();
  async function getInfo() {
    const res = await getSources();
    const resData = res.data.value;
    postData.type1 = resData.type1;
    postData.type2 = resData.type2;
    postData.type3 = resData.type3;
  }
  const postToServer = (item: arrItem) => {
    setInfo({
      data: {
        source_id: item.source_id,
        source_name: item.name,
        source_type: item.type,
      },
    })
      .then(() => {
        Message.success('操作成功');
      })
      .catch(() => {
        getInfo();
      });
  };
  const saveEdit = (item: arrItem) => {
    if (!item.name) {
      return;
    }
    postToServer(item);
  };
  const addType = (index: number) => {
    postData[`type${index}`].push({
      can_deleted: '0',
      name: '',
      source_id: '',
      type: `${index}`,
    });
  };
  const deleteType = (index: number, index2: number) => {
    const curItem = postData[`type${index}`][index2];
    if (curItem.source_id) {
      postToServer({ source_id: curItem.source_id, type: curItem.type });
    }
    postData[`type${index}`].splice(index2, 1);
  };

  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
