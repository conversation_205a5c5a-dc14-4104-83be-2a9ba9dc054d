<template>
  <div class="base-set-box pay-type-box">
    <div class="base-set-content">
      <a-spin style="display: block" :loading="loading">
        <a-form
          ref="formRef"
          class="base-set-form"
          :disabled="!isEdit"
          :model="state"
          :style="{ width: '800px' }"
          auto-label-width>
          <a-form-item label="支付方式">
            <a-checkbox-group v-model="state.ids">
              <a-checkbox
                v-for="pay in state.payTyes"
                :key="pay.id"
                class="checkbox"
                :disabled="pay.can_close === '0'"
                :value="pay.id">
                {{ pay.pay_type_name }}
                <div class="pay-edit">
                  <icon-edit
                    v-if="pay.can_edit === '1'"
                    size="18"
                    title="编辑"
                    style="margin-right: 8px; color: #5fb75d"
                    @click.prevent="editPay(pay)" />
                  <icon-delete
                    v-if="pay.can_del === '1'"
                    style="color: #ccc"
                    size="18"
                    title="删除"
                    @click.prevent="deletePay(pay.id)" />
                </div>
              </a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          <a-form-item>
            <a-button type="outline" @click="clickAddPay">新增支付渠道</a-button>
            <div v-if="state.addingPay" class="add-pay-input">
              <a-input
                ref="payInputRef"
                v-model="state.payName"
                :maxlength="10"
                style="width: 180px"
                placeholder="支付方式名称"
                @press-enter="payOnEnter"></a-input>
              <icon-check-circle
                v-if="state.isEditPay"
                size="24"
                style="margin: 0 10px; cursor: pointer; color: #5cb85c"
                title="确认"
                @click="editConfirm" />
              <icon-plus-circle
                v-else
                size="24"
                style="margin: 0 10px; cursor: pointer; color: #5cb85c"
                title="添加"
                @click="addConfirm" />
              <icon-close-circle
                size="24"
                style="cursor: pointer; color: #d9544f"
                title="取消"
                @click="state.addingPay = state.isEditPay = false" />
            </div>
          </a-form-item>
          <a-form-item label="微信小程序支付渠道">
            <a-radio-group v-model="state.payment_option" @change="setPaymentOptions">
              <a-radio value="1">微信特约商户</a-radio>
              <a-radio value="2">收钱吧</a-radio>
              <a-radio value="3">杉德</a-radio>
              <a-radio value="4">工行</a-radio>
              <a-radio value="5">建行</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </a-spin>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message, Modal } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { usePayStore } from '@/store';
  import {
    switchPayType,
    getPaymentOption,
    setPaymentOption,
    getBusAllPayType,
    editPayType,
    delPayType,
    addBusPayType,
  } from '@/api/payment-manage';

  const state = reactive({
    payName: '',
    initPaymentOption: '1',
    payment_option: '1',
    initialName: '',
    isEditPay: false,
    payId: '',
    addingPay: false,
    coachId: '',
    ids: [],
    payTyes: [],
  });
  const payInfo = usePayStore();
  const { isLoading: switchPayTypeLoading, execute: switchPayExecute } = switchPayType();
  async function getData() {
    const res = await getBusAllPayType();
    const resData = res.data.value;
    state.payTyes = resData;
    state.ids = state.payTyes.filter((item) => item.is_open === '1').map((item) => item.id);
    return resData;
  }
  watch(
    () => state.ids,
    (val, oldVal) => {
      if (oldVal && oldVal.length && JSON.stringify(val) !== JSON.stringify(oldVal)) {
        switchPayExecute({ data: { ids: val } })
          .then(() => {
            payInfo.setPayTypesInit();
          })
          .catch(() => {
            getData();
          });
      }
    }
  );
  const isEdit = ref(false);
  const { isLoading: setPaymentOptionLoading, execute: setPaymentOptionExecute } = setPaymentOption();
  const formRef = ref<FormInstance>();

  async function getPaymentOptions() {
    const res = await getPaymentOption();
    const resData = res.data.value;
    state.initPaymentOption = resData.info.payment_option;
    state.payment_option = resData.info.payment_option;
  }
  async function setPaymentOptions(payment_option) {
    try {
      await setPaymentOptionExecute({ data: { payment_option } });
      state.initPaymentOption = payment_option;
      Message.success('设置成功');
    } catch (error) {
      console.error(error);
      state.payment_option = state.initPaymentOption;
    }
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getData();
    getPaymentOptions();
  };

  const payInputRef = ref();
  function editPay({ id, pay_type_name }) {
    state.payName = pay_type_name;
    state.initialName = pay_type_name;
    state.payId = id;
    state.addingPay = true;
    state.isEditPay = true;
    nextTick(() => {
      payInputRef.value.focus();
    });
  }
  function clickAddPay() {
    state.isEditPay = false;
    state.addingPay = true;
    state.payId = '';
    state.payName = '';
    nextTick(() => {
      payInputRef.value.focus();
    });
  }
  const { isLoading: editPayTypeLoading, execute: editPayTypeExecute } = editPayType();
  function editConfirm() {
    if (state.payName === state.initialName) {
      state.addingPay = false;
      return;
    }
    if (!state.payName) {
      Message.error('请先输入');
      return;
    }
    editPayTypeExecute({
      data: {
        id: state.payId,
        name: state.payName,
      },
    }).then(() => {
      state.addingPay = false;
      getData();
    });
  }
  const { isLoading: delPayTypeLoading, execute: delPayTypeExecute } = delPayType();
  function deletePay(id) {
    Modal.confirm({
      title: '删除',
      content: '确认删除该支付方式吗？',
      onOk: () => {
        delPayTypeExecute({ data: { id } }).then(() => {
          getData();
        });
      },
    });
  }

  const { isLoading: addBusPayTypeLoading, execute: addBusPayTypeExecute } = addBusPayType();
  function addConfirm() {
    if (!state.payName) {
      Message.error('请先输入');
      return;
    }
    addBusPayTypeExecute({
      data: {
        name: state.payName,
      },
    }).then(() => {
      state.addingPay = false;
      getData();
    });
  }

  function payOnEnter() {
    if (state.isEditPay) {
      editConfirm();
    } else {
      addConfirm();
    }
  }

  getData();
  getPaymentOptions();
  const loading = computed(() => {
    return (
      switchPayTypeLoading.value ||
      setPaymentOptionLoading.value ||
      editPayTypeLoading.value ||
      delPayTypeLoading.value ||
      addBusPayTypeLoading.value
    );
  });
</script>

<style lang="less" scoped>
  .pay-edit {
    display: none;
    padding-left: 16px;
  }

  .pay-type-box {
    font-size: 14px;
    .ivu-form .ivu-form-item-label {
      font-size: 14px;
      padding-right: 20px;
    }
    .input {
      width: 300px;
    }

    .checkbox {
      width: 30%;
      height: 28px;
      margin-bottom: 16px;

      &:hover {
        .pay-edit {
          display: inline-block;
        }
      }
    }

    .add-pay {
      margin-top: 20px;
      display: flex;
    }

    .add-pay-input {
      display: flex;
      align-items: center;
      margin-left: 30px;
    }
    .payment-wrap {
      margin-top: 24px;
      padding-top: 24px;
      border: 1px solid #eee;
    }
  }
</style>
