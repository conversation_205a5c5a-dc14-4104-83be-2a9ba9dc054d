<template>
  <div>
    <a-space v-if="filterTags.length || showInput" wrap>
      <template v-for="(tag, index) of tags" :key="index">
        <a-tag
          v-show="tag.deleted !== 1"
          color="red"
          :closable="(firstClose && index === 0) || index !== 0"
          :checkable="(firstClose && index === 0) || index !== 0"
          checked
          @check="handleEdit(tag)"
          @close="handleRemove(index)">
          {{ tag.value }}
        </a-tag>
      </template>
      <a-input
        v-if="showInput"
        ref="inputRef"
        v-model.trim="editItem.value"
        :style="{ width: '90px' }"
        size="mini"
        @keyup.enter="handleAdd"
        @blur="handleAdd" />
    </a-space>
    <div>
      <a-button v-if="!showInput" size="mini" type="outline" @click="handleEdit">添加类型</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps({
    modelValue: {
      type: Array,
      required: true,
    },
    // 第一个标签能否删除
    firstClose: {
      type: Boolean,
      default: true,
    },
  });
  const emit = defineEmits(['update:modelValue']);
  const tags = computed({
    get: () => props.modelValue,
    set: (val) => {
      emit('update:modelValue', val);
    },
  });
  const filterTags = computed(() => {
    return tags.value.filter((item) => item.deleted !== 1);
  });
  const inputRef = ref(null);
  const showInput = ref(false);
  const initItem = {
    value: '',
    deleted: 0,
    id: '',
  };
  const editItem = ref({
    ...initItem,
  });

  const handleEdit = (tag: Record<string, any> | undefined) => {
    showInput.value = true;
    if (tag?.id) {
      editItem.value = {
        ...tag,
      };
    } else {
      editItem.value = { ...initItem };
    }
    nextTick(() => {
      if (inputRef.value) {
        inputRef.value.focus();
      }
    });
  };

  const handleAdd = () => {
    if (editItem.value.value) {
      if (editItem.value.id) {
        tags.value = tags.value.map((item) => {
          if (item.id === editItem.value.id) {
            return {
              ...item,
              deleted: 0,
              ...editItem.value,
            };
          }
          return item;
        });
      } else {
        tags.value.push(editItem.value);
      }
      editItem.value = { ...initItem };
    }
    showInput.value = false;
  };

  const handleRemove = (index) => {
    tags.value[index].deleted = 1;
  };
</script>

<style lang="less" scoped></style>
