<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="会员端退票">
          <a-radio-group v-model="postData.cancel_member">
            <a-radio :value="1">开启</a-radio>
            <a-radio :value="0">关闭</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getSanSetting, updateSanSetting } from '@/api/san';

  const postData = reactive({
    cancel_member: 0,
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = updateSanSetting();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      setInfo({
        data: postData,
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getSanSetting();
    const resData = res.data.value;
    postData.cancel_member = resData.cancel_member ? 1 : 0;
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
