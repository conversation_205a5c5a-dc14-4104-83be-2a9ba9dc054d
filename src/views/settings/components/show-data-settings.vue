<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form class="base-set-form" :model="postData" :style="{ width: '800px' }" auto-label-width>
        <a-form-item label="概况数据显示">
          <a-checkbox v-model="showSwimming" :disabled="!isEdit">泳教数据</a-checkbox>
          <a-checkbox v-model="showBooking" :disabled="!isEdit">订场和票务数据</a-checkbox>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { getShowDataSettings, setShowDataSettings } from '@/api/show-data-settings';

  const postData = reactive({
    swim_data: '0',
    booking_data: '0',
  });

  const isEdit = ref(false);
  const showSwimming = ref(false);
  const showBooking = ref(false);
  const { isLoading, execute: setInfo } = setShowDataSettings();

  const saveEdit = () => {
    postData.swim_data = showSwimming.value ? '1' : '0';
    postData.booking_data = showBooking.value ? '1' : '0';

    setInfo({
      data: postData,
    }).then(() => {
      isEdit.value = false;
      Message.success('设置成功');
    });
  };

  function getInfo() {
    getShowDataSettings().then((res) => {
      const resData = res.data.value;
      showSwimming.value = Number(resData.swim_data) === 1;
      showBooking.value = Number(resData.booking_data) === 1;
    });
  }

  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };

  getInfo();
</script>

<style lang="less" scoped></style>
