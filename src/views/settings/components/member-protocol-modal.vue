<template>
  <a-modal
    v-model:visible="isShowModal"
    :ok-loading="isLoading"
    title="购卡协议"
    :width="720"
    ok-text="保存"
    :mask-closable="false"
    :on-before-ok="hansleConfirm">
    <Editor v-model="postData.protocol_content" :options="options"></Editor>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import Editor from '@/components/form/editor.vue';
  import { unescapeHTML } from '@/utils';
  import { getProtocolContent, updateProtocolContent } from '@/api/buy-card-protocol';

  const postData = reactive({
    protocol_content: '',
  });
  const props = defineProps<{
    modelValue: boolean;
  }>();
  const options = {
    modules: {
      toolbar: {
        container: [
          [{ header: [1, 2, 3, 4, 5, 6, false] }],
          ['bold', 'italic', 'underline', 'strike'],
          [{ color: [] }, { background: [] }],
          [{ list: 'ordered' }, { list: 'bullet' }],
          [{ align: [] }],
          [{ script: 'sub' }, { script: 'super' }],
        ],
      },
    },
  };

  const emits = defineEmits(['update:modelValue', 'refresh']);

  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  async function getInfo() {
    const res = await getProtocolContent();
    const resData = res.data.value.info;
    postData.protocol_content = unescapeHTML(resData.protocol_content || '');
  }
  const { isLoading, execute: setInfo } = updateProtocolContent();
  const hansleConfirm = async () => {
    if (!postData.protocol_content) {
      Message.error('协议内容不能为空');
      return false;
    }
    await setInfo({
      data: {
        protocol_content: postData.protocol_content,
      },
    });
    Message.success('设置成功');
    return true;
  };
  getInfo();
</script>

<style lang="less" scoped>
  .top-row {
    height: 32px;
    line-height: 32px;
    margin-bottom: 16px;
  }
</style>
