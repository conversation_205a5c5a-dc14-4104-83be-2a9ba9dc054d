<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-alert type="warning" style="margin-bottom: 16px">
          爽约惩罚开启后，会员预约后必须要进行课程签到，第二天仍未签到皆视为爽约
        </a-alert>
        <a-form-item label="爽约惩罚">
          <a-switch v-model="postData.miss_turn_on" :checked-value="1" :unchecked-value="0" />
        </a-form-item>
        <a-form-item label="爽约计算周期（天）">
          <a-input-number
            v-model="postData.miss_cycle"
            :max="90"
            :min="1"
            :precision="0"
            :disabled="postData.miss_turn_on === 0"></a-input-number>
        </a-form-item>
        <a-form-item label="禁止约课（天）">
          <a-space direction="vertical" style="width: 100%" fill>
            <a-row v-for="(item, index) in postData.prohibits" :key="index" align="center">
              <a-col flex="160px">{{ postData.miss_cycle }}天内爽约{{ index + 1 }}次禁止约课</a-col>
              <a-col flex="auto">
                <a-input-number
                  v-model="postData.prohibits[index]"
                  style="width: 100%"
                  :max="30"
                  :min="1"
                  :precision="0"
                  placeholder="天"
                  :disabled="postData.miss_turn_on === 0" />
              </a-col>
              <a-col v-if="isEdit && postData.prohibits.length > 1" flex="70px">
                <a-button :style="{ marginLeft: '8px' }" @click="handleDeleteListClick(index)">删除</a-button>
              </a-col>
            </a-row>
            <a-button v-if="isEdit && postData.miss_turn_on === 1" type="outline" @click="handleAddListClick">
              添加
            </a-button>
          </a-space>
        </a-form-item>
        <a-form-item label="会员约课限制" field="limit_card_set" :rules="[{ validator: checkLimit }]">
          <a-space direction="vertical" :size="16" style="display: block">
            <div v-if="!isEdit && !(postData.limit_card_set && postData.limit_card_set.length)">无限制</div>
            <a-row v-for="(item, index) in postData.limit_card_set" :key="index" style="width: 100%">
              <a-col flex="1">
                <a-row style="width: 100%; margin-bottom: 16px" align="center">
                  <TreeCardSelect
                    v-model="postData.limit_card_set[index].list"
                    :data="treeData"
                    :disabled-ids="getDisabledIds(index)"
                    style="width: 100%"></TreeCardSelect>
                </a-row>
                <a-row style="width: 100%" :gutter="8" align="center">
                  每天的课可约
                  <a-col flex="1">
                    <a-input-number
                      v-model="postData.limit_card_set[index].every_day_times"
                      :min="1"
                      :max="99"
                      placeholder="1~99"
                      :precision="0" />
                  </a-col>
                  节，
                  <a-col flex="1">
                    <a-input-number
                      v-model="postData.limit_card_set[index].days"
                      placeholder="请填写"
                      :min="1"
                      :precision="0" />
                  </a-col>
                  天内会员共可约
                  <a-col flex="1">
                    <a-input-number
                      v-model="postData.limit_card_set[index].times"
                      placeholder="请填写"
                      :min="1"
                      :precision="0" />
                  </a-col>
                  节
                </a-row>
              </a-col>
              <a-col flex="70px">
                <a-button :style="{ marginLeft: '8px' }" @click="handleDeleteLimit(index)">删除</a-button>
              </a-col>
            </a-row>
            <a-button v-if="isEdit" type="outline" @click="handleAddLimit">添加</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { times } from 'lodash';
  import TreeCardSelect from '@/components/form/tree-card-select.vue';
  import { getBusinessMissSetting, setBusinessMissSetting } from '@/api/business-miss-setting';
  import { getCardsForAll } from '@/api/activity';

  const postData = reactive({
    miss_turn_on: 1,
    miss_cycle: 30,
    prohibits: [1],
    is_limit_card: 0, // 是否开启期限卡预约限制0否1是
    is_confirm: 0, // 是否开启会员确认0否1是
    limit_card_set: [],
  });
  function handleDeleteListClick(index: number) {
    postData.prohibits.splice(index, 1);
  }
  function handleAddListClick() {
    if (postData.prohibits.length < 5) {
      postData.prohibits.push(1);
    } else {
      Message.error('最多设置5条!');
    }
  }
  const checkLimit = (value: string, callback: (arg0?: string) => void) => {
    if (postData.limit_card_set && postData.limit_card_set.length) {
      postData.limit_card_set.forEach((item) => {
        if (!item.list.length) {
          callback('请设置约课限制卡种');
        } else if (!item.every_day_times || !item.days || !item.times) {
          callback('请设置天数或节数');
        } else {
          callback();
        }
      });
    } else {
      callback();
    }
  };
  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = setBusinessMissSetting();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const datas = {
      ...postData,
      limit_card_set: JSON.stringify(
        postData.limit_card_set.map((item) => {
          return {
            ...item,
            list: item.list.map((i) => {
              return {
                id: i.value,
                name: i.label,
              };
            }),
          };
        })
      ),
    };
    const errors = await formRef.value?.validate();
    if (!errors) {
      setInfo({
        data: datas,
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getBusinessMissSetting();
    const resData = res.data.value;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id, bus_id, miss_cycle, prohibits, ...rest } = resData;
    Object.entries(rest).forEach(([key, val]) => {
      if (typeof val === 'string' && val) {
        postData[key] = Number(val);
      } else if (Object.prototype.toString.call(val) === '[object Object]') {
        Object.entries(val).forEach(([k, v]) => {
          postData[key][k] = Number(v);
        });
      } else {
        postData[key] = val;
      }
    });
    postData.prohibits = prohibits?.length ? prohibits.map((str) => Number(str)) : [1];
    postData.miss_cycle = +miss_cycle || 30;
    postData.limit_card_set = postData.limit_card_set.map((item) => {
      return {
        ...item,
        list: item.list.map((i) => {
          return {
            value: i.id,
            label: i.name,
          };
        }),
      };
    });
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  const cardList = ref([]);
  const treeData = ref([]);
  function getDisabledIds(index: number) {
    return postData.limit_card_set.flatMap((i, subIndex) => (subIndex === index ? [] : i.list.map((j) => j.value)));
  }
  function getCardByType(type) {
    return cardList.value.filter((item) => item.card_type_id === type && item.is_pt_time_limit_card !== '1');
  }
  function getCardList() {
    getCardsForAll().then((res) => {
      cardList.value = res.data.value.map((item) => {
        return {
          ...item,
          title: item.card_name,
          key: item.card_id,
          disabled: false,
        };
      });
      treeData.value = [
        {
          key: 'all',
          title: '全部卡种',
          disabled: false,
          children: [
            {
              key: 'term',
              title: '期限卡',
              disabled: false,
              children: getCardByType('1'),
            },
            {
              key: 'times',
              title: '次卡',
              disabled: false,
              children: getCardByType('2'),
            },
            {
              key: 'value',
              title: '储值卡',
              disabled: false,
              children: getCardByType('3'),
            },
            {
              key: 'private',
              title: '私教课',
              disabled: false,
              children: getCardByType('4'),
            },
            {
              key: 'swim',
              title: '泳教课',
              disabled: false,
              children: getCardByType('5'),
            },
          ],
        },
      ];
    });
  }
  function handleAddLimit() {
    postData.limit_card_set.push({
      every_day_times: 5,
      days: 1,
      times: 3,
      list: [],
    });
  }
  function handleDeleteLimit(index: number) {
    postData.limit_card_set.splice(index, 1);
  }
  getCardList();
  getInfo();
</script>

<style lang="less" scoped></style>
