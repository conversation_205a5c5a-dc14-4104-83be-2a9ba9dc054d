<template>
  <a-modal
    v-model:visible="isShowModal"
    :title="`${readonly ? '查看' : '编辑'}审批人`"
    :width="readonly ? 400 : 720"
    :footer="!readonly"
    :on-before-ok="hansleConfirm">
    <a-row :gutter="16" justify="center">
      <a-col v-if="!readonly" :span="12">
        <a-input-search
          v-model="leftState.search"
          placeholder="请输入"
          style="margin-bottom: 16px"
          @search="getList"
          @press-enter="getList" />
        <!-- 获取当前场馆账号列表需要拿到对应的region_bus -->
        <admin-region v-model="leftState.region_bus" style="display: none" :multiple="false" :should-default="1" />
        <a-table
          v-model:selectedKeys="leftSelectedIds"
          row-key="id"
          :data="leftList"
          :loading="getAdminListApproveLoading"
          :row-selection="rowSelection"
          :virtual-list-props="{ height: 400 }"
          :pagination="false"
          @select="handleLeftSelect">
          <template #columns>
            <a-table-column title="姓名" data-index="realname" />
            <a-table-column title="手机号" data-index="phone" />
          </template>
        </a-table>
      </a-col>
      <a-col :span="readonly ? 24 : 12">
        <div v-if="!readonly" class="top-row">
          <span style="font-weight: bold; font-size: 15px">已有审批账号</span>
        </div>
        <a-table
          key="id"
          :loading="getAdminListApproveLoading"
          :data="rightList"
          :virtual-list-props="{ height: 400 }"
          :pagination="false">
          <template #columns>
            <a-table-column title="姓名" data-index="realname" />
            <a-table-column title="手机号" data-index="phone" />
            <a-table-column v-if="!readonly" title="操作">
              <template #cell="{ record }">
                <a-link status="danger" @click="handleDelete(record)">删除</a-link>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import AdminRegion from '@/components/form/adminRegion.vue';
  import { getAdminListApprove } from '@/api/admin';
  import { setApproveRule } from '@/api/approve-set';

  const props = defineProps<{
    modelValue: boolean;
    itemData: Record<string, any>;
    readonly: boolean;
  }>();
  const leftList = ref([]);
  const leftSelectedIds = ref([]);
  const leftState = reactive({
    search: '',
    region_bus: '',
    page_no: 1,
    page_size: 999,
  });
  const rightList = ref([]);

  const rightIds = computed(() => {
    return rightList.value.map((item) => item.id);
  });

  const rowSelection = {
    type: 'checkbox',
    showCheckedAll: false,
  };
  const emits = defineEmits(['update:modelValue', 'refresh']);

  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const { isLoading: getAdminListApproveLoading, execute: getAdminListApproveExecute } = getAdminListApprove();
  function getList() {
    getAdminListApproveExecute({ data: leftState }).then((res) => {
      leftList.value = res.data.value.list
        .filter((v) => {
          return !rightIds.value.includes(v.admin_id);
        })
        .map((item) => {
          return {
            ...item,
            id: item.admin_id,
          };
        });
      leftSelectedIds.value = [];
    });
  }

  watch(
    () => isShowModal.value,
    (newValue) => {
      if (newValue) {
        rightList.value = props.itemData.approve_admin_ids;
        if (!props.readonly) {
          getList();
        }
      } else {
        leftState.search = '';
        leftList.value = [];
      }
    }
  );

  function handleLeftSelect(rowKeys: string | number[], rowKey: string | number, record: any) {
    // 左边选中
    if (rowKeys.includes(rowKey)) {
      rightList.value.unshift(record);
    } else {
      // 左边取消选中
      rightList.value = rightList.value.filter((item) => item.id !== record.id);
    }
  }

  function handleDelete(record) {
    rightList.value = rightList.value.filter((item) => item.id !== record.id);
    const index = leftList.value.findIndex((item) => item.id === record.id);
    if (index !== -1) {
      leftSelectedIds.value = leftSelectedIds.value.filter((item) => item !== record.id);
    } else {
      leftList.value.unshift(record);
    }
  }

  const { execute: setInfo } = setApproveRule();
  async function hansleConfirm() {
    const { approve_type, status, auto_approve } = props.itemData;
    const postData = {
      approve_type,
      status,
      auto_approve,
      approve_admins: rightIds.value,
    };

    try {
      await setInfo({ data: postData });
      Message.success('设置成功！');
      emits('refresh');
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }
</script>

<style lang="less" scoped>
  .top-row {
    height: 32px;
    line-height: 32px;
    margin-bottom: 16px;
  }
</style>
