<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="储值卡消费确认">
          <a-radio-group v-model="postData.open_confirm">
            <a-radio :value="1">需要一体机/短信验证码确认</a-radio>
            <a-radio :value="0">不需要确认</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="混合支付">
          <a-radio-group v-model="postData.stored_value_blend">
            <a-radio :value="1">储值卡可以和微信、支付宝等共同支付</a-radio>
            <a-radio :value="0">储值卡和用其他支付方式不共用</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="会员端支付">
          <a-radio-group v-model="postData.stored_card_member_pay">
            <a-radio :value="1">会员端可以用储值卡进行支付</a-radio>
            <a-radio :value="0">会员端禁用储值卡支付</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="商品用储值卡">
          <a-radio-group v-model="postData.card_use_type">
            <a-radio :value="1">所有储值卡可用</a-radio>
            <a-radio :value="2">部分储值卡可用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="postData.card_use_type === 2" label="指定储值卡">
          <a-select v-model="postData.card_id_list" multiple allow-clear allow-search>
            <a-option v-for="item in nonMemberCardList" :key="item.id" :value="item.id">
              {{ item.name }}
            </a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getCardList } from '@/api/card';
  import { getSetting, saveSetting } from '@/api/commodity';
  import { useBusInfoStore } from '@/store';

  const busInfo = useBusInfoStore();
  const postData = reactive({
    open_confirm: 0,
    stored_value_blend: 0,
    stored_card_member_pay: 0,
    card_use_type: 1,
    card_id_list: [],
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = saveSetting();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      setInfo({
        data: {
          ...postData,
          card_id_list: postData.card_id_list.join(','),
        },
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getSetting();
    const resData = res.data.value;
    Object.entries(resData).forEach(([key, val]) => {
      if (typeof val === 'string' && val) {
        postData[key] = Number(val);
      }
    });
    if (postData.card_use_type === 2) {
      postData.card_id_list = resData.card_id_list.split(',');
    } else {
      postData.card_id_list = [];
    }
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };

  const nonMemberCardList = ref([]);
  function getMoneyCardList() {
    getCardList(
      {
        bus_id: busInfo.bus_id,
        page_size: 9999,
      },
      true
    ).then((res) => {
      nonMemberCardList.value = res.data.value.list.filter((item) => item.card_type_id === '3');
    });
  }
  getMoneyCardList();
  getInfo();
</script>

<style lang="less" scoped></style>
