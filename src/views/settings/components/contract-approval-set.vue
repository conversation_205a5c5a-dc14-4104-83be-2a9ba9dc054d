<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-alert type="warning" style="margin-bottom: 16px">开启审批后需添加审批人，若无审批人则无法查看审批数据</a-alert>
      <a-table :loading="isLoading" :data="list" :pagination="false" style="width: 100%">
        <template #columns>
          <a-table-column title="审批任务" data-index="taskName" />
          <a-table-column title="状态">
            <template #cell="{ record }">
              <a-switch
                :model-value="record.status"
                :disabled="!isEdit"
                checked-value="1"
                unchecked-value="0"
                @change="handleStatusChange($event, record, 'status')" />
            </template>
          </a-table-column>
          <a-table-column title="自动审批">
            <template #cell="{ record }">
              <a-checkbox
                v-if="['1', '2', '3'].includes(record.approve_type)"
                :model-value="record.auto_approve === '1'"
                :disabled="!isEdit || record.status === '0'"
                @change="handleStatusChange($event, record, 'auto_approve')">
                自动审批
              </a-checkbox>
              <span v-else>暂不支持</span>
            </template>
          </a-table-column>
          <a-table-column title="审批人数">
            <template #cell="{ record }">
              <a-link :disabled="!isEdit || +record.approve_admin_num === 0" @click="handleShowEditModal(record)">
                {{ record.approve_admin_num }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-link :disabled="!isEdit" @click="handleShowEditModal(record, false)">编辑审批人</a-link>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
  <approval-edit-modal v-model="isShowEditModal" :item-data="itemData" :readonly="readonly" @refresh="getInfo" />
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { getApproveSet, setApproveRule } from '@/api/approve-set';
  import ApprovalEditModal from './approval-edit-modal.vue';

  const taskNames = {
    1: '购卡',
    2: '跨店购卡',
    3: '续卡',
    4: '升卡',
    5: '销卡',
    6: '拆分',
    7: '请假',
    8: '转卡',
    9: '补卡',
    10: '编辑会员卡',
    11: '赠体验卡/课',
    12: '会员信息',
    13: '会员头像',
    14: '折扣券赠送',
    15: '积分赠送', // 积分/金币赠送
  };
  const list = ref([]);

  const isShowEditModal = ref(false);
  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = setApproveRule();
  async function getInfo() {
    const res = await getApproveSet();
    const resData = res.data.value;
    list.value = resData.map((v) => {
      v.taskName = `${taskNames[v.approve_type] || '其它'}审批`;
      return v;
    });
  }

  async function handleStatusChange(val: string | boolean, info, key) {
    const { approve_type, status, auto_approve, approve_admin_ids } = info;
    const postData = {
      approve_type,
      status,
      auto_approve,
      approve_admins: approve_admin_ids.map((v) => v.id),
    };
    if (key === 'auto_approve') {
      postData[key] = val ? '1' : '0';
      info[key] = val ? '1' : '0';
    } else if (val === '0') {
      postData.status = '0';
      postData.auto_approve = '0';
      info.auto_approve = '0';
      info.status = '0';
    } else {
      postData[key] = val;
      info[key] = val;
    }

    try {
      await setInfo({ data: postData });
      Message.success('设置成功！');
    } catch (error) {
      getInfo();
    }
  }
  const itemData = ref({});
  const readonly = ref(false);
  function handleShowEditModal(item, only = true) {
    isShowEditModal.value = true;
    itemData.value = item;
    readonly.value = only;
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
