<template>
  <a-modal v-model:visible="isShowModal" title="团课评价" :width="720" :mask-closable="false" @ok="hansleConfirm">
    <a-form ref="formRef" class="base-set-form" :model="postData" :style="{ width: '100%' }" auto-label-width>
      <a-form-item v-if="isShowModal" label="评分类型" :content-flex="false">
        <tag-set v-model="postData.GROUP_CLASS_GRADING_TYPE_LIST" :first-close="false" />
      </a-form-item>
      <a-form-item label="评分设置">
        <a-radio-group v-model="postData.GROUP_CLASS_MAXIMUM_SCORE_VALUE.value">
          <a-radio :value="3">1～3分</a-radio>
          <a-radio :value="5">1～5分</a-radio>
          <a-radio :value="10">1～10分</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="评价标签">
        <tag-set v-if="isShowModal" v-model="postData.GROUP_CLASS_LABEL_SETTINGS_LIST" />
      </a-form-item>
      <a-form-item label="文字反馈">
        <a-switch v-model="postData.GROUP_CLASS_FEEDBACK_SWITCH.value" :checked-value="1" :unchecked-value="0" />
      </a-form-item>
      <a-form-item label="评价推送时间">
        课程结束后
        <a-col flex="auto" style="margin: 0 16px">
          <a-input-number
            v-model="postData.GROUP_CLASS_MINIMUM_ASSESSABLE_TIME_AFTER_THE_END_OF_THE_CLASS.value"
            :precision="0"
            :min="0"
            :max="60"
            style="width: 100%" />
        </a-col>
        分钟，会员可进行评价
        <template #extra>输入0，即课程结束后就可评价，最多60分钟</template>
      </a-form-item>
      <a-form-item label="评价有效天数">
        <a-input-number
          v-model="postData.GROUP_CLASS_THE_MAXIMUM_EVALUATION_TIME_AFTER_THE_COURSE.value"
          :min="0"
          :max="30"
          :precision="0"
          style="width: 100%" />
        <template #extra>
          <span>输入0，即不限制;最多30天。</span>
        </template>
      </a-form-item>
      <a-form-item label="追评">
        <a-switch v-model="postData.GROUP_CLASS_FOLLOW_UP_SWITCH.value" :checked-value="1" :unchecked-value="0" />
      </a-form-item>
      <a-form-item label="追评有效天数">
        <a-input-number
          v-model="postData.GROUP_CLASS_COURSE_MAXIMUM_FOLLOW_UP_ASSESSMENT_TIME.value"
          :min="0"
          :max="30"
          :precision="0"
          style="width: 100%" />
        <template #extra>
          <span>第一次评价后x天不能追评，0表示不限制;最多30天。</span>
        </template>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import TagSet from './tag-set.vue';

  const props = defineProps<{
    modelValue: boolean;
    initData: Record<string, any>;
    booleanKeysComment: string[];
  }>();

  const emits = defineEmits(['update:modelValue', 'onOk']);
  const postData = reactive({
    GROUP_CLASS_GRADING_TYPE_LIST: [],
    GROUP_CLASS_LABEL_SETTINGS_LIST: [],
    GROUP_CLASS_MAXIMUM_SCORE_VALUE: {
      id: '',
      value: 3,
    },
    GROUP_CLASS_FEEDBACK_SWITCH: {
      id: '',
      value: 0,
    },
    GROUP_CLASS_MINIMUM_ASSESSABLE_TIME_AFTER_THE_END_OF_THE_CLASS: {
      id: '',
      value: 0,
    },
    GROUP_CLASS_THE_MAXIMUM_EVALUATION_TIME_AFTER_THE_COURSE: {
      id: '',
      value: 0,
    },
    GROUP_CLASS_FOLLOW_UP_SWITCH: {
      id: '',
      value: 0,
    },
    GROUP_CLASS_COURSE_MAXIMUM_FOLLOW_UP_ASSESSMENT_TIME: {
      id: '',
      value: 0,
    },
  });
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  watch(
    () => isShowModal.value,
    (val) => {
      if (val) {
        Object.entries(props.initData).forEach(([key, value]) => {
          if (!props.booleanKeysComment.includes(key) && value.value) {
            postData[key] = {
              ...value,
              value: Number(value.value),
            };
          } else {
            postData[key] = value;
          }
        });
      }
    }
  );
  function hansleConfirm() {
    emits('onOk', postData);
  }
</script>

<style lang="less" scoped>
  .top-row {
    height: 32px;
    line-height: 32px;
    margin-bottom: 16px;
  }
</style>
