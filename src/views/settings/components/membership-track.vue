<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="非会员跟进">
          <span>跟进有效天数</span>
          <a-col flex="auto" style="margin-left: 16px">
            <a-input-number
              v-model="postData.nonmember_follow_up_valid_time"
              :precision="0"
              style="width: 100%"></a-input-number>
          </a-col>
        </a-form-item>
        <a-form-item label="会员跟进">
          <span>跟进有效天数</span>
          <a-col flex="auto" style="margin-left: 16px">
            <a-input-number v-model="postData.follow_up_valid_time" :precision="0" style="width: 100%"></a-input-number>
          </a-col>
          <template #extra>
            <span>会籍名下会员/非会员需要多少天未进行跟进将客户信息转入公海；默认0天，0天表示不启用</span>
          </template>
        </a-form-item>
        <a-form-item label="公海会员">
          <a-radio-group v-model="postData.group_public_sea_member">
            <a-radio :value="1">看自己组内公海会员数据</a-radio>
            <a-radio :value="0">看整个公海会员数据</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="最多预约人数">
          <a-input-number
            v-model="postData.reservation_preson_max"
            :min="0"
            :precision="0"
            style="width: 100%"></a-input-number>
          <template #extra>一名会籍人员最多添加到预约列表中的人数；默认20人，0人不启用</template>
        </a-form-item>
        <a-form-item label="定金金额">
          <a-radio-group v-model="postData.deposit">
            <a-radio :value="1">会籍决定收取金额</a-radio>
            <a-radio :value="2">固定金额</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="postData.deposit === 2" label="收取金额">
          <a-input-number v-model="postData.money" :precision="0" :min="1" style="width: 100%"></a-input-number>
        </a-form-item>
        <a-form-item label="体验卡限制">
          <a-radio-group v-model="postData.receive_card_setting">
            <a-radio :value="1">一个客户只能从会籍端领取一种体验卡</a-radio>
            <a-radio :value="0">无限制</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="会籍开单">
          <a-switch v-model="postData.membership_billed" :checked-value="1" :unchecked-value="0" />
        </a-form-item>
        <a-form-item label="开单限制">
          <a-checkbox v-model="postData.can_edit_fields">可修改售价、赠送、有效期等</a-checkbox>
          <a-checkbox v-model="postData.can_edit_create_time">可修改开卡时间</a-checkbox>
        </a-form-item>
        <a-form-item label="开单核查">
          <a-switch v-model="postData.membership_audit" :checked-value="1" :unchecked-value="0" />
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getParam, addParam } from '@/api/param';

  const postData = reactive({
    nonmember_follow_up_valid_time: undefined,
    follow_up_valid_time: undefined,
    group_public_sea_member: 1,
    reservation_preson_max: 20,
    receive_card_setting: 1,
    membership_billed: 0,
    can_edit_create_time: false,
    can_edit_fields: false,
    membership_audit: 0,
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = addParam();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      setInfo({
        data: {
          ...postData,
          can_edit_create_time: postData.can_edit_create_time ? 1 : 0,
          can_edit_fields: postData.can_edit_fields ? 1 : 0,
        },
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getParam();
    const resData = res.data.value.info;
    const booleanKeys = ['can_edit_create_time', 'can_edit_fields'];
    Object.entries(resData).forEach(([key, val]) => {
      if (typeof val === 'string' && val && !booleanKeys.includes(key)) {
        postData[key] = Number(val);
      }
      if (booleanKeys.includes(key)) {
        postData[key] = val === '1';
      }
    });
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
