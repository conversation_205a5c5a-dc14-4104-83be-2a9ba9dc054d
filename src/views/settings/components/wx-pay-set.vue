<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="商户名称" field="sub_mch_name" :rules="{ required: true, message: '请输入商户名称' }">
          <a-input v-model="postData.sub_mch_name" style="width: 100%"></a-input>
        </a-form-item>
        <a-form-item label="商户号" field="sub_mch_id" :rules="{ required: true, message: '请输入商户号' }">
          <a-input v-model="postData.sub_mch_id" style="width: 100%"></a-input>
        </a-form-item>
      </a-form>
      <div style="padding: 20px 60px 50px; border: 1px solid #f1f3f7; border-radius: 5px">
        <h3 style="margin-bottom: 20px">说明</h3>
        <ul>
          <li>
            1、完成配置，“会员端购卡”等线上支付的款项可
            <span style="color: #f00">直接入到商家账上，无需再提现</span>
            ，如图：
          </li>
          <a-row align="center" style="padding: 30px 0" :gutter="20">
            <a-col :span="12">
              <img :src="getAssetsImg('weixin-01.png')" alt="" />
              <div style="text-align: center; margin-top: 20px">图一 支付流程</div>
            </a-col>
            <a-col :span="12">
              <img :src="getAssetsImg('weixin-02.png')" alt="" />
              <div style="text-align: center; margin-top: 20px">图二 支付页面</div>
            </a-col>
          </a-row>
          <li>
            2、请谨慎配置，一经保存，商家无法取消配置，若配置信息有误，会员将
            <span style="color: #f00">无法支付</span>
          </li>
          <li>
            3、
            <a-link @click="goHelpPage({ active: 3 })">了解更多配置说明</a-link>
          </li>
        </ul>
      </div>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message, Modal } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getAssetsImg } from '@/utils';
  import { getSettingInfo, saveSettingInfo } from '@/api/wx-pay-submch-conf';
  import { goHelpPage } from '@/utils/router-go';

  const postData = reactive({
    sub_mch_id: '',
    sub_mch_name: '',
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = saveSettingInfo();
  const formRef = ref<FormInstance>();

  function postToServer() {
    setInfo({
      data: postData,
    }).then(() => {
      isEdit.value = false;
      Message.success('保存成功');
    });
  }
  const isFirstSetting = ref(false);
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      if (isFirstSetting.value) {
        Modal.confirm({
          title: '保存配置',
          content: '一经保存将无法取消配置，确定保存？',
          onOk: () => {
            postToServer();
          },
        });
      } else {
        postToServer();
      }
    }
  };

  async function getInfo() {
    const res = await getSettingInfo();
    const resData = res.response.value.info;
    if (!resData.sub_mch_id) {
      isFirstSetting.value = true;
    } else {
      Object.assign(postData, resData);
    }
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
