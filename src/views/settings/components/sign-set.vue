<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="储值卡扫码入场">
          <a-switch v-model="postData.is_value_card_sign" :checked-value="1" :unchecked-value="0" />
        </a-form-item>
        <a-form-item v-if="postData.is_value_card_sign" label="储值卡扣费" :gutter="16">
          单次进场扣费（元/次）
          <a-col flex="auto">
            <a-input-number
              v-model="postData.value_card_sign_price"
              :min="1"
              :precision="2"
              style="width: 100%"></a-input-number>
          </a-col>
        </a-form-item>
        <a-form-item label="签到激活">
          <a-switch v-model="postData.allow_user_active_card" :checked-value="1" :unchecked-value="0" />
          会员签到时可自行启用“请假或未激活”的会员卡
        </a-form-item>
        <a-form-item label="签到定位" :gutter="16">
          <a-switch v-model="postData.is_bus_location_sign" :checked-value="1" :unchecked-value="0" />
          <span>扫码签到和签退必须在场馆周围</span>
          <a-col flex="auto">
            <a-input-number
              v-model="postData.bus_location_sign_range"
              :min="200"
              :max="3000"
              :precision="0"
              style="width: 100%"></a-input-number>
          </a-col>
          <span>米</span>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getSignSetting, updateSignSetting } from '@/api/sign-setting';

  const props = defineProps({
    isSwim: {
      type: Boolean,
      default: false,
    },
  });
  const postData = reactive({
    is_value_card_sign: 1,
    is_bus_location_sign: 1,
    value_card_sign_price: 2.5,
    allow_user_active_card: 0,
    bus_location_sign_range: 300,
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = updateSignSetting();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      setInfo({
        data: postData,
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getSignSetting({
      is_swim: props.isSwim ? 1 : 0,
    });
    const resData = res.data.value.info;
    Object.entries(postData).forEach(([key]) => {
      const resVal = resData[key];
      postData[key] = Number(resVal);
    });
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
