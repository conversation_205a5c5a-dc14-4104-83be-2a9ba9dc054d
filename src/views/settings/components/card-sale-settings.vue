<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form class="base-set-form" :model="postData" :style="{ width: '800px' }" auto-label-width>
        <a-form-item label="会籍允许售卖">
          <a-checkbox-group v-model="postData.membership_set" :disabled="!isEdit">
            <a-checkbox value="1">会籍卡</a-checkbox>
            <a-checkbox value="2">私教课</a-checkbox>
            <a-checkbox value="3">泳教课</a-checkbox>
            <a-checkbox value="4">套餐包</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="私教允许售卖">
          <a-checkbox-group v-model="postData.coach_pt_set" :disabled="!isEdit">
            <a-checkbox value="1">会籍卡</a-checkbox>
            <a-checkbox value="2">私教课</a-checkbox>
            <a-checkbox value="3">泳教课</a-checkbox>
            <a-checkbox value="4">套餐包</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="泳教允许售卖">
          <a-checkbox-group v-model="postData.coach_swim_set" :disabled="!isEdit">
            <a-checkbox value="1">会籍卡</a-checkbox>
            <a-checkbox value="2">私教课</a-checkbox>
            <a-checkbox value="3">泳教课</a-checkbox>
            <a-checkbox value="4">套餐包</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { getSaleSetting, saleSetting } from '@/api/package';

  const postData = reactive({
    membership_set: [],
    coach_pt_set: [],
    coach_swim_set: [],
  });
  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = saleSetting();
  const saveEdit = () => {
    setInfo({
      data: postData,
    }).then(() => {
      isEdit.value = false;
      Message.success('设置成功');
    });
  };
  function getInfo() {
    getSaleSetting().then((res) => {
      const resData = res.data.value;
      postData.membership_set = resData.membership_set;
      postData.coach_pt_set = resData.coach_pt_set;
      postData.coach_swim_set = resData.coach_swim_set;
    });
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };
  getInfo();
</script>

<style lang="less" scoped></style>
