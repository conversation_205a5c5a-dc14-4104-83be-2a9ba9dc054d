<template>
  <div class="base-set-box">
    <div class="base-set-content">
      <a-form
        ref="formRef"
        class="base-set-form"
        :disabled="!isEdit"
        :model="postData"
        :style="{ width: '800px' }"
        auto-label-width>
        <a-form-item label="会员端查看合同">
          <a-switch v-model="postData.see_status" checked-value="1" unchecked-value="0" />
        </a-form-item>
        <a-form-item label="会员端签署合同">
          <a-switch
            v-model="postData.electronic_status"
            checked-value="1"
            unchecked-value="0"
            @change="handleElectronicStatusChange" />
        </a-form-item>
        <a-form-item
          v-if="postData.electronic_status === '1'"
          label="实名认证次数"
          tooltip="实名认证不通过次数超过设置值后只能进行纸打合同签署">
          <a-input-number v-model="postData.user_auth_max" :precision="0" :min="1" style="width: 100%"></a-input-number>
        </a-form-item>
        <a-form-item v-if="postData.electronic_status === '1'" label="实名认证方式">
          <a-select v-model="postData.factors_person_auth" style="width: 100%">
            <a-option value="2">姓名+身份证 1合同券/次</a-option>
            <a-option value="3">姓名+电话+身份证号 1合同券/次</a-option>
            <a-option value="4">姓名+身份证+人脸识别 1.5合同券/次</a-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="postData.electronic_status === '1'" label="实名信息">
          <a-select v-model="postData.user_account_auth_type" style="width: 100%">
            <a-option value="1">仅能从系统中读取会员信息进行认证</a-option>
            <a-option value="2">会员可填写 /修改信息进行认证</a-option>
            <a-option value="0">会员仅能填写缺失信息进行认证</a-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="postData.electronic_status === '1' && postData.user_account_auth_type !== '1'"
          label="信息同步">
          <a-switch v-model="postData.user_sync_status" checked-value="1" unchecked-value="0" />
          <a-alert type="warning" style="margin-left: 16px">
            姓名 或 身份证信息 在实名认证成功后将同步修改系统中会员信息
          </a-alert>
        </a-form-item>
        <a-form-item label="合同签署类型">
          <a-table :data="postData.contract_type_setting_list" :pagination="false" style="width: 100%">
            <template #columns>
              <a-table-column title="业务类型" data-index="contract_type_name" />
              <a-table-column title="签署方式">
                <template #cell="{ record }">
                  <a-checkbox-group :model-value="record.sign_type" @change="handleSignTypeChange($event, record)">
                    <a-checkbox value="0">免签</a-checkbox>
                    <a-checkbox value="1" disabled>线下签署</a-checkbox>
                    <a-checkbox v-if="postData.electronic_status === '1'" value="2">会员端签署</a-checkbox>
                  </a-checkbox-group>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </a-form-item>

        <a-form-item label="未签合同" tooltip="针对新购、续、升级合同执行类型">
          <a-radio-group v-model="postData.card_disable">
            <a-radio value="1">会员卡/课无法使用</a-radio>
            <a-radio value="0">会员卡/课正常使用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item>
          <a-alert type="warning">以上全部设置保存后新合同按新签署规则执行，已有合同按之前设置执行</a-alert>
        </a-form-item>
      </a-form>
    </div>
    <div class="base-set-btns">
      <a-button v-show="!isEdit" type="primary" @click="isEdit = true">编辑</a-button>
      <a-space v-if="isEdit">
        <a-button type="primary" :loading="isLoading" @click="saveEdit">保存</a-button>
        <a-button @click="cancelEdit">取消</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message, Modal } from '@arco-design/web-vue';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { getContractSetting, updateContractSetting } from '@/api/contract-setting';
  import { useAdminInfoStore } from '@/store';
  import { goElectronic } from '@/utils/router-go';

  const adminInfo = useAdminInfoStore();
  interface ContractTypeSettingList {
    // 合同类型 '0 购卡 1 升卡 2 续卡 3 购私教 4 续私教 5 购泳教 6 续泳教 7 转卡 8 补卡 9 请假 10 销卡 11 拆分 12 租柜 13 跨店购卡 14 套餐包 ',
    contract_type: string;
    contract_type_name: string;
    // 合同类型设置id
    id: string;
    // 签署设置  后端与前端该值含义不同 后端为字符串：0免签 1仅线下签署 2会员端与线下签署 前端为数组: 0免签 1线下签署 2会员端签署
    sign_type: string;
  }

  interface PostDataInterface {
    see_status: string;
    // 0 未签合同 会员卡/课可以使用 1未签合同 会员卡/课不可使用
    card_disable: string;
    // 合同类型列表
    contract_type_setting_list: ContractTypeSettingList[];
    // 是否开启电子合同签署  0否1是
    electronic_status: string;
    // 电子签认证方式 2为二要素认证 3为三要素认证
    factors_person_auth: string;
    // 会员实名信息 0 会员仅能添写缺失信息 1 仅能从系统中读取会员信息 2 可填写/修改会员信息进行认证
    user_account_auth_type: string;
    // 实名认证机会错误上限
    user_auth_max: number;
    // 信息同步 0 不同步 1同步
    user_sync_status: string;
  }
  const postData = reactive<PostDataInterface>({
    see_status: '0',
    card_disable: '0',
    contract_type_setting_list: [],
    electronic_status: '0',
    factors_person_auth: '2',
    user_account_auth_type: '0',
    user_auth_max: 3,
    user_sync_status: '0',
  });

  const isEdit = ref(false);
  const { isLoading, execute: setInfo } = updateContractSetting();
  const formRef = ref<FormInstance>();
  const saveEdit = async () => {
    const errors = await formRef.value?.validate();
    if (!errors) {
      if (!(postData.electronic_status === '1' && postData.user_account_auth_type !== '1')) {
        postData.user_sync_status = '0';
      }
      setInfo({
        data: {
          ...postData,
          contract_type_setting_list: postData.contract_type_setting_list.map((item) => {
            // 保存时如果关闭了会员端签署合同选项
            if (postData.electronic_status === '0') {
              item.sign_type = item.sign_type.filter((num) => num !== '2');
            }
            return {
              ...item,
              // 转化为后端的值 0免签 1仅线下签署 2会员端与线下签署
              sign_type: item.sign_type.includes('2') ? '2' : item.sign_type[0],
            };
          }),
        },
      }).then(() => {
        isEdit.value = false;
        Message.success('设置成功');
      });
    }
  };
  async function getInfo() {
    const res = await getContractSetting();
    const resData = res.data.value;
    Object.assign(postData, resData);
    postData.user_auth_max = Number(resData.user_auth_max);
    postData.contract_type_setting_list = resData.contract_type_setting_list.map((item) => {
      return {
        id: item.id,
        contract_type: item.contract_type,
        contract_type_name: item.contract_type_name,
        sign_type: item.sign_type === '2' ? ['1', '2'] : [item.sign_type],
      };
    });
  }
  function handleSignTypeChange(val, info) {
    const oldVal = info.sign_type;
    if (val.includes('0') && !oldVal.includes('0')) {
      info.sign_type = ['0'];
    } else if (!val.includes('2')) {
      info.sign_type = ['1'];
    } else {
      info.sign_type = ['1', '2'];
    }
  }
  const cancelEdit = () => {
    isEdit.value = false;
    getInfo();
  };

  function handleElectronicStatusChange(val) {
    if (val === '1' && adminInfo.esign_status !== 1) {
      postData.electronic_status = '0';
      Modal.confirm({
        title: '提示',
        content: '请先开通电子合同功能',
        okText: '查看如何开通',
        cancelText: '取消',
        onOk: () => {
          goElectronic();
        },
      });
    }
  }

  getInfo();
</script>

<style lang="less" scoped></style>
