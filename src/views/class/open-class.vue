<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="region_bus" label="场馆/区域">
                  <admin-region
                    v-model="searchParam.region_bus"
                    :multiple="false"
                    placeholder="请选择"
                    :should-default="1"
                    @change="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="class_name" label="课程名称">
                  <a-input
                    v-model="searchParam.class_name"
                    placeholder="请输入"
                    allow-clear
                    @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="收费方案">
                  <a-select v-model="searchParam.class_charge_plan_id" placeholder="请选择" allow-search allow-clear>
                    <a-option value="">全部</a-option>
                    <a-option value="0">未配置</a-option>
                    <a-option v-for="item in chargeList" :key="item.id" :value="item.id">
                      {{ item.name }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="$router.push('/class/open-add')">添加课程</a-button>
            <a-button @click="handleSetClassCharge">设置收费方案</a-button>
            <a-button @click="$router.push('/class/charge-plane')">收费方案管理</a-button>
            <a-button :loading="isDelLoading" @click="batchDel">批量删除</a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportButton :data="exportPostParams" url="/Web/OpenClass/get_open_class_list" />
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-model:selectedKeys="selectedIds" v-on="tableEvent">
        <template #columns>
          <a-table-column title="课程名称" data-index="class_name" />
          <a-table-column title="预约" data-index="is_free_text" />
          <a-table-column title="最少开课人数" data-index="min_number" />
          <a-table-column title="支持场馆" data-index="support_store">
            <template #cell="{ record }">
              <a-link @click="handleShowDetailBus(record)">详情</a-link>
            </template>
          </a-table-column>
          <a-table-column title="收费方案" data-index="class_charge_plan_name">
            <template #cell="{ record }">
              <span v-if="record.is_free === '1'">-</span>
              <a-link
                v-else-if="record.class_charge_plan_id === '0' || !record.class_charge_plan_id"
                @click="handleSetOneCharge(record)">
                去设置
              </a-link>
              <a-link v-else @click="handleShowDetail(record)">{{ record.class_charge_plan_name }}</a-link>
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="handleChange(record)">编辑</a-link>
                <a-link status="danger" @click="handleDelete(record)">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <SetClassCharge
      v-model="isShowSetClassCharge"
      :is-frome-list-set="isFromeListSet"
      :class-ids="selectedIds"
      @on-success="setChargeSuccess"
      @on-before-close="handleSetClassChargeClose" />
    <BusListModal v-model="isShowBusList" :table-data="curBusList" name-key="name" />
    <ChargeDetailModal :id="chargeId" v-model="isShowDetail" :is-show-edit-btn="true" />
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { getClassChargePlanAllList } from '@/api/class-charge-plan';
  import { getOpenClassList, hiddenOpenClass, openclassSupportBus } from '@/api/open-class';
  import ExportButton from '@/components/form/export-button.vue';
  import BusListModal from '@/components/bus-list-modal/index.vue';
  import AdminRegion from '@/components/form/adminRegion.vue';
  import useTableProps from '@/hooks/table-props';
  import SetClassCharge from './components/set-class-charge.vue';
  import ChargeDetailModal from './components/charge-detail-modal.vue';

  defineOptions({
    name: 'OpenClass',
  });
  const router = useRouter();
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    getOpenClassList,
    (list) => {
      return list.map((item) => {
        return {
          ...item,
          is_free_text: item.is_free === '0' ? '需要预约' : '无需预约',
        };
      });
    }
  );
  tableProps.value['row-selection'] = {
    type: 'checkbox',
    showCheckedAll: true,
  };
  // 设置除分页外的其它属性值
  setSearchParam({
    class_name: '',
    region_bus: '',
    class_charge_plan_id: '',
  });
  const exportPostParams = computed(() => {
    return {
      ...searchParam,
      is_export: 1,
      current: 1,
      pageSize: tableProps.value.pagination.total,
    };
  });

  const curBusList = ref<Record<string, any>[]>([]);
  const isShowBusList = ref(false);
  function handleShowDetailBus(record: Record<string, any>) {
    const { id } = record;
    openclassSupportBus({ id }).then((res) => {
      curBusList.value = res.data.value;
      isShowBusList.value = true;
    });
  }
  // 跳转详情
  const handleChange = (record: Record<string, any>) => {
    router.push(`/class/open-add/${record.id}`);
  };

  const isShowSetClassCharge = ref(false);
  const isFromeListSet = ref(false);
  const selectedIds = ref([]);
  function handleSetClassCharge() {
    if (!selectedIds.value.length) {
      Message.error('请先勾选需要设置收费方案的课程');
      return;
    }
    isFromeListSet.value = false;
    isShowSetClassCharge.value = true;
  }
  function handleSetOneCharge(record: Record<string, any>) {
    selectedIds.value = [record.id];
    isFromeListSet.value = true;
    isShowSetClassCharge.value = true;
  }
  function setChargeSuccess() {
    selectedIds.value = [];
    loadTableList();
  }
  function handleSetClassChargeClose(info) {
    if (info.isFromeListSet) {
      selectedIds.value = [];
    }
  }
  // 删除确认
  const { isLoading: isDelLoading, execute: executeDel } = hiddenOpenClass();
  async function handleDeletePost(ids) {
    executeDel({ data: { ids } }).then((res) => {
      loadTableList();
      selectedIds.value = [];
      Message.success(res.response.value.errormsg);
    });
  }
  const handleDelete = (record: Record<string, any>) => {
    if (record.is_deal !== 1) {
      Message.error('账号操作权限不足，请联系管理员操作');
      return;
    }
    Modal.confirm({
      title: '提示',
      content: `确定要删除选中的课程吗？`,
      okText: '删除',
      onOk: () => {
        handleDeletePost(record.id);
      },
    });
  };

  // 批量删除
  function batchDel() {
    if (!selectedIds.value.length) {
      Message.error('请先勾选需要删除的课程');
      return;
    }
    if (tableProps.value.data[0].is_deal !== 1) {
      Message.error('账号操作权限不足，请联系管理员操作');
      return;
    }
    Modal.confirm({
      title: '删除课程',
      content: '确定要删除选中的课程吗？',
      onOk: () => {
        handleDeletePost(selectedIds.value.join());
      },
    });
  }

  const isShowDetail = ref(false);
  const chargeId = ref('');
  function handleShowDetail(record: Record<string, any>) {
    chargeId.value = record.class_charge_plan_id;
    isShowDetail.value = true;
  }

  const chargeList = ref([]);
  function getChargeAll() {
    getClassChargePlanAllList().then((res) => {
      const { list } = res.data.value;
      chargeList.value = list;
    });
  }
  getChargeAll();
</script>
