<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
        <a-form-item label="方案名称" field="name" :rules="{ required: true, message: '请填写' }">
          <a-input v-model="formData.name" />
          <a-button type="outline" style="margin-left: 20px" @click="isShowCopyModal = true">复制现有方案</a-button>
        </a-form-item>
        <a-form-item label="适用卡种" :content-flex="false">
          <a-radio-group v-model="formData.type" style="margin-bottom: 20px">
            <a-radio :value="1">按标签配置</a-radio>
            <a-radio :value="2">按卡种配置</a-radio>
          </a-radio-group>
          <a-spin :loading="!isDataReady" style="width: 100%">
            <ChargeBoxList v-if="isDataReady" ref="chargeBoxListRef" v-model="formData.detail" :type="formData.type" />
          </a-spin>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="handleCancel">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
    <PlaneTableModal v-if="isShowCopyModal" v-model:visible="isShowCopyModal" @confirm="handleCopyPlane" />
  </div>
</template>

<script lang="ts" setup>
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { addClassChargePlan, editClassChargePlan, getClassChargePlanInfo } from '@/api/class-charge-plan';
  import ChargeBoxList from './components/charge-box-list.vue';
  import PlaneTableModal from './components/plane-table-modal.vue';

  const INIT_CHARGE_PLAN = [
    {
      num: 1,
      price: null,
      pay_type: 1,
      list: [],
    },
  ];
  const INIT_LIST = [
    {
      card_type_id: 1,
      name: '期限卡',
      enabled: true,
      charge_plan: INIT_CHARGE_PLAN,
    },
    {
      card_type_id: 2,
      name: '次卡',
      enabled: false,
      charge_plan: INIT_CHARGE_PLAN,
    },
    {
      card_type_id: 3,
      name: '储值卡',
      enabled: false,
      charge_plan: INIT_CHARGE_PLAN,
    },
    {
      card_type_id: 4,
      name: '私教课',
      enabled: false,
      charge_plan: INIT_CHARGE_PLAN,
    },
    {
      card_type_id: 5,
      name: '泳教课',
      enabled: false,
      charge_plan: INIT_CHARGE_PLAN,
    },
  ];

  const formRef = ref<FormInstance>();
  const route = useRoute();
  const { id } = route.params;
  const formData = reactive({
    id: id || '',
    type: 1,
    name: '',
    detail: JSON.parse(JSON.stringify(INIT_LIST)),
  });

  const isDataReady = ref(false);
  if (!id) {
    isDataReady.value = true;
  }
  function getInfo(obj) {
    getClassChargePlanInfo(obj, true)
      .then((res) => {
        const info = res.data.value;
        info.detail = info.detail.map((item) => {
          return {
            ...item,
            charge_plan: item.charge_plan.map((i) => {
              return {
                ...i,
                list: i.list.map((j) => {
                  return {
                    value: j.id,
                    label: j.name,
                  };
                }),
              };
            }),
          };
        });
        Object.assign(formData, info);
        isDataReady.value = true;
      })
      .catch(() => {
        isDataReady.value = true;
      });
  }
  if (id) {
    getInfo({ id });
  }

  const { isLoading, execute: executeUpdate } = id ? editClassChargePlan() : addClassChargePlan();
  const router = useRouter();
  const chargeBoxListRef = ref();
  async function handleSubmit() {
    const boxValidate = await chargeBoxListRef.value.validate();
    if (!boxValidate) {
      Message.error('请先完善表单内容');
      return;
    }
    const errors = await formRef.value?.validate();
    const postDetailData = formData.detail.map((item) => {
      if (item.enabled) {
        return {
          ...item,
          charge_plan: item.charge_plan.map((i) => {
            return {
              ...i,
              list: i.list.map((j) => {
                return {
                  id: j.value,
                  name: j.label,
                };
              }),
            };
          }),
        };
      }
      return {
        ...item,
        charge_plan: item.charge_plan.map((i) => {
          return {
            ...i,
            list: [],
          };
        }),
      };
    });
    if (!errors) {
      executeUpdate({
        data: {
          ...formData,
          detail: JSON.stringify(postDetailData),
        },
      }).then(() => {
        Message.success('操作成功');
        router.replace('/class/charge-plane');
      });
    } else {
      Message.error('请先完善表单内容');
    }
  }

  const isShowCopyModal = ref(false);
  function handleCopyPlane(info) {
    info = {
      ...info,
      id: formData.id,
      name: formData.name,
      detail: info.detail.map((item) => {
        return {
          ...item,
          charge_plan: item.charge_plan.map((i) => {
            return {
              ...i,
              list: i.list.map((j) => {
                return {
                  value: j.id,
                  label: j.name,
                };
              }),
            };
          }),
        };
      }),
    };
    isDataReady.value = false;
    Object.assign(formData, info);
    nextTick(() => {
      isDataReady.value = true;
    });
  }

  const handleCancel = () => {
    const hasPreviousPage = router.getRoutes().length > 1;
    if (hasPreviousPage) {
      router.back();
    } else {
      router.replace('/class/charge-plane');
    }
  };
</script>
