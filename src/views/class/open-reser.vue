<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="belong_bus_id" label="场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" placeholder="请选择" @change="handleBusChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="时间">
                  <a-range-picker v-model="rangeValue" :allow-clear="false" @change="handleTimeChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="coach_id" label="教练">
                  <a-select v-model="searchParam.coach_id" placeholder="请选择" allow-clear allow-search>
                    <a-option v-for="item in coachList" :key="item.coach_id" :value="item.coach_id">
                      {{ item.coach_name }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="class_id" label="课程">
                  <a-select v-model="searchParam.class_id" placeholder="请选择" allow-clear allow-search>
                    <a-option v-for="item in classList" :key="item.id" :value="item.id">{{ item.class_name }}</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="router.push('/v2/reservation/class/resProtocol')">团课预约协议</a-button>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportButton :data="exportPostParams" url="/Web/ClassMark/pc_class_mark_list" />
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column title="课程名称" data-index="class_name" />
          <a-table-column title="开课时间" data-index="b_time" />
          <a-table-column title="课程教练" data-index="coach_name" />
          <a-table-column title="教室" data-index="classroom_name" />
          <a-table-column title="可预约人数" data-index="reserve_number" />
          <a-table-column title="已预约人数" data-index="sncount">
            <template #cell="{ record }">
              <a-link
                @click="router.push(`/class/open-reser-detail/${record.schedule_id}?busId=${searchParam.bus_id}`)">
                {{ record.sncount }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="订阅人数" data-index="wait_count">
            <template #cell="{ record }">
              <a-link
                @click="router.push(`/class/open-reser-subscription/${record.schedule_id}/${searchParam.bus_id}`)">
                {{ record.wait_count }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="handleReser(record)">预约</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <AddOpenReser
      v-model="isShowReserModal"
      :bus-id="searchParam.bus_id"
      :current-class="currentClass"
      @confirm="loadTableList" />
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { useBusInfoStore } from '@/store';
  import ExportButton from '@/components/form/export-button.vue';
  import { pcClassMarkList } from '@/api/class-mark';
  import useTableProps from '@/hooks/table-props';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import AddOpenReser from './components/add-open-reser.vue';

  defineOptions({
    name: 'OpenReser',
  });
  const busInfo = useBusInfoStore();
  const router = useRouter();
  const coachList = ref([]);
  const classList = ref([]);
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, dataPath, loadTableList } = useTableProps(
    pcClassMarkList,
    (list) => {
      return list.map((item) => {
        return {
          ...item,
          b_time: item.b_time ? dayjs(item.b_time * 1000).format('YYYY-MM-DD HH:mm') : '',
        };
      });
    },
    (res) => {
      coachList.value = res.data.value.coach_list;
      classList.value = res.data.value.open_class_list;
    }
  );
  dataPath.value = {
    list: 'class_mark_list',
    count: 'count',
  };
  const route = useRoute();
  // const rangeValue = ref<string[]>([dayjs().format('YYYY-MM-DD'), dayjs().add(5, 'd').format('YYYY-MM-DD')]);
  const rangeValue = ref<string[]>([
    route.query.startDate?.toString() || dayjs().format('YYYY-MM-DD'),
    route.query.endDate?.toString() || dayjs().add(5, 'd').format('YYYY-MM-DD'),
  ]);
  if (route.query.date) {
    const date = dayjs(route.query.date as string).format('YYYY-MM-DD');
    rangeValue.value = [date, date];
  }
  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: route.query.busId || busInfo.bus_id || '',
    is_export: 0,
    s_date: rangeValue.value[0],
    e_date: rangeValue.value[1],
    class_id: route.query.classId ? route.query.classId : '',
    coach_id: route.query.coachId ? route.query.coachId : '',
  });
  const exportPostParams = computed(() => {
    const params: any = {
      ...searchParam,
      is_export: 1,
      current: 1,
      pageSize: tableProps.value.pagination.total,
    };
    return params;
  });
  onActivated(() => {
    loadTableList();
  });
  function handleTimeChange(params: any) {
    const [beginTime, endTime] = params || ['', ''];
    searchParam.s_date = beginTime;
    searchParam.e_date = endTime;
  }

  const isShowReserModal = ref(false);
  const currentClass = ref({});
  function handleReser(record: any) {
    currentClass.value = record;
    isShowReserModal.value = true;
  }
  function handleBusChange() {
    setSearchParam({ coach_id: '', class_id: '' });
    handleSearch();
  }
</script>
