<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="search" label="会员">
                  <a-input v-model="searchParam.search" placeholder="姓名/电话" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" :loading="isBatchSignLoading" @click="batchSignReservation">批量签到</a-button>
            <a-button :loading="isBatchCancelLoading" @click="batchCancelReservation">批量取消</a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel ref="exportExcel">
            <template #default="{ handleExport }">
              <a-button @click="handleClickExport(handleExport as Callback<ExportData>)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-model:selectedKeys="selectedIds" v-on="tableEvent">
        <template #columns>
          <a-table-column title="序号" align="center">
            <template #cell="{ rowIndex }">
              {{
                tableProps.pagination
                  ? (tableProps.pagination.current - 1) * tableProps.pagination.pageSize + rowIndex + 1
                  : rowIndex + 1
              }}
            </template>
          </a-table-column>
          <a-table-column title="头像" data-index="avatar">
            <template #cell="{ record }">
              <a-avatar :image-url="record.avatar"></a-avatar>
            </template>
          </a-table-column>
          <a-table-column title="昵称" data-index="username" align="center">
            <template #cell="{ record }">
              <a-link
                :href="goSubDetail(record.user_id, record.bus_id, false)"
                @click.prevent="goSubDetail(record.user_id, record.bus_id)">
                {{ record.username || record.nickname || '--' }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="预约用卡" data-index="c_name" />
          <a-table-column title="预约人数" data-index="sign_number" />
          <a-table-column title="座位号" data-index="seats_num">
            <template #cell="{ record }">
              <a-link v-if="record.seats_num" @click="isShowSeats = true">{{ record.seats_num }}</a-link>
              <span v-else>-</span>
            </template>
          </a-table-column>
          <a-table-column title="状态" data-index="status_name" />
          <a-table-column title="预约时间" data-index="create_time" />
          <a-table-column title="取消时间" data-index="cancel_time" />
          <a-table-column title="取消方式" data-index="cancel_type_name" />
          <a-table-column title="操作人" data-index="cancel_username" />

          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link :loading="record.isSignLoading" :disabled="record.status !== '1'" @click="goSign(record)">
                  签到
                </a-link>
                <a-link
                  status="danger"
                  :loading="record.isCancelLoading"
                  :disabled="record.status !== '1'"
                  @click="cancelReservation(record)">
                  取消预约
                </a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <a-modal v-model:visible="isShowSeats" title="座位排布">
      <SeatsSet v-if="seatInfo && seatInfo.length" v-model="seatInfo" :is-show-set="false" :is-can-choose="false" />
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { Modal, Message } from '@arco-design/web-vue';
  import { useBusInfoStore } from '@/store';
  import { classMarkDetail, cancelClassMark, batchCancelClassMark } from '@/api/class-mark';
  import { userSign, batchUserSign } from '@/api/sign';
  import { goSubDetail } from '@/utils/router-go';
  import useTableProps from '@/hooks/table-props';
  import SeatsSet from '@/components/seats-set/index.vue';
  import ExportExcel from '@/components/exportExcel.vue';
  import { Callback, ExportData } from '@/types/global';

  const selectedIds = ref([]);
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, dataPath, loadTableList } = useTableProps(
    classMarkDetail,
    (list: Record<string, any>[]) => {
      return list.map((item: Record<string, any>) => {
        item.create_time = dayjs(item.create_time * 1000).format('YYYY-MM-DD HH:mm');
        item.cancel_time = item.cancel_time ? item.cancel_time : '-';
        item.disabled = item.status !== '1' || item.outer_type === 1;
        item.status_name = item.status === '1' ? '已预约' : item.status === '2' ? '已完成' : '已取消';
        item.cancel_type_name =
          item.cancel_type === '1'
            ? '会员取消'
            : item.cancel_type === '2'
            ? '前台取消'
            : item.cancel_type === '3'
            ? '系统取消'
            : '-';
        return item;
      });
    }
  );
  tableProps.value['row-selection'] = {
    type: 'checkbox',
    showCheckedAll: true,
  };

  const busInfo = useBusInfoStore();
  const route = useRoute();
  // 设置除分页外的其它属性值
  dataPath.value = {
    list: 'cm_detail_list',
    count: 'count',
  };
  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: route.query.busId || busInfo.bus_id || '',
    course_schedule_id: route.params.id,
    search: '',
  });
  const seatInfo = ref([]);
  function getList() {
    loadTableList().then((res) => {
      seatInfo.value = res.data.value.seats_list;
    });
  }
  getList();

  function goSign(record: Record<string, any>) {
    const { isLoading, execute: executeSign } = userSign();
    record.isSignLoading = isLoading;
    executeSign({ data: { class_mark_id: record.id, type: 0, bus_id: record.bus_id } }).then((res) => {
      getList();
      Message.success(res.response.value.errormsg);
    });
  }
  function cancelReservation(record: Record<string, any>) {
    const { isLoading, execute: executeCancel } = cancelClassMark();
    record.isCancelLoading = isLoading;
    Modal.confirm({
      title: '提示',
      content: '取消预约将原路退回预约花费，是否确定取消?',
      onOk: () => {
        executeCancel({ data: { class_mark_id: record.id, bus_id: record.bus_id } }).then((res) => {
          getList();
          Message.success(res.response.value.errormsg);
        });
      },
    });
  }

  // 批量签到
  const { isLoading: isBatchSignLoading, execute: executeBatchSign } = batchUserSign();
  function batchSignReservation() {
    if (!selectedIds.value.length) {
      Message.error('请先勾选需要签到的预约');
      return;
    }
    Modal.confirm({
      title: '提示',
      content: '是否确认要批量签到?',
      onOk: () => {
        executeBatchSign({ data: { class_mark_ids: selectedIds.value, type: 0, bus_id: searchParam.bus_id } }).then(
          (res) => {
            selectedIds.value = [];
            getList();
            Message.success(res.response.value.errormsg);
          }
        );
      },
    });
  }

  // 批量取消
  const { isLoading: isBatchCancelLoading, execute: executeBatchCancel } = batchCancelClassMark();
  function batchCancelReservation() {
    if (!selectedIds.value.length) {
      Message.error('请先勾选需要取消的预约');
      return;
    }
    Modal.confirm({
      title: '提示',
      content: '是否确认取消选中的预约?',
      onOk: () => {
        executeBatchCancel({ data: { class_mark_ids: selectedIds.value, bus_id: searchParam.bus_id } }).then((res) => {
          selectedIds.value = [];
          getList();
          Message.success(res.response.value.errormsg);
        });
      },
    });
  }
  const isShowSeats = ref(false);

  const handleClickExport = (cb: Callback<ExportData>) => {
    const columns = [
      {
        title: '序号',
        dataIndex: 'index',
      },
      {
        title: '昵称',
        dataIndex: 'username',
      },
      {
        title: '预约用卡',
        dataIndex: 'c_name',
      },
      {
        title: '预约人数',
        dataIndex: 'sign_number',
      },
      {
        title: '座位号',
        dataIndex: 'seats_num',
      },
      {
        title: '状态',
        dataIndex: 'status_name',
      },
      {
        title: '预约时间',
        dataIndex: 'create_time',
      },
      {
        title: '取消时间',
        dataIndex: 'cancel_time',
      },
      {
        title: '取消方式',
        dataIndex: 'cancel_type_name',
      },
      {
        title: '操作人',
        dataIndex: 'cancel_username',
      },
    ];
    loadTableList(true).then((list) => {
      cb({
        filename: '预约详情',
        columns,
        data: list.map((item: Record<string, any>, index: number) => {
          return {
            index: index + 1,
            ...item,
          };
        }),
      });
    });
  };
</script>
