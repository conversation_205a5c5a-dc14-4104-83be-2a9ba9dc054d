<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="bus_id" label="场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" placeholder="请选择" @change="handleBusChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="name" label="方案名称">
                  <a-input v-model="searchParam.name" placeholder="请输入" allow-clear />
                </a-form-item>
              </a-col>
              <a-col v-if="searchParam.bus_id" :span="8">
                <a-form-item field="class_id" label="课程">
                  <a-select v-model="searchParam.class_id" placeholder="请选择" allow-clear allow-search>
                    <a-option v-for="item in classList" :key="item.id" :value="item.id">{{ item.class_name }}</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="$router.push('/class/charge-plane-detail')">新增方案</a-button>
            <a-button :loading="isBatchCancelLoading" @click="batchDel">批量删除</a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel ref="exportExcel">
            <template #default="{ handleExport }">
              <a-button @click="handleClickExport(handleExport as Callback<ExportData>)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-model:selectedKeys="selectedIds" v-on="tableEvent">
        <template #columns>
          <a-table-column title="方案名称" data-index="name" />
          <a-table-column title="使用课程" data-index="use_class" ellipsis tooltip />
          <a-table-column title="标准详情" data-index="use_class">
            <template #cell="{ record }">
              <a-link @click="handleShowDetail(record)">查看</a-link>
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="$router.push('/class/charge-plane-detail/' + record.id)">编辑</a-link>
                <a-link status="danger" :loading="record.isDelLoading" @click="delPlan(record)">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <ChargeDetailModal :id="chargeId" v-model="isShowDetail" />
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { getClassChargePlanList, delClassChargePlan } from '@/api/class-charge-plan';
  import { useBusInfoStore } from '@/store';
  import { getOpenClassAll } from '@/api/open-class';
  import useTableProps from '@/hooks/table-props';
  import ExportExcel from '@/components/exportExcel.vue';
  import { Callback, ExportData } from '@/types/global';
  import ChargeDetailModal from './components/charge-detail-modal.vue';

  const selectedIds = ref([]);
  const busInfo = useBusInfoStore();
  const classList = ref([]);
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getClassChargePlanList);

  tableProps.value['row-selection'] = {
    type: 'checkbox',
    showCheckedAll: true,
  };
  const route = useRoute();
  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: route.params.bus_id || busInfo.bus_id,
    class_id: route.params.id,
    search: '',
  });

  function getOpenClassList() {
    getOpenClassAll({
      bus_id: searchParam.bus_id,
    }).then((res) => {
      classList.value = res.data.value.list;
    });
  }
  getOpenClassList();
  loadTableList();

  function handleBusChange() {
    searchParam.class_id = '';
    if (searchParam.bus_id) {
      getOpenClassList();
    } else {
      classList.value = [];
    }
  }

  function delPlan(record: Record<string, any>) {
    const { isLoading, execute: executeDel } = delClassChargePlan();
    record.isDelLoading = isLoading;
    Modal.confirm({
      title: '删除方案',
      content: '若关联课程已经排课将影响排课预约收费，确定要删除?',
      onOk: () => {
        executeDel({ data: { id: [record.id] } }).then((res) => {
          loadTableList();
          Message.success(res.response.value.errormsg);
        });
      },
    });
  }

  const isShowDetail = ref(false);
  const chargeId = ref('');
  function handleShowDetail(record: Record<string, any>) {
    chargeId.value = record.id;
    isShowDetail.value = true;
  }

  // 批量删除
  const { isLoading: isBatchCancelLoading, execute: executeBatchDel } = delClassChargePlan();
  function batchDel() {
    if (!selectedIds.value.length) {
      Message.error('请先勾选需要删除的方案');
      return;
    }
    Modal.confirm({
      title: '删除方案',
      content: '若关联课程已经排课将影响排课预约收费，确定要删除?',
      onOk: () => {
        executeBatchDel({ data: { id: selectedIds.value } }).then((res) => {
          console.log(res);
          selectedIds.value = [];
          loadTableList();
          Message.success(res.response.value.errormsg);
        });
      },
    });
  }

  const handleClickExport = (cb: Callback<ExportData>) => {
    const columns = [
      {
        title: '方案名称',
        dataIndex: 'name',
      },
      {
        title: '使用课程',
        dataIndex: 'use_class',
      },
    ];
    loadTableList(true).then((list) => {
      cb({
        filename: '团课方案列表',
        columns,
        data: list,
      });
    });
  };
</script>
