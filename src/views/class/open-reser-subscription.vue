<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="search" label="会员">
                  <a-input v-model="searchParam.search" placeholder="姓名/电话" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-table
        v-bind="tableProps"
        :pagination="{
          ...tableProps.pagination,
          showTotal: true,
          showPageSize: true,
          pageSizeOptions: [10, 20, 30, 40, 50],
          showJumper: true,
        }"
        v-on="tableEvent">
        <template #columns>
          <a-table-column title="序号" align="center">
            <template #cell="{ rowIndex }">
              {{
                tableProps.pagination
                  ? (tableProps.pagination.current - 1) * tableProps.pagination.pageSize + rowIndex + 1
                  : rowIndex + 1
              }}
            </template>
          </a-table-column>
          <a-table-column title="昵称" data-index="username" align="center">
            <template #cell="{ record }">
              <a-link
                :href="goSubDetail(record.user_id, record.bus_id, false)"
                @click.prevent="goSubDetail(record.user_id, record.bus_id)">
                {{ record.username || record.nickname || '--' }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="订阅时间" data-index="sub_time" align="center" />
          <!-- 状态 0未预约 1已预约 -->
          <a-table-column title="预约状态" data-index="mark_status" align="center">
            <template #cell="{ record }">
              <a-tag :color="record.mark_status == 0 ? 'orange' : 'green'">
                {{ record.mark_status == 0 ? '未预约' : '已预约' }}
              </a-tag>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { useBusInfoStore } from '@/store';
  import { pcClassSubscriptionList } from '@/api/class-mark';
  import useTableProps from '@/hooks/table-props';
  import { goSubDetail } from '@/utils/router-go';

  defineOptions({
    name: 'ClassSubscription',
  });

  const busInfo = useBusInfoStore();

  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, dataPath, loadTableList } =
    useTableProps(pcClassSubscriptionList);

  const props = defineProps({
    scheduleId: {
      type: String,
      default: '',
    },
    busId: {
      type: String,
      default: '',
    },
  });
  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: props.busId || busInfo.bus_id || '',
    search: '',
    course_schedule_id: props.scheduleId || '',
  });

  // 设置除分页外的其它属性值
  dataPath.value = {
    list: 'list',
    count: 'count',
  };

  onMounted(() => {
    loadTableList();
  });
</script>

<style scoped>
  .arco-table-tr-disabled .arco-table-td {
    background-color: var(--color-neutral-2);
    color: var(--color-text-3);
  }
</style>
