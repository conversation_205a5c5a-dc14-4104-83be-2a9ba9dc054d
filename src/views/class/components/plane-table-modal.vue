<template>
  <a-modal v-model:visible="isShowModal" title="会员收费方案" :width="720" :on-before-ok="handleOk">
    <a-row>
      <a-col :flex="1">
        <a-form
          :model="searchParam"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="name" label="方案">
                <a-input v-model="searchParam.name" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="class_id" label="课程">
                <a-select v-model="searchParam.class_id" placeholder="请选择" allow-clear allow-search>
                  <a-option v-for="item in classList" :key="item.id" :value="item.id">{{ item.class_name }}</a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
      <a-divider style="height: 32px" direction="vertical" />
      <a-col :flex="'86px'" style="text-align: right">
        <a-space direction="vertical" :size="18">
          <a-button type="primary" @click="handleSearch">
            <template #icon>
              <icon-search />
            </template>
            搜索
          </a-button>
        </a-space>
      </a-col>
    </a-row>

    <a-table v-bind="tableProps" v-model:selectedKeys="selectedIds" v-on="tableEvent">
      <template #columns>
        <a-table-column title="收费方案" data-index="name" />
        <a-table-column title="使用方案课程" data-index="use_class" ellipsis tooltip />
        <a-table-column title="价格详情" data-index="use_class">
          <template #cell="{ record }">
            <a-link @click="handleShowDetail(record)">查看</a-link>
          </template>
        </a-table-column>
      </template>
    </a-table>
    <ChargeDetailModal :id="chargeId" v-model="isShowDetail" />
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getClassChargePlanList, getClassChargePlanInfo } from '@/api/class-charge-plan';
  import { getOpenClassAll } from '@/api/open-class';
  import useTableProps from '@/hooks/table-props';
  import ChargeDetailModal from './charge-detail-modal.vue';

  const props = defineProps<{
    modelValue?: boolean;
    selectedId?: string | number;
  }>();
  const emits = defineEmits(['update:modelValue', 'confirm']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const selectedIds = ref([]);
  watch(
    () => props.selectedId,
    (val) => {
      selectedIds.value = +val ? [+val] : [];
    },
    { immediate: true }
  );
  const classList = ref([]);
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getClassChargePlanList);
  tableProps.value['row-selection'] = {
    type: 'radio',
  };
  const route = useRoute();
  // 设置除分页外的其它属性值
  setSearchParam({
    course_schedule_id: route.params.id,
    search: '',
  });

  function getOpenClassList() {
    getOpenClassAll().then((res) => {
      classList.value = res.data.value.list;
    });
  }
  getOpenClassList();
  loadTableList();
  const isShowDetail = ref(false);
  const chargeId = ref('');
  function handleShowDetail(record: Record<string, any>) {
    chargeId.value = record.id;
    isShowDetail.value = true;
  }

  const { execute } = getClassChargePlanInfo();
  const handleOk = async () => {
    if (selectedIds.value.length === 0) {
      Message.error('请先勾选需要的方案');
      return false;
    }
    const res = await execute({ data: { id: selectedIds.value[0] } });
    emits('confirm', res.data.value);
    return true;
  };
</script>
