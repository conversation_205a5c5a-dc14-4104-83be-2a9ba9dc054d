<template>
  <a-modal
    v-model:visible="isShowModal"
    :title="pickUpFlag ? '选择本周排课复制' : '复制本周排课'"
    :width="720"
    :mask-closable="false"
    :ok-loading="isLoading"
    :on-before-ok="handleConfirm"
    @cancel="handleCancel">
    <p class="modal-txt">
      将
      <strong>{{ weekDateString }}</strong>
      的课程复制到
    </p>
    <a-checkbox-group v-model="checkCopyGroup" class="copy-group" direction="vertical">
      <a-checkbox v-for="(item, index) in copyWeekList" :key="index" class="modal-txt" :value="item.start">
        {{ item.start }} 至 {{ item.end }}
        <span v-if="index === 0">({{ isThisWeek ? '本周' : '下周' }})</span>
      </a-checkbox>
    </a-checkbox-group>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { scheduleCopyThisWeek } from '@/api/course-schedule';
  import dayjs from '@/utils/dayjs';

  const props = defineProps<{
    modelValue: boolean;
    pickUpFlag: boolean;
    pickUpIds: string[];
    weekDateString: string;
    searchParam: Record<string, any>;
  }>();
  const checkCopyGroup = ref([]);
  const emits = defineEmits(['update:modelValue', 'success', 'cancel']);
  const copyWeekList = ref([]);

  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const isThisWeek = ref(false);
  function initCopyWeekList() {
    let start = dayjs().weekday(0).format('YYYY-MM-DD');
    let end = dayjs().weekday(6).format('YYYY-MM-DD');
    const today = dayjs().format('YYYY-MM-DD');
    console.log(dayjs().weekday(0).format('YYYY-MM-DD'), props.searchParam.s_date);
    if (props.searchParam.s_date <= today && today <= props.searchParam.e_date) {
      isThisWeek.value = false;
      start = dayjs(start).add(7, 'day').format('YYYY-MM-DD');
      end = dayjs(end).add(7, 'day').format('YYYY-MM-DD');
    } else {
      isThisWeek.value = true;
    }
    copyWeekList.value = [];
    for (let i = 0; i < 4; i += 1) {
      copyWeekList.value.push({
        start: dayjs(start)
          .add(i * 7, 'day')
          .format('YYYY-MM-DD'),
        end: dayjs(end)
          .add(i * 7, 'day')
          .format('YYYY-MM-DD'),
      });
    }
  }
  watch(
    () => isShowModal.value,
    (val) => {
      if (val) {
        initCopyWeekList();
      }
    }
  );

  const { isLoading, execute: setInfo } = scheduleCopyThisWeek();
  async function handleConfirm() {
    const { s_date, e_date, bus_id } = props.searchParam;
    await setInfo({
      data: {
        s_date,
        e_date,
        copy_s_time: checkCopyGroup.value,
        id: props.pickUpIds,
        bus_id,
      },
    });
    Message.success('复制成功');
    emits('success', checkCopyGroup.value);
    checkCopyGroup.value = [];
    return true;
  }
  function handleCancel() {
    emits('cancel');
  }
</script>

<style lang="less" scoped>
  .modal-txt {
    font-size: 14px;
    margin-bottom: 12px;

    strong {
      color: red;
    }
  }
</style>
