<template>
  <a-modal
    v-model:visible="isShowModal"
    title="复制排课"
    :width="720"
    :mask-closable="false"
    :ok-loading="isLoading"
    :on-before-ok="handleConfirm"
    @cancel="handleCancel">
    <p v-for="(item, index) in dateList" :key="index" class="modal-line">
      将
      <strong>{{ info.class_name }}</strong>
      复制到
      <a-date-picker v-model="dateList[index]" format="YYYY-MM-DD" type="date" placeholder="请选择排课日期" />
      <a-button v-if="!(dateList.length === 1)" type="outline" @click="dateList.splice(index, 1)">删除</a-button>
      <a-button v-if="index === dateList.length - 1 && dateList.length !== 7" type="outline" @click="dateList.push('')">
        添加
      </a-button>
    </p>
  </a-modal>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { Message } from '@arco-design/web-vue';
  import { scheduleCopyOnce } from '@/api/course-schedule';

  const props = defineProps<{
    modelValue: boolean;
    searchParam: Record<string, any>;
    info: Record<string, any>;
  }>();
  const dateList = ref(['']);
  const emits = defineEmits(['update:modelValue', 'success', 'cancel']);

  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  watch(
    () => props.modelValue,
    (value) => {
      if (!value) {
        dateList.value = [''];
      }
    }
  );

  const { isLoading, execute: setInfo } = scheduleCopyOnce();
  async function handleConfirm() {
    const { bus_id } = props.searchParam;
    const res = await setInfo({
      data: {
        schedule_id: props.info.id,
        copy_time: dateList.value,
        bus_id,
      },
    });
    Message.success(res.response.value.errormsg);
    emits('success');
    return true;
  }
  function handleCancel() {
    emits('cancel');
  }
</script>

<style lang="less" scoped>
  .modal-line {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 40px;
    font-size: 14px;

    * {
      margin: 0 4px;
    }
  }
</style>
