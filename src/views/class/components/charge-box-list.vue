<template>
  <a-card v-for="(postData, index) in listData" :key="index" class="mgb20" dis-hover>
    <template #title>
      <a-space>
        <a-switch v-model="listData[index].enabled" />
        <div style="font-size: 14px">{{ listData[index].name }}</div>
      </a-space>
    </template>
    <ChargePlaneBox
      v-show="listData[index].enabled"
      :ref="(el: refItem) => setRefMap(el, index)"
      v-model="listData[index].charge_plan"
      :type="type"
      :name="listData[index].name"
      :card-type-id="postData.card_type_id"
      :wx-pay-auth="wxPayAuth"
      :card-list="getFilterCardList(postData.card_type_id)"
      :tag-list="getFilterTagList(postData.card_type_id === 4 ? 1 : postData.card_type_id === 5 ? 3 : 2)" />
  </a-card>
</template>

<script lang="ts" setup>
  import { getCardGroupList } from '@/api/card-group';
  import { getMerchantsCard } from '@/api/card';
  import { wxPayMarkClass } from '@/api/class-charge-plan';
  import ChargePlaneBox from './charge-plane-box.vue';

  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, any>[];
      type: number;
    }>(),
    {
      modelValue: () => [],
      type: 1,
    }
  );
  const emits = defineEmits(['update:modelValue']);
  const listData = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emits('update:modelValue', value);
    },
  });

  const allCardList = ref<Record<string, any>[]>([]);
  const allTagList = ref<Record<string, any>[]>([]);
  function getCardList() {
    getMerchantsCard().then((res) => {
      allCardList.value = res.data.value.list;
    });
  }
  function getTagList() {
    getCardGroupList({ type: 0 }).then((res) => {
      allTagList.value = res.data.value;
    });
  }
  function getFilterCardList(type: number) {
    return allCardList.value.filter((item) => +item.card_type_id === type);
  }
  // 1私教，2会籍，3泳教
  function getFilterTagList(type: number) {
    return allTagList.value.filter((item) => +item.type === type);
  }
  type refItem = Element | ComponentPublicInstance | null;
  const refMap: Record<number, refItem> = reactive({});
  const setRefMap = (el: refItem, index: number) => {
    if (el) {
      refMap[index] = el;
    }
  };
  async function validate() {
    // 排除enabled为false的数据
    const promiseArr = Object.keys(refMap).map((key) => {
      if (!listData.value[+key].enabled) {
        return Promise.resolve(true);
      }
      return refMap[+key].validate();
    });
    const res = await Promise.all(promiseArr);
    if (res.some((item) => item === false)) {
      return false;
    }
    return true;
  }
  const wxPayAuth = ref(false);
  function getWxPayAuth() {
    wxPayMarkClass().then((res) => {
      wxPayAuth.value = res.response.value.errorcode === 0;
    });
  }
  getCardList();
  getTagList();
  getWxPayAuth();
  defineExpose({
    validate,
  });
</script>

<style lang="less" scoped>
  .mgb20 {
    margin-bottom: 20px;
  }
</style>
