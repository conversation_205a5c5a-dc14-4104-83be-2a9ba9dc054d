<template>
  <a-modal
    v-model:visible="isShowModal"
    title="设置收费方案"
    ok-text="保存"
    :width="720"
    :mask-closable="false"
    :on-before-ok="hansleConfirm"
    @before-close="handleBeforeClose">
    <div v-if="!isFromeListSet" class="tips">将替换课程现有收费方案，并更新已排课课程</div>
    <a-form ref="formRef" class="base-set-form" :model="formData" :style="{ width: '100%' }" auto-label-width>
      <a-form-item label="收费方案" field="id">
        <a-select v-model="formData.id" style="flex: 1" allow-search allow-clear placeholder="请选择">
          <a-option v-for="item in chargeList" :key="item.id" :value="item.id">
            {{ item.name }}
          </a-option>
        </a-select>
        <a-link v-if="isFromeListSet" style="margin-left: 8px" @click="handleAddPlan()">新增方案</a-link>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getClassChargePlanAllList } from '@/api/class-charge-plan';
  import { setClassChargePlan } from '@/api/open-class';

  const props = defineProps<{
    modelValue: boolean;
    isFromeListSet: boolean; // 从课种列表没有方案的行点击去设置进入
    classIds: string[];
    planId?: string;
    courseScheduleId?: string; // 从课程排课编辑过来需要
  }>();
  const emits = defineEmits(['update:modelValue', 'onSuccess', 'onBeforeClose']);
  const formData = reactive({
    id: '',
    class_ids: [],
  });
  watch(
    () => props.classIds,
    (ids) => {
      formData.class_ids = ids;
    },
    { immediate: true }
  );
  watch(
    () => props.planId,
    (id) => {
      formData.id = id ? Number(id) : '';
    },
    { immediate: true }
  );

  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const chargeList = ref([]);
  function getChargeAll() {
    getClassChargePlanAllList().then((res) => {
      const { list } = res.data.value;
      chargeList.value = list;
    });
  }

  watch(
    () => isShowModal.value,
    (value) => {
      if (!value) {
        formData.id = '';
        formData.class_ids = [];
      } else {
        getChargeAll();
      }
    },
    { immediate: true }
  );

  const { execute: setInfo } = setClassChargePlan();
  const formRef = ref();
  async function hansleConfirm() {
    try {
      await setInfo({
        data: {
          ...formData,
          course_schedule_id: props.courseScheduleId || '',
        },
      });
      Message.success('设置成功！');
      emits('onSuccess');
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }
  function handleBeforeClose() {
    emits('onBeforeClose', { isFromeListSet: props.isFromeListSet });
  }
  const shouldReLoadList = ref(false);
  function handleAddPlan() {
    shouldReLoadList.value = true;
    window.open('/class/charge-plane-detail');
  }
  function handleVisibilityChange() {
    if (document.visibilityState === 'visible' && shouldReLoadList.value) {
      shouldReLoadList.value = false;
      getChargeAll();
    }
  }
  onMounted(() => {
    window.addEventListener('visibilitychange', handleVisibilityChange, false);
  });
  onUnmounted(() => {
    window.removeEventListener('visibilitychange', handleVisibilityChange, false);
  });
</script>

<style lang="less" scoped>
  .tips {
    margin-bottom: 16px;
    text-align: center;
  }
</style>
