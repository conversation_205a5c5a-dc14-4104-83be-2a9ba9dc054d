<template>
  <div>
    <a-row v-for="(item, index) in chargePlanData" :key="index">
      <a-col :flex="1">
        <a-form :ref="(el: refItem) => setRefMap(el, index)" :model="chargePlanData[index]" layout="inline">
          <a-form-item
            field="list"
            style="width: 100%"
            :label="type === 1 ? '标签' : '会员卡'"
            label-col-flex="70px"
            :rules="{ required: true, message: '请选择' }">
            <TreeCardSelect
              v-model="chargePlanData[index].list"
              :data="type === 1 ? initTagDataArr : initCardDataArr"
              :disabled-ids="getDisabledIds(index)"
              style="width: 100%"></TreeCardSelect>
          </a-form-item>
          <a-form-item
            style="flex: 1"
            field="num"
            label-col-flex="70px"
            label="可约人数"
            :rules="{ required: true, message: '请输入' }">
            <a-input-number v-model="chargePlanData[index].num" :min="0" :precision="0" />
            <template #extra>0表示不限制</template>
          </a-form-item>
          <a-form-item style="flex: 1" field="pay_type" label="支付方式" :rules="{ required: true, message: '请选择' }">
            <a-select
              v-model="chargePlanData[index].pay_type"
              style="width: 100%"
              @change="handlePayTypeChange(index, $event)">
              <a-option :value="1">卡内余额</a-option>
              <a-option :value="2">微信支付</a-option>
            </a-select>
          </a-form-item>
          <a-form-item
            v-if="chargePlanData[index].pay_type === 2"
            style="flex: 1"
            field="price"
            label="单价"
            :rules="{ required: true, type: 'number', message: '请输入大于0的金额', min: 0.01 }">
            <a-input-number
              v-model="chargePlanData[index].price"
              :min="0.01"
              :precision="2"
              :placeholder="getPlaceholder(chargePlanData[index].pay_type)" />
          </a-form-item>
          <a-form-item
            v-if="cardTypeId !== 1 && chargePlanData[index].pay_type === 1"
            style="flex: 1"
            field="price"
            label="单价"
            :rules="{ required: true, message: '请输入' }">
            <a-input-number
              v-model="chargePlanData[index].price"
              :min="0"
              :precision="cardTypeId === 3 ? 2 : 0"
              :placeholder="getPlaceholder(chargePlanData[index].pay_type)" />
            <template #extra>0表示不扣卡课数量</template>
          </a-form-item>
        </a-form>
      </a-col>
      <a-col flex="20px" style="line-height: 35px">
        <icon-delete
          v-if="chargePlanData.length > 1"
          class="del-ico"
          style="cursor: pointer; color: red; font-size: 18px"
          @click="handleDeleteClick(index)" />
      </a-col>
    </a-row>
    <a-form-item label="" label-col-flex="70px">
      <a-button type="outline" @click="handleAddClick">新增一组</a-button>
    </a-form-item>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import TreeCardSelect from '@/components/form/tree-card-select.vue';

  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, any>;
      name: string;
      cardTypeId: number;
      wxPayAuth?: boolean;
      type: number;
      cardList: Record<string, any>[];
      tagList: Record<string, any>[];
    }>(),
    {
      modelValue: () => {},
      type: 1,
      name: '期限卡',
      cardTypeId: 1,
      wxPayAuth: false,
      cardList: () => [],
      tagList: () => [],
    }
  );
  const emits = defineEmits(['update:modelValue']);
  const chargePlanData = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emits('update:modelValue', val);
    },
  });
  const initCardDataArr = ref<Record<string, any>[]>([]);
  const initTagDataArr = ref<Record<string, any>[]>([]);
  watch(
    () => props.cardList,
    (val) => {
      initCardDataArr.value =
        val.length > 0
          ? [
              {
                key: -1,
                title: props.name,
                children: val.map((item: any) => {
                  return {
                    ...item,
                    title: item.name,
                    disabled: false,
                    key: item.id,
                  };
                }),
              },
            ]
          : [];
    }
  );
  watch(
    () => props.tagList,
    (val) => {
      initTagDataArr.value =
        val.length > 0
          ? [
              {
                key: -1,
                title: props.name,
                children: val.map((item: any) => {
                  return {
                    ...item,
                    disabled: false,
                    key: item.id,
                  };
                }),
              },
            ]
          : [];
    }
  );

  function getDisabledIds(index: number) {
    return chargePlanData.value.flatMap((i, subIndex) => (subIndex === index ? [] : i.list.map((j) => j.value)));
  }
  // type更改 重置list
  watch(
    () => props.type,
    () => {
      chargePlanData.value.forEach((item) => {
        item.list = [];
      });
    }
  );
  type refItem = Element | ComponentPublicInstance | null;
  const refMap: Record<number, refItem> = reactive({});
  const setRefMap = (el: refItem, index: number) => {
    if (el) {
      refMap[index] = el;
    }
  };
  async function handleAddClick() {
    const errors = await refMap[chargePlanData.value.length - 1].validate();
    if (!errors) {
      chargePlanData.value.push({
        list: [],
        num: 1,
        pay_type: 1,
        price: null,
      });
    }
  }
  function handleDeleteClick(index: number) {
    chargePlanData.value.splice(index, 1);
  }
  function getPlaceholder(payType) {
    const typeName =
      props.cardTypeId === 1 ? '天' : props.cardTypeId === 2 ? '次' : props.cardTypeId === 3 ? '元' : '节';
    return `${payType === 1 ? typeName : '元'}/人`;
  }

  function handlePayTypeChange(index: number, payType: number) {
    if (payType === 2 && !props.wxPayAuth) {
      chargePlanData.value[index].pay_type = 1;
      Message.error('无操作权限！');
    }
    // 切换支付方式时重置价格 防止价格小数位数不一致
    chargePlanData.value[index].price = null;
  }

  // 外部页面提交时调用
  async function validate() {
    const promiseArr = chargePlanData.value.map((item, index) => {
      return refMap[index].validate();
    });
    const errors = await Promise.all(promiseArr);
    if (errors.some((item) => item !== undefined)) {
      return false;
    }
    return true;
  }

  defineExpose({
    validate,
  });
</script>

<style lang="less" scoped></style>
