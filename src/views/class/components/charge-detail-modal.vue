<template>
  <a-modal
    v-model:visible="isShowModal"
    title="收费详情"
    :width="720"
    :footer="false"
    @before-close="popupVisibleChange">
    <div class="tips-header">
      <div class="tips">0表示不扣次、不扣费、或可预约的人数无上限</div>
      <a-button v-if="isShowEditBtn" type="primary" @click="$router.push('/class/charge-plane-detail/' + id)">
        编辑方案
      </a-button>
    </div>
    <a-table :data="tableData" :loading="isLoading" :pagination="pagination" @page-change="onPageChange">
      <template #columns>
        <a-table-column title="卡种/标签" data-index="name" />
        <a-table-column title="可约人数" data-index="num" />
        <a-table-column title="单价" data-index="nameKey">
          <template #cell="{ record }">
            <span v-if="record.pay_type === 2 && record.price">支付</span>
            {{ getPlaceholder(record) }}
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Pagination } from '@/types/global';
  import { getClassChargePlanInfo } from '@/api/class-charge-plan';

  const props = defineProps<{
    modelValue: boolean;
    id: string | number | undefined;
    isShowEditBtn: boolean;
  }>();
  const { isLoading, execute } = getClassChargePlanInfo();
  const tableData = ref([]);
  watch(
    () => props.id,
    () => {
      execute({
        data: {
          id: props.id,
          only_detail: 1,
        },
      }).then((res) => {
        const cardTypeNames = ['期限卡', '次卡', '储值卡', '私教', '泳教'];
        tableData.value = (res.data.value.detail || []).map((item) => {
          return {
            ...item,
            name: `${item.name} - ${cardTypeNames[+item.card_type_id - 1]}`,
          };
        });
      });
    }
  );
  const emits = defineEmits(['update:modelValue']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const pagination = reactive<Pagination>({
    current: 1,
    pageSize: 10,
  });
  const onPageChange = (current: number) => {
    pagination.current = current;
  };
  function popupVisibleChange() {
    pagination.current = 1;
  }
  function getPlaceholder(info) {
    const { price, pay_type, card_type_id } = info;
    const typeName = card_type_id === 1 ? '天' : card_type_id === 2 ? '次' : card_type_id === 3 ? '元' : '节';
    return pay_type === 1 && card_type_id === 1 ? '-' : `${price}${pay_type === 1 ? typeName : '元'}/人`;
  }
</script>

<style lang="less" scoped>
  .tips-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  .tips {
    color: var(--color-text-3);
    font-size: 12px;
  }
</style>
