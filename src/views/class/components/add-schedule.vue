<template>
  <a-modal
    v-model:visible="isShowModal"
    title="课程排课"
    :width="720"
    :mask-closable="false"
    :footer="isJustLook ? false : true"
    :on-before-ok="hansleConfirm">
    <a-spin :loading="isListLoading || isDetailLoading" style="display: block; width: 100%">
      <a-form ref="formRef" class="base-set-form" :model="saveForm" :style="{ width: '100%' }" auto-label-width>
        <a-form-item
          label="课程"
          field="class_id"
          :rules="[{ required: true, message: '请选择课程' }]"
          :content-flex="false">
          <a-select
            v-model="saveForm.class_id"
            placeholder="请选择"
            :disabled="isJustLook || !!saveForm.id"
            allow-clear
            allow-search
            @change="handleClassChange">
            <a-option v-for="item in classList" :key="item.class_id" :value="item.class_id">
              {{ item.class_name }}
            </a-option>
          </a-select>
          <a-card v-if="selectClass.class_id" class="desc-card">
            <div class="desc-item">
              <div class="desc-title">最小开课人数</div>
              <div class="desc-value">{{ selectClass.min_number }}人</div>
            </div>
            <div v-if="selectClass.mark_object === '1'" class="desc-item">
              <div class="desc-title">非会员价</div>
              <div class="desc-value">{{ selectClass.nonmember_price }}元/人</div>
            </div>
            <div v-if="saveForm.class_category === '0'" class="desc-item">
              <div class="desc-title">会员收费方案</div>
              <div class="desc-value">
                {{ !!selectClass.class_charge_plan_id ? selectClass.class_charge_plan_name : '未设置' }}
              </div>
              <div v-if="!isJustLook" class="desc-value">
                <a-link v-if="!!selectClass.class_charge_plan_id" @click="handleSetPlan()">修改</a-link>
                <a-link v-else @click="handleSetPlan()">去设置</a-link>
              </div>
            </div>
          </a-card>
        </a-form-item>
        <a-form-item label="日期" field="date_time" :rules="[{ required: true, message: '请选择日期' }]">
          <a-date-picker
            v-if="!(isJustLook || !!saveForm.id)"
            v-model="saveForm.date_time"
            format="YYYY-MM-DD"
            type="date"
            style="width: 100%"
            placeholder="请选择日期" />
          <div v-else class="desc-pro">{{ saveForm.date_time }}</div>
        </a-form-item>
        <a-form-item
          v-if="!isJustLook"
          label="时间"
          field="beg_time"
          :rules="[{ required: true, validator: timeValidator }]">
          <a-time-picker
            v-model="saveForm.beg_time"
            disable-confirm
            style="flex: 1; margin-right: 8px"
            format="HH:mm"
            @popup-visible-change="timeStartChange" />
          <span>~</span>
          <a-time-picker
            v-model="saveForm.end_time"
            disable-confirm
            style="flex: 1; margin-left: 8px"
            format="HH:mm"
            @popup-visible-change="timeEndChange" />
        </a-form-item>
        <a-form-item v-else label="时间">
          <div class="desc-pro">{{ saveForm.beg_time }} 至 {{ saveForm.end_time }}</div>
        </a-form-item>
        <a-form-item
          v-if="saveForm.class_category === '0'"
          label="可约人数"
          :rules="[{ required: true, message: '请输入可约人数' }]">
          <a-input-number
            v-if="!isJustLook && selectClass.class_id"
            v-model="saveForm.reserve_number"
            :min="+selectClass.min_number"
            :precision="0"
            @change="handleReserveNumberChange" />
          <div v-else class="desc-pro">{{ saveForm.reserve_number }}</div>
        </a-form-item>
        <a-form-item v-if="selectClass.mark_object === '1'" label="预约名额">
          <a-row v-if="!isJustLook" :gutter="12" style="width: 100%" align="center">
            会员
            <a-col flex="1">
              <a-input-number
                v-model="saveForm.member_mark_number"
                :max="saveForm.reserve_number"
                :min="0"
                :precision="0"
                placeholder="不限制"
                @change="setNonmemberNumber" />
            </a-col>
            人， 非会员
            <a-col flex="1">
              <a-input-number
                v-model="saveForm.nonmember_mark_number"
                :max="saveForm.reserve_number"
                :min="0"
                :precision="0"
                placeholder="不限制"
                @change="setMemberNumber" />
            </a-col>
            人
          </a-row>
          <div v-else class="desc-pro">
            会员{{ getNumDes(saveForm.member_mark_number) }}， 非会员{{ getNumDes(saveForm.nonmember_mark_number) }}
          </div>
        </a-form-item>
        <a-form-item label="教练" field="coach_id" :rules="[{ required: true, message: '请选择教练' }]">
          <a-select
            v-if="!isJustLook"
            v-model="saveForm.coach_id"
            placeholder="请选择教练"
            allow-search
            @change="hadleCoachChange">
            <a-option v-for="item in coachList" :key="item.coach_id" :value="item.coach_id">
              {{ item.coach_name }}
            </a-option>
          </a-select>
          <div v-else class="desc-pro">{{ saveForm.coach_name }}</div>
        </a-form-item>
        <a-form-item label="教室" field="classroom_id" :rules="[{ validator: classroomValidator }]">
          <a-select
            v-if="!isJustLook"
            v-model="saveForm.classroom_id"
            allow-search
            allow-clear
            placeholder="请选择教室"
            @change="hadleClassRoomChange">
            <a-option v-for="item in roomList" :key="item.id" class="room-select-option" :value="item.id">
              <span>{{ item.classroom_name }} &nbsp;</span>
              <span class="desc">{{ item.allow_number }}人</span>
              <span v-if="+item.seat_number" class="desc">{{ item.seat_number }}座位</span>
            </a-option>
          </a-select>
          <div v-else class="desc-pro">{{ saveForm.classroom_name }}</div>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
  <SetClassCharge
    v-if="isShowSetClassCharge"
    v-model="isShowSetClassCharge"
    :plan-id="selectClass.class_charge_plan_id"
    :class-ids="selectedIds"
    :course-schedule-id="id"
    @on-success="setChargeSuccess" />
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { Message, Modal } from '@arco-design/web-vue';
  import {
    getAddCourseScheduleData,
    addCourseSchedule,
    getCourseSchedule,
    updateCourseSchedule,
  } from '@/api/course-schedule';
  import SetClassCharge from './set-class-charge.vue';

  const props = defineProps<{
    modelValue: boolean;
    isJustLook: boolean;
    busId?: string;
    id?: string;
    dateTime?: string;
  }>();

  const coachList = ref([]);
  const roomList = ref([]);
  const classList = ref([]);
  const emits = defineEmits(['update:modelValue', 'success']);
  const formRef = ref();
  const saveForm = reactive({
    id: props.id || '',
    class_id: '',
    class_category: '',
    class_name: '',
    date_time: '',
    reserve_number: undefined,
    member_mark_number: undefined,
    nonmember_mark_number: undefined,
    coach_id: '',
    coach_name: '',
    classroom_id: '',
    classroom_name: '',
    bus_id: '',
    beg_time: '',
    end_time: '',
  });
  const isDetailInfoReady = ref(false);
  const initDetailInfo = ref({});

  const initClassInfo = {
    class_charge_plan_id: '',
    class_charge_plan_name: '',
    class_hour: '',
    class_id: '',
    class_name: '',
    is_free: '',
    mark_object: '',
    min_number: '1',
    nonmember_price: '',
    reserve_number: '',
    support_line_up: '',
  };
  const selectClass = reactive(initClassInfo);
  const selectedIds = computed(() => {
    return [selectClass.class_id];
  });
  const isShowSetClassCharge = ref(false);

  watch(
    () => props.busId,
    (val) => {
      saveForm.bus_id = val;
    },
    { immediate: true }
  );

  watch(
    () => props.dateTime,
    (val) => {
      saveForm.date_time = val;
    },
    { immediate: true }
  );

  function timeStartChange(visible: boolean) {
    if (selectClass.class_hour && saveForm.beg_time && !saveForm.end_time && !visible) {
      saveForm.end_time = dayjs(saveForm.beg_time, 'HH:mm').add(selectClass.class_hour, 'minute').format('HH:mm');
      nextTick(() => {
        formRef.value.validateField('beg_time');
      });
    }
  }
  function timeEndChange(visible: boolean) {
    if (selectClass.class_hour && saveForm.end_time && !saveForm.beg_time && !visible) {
      saveForm.beg_time = dayjs(saveForm.end_time, 'HH:mm').subtract(selectClass.class_hour, 'minute').format('HH:mm');
      nextTick(() => {
        formRef.value.validateField('beg_time');
      });
    }
  }
  function handleClassChange(classId) {
    if (!classId) {
      Object.assign(selectClass, initClassInfo);
      return;
    }
    const info = classList.value.find((item) => item.class_id === classId);
    Object.assign(selectClass, info);
    saveForm.class_name = info.class_name;
    saveForm.class_category = info.is_free;
  }

  const { isLoading: isListLoading, execute: executeGetList } = getAddCourseScheduleData();
  function getList() {
    // scene int 1添加排课 过滤删除和隐藏，2查询排课信息 查询全部课程，缺省值1
    return executeGetList({ data: { bus_id: props.busId, scene: props.isJustLook || !!saveForm.id ? 2 : 1 } }).then(
      (res) => {
        const resData = res.data.value;
        coachList.value = resData.coach_list;
        roomList.value = resData.classroom_list;
        classList.value = resData.class_list;
        if (selectClass.class_id) {
          handleClassChange(saveForm.class_id);
        }
        return res.data.value;
      }
    );
  }
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  watch(
    () => props.modelValue,
    (val) => {
      if (!val) {
        formRef.value.resetFields();
      }
    }
  );

  const roomInfo = reactive({
    id: '',
    classroom_name: '',
    seat_number: null,
    allow_number: null,
  });

  function confirmRoomChange(id) {
    if (!id) {
      Object.assign(roomInfo, { id: '', classroom_name: '', seat_number: null, allow_number: null });
      saveForm.classroom_name = '';
      return;
    }
    const info = roomList.value.find((item) => item.id === id);
    Object.assign(roomInfo, info);
    saveForm.classroom_name = info.classroom_name;
  }
  function hadleClassRoomChange(id) {
    if (initDetailInfo.value.classroom_id !== id && +initDetailInfo.value.sign_number > 0) {
      Modal.confirm({
        title: '提示',
        id: 'changeClassRoomConfirm',
        content: '切换教室将导致已选座位的会员座位信息失效，是否确定修改？',
        onOk: () => {
          confirmRoomChange(id);
        },
        onCancel: () => {
          saveForm.classroom_id = initDetailInfo.value.classroom_id;
        },
      });
      return;
    }
    confirmRoomChange(id);
  }

  function classroomValidator(value: string, callback: (arg0?: string) => void) {
    if (value && saveForm.reserve_number && +roomInfo.seat_number && +roomInfo.seat_number < +saveForm.reserve_number) {
      callback('座位数不能小于可约人数');
    } else if (
      value &&
      selectClass.min_number &&
      +roomInfo.seat_number &&
      +roomInfo.seat_number < +selectClass.min_number
    ) {
      callback('座位数不能小于最小开课人数');
    } else {
      callback();
    }
  }
  function timeValidator(value: string, callback: (arg0?: string) => void) {
    if (!saveForm.beg_time || !saveForm.end_time) {
      callback('请选择时间');
    } else if (dayjs(saveForm.end_time, 'HH:mm').isBefore(dayjs(saveForm.beg_time, 'HH:mm'))) {
      callback('结束时间不能早于开始时间');
    } else {
      callback();
    }
  }
  // 当可约人数修改数值后，预约名额 需要重新安装可约人数进行填充
  function handleReserveNumberChange() {
    if (saveForm.id && !isDetailInfoReady.value) {
      return;
    }
    nextTick(() => {
      saveForm.member_mark_number = undefined;
      saveForm.nonmember_mark_number = undefined;
    });
  }
  function setNonmemberNumber(val) {
    if (saveForm.id && !isDetailInfoReady.value) {
      return;
    }
    if (saveForm.reserve_number && typeof val !== 'undefined') {
      saveForm.nonmember_mark_number = saveForm.reserve_number - (val || 0);
    }
  }
  function setMemberNumber(val) {
    if (saveForm.id && !isDetailInfoReady.value) {
      return;
    }
    if (saveForm.reserve_number && typeof val !== 'undefined') {
      saveForm.member_mark_number = saveForm.reserve_number - (val || 0);
    }
  }

  const { execute: setInfo } = saveForm.id ? updateCourseSchedule() : addCourseSchedule();
  function isNullOrUndefined(val) {
    return val === null || val === undefined;
  }
  async function hansleConfirm() {
    try {
      const errors = await formRef.value.validate();
      if (errors) return false;
      await setInfo({
        data: {
          ...saveForm,
          member_mark_number: isNullOrUndefined(saveForm.member_mark_number) ? '' : saveForm.member_mark_number,
          nonmember_mark_number: isNullOrUndefined(saveForm.nonmember_mark_number)
            ? ''
            : saveForm.nonmember_mark_number,
        },
      });
      Message.success('设置成功！');

      emits('success');
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }
  function hadleCoachChange(id) {
    if (!id) {
      saveForm.coach_name = '';
      return;
    }
    const info = coachList.value.find((item) => item.coach_id === id);
    saveForm.coach_name = info.coach_name;
  }

  const { isLoading: isDetailLoading, execute: getDetailInfo } = getCourseSchedule();
  function getDetail() {
    getDetailInfo({ data: { id: props.id, bus_id: props.busId } }).then((res) => {
      const resData = res.data.value.info;
      const numberKeys = ['reserve_number', 'member_mark_number', 'nonmember_mark_number'];
      Object.keys(saveForm).forEach((key) => {
        if (numberKeys.includes(key)) {
          saveForm[key] = resData[key] === '' ? undefined : Number(resData[key]);
        } else {
          saveForm[key] = resData[key];
        }
      });
      saveForm.date_time = dayjs(resData.date_time * 1000).format('YYYY-MM-DD');
      initDetailInfo.value = resData;
      handleClassChange(saveForm.class_id);
      nextTick(() => {
        isDetailInfoReady.value = true;
      });
    });
  }
  function handleSetPlan() {
    isShowSetClassCharge.value = true;
  }
  function getNumDes(num: string | undefined) {
    if (num === undefined || num === '') {
      return '无限制';
    }
    return `${num}人`;
  }
  function setChargeSuccess() {
    getList();
  }
  onMounted(() => {
    getList().then(() => {
      if (props.id) {
        getDetail();
      }
    });
  });
</script>

<style lang="less" scoped>
  .desc-card {
    margin-top: 8px;
  }
  .desc-item {
    margin-bottom: 8px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    &:last-child {
      margin-bottom: 0;
    }
    .desc-title {
      width: 100px;
      text-align: right;
      color: var(--color-text-6);
    }
    .desc-value {
      display: flex;
      align-items: center;
      margin-left: 8px;
      color: var(--color-text-3);
    }
  }
  .room-select-option {
    width: 100%;
    .desc {
      margin-left: 16px;
      color: var(--color-text-3);
    }
  }
</style>
