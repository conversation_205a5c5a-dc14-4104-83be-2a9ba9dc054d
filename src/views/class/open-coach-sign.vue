<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="class_id" label="课程">
                  <a-select v-model="searchParam.class_id" placeholder="请选择" allow-clear allow-search>
                    <a-option v-for="item in classList" :key="item.id" :value="item.id">{{ item.class_name }}</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="coach_id" label="教练">
                  <a-select v-model="searchParam.coach_id" placeholder="请选择" allow-clear allow-search>
                    <a-option v-for="item in coachList" :key="item.coach_id" :value="item.coach_id">
                      {{ item.coach_name }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="签到状态">
                  <a-select v-model="searchParam.time_status" placeholder="请选择" allow-clear allow-search>
                    <a-option :value="0">正常</a-option>
                    <a-option :value="1">迟到</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="时间">
                  <a-range-picker v-model="rangeValue" :allow-clear="false" @change="handleTimeChange" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="router.push('/v2/signin/manual?active=4')">教练自助扫码签到</a-button>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: end">
          <!-- <ExportButton :data="exportPostParams" url="/Web/CoachSign/get_coach_sign_list" /> -->
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button :loading="isLoading" @click="handleClickExport(handleExport)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column title="课程" data-index="class_name" />
          <a-table-column title="时间" data-index="date_time">
            <template #cell="{ record }">{{ record.date_time }} {{ record.beg_time }}</template>
          </a-table-column>
          <a-table-column title="排课教练" data-index="class_coach_name" />
          <a-table-column title="预约人数" data-index="mark_number">
            <template #cell="{ record }">
              <a-link
                v-if="record.class_category != 1"
                @click="
                  router.push({
                    path: '/class/open-reser',
                    query: { date: record.date_time, coachId: record.class_coach_id, classId: record.class_id },
                  })
                ">
                {{ record.mark_number }}
              </a-link>
              <span v-else style="color: #cccccc; margin-left: 4px">-</span>
            </template>
          </a-table-column>
          <a-table-column title="实到人数" data-index="sign_number">
            <template #cell="{ record }">
              <div
                v-if="Number(record.sign_number) > 0 && record.check_user_list == 1"
                class="coach-table-imgwrap"
                @click.self="handleArrivalModal(record)">
                {{ record.sign_number }}
              </div>
              <div v-else class="coach-table-imgwrap coach-table-disable">
                {{ record.sign_number }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="签到时间" data-index="sign_time" />
          <a-table-column title="签到状态" data-index="status">
            <template #cell="{ record }">
              <span v-if="record.status == 0 || record.status == 3" style="color: #cccccc; margin-left: 4px">-</span>
              <span v-else-if="record.time_status == 0" style="color: #19be6b">正常</span>
              <span v-else-if="record.time_status == 1" style="color: #ed3f14">迟到</span>
            </template>
          </a-table-column>
          <a-table-column title="上课教练" data-index="coach_name" />
          <a-table-column title="操作记录" data-index="csor_num">
            <template #cell="{ record }">
              <a-link v-if="record.csor_num" @click="showRecordModal(record.coach_sign_id)">
                {{ record.csor_num }}条
              </a-link>
              <span v-else style="color: #cccccc; margin-left: 4px">-</span>
              <a-avatar
                v-if="record.sign_image_url"
                shape="square"
                :size="20"
                style="margin-left: 10px; cursor: pointer">
                <img class="coach-table-img" :src="record.sign_image_url" @click="showImgModal(record)" />
              </a-avatar>
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <span v-if="record.outer_type == 1" style="color: #cccccc; margin-left: 4px">-</span>
              <a-link v-else-if="record.status == 0 || record.status == 3" @click="doSign(record)">签到</a-link>
              <span v-else>
                <a-link @click="doSign(record, true)">编辑</a-link>
                <a-link status="danger" @click="cancelSign(record.coach_sign_id)">取消签到</a-link>
              </span>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>

    <a-modal v-model:visible="isShowArrival" title="实到名单" :footer="false">
      <a-table :data="arrivalList" :show-header="false" :bordered="false" :pagination="false" :hoverable="false">
        <template #columns>
          <a-table-column title="头像" data-index="avatar">
            <template #cell="{ record }">
              <a-avatar shape="circle">
                <img v-if="record.avatar" :src="record.avatar" />
                <img v-else src="https://imagecdn.rocketbird.cn/default/man_member_default.png" />
              </a-avatar>
            </template>
          </a-table-column>
          <a-table-column title="姓名" data-index="username" />
          <a-table-column title="手机号" data-index="phone" />
          <a-table-column title="到场方式" data-index="arrive_type">
            <template #cell="{ record }">
              <span>ivep扫码</span>
              <span v-if="record.is_cancel == 1" style="color: #ed3f14; margin-left: 4px">(已取消)</span>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-modal>

    <a-modal v-model:visible="isShowRecord" title="操作记录" :footer="false">
      <a-table :data="recordTableData" :show-header="false" :bordered="false" :pagination="false" :hoverable="false">
        <template #columns>
          <a-table-column title="时间" data-index="date" />
          <a-table-column title="账号" data-index="admin_name" />
          <a-table-column title="操作" data-index="content" />
        </template>
      </a-table>
    </a-modal>

    <a-modal v-model:visible="isShowImg" title="图片预览" :footer="false">
      <div class="coach-imgmodal">
        <span>{{ imgData?.sign_image_num }}人</span>
        <img :src="imgData?.sign_image_url" />
      </div>
    </a-modal>

    <a-modal v-model:visible="isShowSign" title="签到" @ok="addSign">
      <a-form class="modal-form" :model="signModalData">
        <a-form-item label="课程">
          <a-input v-model="signModalData.class_name" placeholder="课程" :disabled="true" />
        </a-form-item>
        <a-form-item label="上课教练">
          <a-select v-model="signModalData.coach_id" :label-in-value="true" filterable @change="coachChange">
            <a-option
              v-for="item in coachList"
              :key="item.coach_id"
              :value="item.coach_id"
              :label="item.coach_name"></a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="实到人数">
          <a-input-number v-model="signModalData.sign_number" :min="1" placeholder="签到后可修改"></a-input-number>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { Modal, Message } from '@arco-design/web-vue';
  // import ExportButton from '@/components/form/export-button.vue';
  import ExportExcel from '@/components/exportExcel.vue';
  import {
    getCoachSignList,
    getRecordList,
    getArrivalList,
    getCoachList,
    coachSign,
    cancelCoachSign,
  } from '@/api/class-mark';
  import { getOpenClassAll } from '@/api/open-class';
  import useTableProps from '@/hooks/table-props';
  import { useBusInfoStore } from '@/store';

  defineOptions({
    name: 'OpenCoachSign',
  });
  const busInfo = useBusInfoStore();
  const router = useRouter();
  const coachList = ref<any>([]);
  const classList = ref<any>([]);
  const { isLoading, tableProps, tableEvent, searchParam, handleSearch, setSearchParam, dataPath, loadTableList } =
    useTableProps(getCoachSignList);
  dataPath.value = {
    list: 'list',
    count: 'count',
  };
  const route = useRoute();
  const today = dayjs().format('YYYY-MM-DD');
  const rangeValue = ref<string[]>([today, today]);
  // 设置除分页外的其它属性值
  setSearchParam({
    is_export: 0,
    begin_date: rangeValue.value[0],
    end_date: rangeValue.value[1],
    class_id: route.query.classId ? route.query.classId : '',
    coach_id: '',
    time_status: '',
  });
  // const exportPostParams = computed(() => {
  //   const params: any = {
  //     is_export: 1,
  //     ...searchParam.value,
  //     current: 1,
  //     pageSize: tableProps.value.pagination.total,
  //   };
  //   console.log('params: ', params);
  //   return params;
  // });
  loadTableList();
  getCoachList({}).then(({ response }: any) => {
    if (response.value.errorcode === 0) {
      coachList.value = response.value.data.list;
    }
  });
  getOpenClassAll({
    bus_id: busInfo.bus_id,
  }).then((res) => {
    classList.value = res.data.value.list;
  });
  function handleTimeChange(params: any) {
    const [beginTime, endTime] = params || ['', ''];
    searchParam.begin_date = beginTime;
    searchParam.end_date = endTime;
  }

  // image modal
  const imgData = ref<any>();
  const isShowImg = ref(false);
  const showImgModal = (rowInfo: any) => {
    imgData.value = rowInfo;
    isShowImg.value = true;
  };
  // record modal
  const isShowRecord = ref(false);
  const recordTableData = ref([]);
  const showRecordModal = (coach_sign_id: any) => {
    getRecordList({ coach_sign_id }).then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        recordTableData.value = response.value.data.list;
        isShowRecord.value = true;
      }
    });
  };
  // arrival modal
  const isShowArrival = ref(false);
  const arrivalList = ref([]);
  const handleArrivalModal = (row: any) => {
    if (!row) return;
    if (Number(row.sign_number) === 0) return;
    const { course_schedule_id } = row;
    getArrivalList({ course_schedule_id }).then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        arrivalList.value = response.value.data;
        isShowArrival.value = true;
      }
    });
  };
  // sign action
  const isShowSign = ref(false);
  const signModalData = ref<any>({});
  const doSign = (rowInfo: any, isEdit?: any) => {
    const { class_id, class_name, class_category, date_time, beg_time, end_time, course_schedule_id }: any = {
      ...rowInfo,
    };
    signModalData.value = {
      class_id,
      class_name,
      class_category,
      date_time,
      beg_time,
      end_time,
      course_schedule_id,
      coach_id: isEdit ? rowInfo.coach_id : rowInfo.class_coach_id || '',
      coach_name: isEdit ? rowInfo.coach_name : rowInfo.class_coach_name || '', // TODO  当更新了教练名字时，还是取行信息的排课教练名会不匹配（排课时教练名称已固定）
      sign_number: isEdit ? +rowInfo.sign_number : null,
      coach_sign_id: isEdit ? rowInfo.coach_sign_id : '',
    };
    isShowSign.value = true;
  };
  const addSign = () => {
    coachSign(signModalData.value).then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        loadTableList();
        isShowSign.value = false;
      } else {
        Message.error(response.value.errormsg);
      }
    });
  };
  const cancelSign = (coach_sign_id: any) => {
    Modal.confirm({
      title: '取消签到',
      content: '确认取消这次签到吗？',
      onOk: () => {
        cancelCoachSign({ coach_sign_id }).then(({ response }: any) => {
          if (response.value.errorcode === 0) {
            loadTableList();
          } else {
            Message.error(response.value.errormsg);
          }
        });
      },
    });
  };
  const coachChange = (id: any) => {
    const coach = coachList.value.find((item: any) => item.coach_id === id);
    if (!coach) return;
    signModalData.value.coach_id = coach.coach_id;
    signModalData.value.coach_name = coach.coach_name;
  };

  // export
  const handleClickExport = (cb: any) => {
    const filename = `团课教练签到(${searchParam.begin_date}至${searchParam.end_date})`;
    loadTableList(true).then((list) => {
      list.forEach((item: any) => {
        item.date_beg_time = `${item.date_time} ${item.beg_time}`;
        if (Number(item.status) === 0) {
          item.status_text = '-';
        } else if (Number(item.time_status) === 0) {
          item.status_text = '正常';
        } else if (Number(item.time_status) === 1) {
          item.status_text = '迟到';
        }
      });
      cb({
        filename,
        columns: [
          {
            title: '课程',
            dataIndex: 'class_name',
          },
          {
            title: '时间',
            dataIndex: 'date_beg_time',
          },
          {
            title: '排课教练',
            dataIndex: 'class_coach_name',
          },
          {
            title: '预约人数',
            dataIndex: 'mark_number',
          },
          {
            title: '实到人数',
            dataIndex: 'sign_number',
          },
          {
            title: '签到时间',
            dataIndex: 'sign_time',
          },
          {
            title: '签到状态',
            dataIndex: 'status_text',
          },
          {
            title: '上课教练',
            dataIndex: 'coach_name',
          },
          {
            title: '操作记录',
            dataIndex: 'csor_num',
          },
        ],
        data: list,
      });
    });
  };
</script>

<style lang="less" scoped>
  .coach-table-imgwrap {
    position: relative;
    cursor: pointer;
    color: #2d8cf0;
  }

  .coach-table-disable {
    cursor: not-allowed;
    color: #ccc;
  }

  .coach-imgmodal {
    margin-top: 20px;
    width: 100%;
    position: relative;

    img {
      width: 100%;
    }

    span {
      display: block;
      position: absolute;
      left: 10px;
      top: 10px;
      width: 70px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      color: #fff;
      background: rgba(0, 0, 0, 0.3);
    }
  }
</style>
