<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
        <a-form-item label="课程名称" field="class_name" :rules="{ required: true, message: '请填写' }">
          <a-input v-model="formData.class_name" />
        </a-form-item>
        <a-form-item label="单节时长" field="class_hour" :rules="{ required: true, message: '请填写' }">
          <a-input-number v-model="formData.class_hour" :min="5" :precision="0" placeholder="分钟" />
        </a-form-item>
        <a-form-item label="课程种类" field="class_type">
          <a-radio-group v-model="formData.class_type">
            <a-radio value="0" :disabled="!!formData.id">团课(预约上课)</a-radio>
            <a-radio value="1" :disabled="!!formData.id">操课(无需预约)</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="课程强度" field="class_level">
          <a-radio-group v-model="formData.class_level">
            <a-radio :value="1">1星</a-radio>
            <a-radio :value="2">2星</a-radio>
            <a-radio :value="3">3星</a-radio>
            <a-radio :value="4">4星</a-radio>
            <a-radio :value="5">5星</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="课程颜色" field="class_color">
          <a-color-picker v-if="formData.class_color" v-model="formData.class_color" show-preset />
          <a-link v-else @click="formData.class_color = '#ffffff'">设置</a-link>
        </a-form-item>
        <a-form-item label="适用场馆" field="region_bus" :rules="{ required: true, message: '请选择' }">
          <AdminRegion
            :id="formData.id"
            v-model="formData.region_bus"
            url="/Web/OpenClass/get_openclass_region_bus"
            :should-default="1"
            filterable />
        </a-form-item>
        <a-form-item label="最少开课人数" field="min_number" :rules="{ required: true, message: '请填写' }">
          <a-input-number v-model="formData.min_number" :min="1" :precision="0" />
        </a-form-item>
        <template v-if="formData.class_type === '0'">
          <a-form-item label="约课对象" field="mark_object">
            <a-radio-group v-model="formData.mark_object">
              <a-radio value="0">仅会员</a-radio>
              <a-radio value="1">会员和非会员</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="会员收费方案" field="class_charge_plan_id">
            <a-input
              v-model="formData.class_charge_plan_name"
              placeholder="点击选择"
              allow-clear
              style="flex: 1; margin-right: 8px"
              readonly
              @clear.stop="handleClearPlan"
              @click="handleSelectPlan" />
            <a-link type="primary" class="ml-10" @click="handleNewPlan">新增方案</a-link>
          </a-form-item>
          <a-form-item
            v-if="formData.mark_object === '1'"
            label="非会员价"
            field="nonmember_price"
            :rules="{ required: true, message: '请填写' }">
            <a-input-number v-model="formData.nonmember_price" :min="0.01" :precision="2" placeholder="请输入">
              <template #append>元/人</template>
            </a-input-number>
          </a-form-item>
          <a-form-item v-if="formData.class_type === '0'" label="预约时间限制" :content-flex="false">
            <a-space direction="vertical" :size="20">
              <a-row align="center" :gutter="8">
                <a-radio-group v-model="formData.mark_time_switch">
                  <a-col>
                    <a-radio value="1">是</a-radio>
                    <a-radio value="2">否</a-radio>
                  </a-col>
                </a-radio-group>
              </a-row>
              <a-row align="center" :gutter="8">
                <a-col flex="50px">上课前</a-col>
                <a-col flex="120px">
                  <a-input-number
                    v-model="formData.stop_mark"
                    :disabled="formData.mark_time_switch === '2'"
                    :min="1"
                    :precision="0"
                    style="width: 100%" />
                </a-col>
                <a-col flex="120px">分钟停止预约，</a-col>
                <a-col flex="120px">
                  <a-input-number
                    v-model="formData.stop_result_mark"
                    :disabled="formData.mark_time_switch === '2'"
                    :min="1"
                    :precision="0"
                    style="width: 100%" />
                </a-col>
                <a-col flex="120px">分钟停止取消</a-col>
              </a-row>
              <a-row align="center" :gutter="8">
                <a-col flex="50px">可提前</a-col>
                <a-col flex="120px">
                  <a-input-number
                    v-model="formData.mark_day"
                    :disabled="formData.mark_time_switch === '2'"
                    :min="0"
                    :max="5"
                    :precision="0"
                    style="width: 100%" />
                </a-col>
                <a-col flex="380px">天开始约课（最大只能提前5天，0表示只能约当天课程）</a-col>
              </a-row>
              <a-row align="center" :gutter="8">
                <a-col flex="50px">设置</a-col>
                <a-col flex="120px">
                  <a-time-picker v-model="formData.mark_time" format="HH:mm" style="width: 100%" />
                </a-col>
                <a-col flex="120px">后才能约课</a-col>
              </a-row>
              <div>
                <a-checkbox v-model="formData.result_send">
                  开课前
                  <a-input-number
                    v-model="formData.cancel_mark_time"
                    placeholder="请输入"
                    :min="0"
                    :precision="0"
                    style="width: 80px" />
                  分钟人数不满时自动取消预约，并发送消息通知用户
                </a-checkbox>
              </div>
              <div>
                <a-checkbox v-model="formData.support_line_up" @change="handleSupportLineUp">
                  课程约满时，用户可以进行空位订阅，有空位时消息通知用户
                </a-checkbox>
              </div>
            </a-space>
          </a-form-item>
        </template>
        <a-form-item label="课程封面" field="thumb" :rules="{ required: true, message: '请上传' }">
          <div class="image-description">
            <img v-show="formData.thumb" :src="formData.thumb" />
            <a-button type="outline" @click="uploadModal = true">选择图片</a-button>
          </div>
          <template #extra>
            <p>建议大小不超过100KB</p>
            <p>图片格式：jpg、png</p>
            <p>建议尺寸：750X424</p>
          </template>
        </a-form-item>
        <a-form-item label="课程介绍" field="description">
          <FormEditor v-model="formData.description" style="width: 100%" />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" :loading="isLoading" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
    <ImgUpload
      v-model="uploadModal"
      :default-list="defaultList"
      :options="{ fixedNumber: [750, 424] }"
      :allow-camera="false"
      @on-change="handleUpload" />
    <PlaneTableModal
      v-if="isShowPlaneModal"
      v-model:visible="isShowPlaneModal"
      :selected-id="formData.class_charge_plan_id"
      @confirm="handleSetPlane" />
  </div>
</template>

<script lang="ts" setup>
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { getOpenClassInfo, addOpenClass, updateOpenClass, supportLineUp } from '@/api/open-class';
  import AdminRegion from '@/components/form/adminRegion.vue';
  import ImgUpload from '@/components/form/img-upload.vue';
  import FormEditor from '@/components/form/editor.vue';
  import { unescapeHTML } from '@/utils';
  import PlaneTableModal from './components/plane-table-modal.vue';

  const route = useRoute();
  const { id } = route.params;
  const formRef = ref<FormInstance>();
  const formData = reactive({
    id: id || '',
    class_name: '',
    class_hour: 30,
    class_type: '0',
    min_number: 1,
    class_level: 0, // 课程强度（1-5） 默认为0
    class_color: 'rgba(168,233,255,1)', // 课程颜色
    mark_object: '0',
    nonmember_price: null,
    mark_time: '00:00',
    cancel_mark_time: null,
    thumb: '',
    description: '',
    region_bus: [],
    mark_day: 2,
    stop_mark: 1,
    result_send: false,
    mark_time_switch: '1',
    stop_result_mark: 5,
    support_line_up: false,
    class_charge_plan_id: '',
    class_charge_plan_name: '',
  });
  const prefixStr = 'https://imagecdn.rocketbird.cn/test/image/';
  const defaultList = ref([
    `${prefixStr}37584282d2958670517fa3cf378b422a.png`,
    `${prefixStr}1c8f8ff699a73c27ebcba43c339e7a04.png`,
    `${prefixStr}c240b15e2433990cbc475723bbc582b3.png`,
    `${prefixStr}4b2d605d0bc6a30cda14ac1fdddedaf9.png`,
    `${prefixStr}7e985e8d86fe06ae60a8f0e593761b6a.png`,
    `${prefixStr}14f99aade526ee50ea7d0e0efca1b62d.png`,
    `${prefixStr}fff1055e6f8d9d7d9901583a4eff2bde.png`,
    `${prefixStr}41fe2014c999bd593dcd274991f55656.png`,
    `${prefixStr}2d5cbf19122a6b8893262ee8945bfdc3.png`,
    `${prefixStr}007534b1d20d6efebc772293584ba557.png`,
    `${prefixStr}505521d05f8e2fc5e8046550bd733252.png`,
    `${prefixStr}b4da47f421d1fe04c578841e68c09350.png`,
    `${prefixStr}c4cebbca71e94945f35cdf356402965e.png`,
    `${prefixStr}ef81aaf838f093962764a288fe6cd465.png`,
  ]);

  const { isLoading, execute: executeUpdate } = id ? updateOpenClass() : addOpenClass();
  const router = useRouter();
  async function handleSubmit() {
    const errors = await formRef.value?.validate();
    if (!errors) {
      const postData = { ...formData };
      if (postData.class_type === '1') {
        postData.mark_time_switch = '';
        postData.stop_mark = null;
        postData.stop_result_mark = null;
        postData.mark_day = null;
        postData.mark_time = '00:00';
        postData.result_send = false;
        postData.support_line_up = false;
      }
      postData.result_send = postData.result_send ? 1 : 2;
      postData.support_line_up = postData.support_line_up ? 1 : 0;
      executeUpdate({
        data: postData,
      }).then(() => {
        Message.success('操作成功');
        router.back();
      });
    } else {
      Message.error('请完善表单信息');
    }
  }
  const uploadModal = ref(false);
  // 课程封面-选择图片-确定按钮事件
  function handleUpload(path: string) {
    formData.thumb = path;
  }

  function getInfo() {
    getOpenClassInfo({ id }).then((res) => {
      const info = res.data.value;
      const numberDataKeys = [
        'min_number',
        'class_hour',
        'stop_mark',
        'nonmember_price',
        'stop_result_mark',
        'cancel_mark_time',
        'mark_day',
        'class_level',
      ];
      const BooleanDataKeys = ['result_send', 'support_line_up'];
      numberDataKeys.forEach((key) => {
        if (info[key]) {
          info[key] = info[key] === '' ? null : Number(info[key]);
        }
      });
      if (info.mark_object === '0') {
        info.nonmember_price = null;
      }
      BooleanDataKeys.forEach((key) => {
        if (info[key]) {
          info[key] = String(info[key]) === '1';
        }
      });
      info.description = unescapeHTML(info.description || '');
      Object.assign(formData, info);
    });
  }

  const supportLineUpAuth = ref(false);
  function handleSupportLineUpAuth() {
    supportLineUp().then((res) => {
      supportLineUpAuth.value = res.response.value.errorcode === 0;
    });
  }
  function handleSupportLineUp() {
    if (!supportLineUpAuth.value) {
      formData.support_line_up = !formData.support_line_up;
      Message.error('暂无权限');
    }
  }
  onMounted(() => {
    handleSupportLineUpAuth();
    if (id) {
      getInfo();
    }
  });
  const isShowPlaneModal = ref(false);
  function handleClearPlan() {
    formData.class_charge_plan_id = '';
    formData.class_charge_plan_name = '';
  }
  function handleSelectPlan() {
    isShowPlaneModal.value = true;
  }
  function handleSetPlane(info) {
    formData.class_charge_plan_id = info.id;
    formData.class_charge_plan_name = info.name;
  }
  function handleNewPlan() {
    window.open('/class/charge-plane-detail', '_blank');
  }
</script>

<style lang="less" scoped>
  .image-description {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    img {
      width: 345px;
      height: 200px;
      margin-bottom: 10px;
    }
  }
</style>
