<template>
  <div class="base-box open-schedule">
    <Breadcrumb />
    <a-card class="general-card" style="flex: 1">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="bus_id" label="场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" placeholder="请选择" @change="handleBusChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="s_date" label="时间">
                  <a-week-picker
                    v-model="searchParam.s_date"
                    :popup-visible="weekPickerFlag"
                    :day-start-of-week="1"
                    placeholder="请输入"
                    @change="handleWeekSelect">
                    <a-input
                      ref="weekInputRef"
                      v-model="weekDateString"
                      placeholder="默认本周"
                      readonly
                      @focus="weekPickerFlag = true"
                      @blur="weekPickerFlag = false">
                      <template #suffix>
                        <icon-calendar />
                      </template>
                    </a-input>
                  </a-week-picker>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="course_id" label="课程">
                  <a-select v-model="searchParam.course_id" placeholder="请选择" allow-clear allow-search>
                    <a-option v-for="item in weekCourseList" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="coach_id" label="教练">
                  <a-select v-model="searchParam.coach_id" placeholder="请选择" allow-clear allow-search>
                    <a-option v-for="item in weekCoachList" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="initWeekDateList">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button :disabled="pickUpFlag" type="primary" @click="handleToClassPreview">生成课表</a-button>
            <a-button @click="handlePickUpToCopy">{{ pickUpFlag ? '复制到' : '选择性复制' }}</a-button>
            <a-button :disabled="pickUpFlag" @click="handleCopyOpen">复制本周排课</a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <a-button :disabled="pickUpFlag" :loading="isClearing" @click="clearWeek">清空本周排课</a-button>
        </a-col>
      </a-row>
      <a-spin :loading="isListLoading" style="display: block; width: 100%; height: 0; flex-grow: 1">
        <div class="plan-week">
          <div v-for="(item, index) in weekDateList" :key="index" class="plan-day">
            <a-card :class="['plan-head', item.isToday ? 'plan-today' : '']">
              <h3>{{ item.week }}</h3>
              <h5>{{ item.day }}</h5>
            </a-card>
            <div class="course-scroll">
              <div
                v-for="(cs, i) in item.list"
                :key="i"
                class="plan-body"
                :class="[{ nohover: pickUpFlag }, { 'scroll-down': isPickDownCur(cs.id) }]"
                :style="{ border: pickUpFlag && !isPickDownCur(cs.id) ? '1px solid #FF696A' : 'none' }">
                <div class="plan-info" @click="handlePickUp(cs.id)">
                  <p class="title">{{ cs.class_name }}</p>
                  <p class="time">
                    <icon-clock-circle />
                    {{ cs.beg_time }}
                  </p>
                  <p class="coche">
                    <icon-user />
                    {{ cs.coach_name }}
                  </p>
                  <div class="gym">
                    <p class="gym-name">{{ cs.classroom_name }}</p>
                    <div v-if="cs.class_category === '0'" class="zj-badage">
                      {{ cs.already_order + '/' + cs.reserve_number }}
                    </div>
                  </div>
                </div>
                <div v-if="!pickUpFlag" class="plan-contraller">
                  <!--status 0未开始1进行中2已完成-->
                  <a-row style="height: 50%; display: flex; align-items: center">
                    <a-col
                      v-if="cs.status === '0' && cs.class_category === '0'"
                      :span="8"
                      class="plan-ctrl-btn"
                      @click="
                        $router.push(
                          `/class/open-reser?busId=${cs.bus_id}&classId=${cs.class_id}&startDate=${item.date}&endDate=${item.date}`
                        )
                      ">
                      <icon-schedule class="hover-icon" />
                      <div>预约</div>
                    </a-col>
                    <a-col :span="8" class="plan-ctrl-btn" @click="handleLook(cs.id)">
                      <icon-file class="hover-icon" />
                      <div>详情</div>
                    </a-col>
                    <a-col v-if="cs.status !== '2'" :span="8" class="plan-ctrl-btn" @click="handleEdit(cs.id)">
                      <icon-edit class="hover-icon" />
                      <div>编辑</div>
                    </a-col>
                    <a-col :span="8" class="plan-ctrl-btn" @click="handleCopy(cs)">
                      <icon-copy class="hover-icon" />
                      <div>复制</div>
                    </a-col>
                    <a-col v-if="cs.status === '0'" :span="8" class="plan-ctrl-btn" @click="handleDel(cs)">
                      <icon-delete class="hover-icon" />
                      <div>删除</div>
                    </a-col>
                    <a-col :span="8" class="plan-ctrl-btn" @click="handleBatchDel(cs)">
                      <icon-eraser class="hover-icon" />
                      <div>批量删除</div>
                    </a-col>
                  </a-row>
                </div>
              </div>
            </div>
            <div v-if="!pickUpFlag" class="plan-foot">
              <a-button
                :class="{ 'foot-btn': !item.isAdd }"
                :disabled="item.isAdd"
                @click="handleAddSchedule(item.date)">
                添加排课
              </a-button>
            </div>
          </div>
        </div>
      </a-spin>
    </a-card>
    <AddSchedule
      v-if="isShowAddSchedule"
      :id="scheduleId"
      v-model="isShowAddSchedule"
      :date-time="dateTime"
      :bus-id="searchParam.bus_id"
      :is-just-look="isJustLook"
      @success="getListInfo" />
    <CopySchedule
      v-model="isShowCopySchedule"
      :pick-up-flag="pickUpFlag"
      :pick-up-ids="pickUpIds"
      :week-date-string="weekDateString"
      :search-param="searchParam"
      @success="handleCopySuccess"
      @cancel="handleCopyCancel" />
    <CopyOneSchedule
      v-model="isShowCopyOneSchedule"
      :info="copyOneInfo"
      :search-param="searchParam"
      @success="getListInfo" />
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { getClassList, delScheduleOnce, delSchedules, batchDelSchedules } from '@/api/course-schedule';
  import { useBusInfoStore } from '@/store';
  import dayjs from '@/utils/dayjs';
  import { goSchedulePreview } from '@/utils/router-go';
  import AddSchedule from './components/add-schedule.vue';
  import CopySchedule from './components/copy-schedule.vue';
  import CopyOneSchedule from './components/copy-one-schedule.vue';
  // config dayjs
  defineOptions({
    name: 'OpenSchedule',
  });
  const busInfo = useBusInfoStore();
  const searchParam = reactive({
    course_id: '',
    coach_id: '',
    s_date: dayjs().weekday(0).format('YYYY-MM-DD'),
    e_date: dayjs().weekday(6).format('YYYY-MM-DD'),
    bus_id: busInfo.bus_id,
  });

  const weekPickerFlag = ref(false);
  // 时间周期文本
  const weekDateString = ref('');
  const weekList = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  const weekKey = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  // 周排课数据
  const weekDateList = ref([]);
  const weekInputRef = ref();
  // 选择复制
  const pickUpFlag = ref(false);
  const pickUpIds = ref([]);

  const { isLoading: isListLoading, execute: getList } = getClassList({});
  // 本周已排课程中，获取用于筛选的教练、课程选项列表
  const weekCoachList = ref([]);
  const weekCourseList = ref([]);
  function getListInfo() {
    return getList({ data: searchParam }).then((res) => {
      const week = res.data.value.schedule_list;
      weekCourseList.value = [];
      weekCoachList.value = [];
      const coachIdArr = [];
      const courseIdArr = [];
      weekDateList.value.forEach((item) => {
        item.list = week[item.key];
        item.initList = week[item.key];
        week[item.key].forEach((i) => {
          if (weekCourseList.value.findIndex((j) => j.value === i.class_id) === -1) {
            weekCourseList.value.push({
              value: i.class_id,
              label: i.class_name,
            });
            courseIdArr.push(i.class_id);
          }
          if (weekCoachList.value.findIndex((j) => j.value === i.coach_id) === -1) {
            weekCoachList.value.push({
              value: i.coach_id,
              label: i.coach_name,
            });
            coachIdArr.push(i.coach_id);
          }
        });
      });
      searchParam.course_id = courseIdArr.includes(searchParam.course_id) ? searchParam.course_id : '';
      searchParam.coach_id = coachIdArr.includes(searchParam.coach_id) ? searchParam.coach_id : '';
      if (searchParam.course_id || searchParam.coach_id) {
        weekDateList.value.forEach((item) => {
          item.list = item.initList.filter((i) => {
            return (
              (searchParam.course_id ? i.class_id === searchParam.course_id : true) &&
              (searchParam.coach_id ? i.coach_id === searchParam.coach_id : true)
            );
          });
        });
      }

      return res.data.value;
    });
  }

  function initWeekDateList() {
    const start = searchParam.s_date;
    const end = dayjs(start).add(6, 'day').format('YYYY-MM-DD');
    searchParam.e_date = end;
    weekDateString.value = `${start} ~ ${end}`;
    weekDateList.value = [];
    for (let i = 0; i < 7; i += 1) {
      const date = dayjs(start).add(i, 'day');
      const formatDate = date.format('YYYY-MM-DD');
      const day = date.format('M/DD');
      weekDateList.value.push({
        week: weekList[i],
        date: formatDate,
        day,
        key: weekKey[i],
        isToday: formatDate === dayjs().format('YYYY-MM-DD'),
        isAdd: formatDate < dayjs().format('YYYY-MM-DD'),
        list: [],
        initList: [],
      });
    }
    getListInfo();
  }
  function handleBusChange() {
    searchParam.coach_id = '';
    searchParam.course_id = '';
    getListInfo();
  }
  function handleWeekSelect() {
    weekPickerFlag.value = false;
    weekInputRef.value.blur();
    initWeekDateList();
  }

  // 复制
  const isShowCopySchedule = ref(false);
  const isShowCopyOneSchedule = ref(false);
  const copyOneInfo = ref({});
  function handleCopy(info) {
    copyOneInfo.value = info;
    isShowCopyOneSchedule.value = true;
  }

  // 删除
  function handleDel(info) {
    const content = `确定删除“${info.class_name}”课程,同时将全部预约取消，并自动进行退款？`;
    const { isLoading: delLoading, execute: postToDel } = delScheduleOnce();
    Modal.confirm({
      title: '删除排课',
      content,
      okButtonProps: {
        loading: delLoading,
      },
      onBeforeOk: async () => {
        await postToDel({
          data: {
            schedule_id: info.id,
            bus_id: searchParam.bus_id,
          },
        });
        Message.success('删除成功！');
        initWeekDateList();
      },
    });
  }
  function handleBatchDel(info) {
    const content = `确定要删除所有未开始的“${info.class_name}”课程,同时将全部预约取消，并自动进行退款？`;
    const { isLoading: delLoading, execute: postToDel } = delSchedules();
    Modal.confirm({
      title: '批量删除排课',
      content,
      okButtonProps: {
        loading: delLoading,
      },
      onBeforeOk: async () => {
        await postToDel({
          data: {
            schedule_id: info.id,
            bus_id: searchParam.bus_id,
          },
        });
        Message.success('批量删除排课成功！');
        initWeekDateList();
      },
    });
  }
  // 清空本周排课
  const { isLoading: isClearing, execute: postToClear } = batchDelSchedules();
  function clearWeek() {
    const content = `确认删除“${weekDateString.value}”所有未开始的课程排课？若课程有学员预约，请自行通知学员课程已变更。`;
    Modal.confirm({
      title: '清空本周排课',
      content,
      onOk: async () => {
        const { s_date, e_date, bus_id } = searchParam;
        await postToClear({
          data: {
            s_date,
            e_date,
            bus_id,
          },
        });
        Message.success('清空排课成功！');
        initWeekDateList();
      },
    });
  }

  // 选择复制
  function handleCopyOpen() {
    isShowCopySchedule.value = true;
  }
  function handlePickUpToCopy() {
    if (pickUpFlag.value) {
      if (Array.isArray(pickUpIds.value) && pickUpIds.value.length > 0) {
        handleCopyOpen();
      } else {
        pickUpFlag.value = false;
        pickUpIds.value = [];
      }
    } else {
      pickUpFlag.value = true;
    }
  }

  function handleCopySuccess() {
    pickUpFlag.value = false;
    pickUpIds.value = [];
  }
  function handleCopyCancel() {
    pickUpFlag.value = false;
    pickUpIds.value = [];
  }

  function isPickDownCur(id) {
    if (pickUpFlag.value) {
      return pickUpIds.value.findIndex((item) => item === id) === -1;
    }
    return false;
  }

  function handlePickUp(id) {
    const idx = pickUpIds.value.findIndex((item) => item === id);
    if (idx === -1) {
      pickUpIds.value.push(id);
    } else {
      pickUpIds.value.splice(idx, 1);
    }
  }

  // 生成课表
  function handleToClassPreview() {
    const scheduleList = weekDateList.value.map(({ list }) => list);
    goSchedulePreview({
      selectedBusId: searchParam.bus_id,
      scheduleList: JSON.stringify(scheduleList),
      weekDateString: weekDateString.value,
    });
  }
  // 添加、编辑、详情排课弹窗
  const isShowAddSchedule = ref(false);
  const isJustLook = ref(false);
  const dateTime = ref('');
  const scheduleId = ref('');
  function handleAddSchedule(date) {
    scheduleId.value = '';
    dateTime.value = date;
    isJustLook.value = false;
    isShowAddSchedule.value = true;
  }
  function handleEdit(id) {
    scheduleId.value = id;
    isJustLook.value = false;
    isShowAddSchedule.value = true;
  }
  function handleLook(id) {
    scheduleId.value = id;
    isJustLook.value = true;
    isShowAddSchedule.value = true;
  }
  onMounted(() => {
    initWeekDateList();
  });
</script>

<style lang="less" scoped>
  .open-schedule {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .general-card {
    :deep(.arco-card-body) {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
  .plan-week {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 100%;

    .plan-day {
      display: flex;
      flex-direction: column;
      margin-top: 16px;
      width: 14%;
      .course-scroll {
        flex-grow: 1;
        overflow: scroll;
      }
      .plan-today {
        background-color: rgb(var(--primary-6));
        box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.5);
        transform: scale(1.02);
        z-index: 99;
        color: #fff;
        :deep(.arco-card-body) {
          color: #fff;
        }
      }

      .plan-head {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 1px solid #ccc;
        height: 66px;
        line-height: 1.5;
      }

      .plan-body {
        min-height: 100px;
        padding: 8px;
        margin: 8px 0;
        vertical-align: middle;
        box-shadow: 0 0 1px transparent;
        position: relative;
        background-color: #fbfbfb;

        .pick-up-me {
          height: 40px;
          width: 40px;
          border-radius: 50%;
          overflow: hidden;
          position: absolute;
          top: 50% - 20px;
          left: 50% - 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 999;
          cursor: pointer;
        }

        .plan-info {
          cursor: pointer;
          line-height: 1.5;
          .title {
            font-size: 16px;
            font-weight: bold;
            line-height: 1.75;
          }
          .title,
          .coche {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .gym {
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .gym-name {
              white-space: nowrap;
              width: 60%;
            }
          }
        }

        .plan-contraller {
          display: none;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          color: #666;

          .plan-ctrl-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            color: #333;
            margin-top: 8px;
          }
        }
      }
      .hover-icon {
        color: #52a4ea;
        font-size: 22px;
        margin-bottom: 4px;
      }

      .plan-body:not(.nohover):before {
        content: '';
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.2);
      }

      .scroll-down {
        background: white;
        opacity: 0.4;
      }

      .plan-body:not(.nohover):hover .plan-contraller {
        display: flex;
        flex-direction: column;
      }

      .plan-body:not(.nohover):hover .plan-info {
        opacity: 0.1;
      }

      .plan-foot {
        flex-shrink: 0;
        height: 60px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
</style>
