<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="search" label="姓名/电话">
                  <a-input v-model="searchParam.search" placeholder="请输入" allow-clear @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="coupon_id" label="折扣券">
                  <BusDiscount v-model="searchParam.coupon_id" show-history allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="status" label="使用状态">
                  <a-select v-model="searchParam.status" placeholder="请选择" allow-clear>
                    <a-option value="1">未使用</a-option>
                    <a-option value="2">已使用</a-option>
                    <a-option value="3">已过期</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12"></a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel ref="exportExcel">
            <template #default="{ handleExport }">
              <a-button @click="handleClickExport(handleExport as Callback<ExportData>)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column title="发放时间" data-index="create_time" />
          <a-table-column title="折扣券" data-index="coupon_name" :width="200" />
          <a-table-column title="姓名" data-index="user_name">
            <template #cell="{ record }">
              <a-link
                :href="goSubDetail(record.user_id, record.bus_id, false)"
                @click.prevent="handleToUserDetail(record)">
                {{ record.user_name }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="赠送原因" data-index="reason" />
          <a-table-column title="发放账号" data-index="admin_name" />
          <a-table-column title="使用状态" data-index="usage" />
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { goSubDetail } from '@/utils/router-go';
  import BusDiscount from '@/components/form/bus-discount.vue';
  import { getGrantList } from '@/api/coupon';
  import useTableProps from '@/hooks/table-props';
  import ExportExcel from '@/components/exportExcel.vue';
  import { Callback, ExportData } from '@/types/global';

  const STATUS = ['未使用', '已使用', '已过期'];
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    getGrantList,
    (list) => {
      console.log(list);
      return list.map((v) => {
        v.status = +v.status;
        v.usage = STATUS[v.status - 1];
        v.reason = v.reason.replace(',', '，');
        return v;
      });
    }
  );
  // 跳转会员详情
  const handleToUserDetail = (record: Record<string, any>) => {
    goSubDetail(record.user_id, record.bus_id);
  };
  const route = useRoute();
  const { id } = route.params;
  // 设置除分页外的其它属性值
  setSearchParam({
    coupon_id: id || '',
    search: '',
    status: '',
  });

  loadTableList();
  const handleClickExport = (cb: Callback<ExportData>) => {
    const columns = [
      { title: '发放时间', dataIndex: 'create_time' },
      { title: '折扣券', dataIndex: 'coupon_name' },
      { title: '姓名', dataIndex: 'user_name' },
      { title: '赠送原因', dataIndex: 'reason' },
      { title: '发放账号', dataIndex: 'admin_name' },
      { title: '使用状态', dataIndex: 'usage' },
    ];
    loadTableList(true).then((list) => {
      cb({
        filename: '发放记录',
        columns,
        data: list,
      });
    });
  };
</script>
