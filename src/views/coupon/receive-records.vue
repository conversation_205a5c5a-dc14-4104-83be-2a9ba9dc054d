<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="search" label="姓名/电话">
                  <a-input v-model="searchParam.search" placeholder="请输入" allow-clear @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="status" label="是否会员">
                  <a-select v-model="searchParam.is_member" placeholder="会员/潜客" allow-clear>
                    <a-option value="1">会员</a-option>
                    <a-option value="2">潜客</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="status" label="获取方式">
                  <a-select v-model="searchParam.receive_type" placeholder="请选择" allow-clear>
                    <a-option v-for="(item, index) in RECEIVES" :key="index" :value="`${index + 1}`">
                      {{ item }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="coupon_id" label="折扣券">
                  <BusDiscount v-model="searchParam.coupon_id" show-history allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="status" label="使用状态">
                  <a-select v-model="searchParam.status" placeholder="请选择" allow-clear>
                    <a-option value="1">未使用</a-option>
                    <a-option value="2">已使用</a-option>
                    <a-option value="3">已过期</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <Total v-model="totalItems" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12"> </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel ref="exportExcel">
            <template #default="{ handleExport }">
              <a-button @click="handleClickExport(handleExport as Callback<ExportData>)"> 导出 </a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column title="姓名/昵称" data-index="user_name">
            <template #cell="{ record }">
              <a-link @click.prevent="goSubDetail(record.user_id)">
                {{ record.user_name }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="电话" data-index="phone" />
          <a-table-column title="会员/潜客" data-index="is_member" />
          <a-table-column title="获取方式" data-index="receive_type" />
          <a-table-column title="转赠次数" data-index="transfer_times" />
          <a-table-column title="最初拥有者" data-index="first_owner">
            <template #cell="{ record }">
              <a-link v-if="record.first_owner_id" @click.prevent="goSubDetail(record.first_owner_id)">
                {{ record.first_owner }}
              </a-link>
              <span v-else>{{ record.first_owner || '-' }}</span>
            </template>
          </a-table-column>

          <a-table-column title="折扣券" data-index="coupon_name" />
          <a-table-column title="使用状态" data-index="usage" />
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { goSubDetail } from '@/utils/router-go';
  import BusDiscount from '@/components/form/bus-discount.vue';
  import Total from '@/components/form/total.vue';
  import { getReceiveList } from '@/api/coupon';
  import useTableProps from '@/hooks/table-props';
  import ExportExcel from '@/components/exportExcel.vue';
  import { Callback, ExportData } from '@/types/global';

  defineOptions({ name: 'ReceiveRecords' });
  const STATUS = ['未使用', '已使用', '已过期'];
  const RECEIVES = [
    '前台发放',
    '红包',
    '魅力值兑换',
    '他人转赠(前台)',
    '他人转赠(红包)',
    '他人转赠(魅力值)',
    '运营计划',
    '客带客活动奖励',
    '积分兑换',
    '他人转赠(积分)',
    '接龙活动购买',
    '团购兑换',
  ];
  const totalItems = ref([
    { value: 0, name: '总发放量' },
    { value: 0, name: '转赠次数' },
    { value: 0, name: '已使用量' },
    { value: '0 / 0', name: '潜客 / 会员' },
  ]);
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    getReceiveList,
    (list) => {
      return list.map((v) => {
        v.status = +v.status;
        v.usage = STATUS[v.status - 1];
        return v;
      });
    },
    (res) => {
      const data = res.data.value;
      totalItems.value = [
        { value: data.grant_count, name: '总发放量' },
        { value: data.transfer_count, name: '转赠次数' },
        { value: data.used_count, name: '已使用量' },
        { value: `${data.submersible_count} / ${data.member_count}`, name: '潜客 / 会员' },
      ];
    }
  );
  const route = useRoute();
  const { id } = route.params;
  // 设置除分页外的其它属性值
  setSearchParam({
    search: '',
    is_member: '',
    receive_type: '',
    status: '',
    coupon_id: id || '',
  });

  loadTableList(false, (res) => {});
  const handleClickExport = (cb: Callback<ExportData>) => {
    const columns = [
      { title: '姓名/昵称', dataIndex: 'user_name' },
      { title: '电话', dataIndex: 'phone' },
      { title: '会员/潜客', dataIndex: 'is_member' },
      { title: '获取方式', dataIndex: 'receive_type' },
      { title: '转赠次数', dataIndex: 'transfer_times' },
      { title: '最初拥有者', dataIndex: 'first_owner' },
      { title: '折扣券', dataIndex: 'coupon_name' },
      { title: '使用状态', dataIndex: 'usage' },
    ];
    loadTableList(true).then((list) => {
      cb({
        filename: '折扣券领取记录',
        columns,
        data: list,
      });
    });
  };
</script>
