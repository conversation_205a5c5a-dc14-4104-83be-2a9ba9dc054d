<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
        <a-form-item label="折扣券名称" field="coupon_name" :rules="{ required: true, message: '请输入' }">
          <a-input v-model="formData.coupon_name" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="折扣金额" field="discount_amount" :rules="{ required: true, message: '请输入' }">
          <a-input-number v-model="formData.discount_amount" :min="1" :precision="2" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="满减条件">
          <a-radio-group v-model="formData.limit_type">
            <a-radio value="99">无条件</a-radio>
            <a-radio value="1">达到指定金额可用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="formData.limit_type == 1"
          field="limit_amount"
          label="使用条件"
          :rules="{ required: true, message: '请输入' }">
          <a-space>
            单笔合同订单消费满
            <a-input-number v-model="formData.limit_amount" :min="1" :precision="2" class="input" />
            元可使用折扣券
          </a-space>
        </a-form-item>
        <a-form-item
          field="use_limit"
          label="使用范围"
          :content-flex="false"
          :rules="{ required: true, validator: checkLimit, message: '至少选择一项' }">
          <a-card class="use-limit-card">
            <a-space direction="vertical" style="width: 100%" :size="16">
              <a-checkbox-group v-model="formData.use_limit_zero">
                <a-checkbox value="1">购卡/购课</a-checkbox>
                <a-checkbox value="2">续卡/续课</a-checkbox>
                <a-checkbox value="3">卡课升级</a-checkbox>
              </a-checkbox-group>
              <a-radio-group
                v-if="formData.use_limit_zero.length > 0"
                v-model="formData.limit_card"
                @change="handleLimitCard">
                <a-radio value="1">除储值卡所有卡种</a-radio>
                <a-radio value="2">仅限购会员卡(除储值卡)</a-radio>
                <a-radio value="3">仅限购私教课</a-radio>
                <a-radio value="4">指定卡种</a-radio>
              </a-radio-group>
              <a-form-item
                v-if="formData.use_limit_zero.length > 0 && formData.limit_card === '4'"
                :rules="{ required: true, type: 'array', min: 1, message: '请选择可使用的卡种' }"
                field="card_species"
                label="指定卡种">
                <a-select v-model="formData.card_species" multiple placeholder="请选择" allow-clear>
                  <a-option v-for="item in couponCardList" :key="item.card_id" :value="item.card_id">
                    {{ item.card_name }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-space>
          </a-card>
          <a-card class="use-limit-card">
            <a-space direction="vertical" style="width: 100%" :size="16">
              <a-checkbox-group v-model="formData.use_limit_one">
                <a-checkbox value="4">订场地</a-checkbox>
              </a-checkbox-group>
              <a-radio-group
                v-if="formData.use_limit_one.length > 0"
                v-model="formData.limit_space_type"
                @change="handleLimitSpace">
                <a-radio value="1">全部场地</a-radio>
                <a-radio value="2">指定场地类型</a-radio>
              </a-radio-group>
              <a-form-item
                v-if="formData.limit_space_type === '2' && formData.use_limit_one.length > 0"
                field="space_type_species"
                class="reset-padding"
                :rules="{ required: true, type: 'array', min: 1, message: '请选择' }"
                label="指定场地类型">
                <a-select v-model="formData.space_type_species" multiple allow-search placeholder="请选择">
                  <a-option
                    v-for="item in bookingTypeList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name"></a-option>
                </a-select>
              </a-form-item>
            </a-space>
          </a-card>

          <a-card class="use-limit-card">
            <a-space direction="vertical" style="width: 100%" :size="16">
              <a-checkbox-group v-model="formData.use_limit_two">
                <a-checkbox value="5">购散场票(仅限会员小程序)</a-checkbox>
              </a-checkbox-group>
              <a-radio-group
                v-if="formData.use_limit_two.length > 0"
                v-model="formData.limit_san_rule"
                @change="handleLimitSan">
                <a-radio value="1">全部散场票</a-radio>
                <a-radio value="2">指定散场票</a-radio>
              </a-radio-group>
              <a-form-item
                v-if="formData.limit_san_rule === '2' && formData.use_limit_two.length > 0"
                field="san_rule_species"
                :rules="{ required: true, type: 'array', min: 1, message: '请选择' }"
                label="指定散场票">
                <a-select v-model="formData.san_rule_species" multiple allow-search placeholder="请选择">
                  <a-option
                    v-for="item in sanRuleSpeciesList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name"></a-option>
                </a-select>
              </a-form-item>
            </a-space>
          </a-card>

          <a-card class="use-limit-card">
            <a-space direction="vertical" style="width: 100%" :size="16">
              <a-checkbox-group v-model="formData.use_limit_class" @change="handleClassAuth">
                <a-checkbox value="6">团课预约(仅限会员小程序 ，且团课必选用微信支付不能用卡内余额支付)</a-checkbox>
              </a-checkbox-group>
              <a-radio-group
                v-if="formData.use_limit_class.length > 0"
                v-model="formData.limit_class"
                @change="handleLimitClass">
                <a-radio value="1">全部团课</a-radio>
                <a-radio value="2">指定团课</a-radio>
              </a-radio-group>
              <a-form-item
                v-if="formData.limit_class === '2' && formData.use_limit_class.length > 0"
                field="class_species"
                :rules="{ required: true, type: 'array', min: 1, message: '请选择' }"
                label="指定团课">
                <a-select v-model="formData.class_species" multiple allow-search placeholder="请选择">
                  <a-option v-for="item in classList" :key="item.id" :value="item.id">{{ item.class_name }}</a-option>
                </a-select>
              </a-form-item>
            </a-space>
          </a-card>
        </a-form-item>
        <a-form-item label="有效期">
          <a-radio-group v-model="formData.use_time_type">
            <a-radio value="1">固定有效期</a-radio>
            <a-radio value="2">领取后开始计算有效期</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="formData.use_time_type === '2'"
          label="有效天数"
          field="use_days"
          :rules="{ required: true, message: '请输入' }">
          <a-input-number v-model="formData.use_days" :min="1" :precision="0" placeholder="天" />
        </a-form-item>
        <a-form-item
          v-if="formData.use_time_type === '1'"
          label="有效期时间"
          field="start_time"
          :rules="{ required: true, message: '请选择' }">
          <a-range-picker
            v-model="duringDate"
            style="width: 100%"
            :placeholder="['开始时间', '结束时间']"
            :disabled-date="(current) => dayjs(current).isBefore(dayjs().subtract(1, 'days'))"
            @change="handleDateChange" />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" :loading="isLoading" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  import { getListAll } from '@/api/san-rule';
  import { getTypes } from '@/api/space';
  import { cardList, addCoupon, openClassCoupon } from '@/api/coupon';
  import { getOpenClassAll } from '@/api/open-class';
  import { useBusInfoStore } from '@/store';

  const busInfo = useBusInfoStore();
  const formRef = ref<FormInstance>();
  const formData = reactive({
    coupon_name: '',
    discount_amount: 1,
    limit_amount: null,
    use_days: null,
    use_limit: [],
    limit_type: '1',
    card_species: [],
    dateRange: [],
    start_time: '',
    end_time: '',
    describe: '',
    use_limit_zero: ['1'],
    limit_card: '1',
    use_limit_one: [],
    limit_space_type: '1',
    space_type_species: [],
    use_limit_two: [],
    limit_san_rule: '1',
    limit_class: '1',
    use_time_type: '1',
    use_limit_class: [],
    san_rule_species: [],
    class_species: [],
  });

  function checkLimit(value, callback) {
    const useLimitArr = formData.use_limit.concat(
      formData.use_limit_zero,
      formData.use_limit_one,
      formData.use_limit_two,
      formData.use_limit_class
    );
    if (useLimitArr.length) {
      callback();
      return true;
    }
    callback('至少选择一个范围');
    return false;
  }

  const duringDate = ref([]);
  function handleDateChange(val: any) {
    const [start, end] = val || ['', ''];
    formData.start_time = start;
    formData.end_time = end;
  }
  const couponCardList = ref([]);
  const bookingTypeList = ref([]);
  const sanRuleSpeciesList = ref([]);
  const classList = ref([]);
  function handleLimitCard(type) {
    if (type === '4' && !couponCardList.value.length) {
      cardList().then((res) => {
        couponCardList.value = res.data.value;
      });
    }
  }
  function handleLimitSpace(type) {
    if (type === '2' && !bookingTypeList.value.length) {
      getTypes().then((res) => {
        bookingTypeList.value = res.data.value;
      });
    }
  }
  function handleLimitSan(type) {
    if (type === '2' && !sanRuleSpeciesList.value.length) {
      getListAll().then((res) => {
        sanRuleSpeciesList.value = res.data.value;
      });
    }
  }
  const classAuth = ref(false);
  function getClassAuth() {
    openClassCoupon()
      .then((res) => {
        classAuth.value = res.response.value.errorcode === 0;
      })
      .catch(() => {
        classAuth.value = false;
      });
  }
  getClassAuth();
  function handleClassAuth() {
    const unAuthValue = formData.use_limit_class[0] ? [] : ['6'];
    if (!classAuth.value) {
      formData.use_limit_class = unAuthValue;
      Message.error('无操作权限，不能操作');
    }
  }
  function handleLimitClass(type) {
    if (type === '2' && !classList.value.length) {
      getOpenClassAll({
        bus_id: busInfo.bus_id,
      }).then((res) => {
        classList.value = res.data.value.list;
      });
    }
  }

  const { isLoading, execute } = addCoupon();
  const router = useRouter();
  async function handleSubmit() {
    const errors = await formRef.value?.validate();
    if (!errors) {
      const postForm = { ...formData };
      if (postForm.limit_card !== '4') {
        postForm.card_species = [];
      }
      if (postForm.limit_space_type !== '2') {
        postForm.space_type_species = [];
      }
      if (postForm.limit_san_rule !== '2') {
        postForm.san_rule_species = [];
      }
      if (postForm.limit_class !== '2') {
        postForm.class_species = [];
      }

      postForm.use_limit = postForm.use_limit.concat(
        postForm.use_limit_zero,
        postForm.use_limit_one,
        postForm.use_limit_two,
        postForm.use_limit_class
      );

      delete postForm.use_limit_zero;
      delete postForm.use_limit_one;
      delete postForm.use_limit_two;
      delete postForm.use_limit_class;
      const postData = {
        ...postForm,
        card_species: postForm.card_species && postForm.card_species.length ? postForm.card_species.join(',') : '',
        use_limit: postForm.use_limit.sort().join(),
        space_type_species:
          postForm.space_type_species && postForm.space_type_species.length
            ? postForm.space_type_species.sort().join()
            : '',
        san_rule_species:
          postForm.san_rule_species && postForm.san_rule_species.length ? postForm.san_rule_species.sort().join() : '',
        class_species:
          postForm.class_species && postForm.class_species.length ? postForm.class_species.sort().join() : '',
      };
      execute({
        data: postData,
      }).then(() => {
        Message.success('操作成功');
        router.back();
      });
    }
  }
</script>

<style lang="less" scoped>
  .use-limit-card {
    width: 100%;
    margin-bottom: 16px;
  }
</style>
