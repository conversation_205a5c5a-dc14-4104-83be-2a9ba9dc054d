<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="coupon_name" label="折扣券名称">
                  <a-input
                    v-model="searchParam.coupon_name"
                    placeholder="请输入"
                    allow-clear
                    @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="class_id" label="状态">
                  <a-select v-model="searchParam.status" placeholder="请选择" allow-clear>
                    <a-option value="1">启用</a-option>
                    <a-option value="2">禁用</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="$router.push('/coupon/add')">新增</a-button>
            <a-button :loading="isBatchCancelLoading" @click="batchChangeStatus('1')">批量启用</a-button>
            <a-button :loading="isBatchCancelLoading" @click="batchChangeStatus('2')">批量禁用</a-button>
            <a-button :loading="isBatchDelLoading" @click="batchDel">批量删除</a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel ref="exportExcel">
            <template #default="{ handleExport }">
              <a-button @click="handleClickExport(handleExport as Callback<ExportData>)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-model:selectedKeys="selectedIds" v-on="tableEvent">
        <template #columns>
          <a-table-column title="折扣券名称" data-index="coupon_name" :width="200" />
          <a-table-column title="折扣金额" data-index="discount_amount" align="right" :width="120" />
          <a-table-column title="限制" data-index="limits_arr_text" ellipsis tooltip />
          <a-table-column title="使用范围" data-index="use_limit_text" ellipsis tooltip />
          <a-table-column title="使用状态" data-index="status_text">
            <template #cell="{ record }">
              <a-switch
                v-model="record.status"
                checked-value="1"
                unchecked-value="2"
                :loading="record.isStatusLoading"
                @change="changeStatus(record)" />
            </template>
          </a-table-column>
          <a-table-column title="使用有效期" data-index="validDate" />
          <a-table-column title="已使用/已发出" data-index="usage">
            <template #cell="{ record }">
              <a-link @click="$router.push('/coupon/receive-records/' + record.coupon_id)">{{ record.usage }}</a-link>
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="$router.push('/coupon/out-records/' + record.coupon_id)">发放记录</a-link>
                <a-link status="danger" :loading="record.isDelLoading" @click="delPlan(record)">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { couponList, setCouponStatus, delCoupon } from '@/api/coupon';
  import useTableProps from '@/hooks/table-props';
  import ExportExcel from '@/components/exportExcel.vue';
  import { Callback, ExportData } from '@/types/global';
  import { DISCOUNT_CARD_LIMIT as LIMIT_TYPE } from '@/store/constants';

  defineOptions({
    name: 'CouponList',
  });
  const useLimit = {
    '1': '购卡/购课',
    '2': '续卡/续课',
    '3': '卡课升级',
    '4': '订场地',
    '5': '购散场票',
    '6': '团课预约',
  };
  const selectedIds = ref([]);
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    couponList,
    (list) => {
      return list.map((v) => {
        const textArr = [
          v.limit_type === '1' ? `消费满${v.limit_amount}元使用` : '',
          LIMIT_TYPE[v.limit_card - 1],
          v.limit_space_type === '1' ? '全部场地' : v.limit_space_type === '2' ? '仅限指定场地类型' : '',
          v.limit_san_rule === '1' ? '全部散场票' : v.limit_san_rule === '2' ? '仅限指定散场票' : '',
          v.limit_class === '1' ? '全部团课' : v.limit_class === '2' ? '指定团课' : '',
        ];
        v.limits_arr_text = textArr.filter((t) => t).join('、');
        v.status_text = v.status === '1' ? '已启用' : '禁用';
        v.validDate = v.use_time_type === '1' ? `${v.start_time}~${v.end_time}` : `${v.use_days}`;
        v.usage = `${v.use_num}/${v.issue_num}`;
        v.use_limit_text = v.use_limit
          ? v.use_limit
              .split(',')
              .map((i) => useLimit[i])
              .join('、')
          : useLimit['1'];
        return v;
      });
    }
  );

  tableProps.value['row-key'] = 'coupon_id';
  tableProps.value['row-selection'] = {
    type: 'checkbox',
    showCheckedAll: true,
  };
  // 设置除分页外的其它属性值
  setSearchParam({
    coupon_name: '',
    status: '',
  });
  onActivated(() => {
    loadTableList();
  });
  function changeStatus(record: Record<string, any>) {
    const { isLoading, execute: executeChange } = setCouponStatus();
    record.isStatusLoading = isLoading;
    executeChange({ data: { coupon_ids: record.coupon_id, status: record.status } })
      .then((res) => {
        loadTableList();
        Message.success(res.response.value.errormsg);
      })
      .catch(() => {
        record.status = record.status === '1' ? '2' : '1';
      });
  }
  // 批量启用/禁用
  const { isLoading: isBatchCancelLoading, execute: executeChange } = setCouponStatus();
  function batchChangeStatus(status) {
    if (!selectedIds.value.length) {
      Message.error('请先勾选需要修改的优惠券');
      return;
    }
    executeChange({ data: { coupon_ids: selectedIds.value.join(','), status } })
      .then((res) => {
        loadTableList();
        selectedIds.value = [];
        Message.success(res.response.value.errormsg);
      })
      .catch(() => {
        loadTableList();
      });
  }

  function delPlan(record: Record<string, any>) {
    const { isLoading, execute: executeDel } = delCoupon();
    record.isDelLoading = isLoading;
    Modal.confirm({
      title: '删除折扣券',
      content: '确定要删除折扣券吗?',
      onOk: () => {
        executeDel({ data: { coupon_id: record.coupon_id } }).then((res) => {
          loadTableList();
          Message.success('删除成功');
        });
      },
    });
  }

  // 批量删除
  const { isLoading: isBatchDelLoading, execute: executeBatchDel } = delCoupon();
  function batchDel() {
    if (!selectedIds.value.length) {
      Message.error('请先勾选需要删除的折扣券');
      return;
    }
    Modal.confirm({
      title: '删除折扣券',
      content: '确定要批量删除折扣券吗?',
      onOk: () => {
        executeBatchDel({ data: { coupon_id: selectedIds.value.join(',') } }).then((res) => {
          selectedIds.value = [];
          loadTableList();
          Message.success('删除成功');
        });
      },
    });
  }

  const handleClickExport = (cb: Callback<ExportData>) => {
    const columns = [
      { title: '折扣券名称', dataIndex: 'coupon_name' },
      { title: '折扣金额', dataIndex: 'discount_amount' },
      { title: '限制', dataIndex: 'limits_arr_text' },
      { title: '使用范围', dataIndex: 'use_limit_text' },
      { title: '状态', dataIndex: 'status_text' },
      { title: '使用有效期', dataIndex: 'validDate' },
      { title: '已使用/已发出', dataIndex: 'usage' },
    ];
    loadTableList(true).then((list) => {
      cb({
        filename: '折扣券列表',
        columns,
        data: list,
      });
    });
  };
</script>
