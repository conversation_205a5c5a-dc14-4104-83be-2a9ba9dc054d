<template>
  <div></div>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import { useAppStore } from '@/store';

  const router = useRouter();
  const appStore = useAppStore();
  const menuTree = computed(() => appStore.menuTree);
  // 寻找第一个菜单的path
  watchEffect(() => {
    if (menuTree.value.length) {
      const menu = menuTree.value.find((item: Record<string, any>) => {
        if (item.path) return true;
        return item.children && item.children.length > 0;
      });
      const path = menu.path || menu.children[0].path;
      if (path) {
        router.replace({ path });
      }
    }
  });
</script>

<style scoped lang="less"></style>
