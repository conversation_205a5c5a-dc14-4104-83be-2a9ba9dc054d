<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
        <a-form-item field="title" label="规则名称" :rules="{ required: true, message: '请填写' }">
          <a-input v-model="formData.title" />
        </a-form-item>
        <a-form-item field="sign_amount" label="签约人数提成" :rules="{ required: true, message: '请填写' }">
          <a-input-number v-model="formData.sign_amount" placeholder="请填写" :min="0" :max="9999">
            <template #suffix>
              <span>元/人</span>
            </template>
          </a-input-number>
        </a-form-item>
        <a-form-item field="commit_type" label="合约回款提成" :rules="{ required: true, message: '请选择' }">
          <a-radio-group v-model="formData.commit_type">
            <a-radio :value="1">按回款金额提成</a-radio>
            <a-radio :value="2">按签约金额提成</a-radio>
          </a-radio-group>
        </a-form-item>

        <template v-if="formData.commit_type === 1">
          <a-alert type="warning" style="margin-bottom: 16px; margin-left: 200px; width: 70%">
            每笔合约每期扣款成功后，进行一次提成，按扣款金额的百分比进行计算
          </a-alert>
          <a-form-item
            field="first_percent"
            label="签约付费/首期扣款提成比例"
            :rules="{ required: true, message: '请填写' }">
            <a-input-number v-model="formData.first_percent" placeholder="请填写" :min="0" :max="100" :step="0.01">
              <template #suffix>
                <span>%</span>
              </template>
            </a-input-number>
          </a-form-item>
          <a-form-item
            field="deduction_percent"
            label="后续每期扣款提成比例"
            :rules="{ required: true, message: '请填写' }">
            <a-input-number v-model="formData.deduction_percent" placeholder="请填写" :min="0" :max="100" :step="0.01">
              <template #suffix>
                <span>%</span>
              </template>
            </a-input-number>
          </a-form-item>
        </template>

        <template v-if="formData.commit_type == 2">
          <a-alert type="warning" style="margin-bottom: 16px; margin-left: 150px; width: 70%">
            每笔合约每期扣款成功后，进行一次提成，按合约总金额的百分比进行计算
            <br />
            组合方案签约金额不会单独提成
          </a-alert>
          <a-form-item field="max_periods" label="最长提成周期" :rules="{ required: true, message: '请填写' }">
            <a-input-number
              v-model="formData.max_periods"
              placeholder="请填写"
              :min="0"
              :max="9999"
              :step="1"
              :precision="0" />
          </a-form-item>
          <a-form-item field="max_percent" label="累积提成比例不超过" :rules="{ required: true, message: '请填写' }">
            <a-input-number v-model="formData.max_percent" placeholder="请填写" :min="0" :max="100" :step="0.01">
              <template #suffix>
                <span>%</span>
              </template>
            </a-input-number>
          </a-form-item>
          <!-- 合约前 N 期数 * 每期提成比例 小于 累积提成比例不超过 -->
          <a-form-item
            field="first_stage_percent"
            label=""
            :rules="[{ required: true, message: '请填写' }, { validator: checkPercent }]">
            <span>合约</span>
            <a-input-number
              v-model="formData.first_stage_periods"
              placeholder="请填写"
              :min="1"
              :max="9999"
              :step="1"
              :precision="0"
              style="width: 100px; margin: 0 8px">
              <template #prefix>
                <span>前</span>
              </template>
              <template #suffix>
                <span>期</span>
              </template>
            </a-input-number>
            <span>每期提成比例</span>
            <a-input-number
              v-model="formData.first_stage_percent"
              placeholder="请填写"
              :min="0"
              :max="100"
              :step="0.01"
              style="width: 100px; margin: 0 8px">
              <template #suffix>
                <span>%</span>
              </template>
            </a-input-number>
          </a-form-item>
          <a-form-item field="" label="">
            <span>合约</span>
            <a-input-number
              v-model="formData.first_stage_periods"
              placeholder="请填写"
              :min="1"
              :max="9999"
              :step="1"
              style="width: 100px; margin: 0 8px"
              disabled>
              <template #suffix>
                <span>期后</span>
              </template>
            </a-input-number>
            <span>每期提成比例</span>
            <a-input-number
              v-model="formData.second_stage_percent"
              placeholder="请填写"
              :min="0"
              :max="100"
              :step="0.01"
              style="width: 100px; margin: 0 8px">
              <template #suffix>
                <span>%</span>
              </template>
            </a-input-number>
          </a-form-item>
        </template>

        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
    <MonthGrid v-if="loadModal" v-model:visible="visible" :rule-id="ruleId" :bus-id="Number(busId)" />
  </div>
</template>

<script lang="ts" setup>
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message } from '@arco-design/web-vue';
  // import { useBusInfoStore } from '@/store';
  // @ts-ignore
  import Big from 'big.js';
  import { getRule, saveRule } from '@/api/sales-commission';
  import MonthGrid from '@/views/alipay/components/MonthGrid.vue';
  import { useBusInfoStore } from '@/store';

  const busInfo = useBusInfoStore();

  defineOptions({
    name: 'SalesCommissionRuleSave',
  });

  interface RuleType {
    bus_id?: number;
    id?: string;
    title: string;
    sign_amount: number;
    commit_type: number;
    first_percent: number;
    deduction_percent: number;
    max_periods: number | undefined;
    max_percent: number;
    first_stage_periods: number;
    first_stage_percent: number;
    second_stage_percent: number;
  }

  // const { IS_BRAND_SITE } = window;
  // const busInfo = useBusInfoStore();
  const route = useRoute();
  const router = useRouter();
  // const { id: queryId, bus_id: busId } = route.query;
  const queryId = route.query.id;
  let busId = route.query.bus_id;
  busId = busId || busInfo.bus_id;
  const formRef = ref<FormInstance>();
  const formData = reactive<RuleType>({
    bus_id: Number(busId),
    title: '',
    sign_amount: 0,
    commit_type: 1,
    first_percent: 0,
    deduction_percent: 0,
    max_periods: undefined,
    max_percent: 20,
    first_stage_periods: 3,
    first_stage_percent: 0,
    second_stage_percent: 0,
  });

  if (queryId) {
    getRule({ id: queryId, bus_id: busId }).then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        Object.assign(formData, response.value.data);
      }
    });
  }

  //  合约前 N 期数 * 每期提成比例 小于 累积提成比例不超过
  const checkPercent = (value: number, cb: any) => {
    const period = new Big(formData.first_stage_periods);
    const percent = new Big(formData.first_stage_percent);
    const current = period.times(percent).toNumber();
    const max = new Big(formData.max_percent).toNumber();
    if (current > max) {
      return cb(`不能超过${max}%`);
    }
    return true;
  };

  const visible = ref(false);
  const ruleId = ref();
  const { isLoading, execute } = saveRule();
  const loadModal = ref(false);
  const handleSubmit = async () => {
    const flag = await formRef.value?.validate();
    if (!flag) {
      const postData = { ...formData };
      if (queryId) {
        postData.id = queryId as string;
      }
      const { response }: any = await execute({ data: postData });
      loadModal.value = true;
      if (response.value.errorcode === 0) {
        Message.success('操作成功！');
        ruleId.value = response.value.data.id;
        setTimeout(() => {
          visible.value = true;
        }, 1000);
        // router.back();
      }
    }
  };

  watch(
    () => visible.value,
    (val) => {
      if (!val) {
        router.back();
      }
    }
  );
</script>
