<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-tabs position="left">
        <a-tab-pane key="1" title="支付宝展现配置">
          <a-typography-title :heading="5">会员端展现配置</a-typography-title>
          <a-alert type="warning" style="margin-top: 16px">
            注意：勾选签约销售展现为“不展现”时，支付宝小程序上月付签约的销售人员字段将隐藏，同时将销售业绩默认为“场馆”
          </a-alert>
          <div class="line">
            <div class="label">签约销售展现:</div>
            <div class="value">
              <a-switch v-model="isShow" :disabled="!isEdit">
                <template #checked>开</template>
                <template #unchecked>关</template>
              </a-switch>
            </div>
          </div>
          <div style="margin-top: 20px">
            <a-button v-if="!isEdit" type="primary" @click="handleEdit">编辑</a-button>
            <blockquote v-else>
              <a-button type="primary" @click="handleSave">保存</a-button>
              <a-button style="margin-left: 20px" @click="handleCancel">取消</a-button>
            </blockquote>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" title="协议配置">
          <a-typography-title :heading="5">月付协议配置</a-typography-title>
          <Protocol />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
  import { Message } from '@arco-design/web-vue';
  import { saveSetting, getSetting } from '@/api/member-settings';
  import Protocol from './components/Protocol.vue';

  const isShow = ref(false);
  const isEdit = ref(false);

  const fetchSetting = () => {
    return getSetting().then(({ response }) => {
      if (response.value.errorcode === 0) {
        console.log(response.value.data);
        if (Number(response.value.data.marketers_show) === 1) {
          isShow.value = true;
        } else {
          isShow.value = false;
        }
        isEdit.value = false;
      }
    });
  };
  fetchSetting();

  const handleSave = () => {
    const marketers_show = isShow.value ? 1 : 2;
    return saveSetting({
      marketers_show,
    }).then(({ response }) => {
      if (response.value.errorcode === 0) {
        Message.success('保存成功');
        fetchSetting();
      }
    });
  };

  const handleEdit = () => {
    isEdit.value = true;
  };
  const handleCancel = () => {
    fetchSetting().then(() => {
      isEdit.value = false;
    });
  };
</script>

<style lang="less" scoped>
  .line {
    display: flex;
    align-items: center;
    margin-top: 16px;

    .label {
      width: 120px;
    }
  }
</style>
