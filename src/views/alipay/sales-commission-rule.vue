<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="bus_id" label="场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="title" label="方案名称">
                  <a-input v-model="searchParam.title" placeholder="请输入" allow-clear @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="$router.push(`/alipay/sales-commission-rule-save?bus_id=${busInfo.bus_id}`)">
            新增提成计算规则
          </a-button>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button :loading="isLoading" @click="handleClickExport(handleExport)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column title="方案名称" data-index="title" />
          <a-table-column title="创建时间" data-index="create_time">
            <template #cell="{ record }">
              {{ dayjs(record.create_time * 1000).format('YYYY-MM-DD HH:mm') }}
            </template>
          </a-table-column>
          <a-table-column title="规则描述" data-index="des" />
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link
                  @click="
                    $router.push(`/alipay/sales-commission-rule-save?id=${record.id}&bus_id=${searchParam.bus_id}`)
                  ">
                  编辑
                </a-link>
                <a-link status="danger" @click="handleRemove(record)">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { Modal, Message } from '@arco-design/web-vue';
  import { getRuleList, removeRule } from '@/api/sales-commission';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import useTableProps from '@/hooks/table-props';
  import { useBusInfoStore } from '@/store';
  import ExportExcel from '@/components/exportExcel.vue';

  defineOptions({
    name: 'SalesCommissionRule',
  });

  // const { IS_BRAND_SITE } = window;
  // const router = useRouter();
  const busInfo = useBusInfoStore();
  const { isLoading, tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getRuleList);

  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: busInfo.bus_id,
    title: '',
  });
  loadTableList();

  // event
  const handleRemove = (record: any) => {
    Modal.confirm({
      title: '提示',
      content: '确定删除吗？',
      okText: '删除',
      cancelText: '取消',
      onOk: async () => {
        const { response }: any = await removeRule({ id: record.id });
        if (response.value.errorcode === 0) {
          Message.success('删除成功');
          loadTableList();
        }
      },
    });
  };

  const handleClickExport = (cb: any) => {
    loadTableList(true).then((list) => {
      list.forEach((v: any) => {
        v.create_time_str = dayjs(v.create_time * 1000).format('YYYY-MM-DD HH:mm');
      });

      cb({
        filename: '方案列表',
        columns: [
          {
            title: '方案名称',
            dataIndex: 'title',
          },
          {
            title: '创建时间',
            dataIndex: 'create_time_str',
          },
          {
            title: '规则描述',
            dataIndex: 'des',
          },
        ],
        data: list,
      });
    });
  };
</script>
