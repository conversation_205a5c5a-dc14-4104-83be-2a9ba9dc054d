<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row style="margin-bottom: 16px">
        <a-col :flex="1">
          <a-tabs :default-active-key="1" :active-key="searchParam.belong_type" @change="handleTabChange">
            <a-tab-pane :key="1" title="会籍"></a-tab-pane>
            <a-tab-pane :key="2" title="私教"></a-tab-pane>
            <a-tab-pane :key="3" title="泳教"></a-tab-pane>
          </a-tabs>
        </a-col>
      </a-row>
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="bus_id" label="场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" @change="searchParam.belong_id = ''" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="commission_time" label="日期">
                  <a-month-picker v-model="searchParam.commission_time" placeholder="请选择" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="bus_id" label="销售人员">
                  <SalesSelect
                    v-model="searchParam.belong_id"
                    :belong-bus-id="searchParam.bus_id"
                    label-in-value
                    :is-membership="searchParam.belong_type == 1"
                    :is-pt-coach="searchParam.belong_type == 2"
                    :is-swim-coach="searchParam.belong_type == 3"
                    :show-coach-type="false"
                    placeholder="请选择"></SalesSelect>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearchThing">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="$router.push('/alipay/sales-commission-rule')">提成规则方案</a-button>
          <a-button style="margin-left: 16px" @click="handleSetting">提成规则执行周期设置</a-button>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button :loading="isLoading" @click="handleClickExport(handleExport)">导出</a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <span>计算提成规则:</span>
          <span v-if="rule?.id">
            {{ rule.title }}
            <a-link
              @click="router.push(`/alipay/sales-commission-rule-save?id=${rule.id}&bus_id=${searchParam.bus_id}`)">
              详情
            </a-link>
          </span>
          <span v-else>
            无
            <a-link @click="router.push('/alipay/sales-commission-rule-save')">去设置</a-link>
          </span>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <!-- 业绩人员 场馆 总提成 新签会员 新签会员提成 合约总回款 合约提成 -->
          <a-table-column title="业绩人员" data-index="name" />
          <a-table-column title="场馆" data-index="bus_name" />
          <a-table-column title="总提成" data-index="all_commission_amount" />
          <a-table-column title="新签会员" data-index="sign_num" />
          <a-table-column title="新签会员提成" data-index="sign_commission_amount">
            <template #cell="{ record }">
              <a-link
                @click="
                  router.push(
                    `/v2/alipay2/memberCommission/${searchParam.bus_id}/${searchParam.belong_type == 1 ? '' : 'c'}${
                      record.id
                    }/${searchParam.commission_time}`
                  )
                ">
                {{ Number(record.sign_commission_amount || 0).toFixed(2) }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="合约总回款" data-index="item_total_amount">
            <template #cell="{ record }">
              <span>{{ Number(record.item_total_amount || 0).toFixed(2) }}</span>
            </template>
          </a-table-column>
          <a-table-column title="合约提成" data-index="pay_commission_amount">
            <template #cell="{ record }">
              <a-link
                @click="
                  router.push(
                    `/v2/alipay2/contractCommission/${searchParam.bus_id}/${searchParam.belong_type == 1 ? '' : 'c'}${
                      record.id
                    }/${searchParam.commission_time}`
                  )
                ">
                {{ Number(record.pay_commission_amount || 0).toFixed(2) }}
              </a-link>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <MonthGrid v-model:visible="visible" :bus-id="Number(searchParam.bus_id)" />
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { getSummary } from '@/api/sales-commission';
  // import AdminRegion from '@/components/form/adminRegion.vue';
  import SalesSelect from '@/components/membership/salesSelect.vue';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import useTableProps from '@/hooks/table-props';
  import { useBusInfoStore } from '@/store';
  import ExportExcel from '@/components/exportExcel.vue';
  import MonthGrid from '@/views/alipay/components/MonthGrid.vue';

  defineOptions({
    name: 'SalesCommission',
  });

  // const { IS_BRAND_SITE } = window;
  const router = useRouter();
  const busInfo = useBusInfoStore();
  const { isLoading, tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getSummary);

  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: busInfo.bus_id,
    belong_type: 1,
    commission_time: dayjs().format('YYYY-MM'),
    belong_id: '',
  });

  const rule = ref<any>();
  const loadTableListThing = () => {
    loadTableList().then(({ response }) => {
      if (response.value.errorcode === 0) {
        rule.value = response.value.data.rule;
      }
    });
  };
  loadTableListThing();

  const handleClickExport = (cb: any) => {
    loadTableList(true).then((list) => {
      cb({
        filename: '提成汇总',
        columns: [
          {
            title: '业绩人员',
            dataIndex: 'name',
          },
          {
            title: '场馆',
            dataIndex: 'bus_name',
          },
          {
            title: '总提成',
            dataIndex: 'all_commission_amount',
          },
          {
            title: '新签会员',
            dataIndex: 'sign_num',
          },
          {
            title: '新签会员提成',
            dataIndex: 'sign_commission_amount',
          },
          {
            title: '合约总回款',
            dataIndex: 'item_total_amount',
          },
          {
            title: '合约提成',
            dataIndex: 'pay_commission_amount',
          },
        ],
        data: list,
      });
    });
  };

  const handleSearchThing = () => {
    handleSearch().then(({ response }) => {
      if (response.value.errorcode === 0) {
        rule.value = response.value.data.rule;
      }
    });
  };
  const handleTabChange = (event: any) => {
    searchParam.belong_type = event;
    searchParam.belong_id = '';
    handleSearchThing();
  };

  const visible = ref(false);
  const ruleId = ref();
  const handleSetting = () => {
    ruleId.value = rule.value.id;
    visible.value = true;
  };

  watch(
    () => visible.value,
    (newValue) => {
      if (!newValue) {
        loadTableListThing();
      }
    }
  );
</script>

<style lang="less" scoped></style>
