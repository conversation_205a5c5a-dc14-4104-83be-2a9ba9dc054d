<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="belong_bus_id" label="对账时间">
                  <a-range-picker v-model="searchParam.date" style="width: 100%" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="18">
          <a-tag>对账时间: 2023.12.07 - 2023.12.09</a-tag>
          <a-divider style="height: 32px" direction="vertical" />
          <a-tag>累计收入：100笔</a-tag>
          <a-divider style="height: 32px" direction="vertical" />
          <a-tag>累计支出：10笔</a-tag>
          <a-divider style="height: 32px" direction="vertical" />
          <a-tag>累计收入金额：￥1,237,000.00</a-tag>
          <a-divider style="height: 32px" direction="vertical" />
          <a-tag>累计支出金额：￥17,000.00</a-tag>
          <a-divider style="height: 32px" direction="vertical" />
          <a-tag>日结余金额：￥17,000.00</a-tag>
        </a-col>
        <a-col :span="6" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button :loading="isLoading" @click="handleClickExport(handleExport as Callback<ExportData>)">
                导出
              </a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <!-- 序号 商家订单编号 支付宝订单流水号 收支方向 收支类型 订单金额 支付宝手续费 实际收入金额 月付方案 月付方案类型 会员 场馆 签约销售 扣款完成时间 -->
          <a-table-column title="序号" data-index="index" />
          <a-table-column title="商家订单编号" data-index="username" />
          <a-table-column title="支付宝订单流水号" data-index="rule_names" />
          <a-table-column title="收支方向" data-index="rule_names" />
          <a-table-column title="收支类型" data-index="rule_names" />
          <a-table-column title="订单金额" data-index="rule_names" />
          <a-table-column title="支付宝手续费" data-index="rule_names" />
          <a-table-column title="实际收入金额" data-index="rule_names" />
          <a-table-column title="月付方案" data-index="rule_names" />
          <a-table-column title="月付方案类型" data-index="rule_names" />
          <a-table-column title="会员" data-index="rule_names" />
          <a-table-column title="场馆" data-index="rule_names" />
          <a-table-column title="签约销售" data-index="rule_names" />
          <a-table-column title="扣款完成时间" data-index="rule_names" />
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  // import { Message } from '@arco-design/web-vue';
  import { getBlacklist, getMerchantBlacklist } from '@/api/user-black-list';
  import useTableProps from '@/hooks/table-props';
  import { useBusInfoStore } from '@/store';
  import { Callback, ExportData } from '@/types/global';
  import ExportExcel from '@/components/exportExcel.vue';

  defineOptions({
    name: 'SalesCommission',
  });

  const { IS_BRAND_SITE } = window;
  const router = useRouter();
  const busInfo = useBusInfoStore();
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    IS_BRAND_SITE ? getMerchantBlacklist : getBlacklist
  );

  // 设置除分页外的其它属性值
  setSearchParam({
    username: '',
    bus_or_region_id: '',
    bus_id: IS_BRAND_SITE ? '' : busInfo.bus_id,
    admin_id: busInfo.admin_id,
  });
  loadTableList();

  // 跳转详情
  const handleDetail = (record: Record<string, any>) => {
    const { id, bus_id } = record;
    router.push({
      path: '/admin/black-list-edit',
      query: {
        id,
        bus_id,
      },
    });
  };

  // const tableRef = ref(null);
  const handleClickExport = (cb: any) => {
    loadTableList(true).then((list) => {
      list.forEach((v: any) => {
        v.name = v.name || '';
      });

      cb({
        filename: '销售提成汇总',
        columns: [
          {
            title: '会员',
            dataIndex: 'username',
          },
          {
            title: '训练次数',
            dataIndex: 'num',
          },
          {
            title: '训练天数',
            dataIndex: 'day',
          },
          {
            title: '平均时长',
            dataIndex: 'ave_duration',
          },
          {
            title: '跟进会籍',
            dataIndex: 'name',
          },
        ],
        data: list,
      });
    });
  };
</script>
