<template>
  <div class="container" style="padding: 20px">
    <a-card :style="isEdit ? '' : 'background-color: #e8eaec'">
      <Editor ref="editor" v-model="description" :disabled="!isEdit" />
    </a-card>
    <div style="margin-top: 20px">
      <a-button v-if="!isEdit" type="primary" @click="handleEdit">编辑</a-button>
      <blockquote v-else>
        <a-button type="primary" @click="handleSave">保存</a-button>
        <a-button style="margin-left: 20px" @click="handleCancel">取消</a-button>
      </blockquote>
    </div>
  </div>
</template>

<script lang="ts" setup>
  // import { ref, defineComponent } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import Editor from '@/components/form/editor.vue';
  import { getProtocol, saveProtocol } from '@/api/member-settings';

  defineComponent({
    name: 'Alipay2Protocol',
  });

  const editor = ref(null);
  const description = ref('');
  const isEdit = ref(false);
  const unescapeHTML = (a: any) => {
    a = `${a}`;
    return a
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&apos;/g, "'");
  };
  const fetchProtocol = () => {
    return getProtocol().then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        description.value = unescapeHTML(response.value.data.agreement || '');
        // editor.value.quill.enable(false);
      } else {
        Message.error(response.value.errormsg);
      }
    });
  };
  const handleSave = () => {
    return saveProtocol({
      agreement: description.value,
    }).then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        Message.success('保存成功');
        isEdit.value = false;
        getProtocol();
      } else {
        Message.error(response.value.errormsg);
      }
    });
  };
  const handleEdit = () => {
    isEdit.value = true;
    // editor.value.quill.enable(true);
    // editor.value.quill.focus();
  };
  const handleCancel = () => {
    fetchProtocol();
    isEdit.value = false;
    // editor.value.quill.enable(false);
  };

  onMounted(() => {
    fetchProtocol();
  });
</script>

<style lang="less" scoped></style>
