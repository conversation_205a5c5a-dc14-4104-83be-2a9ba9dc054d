<template>
  <a-modal v-model:visible="isShow" title="提成规则" @before-ok="handleBeforeOk" @cancel="handleCancel">
    <div class="grid-box">
      <div
        v-for="(month, index) in monthGrid"
        :key="index"
        class="grid-item"
        :class="{ active: month.border }"
        @click="handleMonthClick(index)">
        <div class="grid-item-title">{{ month.label }}</div>
        <div class="grid-item-content">{{ month.title }}</div>
      </div>
    </div>
    <a-divider />
    <a-alert type="warning" style="margin-bottom: 16px">未设置方案的时间段将按照之前最后一次方案执行薪资提成</a-alert>
    <div class="line">
      <div class="label">提成方案</div>
      <a-select v-model="selectRuleId" placeholder="请选择" allow-search>
        <a-option v-for="item in ruleList" :key="item.id" :value="item.id">{{ item.title }}</a-option>
      </a-select>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import { getExecuteRule, saveExecuteRule, getAllRule } from '@/api/sales-commission';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    ruleId: {
      type: [Number, String],
      default: '',
    },
    busId: {
      type: Number,
      default: 0,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible']);

  const isShow = ref(props.visible);
  const selectRuleId = ref(props.ruleId);

  const ruleList = ref<any>([]);
  const fetchAllRule = () => {
    getAllRule({ bus_id: props.busId }).then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        ruleList.value = response.value.data.list;
        // if (ruleList.value.length > 0 && !props.ruleId) {
        //   selectRuleId.value = ruleList.value[0].id;
        // }
      }
    });
  };

  // 16 grid for month
  const monthGrid = ref<any>([]);

  const handleMonthClick = (index: number) => {
    monthGrid.value[index].border = !monthGrid.value[index].border;
  };

  const handleBeforeOk = () => {
    const exec_times = monthGrid.value
      .filter((item: any) => item.border)
      .map((item: any) => item.value)
      .join(',');
    if (!exec_times) {
      Message.error('请选择需要执行的时间段');
      return false;
    }
    if (!selectRuleId.value) {
      Message.error('请选择提成方案');
      return false;
    }

    return saveExecuteRule({
      bus_id: props.busId,
      rule_id: selectRuleId.value,
      exec_times,
    }).then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        Message.success('保存成功');
        emit('update:visible', false);
      }
    });
  };
  const handleCancel = () => {
    emit('update:visible', false);
  };

  const drawGrid = () => {
    // 3 month ago
    monthGrid.value = [];
    const today = new Date();
    // set to 1th of month
    today.setDate(1);
    const threeMonthAgo = new Date();
    // set to 1th of month
    threeMonthAgo.setDate(1);
    threeMonthAgo.setMonth(today.getMonth() - 3);
    for (let i = 0; i < 16; i += 1) {
      monthGrid.value.push({
        label: dayjs(threeMonthAgo).format('YYYY 年 MM 月'),
        value: dayjs(threeMonthAgo).format('YYYY-MM'),
        title: '',
        border: false,
      });
      threeMonthAgo.setMonth(threeMonthAgo.getMonth() + 1);
    }
    // draw border and title by rule
    getExecuteRule({ bus_id: props.busId }).then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        const listByYear = response.value.data.storeExecByYear;
        listByYear.forEach((item: any) => {
          const itemMonth = dayjs(Number(item.exec_time) * 1000).format('YYYY-MM');
          const month = monthGrid.value.find((m: any) => m.value === itemMonth);
          if (month) {
            month.title = item.title;
          }
        });

        // const listByRule = response.value.data.storeExecByRule;
        // listByRule.forEach((item: any) => {
        //   const itemMonth = dayjs(Number(item.exec_time) * 1000).format('YYYY-MM');
        //   const month = monthGrid.value.find((m: any) => m.value === itemMonth);
        //   if (month) {
        //     month.border = true;
        //   }
        // });
      }
    });
  };

  // const handleRuleChange = () => {
  //   drawGrid();
  // };

  watch(
    () => props.visible,
    (newValue) => {
      if (newValue) {
        isShow.value = newValue;
        selectRuleId.value = props.ruleId;
        fetchAllRule();
        drawGrid();
      }
    }
  );
</script>

<style lang="less" scoped>
  .grid-box {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(4, 1fr);
    grid-column-gap: 10px;
    grid-row-gap: 10px;
  }

  .grid-item {
    padding: 10px;
    border: 1px solid #e9e9e9;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .grid-item-title {
      font-size: 14px;
      color: #999999;
      text-align: center;
    }

    .grid-item-content {
      font-size: 16px;
      color: #333333;
      text-align: center;
    }
  }

  .grid-item:hover {
    background-color: #f5f5f5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .active {
    border: 1px solid #1890ff;
  }

  .line {
    display: flex;
    flex-direction: row;
    align-items: center;

    .label {
      width: 80px;
    }
  }
</style>
