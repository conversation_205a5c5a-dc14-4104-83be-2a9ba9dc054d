<template>
  <div class="scanner-box h-100vh overflow-hidden">
    <!-- 主体 -->
    <video id="qrcode-scanner" ref="video" class="h-full absolute left-half"></video>
    <!-- 扫描框 -->
    <div class="scan-box h-800 w-620 rounded-20 absolute-center">
      <!-- 扫描框四个角 -->
      <div
        class="relative h-full w-full"
        :class="{
          'color-white': !qrcodeText,
          'color-blue': qrcodeText,
        }"
      >
        <svg-icon class="absolute left-32 top-32 h-84 w-84" name="scan-top-left" />
        <svg-icon class="absolute right-32 top-32 h-84 w-84" name="scan-top-right" />
        <svg-icon class="absolute bottom-32 left-32 h-84 w-84" name="scan-bottom-left" />
        <svg-icon class="absolute bottom-32 right-32 h-84 w-84" name="scan-bottom-right" />
      </div>
      <!-- 扫描动画 -->
      <div v-if="!loading && !errorText && !qrcodeText" class="absolute top-0 h-full w-full overflow-hidden rounded-20">
        <div class="scanning absolute inset-x-0 inset-y-0 z-2 h-252"></div>
      </div>
      <!-- loading加载 -->
      <div v-if="loading" class="absolute-center">加载中...</div>
      <!-- 异常信息 -->
      <div v-if="errorText" class="absolute-center">{{ errorText }}</div>
      <!-- 切换摄像头 -->
      <button type="button" class="button" @click="toggleCamera">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          fill="currentColor"
          class="bi bi-arrow-repeat"
          viewBox="0 0 16 16"
        >
          <path
            d="M11.534 7h3.932a.25.25 0 0 1 .192.41l-1.966 2.36a.25.25 0 0 1-.384 0l-1.966-2.36a.25.25 0 0 1 .192-.41zm-11 2h3.932a.25.25 0 0 0 .192-.41L2.692 6.23a.25.25 0 0 0-.384 0L.342 8.59A.25.25 0 0 0 .534 9z"
          ></path>
          <path
            fill-rule="evenodd"
            d="M8 3c-1.552 0-2.94.707-3.857 1.818a.5.5 0 1 1-.771-.636A6.002 6.002 0 0 1 13.917 7H12.9A5.002 5.002 0 0 0 8 3zM3.1 9a5.002 5.002 0 0 0 8.757 ********* 0 1 1 .771.636A6.002 6.002 0 0 1 2.083 9H3.1z"
          ></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { BrowserMultiFormatReader } from '@zxing/library';
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import SvgIcon from './svg-icon.vue';

  const emit = defineEmits<{
    (e: 'scaned', value: string): void;
  }>();
  const back = ref(true); // 默认后置摄像头
  const render = new BrowserMultiFormatReader();
  const loading = ref(false); // Loading
  const errorText = ref(''); // 错误信息
  const qrcodeText = ref(''); // 扫描到的内容
  const devices = ref([]);
  const selectedDevice = ref(null);

  function handleError(err: any) {
    loading.value = false;
    if (err.message === 'Permission denied') {
      errorText.value = '授权失败';
    } else {
      errorText.value = '初始化异常请重试';
    }
  }
  async function findCamera() {
    try {
      // 获取所有可用的视频输入设备
      if (!Array.isArray(devices.value) || !devices.value.length) {
        const availableDevices = await navigator.mediaDevices.enumerateDevices();
        devices.value = availableDevices.filter((device) => device.kind === 'videoinput');
      }
      if (!devices.value.length) {
        handleError('未发现摄像头设备');
        return null;
      }
      const selected = devices.value.find((device) => {
        const isBackCamera = device.label.toLowerCase().includes('back') || device.label.includes('后');
        return back.value ? isBackCamera : !isBackCamera;
      });
      return selected || devices.value[devices.value.length - 1];
    } catch (error) {
      handleError(error);
      return null;
    }
  }
  async function decodeFromInputVideo() {
    const camera = await findCamera();
    selectedDevice.value = camera;
    if (!camera) return;
    render
      .decodeFromVideoDevice(camera.deviceId, 'qrcode-scanner', (result) => {
        if (result) {
          const resultText = result.getText();
          render.stopContinuousDecode();
          qrcodeText.value = resultText;
          emit('scaned', qrcodeText.value);
        }
        if (loading.value) loading.value = false;
      })
      .catch((err) => {
        handleError(err);
      });
  }
  function toggleCamera() {
    back.value = !back.value;
    render.reset();
    decodeFromInputVideo();
  }
  function openScan() {
    loading.value = true;
    // 初始化
    decodeFromInputVideo();
  }

  onMounted(() => {
    openScan();
  });

  onBeforeUnmount(() => {
    render.reset();
  });
  defineExpose({
    render,
  });
</script>

<style lang="less">
  .color-white {
    color: #fff;
  }
  .color-blue {
    color: #2d8cf0;
  }
  .left-half {
    left: 50%;
    transform: translateX(-50%);
  }
  .h-800 {
    height: 800px;
  }
  .w-620 {
    width: 620px;
  }
  .inset-x-0 {
    left: 0;
    right: 0;
  }
  .inset-y-0 {
    top: 0;
    bottom: 0;
  }
  .z-2 {
    z-index: 2;
  }
  .h-252 {
    height: 252px;
  }
  .absolute {
    position: absolute;
  }
  .left-32 {
    left: 32px;
  }
  .top-32 {
    top: 32px;
  }
  .bottom-32 {
    bottom: 32px;
  }
  .right-32 {
    right: 32px;
  }
  .h-84 {
    height: 84px;
  }
  .w-84 {
    width: 84px;
  }
  .top-0 {
    top: 0;
  }
  .h-full {
    height: 100%;
  }
  .w-full {
    width: 100%;
  }
  .rounded-20 {
    border-radius: 20px;
  }
  .scanner-box {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    background: #f6f6f8;
  }
  .absolute-center {
    position: absolute !important;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .relative {
    position: relative;
  }
  .h-100vh {
    height: 100vh;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .scan-box {
    box-shadow: rgba(0, 0, 0, 0.5) 0px 0px 0px 5000px;
  }
  .scanning {
    background: linear-gradient(rgba(255, 255, 255, 0), 80%, rgba(96, 165, 250, 0.8));
    animation: move 2s linear infinite;
    -webkit-animation: move 2s linear infinite;
  }

  @keyframes move {
    0% {
      transform: translateY(-126px);
    }
    100% {
      transform: translateY(400px);
    }
  }

  .button {
    position: fixed;
    right: 32px;
    bottom: 32px;
    color: white;
    background-color: transparent;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    border: none;
  }

  .button:hover {
    background-color: transparent;
  }

  .button svg {
    display: inline;
    width: 40px;
    height: 40px;
    margin: 32px;
    color: white;
  }

  .button:focus svg {
    animation: rotate 0.5s forwards;
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
</style>
