<template>
  <div class="record-content">
    <div class="record-header">
      <icon-home class="home-icon" @click="handleHome" />
      <div class="record-tips">移动端仅可查看当日核销记录</div>
    </div>
    <div class="record-list">
      <div v-for="(item, index) in list" :key="index" class="record-item">
        <div class="record-top">
          <div class="name">{{ item.san_name }}</div>
          <span class="tag">{{ item.consume_sn }}</span>
        </div>
        <div class="record-bottom">
          <div class="record-info">
            <div class="tit"> 入场时间 </div>
            <div class="val"> {{ item.sign_in_time }} </div>
          </div>
          <div v-if="item.sign_out_time" class="record-info">
            <div class="tit"> 退场时间 </div>
            <div class="val"> {{ item.sign_out_time }} </div>
          </div>
          <div v-if="item.san_phone" class="record-info">
            <div class="tit"> 预留电话 </div>
            <div class="val"> {{ item.san_phone }} </div>
          </div>
          <div v-if="item.enter_card_number" class="record-info">
            <div class="tit"> 手牌号 </div>
            <div class="val"> {{ item.enter_card_number }} </div>
          </div>
          <div v-if="item.consume_type_copy" class="record-info">
            <div class="tit"> 核销方式 </div>
            <div class="val"> {{ item.consume_type_copy }} </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { verificationList } from '@/api/wap';

  const list = ref([]);
  function handleHome() {
    window.location.href = '/mobile/verify';
  }
  function getList() {
    verificationList({
      page_no: 1,
      page_size: 999,
    }).then((res) => {
      list.value = res.data.value.list;
    });
  }
  getList();
</script>

<style scoped lang="less">
  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .home-icon {
      font-size: 40px;
      cursor: pointer;
    }
  }
  .record-content {
    height: 100%;
    background: #f6f6f8;
    padding: 20px;
  }
  .record-tips {
    font-size: 26px;
    color: #7d7d7d;
    line-height: 25px;
  }
  .record-item {
    background: #fff;
    padding: 0 30px;
    overflow: hidden;
    margin-bottom: 20px;

    .record-top {
      border-bottom: 1px solid #e7e7e7;
      margin-bottom: 20px;
    }
    .tag {
      display: inline-block;
      padding: 10px;
      background: #c6f0ff;
      border-radius: 6px 6px 6px 6px;
      font-weight: bold;
      font-size: 26px;
      color: #0d96ff;
      margin-bottom: 30px;
    }
    .name {
      font-weight: bold;
      font-size: 36px;
      color: #000000;
      line-height: 80px;
    }
    .record-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 26px;
      color: #000000;
      margin-bottom: 20px;
    }
    .val {
      font-weight: bold;
      font-size: 26px;
      color: #000000;
    }
  }
</style>
