<template>
  <div class="mobile-content">
    <div class="verify-link" title="进场核销" @click="goVerifyAction"></div>
    <div class="verify-link record-box" title="核销记录" @click="goVerifyRecord"></div>
  </div>
</template>

<script lang="ts" setup>
  import { verificationWap } from '@/api/san';
  import { goMobileLoginByErr } from '@/utils/mobile';

  const router = useRouter();
  function checkAuth() {
    verificationWap().catch((err) => {
      goMobileLoginByErr(err);
    });
  }
  onMounted(() => {
    checkAuth();
  });
  function goVerifyAction() {
    router.push('/mobile/verify-action');
  }
  function goVerifyRecord() {
    router.push('/mobile/verify-record');
  }
</script>

<style scoped lang="less">
  .verify-link {
    width: 664px;
    height: 400px;
    margin: 120px auto 80px;
    background: url('https://imagecdn.rocketbird.cn/mainsite-fe/mobile-page/verify-in.png') no-repeat center top / 100%
      100%;
  }
  .record-box {
    background: url('https://imagecdn.rocketbird.cn/mainsite-fe/mobile-page/verify-record.png') no-repeat center top /
      100% 100%;
    margin-top: 80px;
  }
</style>
