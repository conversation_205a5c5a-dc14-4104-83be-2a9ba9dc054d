<template>
  <div class="mobile-content">
    <div class="banner-wrap">
      <icon-home class="home-icon" @click="handleHome" />
      <img class="banner-img" src="https://imagecdn.rocketbird.cn/mainsite-fe/mobile-page/verify-banner.png" />
    </div>
    <div class="bottom-wrap">
      <a-radio-group
        v-model="postData.buy_channel"
        :disabled="isConsumeSnLoading"
        class="check-list"
        @change="radioChange"
      >
        <a-radio
          v-for="(item, index) in FOUR_PLATFORM_NAME"
          :key="item.value"
          :value="item.value"
          :style="{ flex: 1, marginRight: index === FOUR_PLATFORM_NAME.length - 1 ? '0' : '20px' }"
        >
          <template #radio="{ checked }">
            <div class="verify-radio-item" :class="{ checked }">
              <img :src="item.img" />
              <div>{{ item.label }}</div>
            </div>
          </template>
        </a-radio>
      </a-radio-group>
      <div class="input-wrap">
        <input
          v-model="postData.consume_sn"
          class="verify-input"
          type="text"
          placeholder="请输入卡券编号"
          @input="handleSNChange"
        />
        <icon-scan class="scan-icon" @click="handleScan" />
      </div>
      <a-spin v-if="isConsumeSnLoading" :loading="isConsumeSnLoading" />
      <div v-if="goodsInfo.group_title" class="verify-card">
        <img class="verify-img" :src="getImgByType(postData.buy_channel)" />
        <div class="verify-info">
          <div class="title">{{ goodsInfo.group_title }}</div>
          <div class="text">{{ goodsInfo.end_time }}</div>
        </div>
      </div>
      <div v-if="consumeSnHelp" class="err-msg">
        {{ consumeSnHelp }}
      </div>
    </div>
    <a-button class="bottom-btn" :loading="isLoading" @click="hansleConfirm">确认核销</a-button>
    <QrcodeScanner v-if="showScanner" @scaned="handleScaned" />
  </div>
</template>

<script lang="ts" setup>
  import debounce from 'lodash/debounce';
  import { Message } from '@arco-design/web-vue';
  import { THIRD_PLATFORM_NAME } from '@/store/constants';
  import { entryVerification } from '@/api/wap';
  import { getVoucher } from '@/api/san-group';
  import { checkStatusByConsumesn } from '@/api/san';
  import { goMobileLoginByErr } from '@/utils/mobile';
  import QrcodeScanner from '../components/qrcode-scanner.vue';

  const FOUR_PLATFORM_NAME = [
    {
      value: '0',
      label: '会员端',
      img: 'https://imagecdn.rocketbird.cn/mainsite-fe/mobile-page/min-member.png',
    },
    ...THIRD_PLATFORM_NAME,
  ];
  const INIT_DATA = {
    consume_sn: '',
    san_rule_id: '',
    buy_channel: '0', // 购票渠道 0会员端 1美团 2大众点评 3抖音 0:会员端
    consume_type: '4', // 4移动端核销
  };
  const INIT_GOODS_DATA = {
    group_title: '',
    end_time: '',
  };
  function handleHome() {
    window.location.href = '/mobile/verify';
  }
  const getImgByType = (type) => {
    return FOUR_PLATFORM_NAME.find((item) => item.value === type)?.img;
  };
  const goodsInfo = ref<Record<string, any>>({});
  const postData = reactive({ ...INIT_DATA });
  function resetGoodsInfo() {
    goodsInfo.value = { ...INIT_GOODS_DATA };
    postData.san_rule_id = '';
  }
  const consumeSnHelp = ref('');
  const isConsumeSnLoading = ref(false);
  function radioChange(value) {
    if (isConsumeSnLoading.value) {
      Message.error('验证中，请稍后再试');
      return;
    }
    postData.buy_channel = value;
    resetGoodsInfo();
    postData.consume_sn = '';
    consumeSnHelp.value = '';
  }
  const handleSNChange = debounce(async (event) => {
    // handleSNChange可能被主动触发 此时event为undefined
    const search = event ? event.target.value : postData.consume_sn;
    postData.consume_sn = search.replace(/\s+/g, '');
    if (!postData.consume_sn) {
      resetGoodsInfo();
      consumeSnHelp.value = '请输入';
      return;
    }
    consumeSnHelp.value = '';
    isConsumeSnLoading.value = true;
    let params = {
      platform_id: postData.buy_channel,
      code: postData.consume_sn,
    };
    let postMethod = getVoucher;
    const isMemberFlag = postData.buy_channel === '0';
    if (isMemberFlag) {
      params = {
        consume_sn: postData.consume_sn,
      };
      postMethod = checkStatusByConsumesn;
    }
    postMethod(params)
      .then((res) => {
        const resData = res.data.value;
        if (isMemberFlag) {
          postData.san_rule_id = resData.id;
          goodsInfo.value = { ...INIT_GOODS_DATA };
        } else {
          postData.san_rule_id = resData.san_rule_id;
          goodsInfo.value = res.data.value;
        }
        isConsumeSnLoading.value = false;
      })
      .catch((err) => {
        resetGoodsInfo();
        consumeSnHelp.value = err.errormsg;
        isConsumeSnLoading.value = false;
      });
  }, 500);

  const { isLoading, execute } = entryVerification();
  async function hansleConfirmAsync() {
    try {
      if (consumeSnHelp.value || !postData.consume_sn) return false;
      // 后端要求移动端核销这里不管啥场景ticket_type都传1
      await execute({ data: { ...postData, ticket_type: 1 } });
      Message.success('核销成功！');
      resetGoodsInfo();
      postData.consume_sn = '';
      consumeSnHelp.value = '';
      return true;
    } catch (err) {
      resetGoodsInfo();
      postData.consume_sn = '';
      consumeSnHelp.value = '';
      goMobileLoginByErr(err);
      return false;
    }
  }
  const hansleConfirm = debounce(hansleConfirmAsync, 500);
  // 扫一扫功能
  const showScanner = ref(false);
  function handleScan() {
    showScanner.value = true;
  }
  function handleScaned(value: string) {
    if (value) {
      postData.consume_sn = value;
      handleSNChange();
      showScanner.value = false;
    }
  }
</script>

<style scoped lang="less">
  .banner-wrap {
    position: relative;
  }
  .home-icon {
    font-size: 40px;
    color: #000;
    position: absolute;
    top: 20px;
    left: 20px;
  }
  .err-msg {
    text-align: left;
    color: #ff0000;
    font-size: 24px;
    margin: 20px 0;
  }
  .mobile-content {
    height: 100%;
    background: #f3fcfb;
    .banner-img {
      width: 100%;
    }
  }
  .bottom-wrap {
    width: 709px;
    padding: 60px 25px;
    background: #ffffff;
    border-radius: 6px;
    margin: 0 auto;
  }
  .check-list {
    display: flex;
    width: 100%;
    radio,
    checkbox {
      display: none;
    }
  }
  .verify-radio-item {
    flex: 1;
    padding: 8px 4px 18px;
    border-radius: 10px;
    margin-right: 16px;
    font-size: 28px;
    color: #000;
    background: linear-gradient(180deg, #f2fbfb 0%, #e7fbfe 100%);
    text-align: center;
    &:last-child {
      margin-right: 0;
    }
    img {
      display: block;
      width: 70px;
      height: 70px;
      margin: 20px auto;
    }
    &.checked {
      background: linear-gradient(180deg, #f2fbfb 0%, #e7fbfe 100%);
      border: 4px solid;
      border-radius: 10px;
      border-image: linear-gradient(127deg, rgba(134, 211, 255, 1), rgba(21, 143, 255, 1), rgba(3, 119, 254, 1)) 4 4;
    }
  }
  .input-wrap {
    position: relative;
    width: 100%;
    margin: 40px auto;
  }
  .scan-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 40px;
  }
  .input-wrap input {
    width: 100%;
    height: 80px;
    line-height: 80px;
    border: 1px solid #c6c6c6;
    border-radius: 10px;
    text-indent: 20px;
    text-align: left;
    &:focus,
    &:active,
    &:focus-visible {
      box-shadow: 0px 0px 7px 0px #14a6ff;
      border: 3px solid;
      border-image: linear-gradient(90deg, rgba(1, 141, 255, 1), rgba(2, 183, 255, 1)) 3 3;
    }
  }
  .bottom-btn {
    display: block;
    border: 0 none !important;
    border-radius: 0;
    width: 690px;
    height: 80px;
    margin: 35px auto;
    line-height: 80px;
    background: linear-gradient(180deg, #b7eeff 0%, #44deff 36%, #0191ff 100%);
    box-shadow: inset 0px -2px 2px 0px rgba(238, 252, 255, 0.68);
    font-weight: bold;
    font-size: 30px;
    color: #ffffff !important;
    text-align: center;
    font-style: normal;
  }
  .verify-card {
    width: 100%;
    box-sizing: border-box;
    padding: 26px;
    display: flex;
    align-items: center;
    background: url('https://imagecdn.rocketbird.cn/mainsite-fe/mobile-page/bg-code.png') center/100% 100% no-repeat;
    border-radius: 10px;
    .verify-img {
      width: 100px;
      height: 100px;
      margin-right: 20px;
    }
    .verify-info {
      .title {
        font-size: 32px;
        font-weight: bold;
        color: #000;
        line-height: 38px;
        text-align: left;
        margin-bottom: 16px;
      }
      .text {
        font-size: 22px;
        color: #7d7d7d;
        line-height: 32px;
      }
    }
  }
</style>
