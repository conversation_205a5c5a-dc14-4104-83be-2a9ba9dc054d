<template>
  <a-grid :cols="24" :col-gap="12" :row-gap="12">
    <a-grid-item :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 6, xxl: 6 }">
      <ChainItem
        title="场馆总量"
        quota="visitors"
        :type="0"
        :card-style="{
          background: 'linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%)',
        }" />
    </a-grid-item>
    <a-grid-item :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 6, xxl: 6 }">
      <ChainItem
        title="收入流水"
        quota="published"
        :type="1"
        :card-style="{
          background: 'linear-gradient(180deg, #F5FEF2 0%, #E6FEEE 100%)',
        }" />
    </a-grid-item>
    <a-grid-item :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 6, xxl: 6 }">
      <ChainItem
        title="销售业绩"
        quota="comment"
        :type="2"
        :card-style="{
          background: 'linear-gradient(180deg, #fffce8 0%, #fffbe1 100%)',
        }" />
    </a-grid-item>
    <a-grid-item :span="{ xs: 12, sm: 12, md: 12, lg: 12, xl: 6, xxl: 6 }">
      <ChainItem
        title="客流"
        quota="share"
        :type="3"
        :card-style="{
          background: 'linear-gradient(180deg, #F7F7FF 0%, #ECECFF 100%)',
        }" />
    </a-grid-item>
  </a-grid>
</template>

<script lang="ts" setup>
  import ChainItem from './chain-item.vue';
</script>
