<template>
  <a-spin :loading="isLoading" style="width: 100%">
    <Chart style="width: 100%; height: 360px" :option="chartOption" />
  </a-spin>
</template>

<script lang="ts" setup>
  import { graphic, LineSeriesOption, format } from 'echarts';
  import { allPassengerFlowPage, cardOrderPage, allFinclFlowPage } from '@/api/statistics';
  import useChartOption from '@/hooks/chart-option';

  const props = withDefaults(
    defineProps<{
      type: number;
    }>(),
    {
      type: 1,
    }
  );
  const apiMap: Record<number, any> = {
    1: allPassengerFlowPage,
    2: cardOrderPage,
    3: allFinclFlowPage,
  };
  const { isLoading, execute: fetchData } = apiMap[props.type]();
  const dateRangeObj = inject('dateRangeObj');
  const xAxis = ref<string[]>([]);
  const legendData = ref<any[]>([]);
  const legendSelected = ref<Record<string, boolean>>({});
  const nameMaps = ref<Record<string, string>[]>([]);
  const seriesData = ref<LineSeriesOption[]>([]);
  const COLORS = ['#07ABF2', '#34D1C9', '#F77234', '#FF2351', '#FFDB6B', '#6323FF'];
  function findNameById(id: string) {
    return nameMaps.value.find((item) => item.bus_id === `${id}`)?.bus_name;
  }
  // 处理 x 轴和 legend 数据
  function dealChart(data: Record<string, any>, nameMap: Record<string, string>[]) {
    xAxis.value = Object.keys(Object.values(data)[0]);
    // const selectedKeys = Object.keys(legendSelected.value);
    legendData.value = nameMap.map((info, index) => {
      const busName = info.bus_name;
      // 默认显示前 3 个场馆
      if (index >= 3) {
        legendSelected.value[busName] = false;
      }
      return {
        name: busName,
      };
    });
  }

  // 处理 series 数据
  function dealSeries(data: Record<string, any>, nameMap: Record<string, string>[]) {
    seriesData.value = nameMap.map((info, index) => {
      const key = info.bus_id;
      return {
        // name: findNameById(key),
        name: info.bus_name,
        type: 'line',
        data: Object.values(data[key]),
        smooth: true,
        showSymbol: false,
        symbol: 'circle',
        symbolSize: 10,
        areaStyle: {
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: COLORS[index % COLORS.length],
            },
            {
              offset: 1,
              color: 'rgba(255, 255, 255, 0)',
            },
          ]),
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            borderWidth: 2,
            borderColor: '#E0E3FF',
          },
        },
      };
    });
  }
  async function getRenderData() {
    const { data } = await fetchData({ data: dateRangeObj });
    const dataPathMap: Record<number, any> = {
      1: 'bus_passenger_flow',
      2: 'bus_card_order',
      3: 'bus_fincl_flow',
    };
    const { name_map } = data.value;
    nameMaps.value = name_map;
    legendSelected.value = {};
    dealChart(data.value[dataPathMap[props.type]], name_map);
    dealSeries(data.value[dataPathMap[props.type]], name_map);
    return data;
  }

  // 监控首页日期变化
  watch(
    () => dateRangeObj,
    () => {
      getRenderData();
    },
    { immediate: true, deep: true }
  );

  const { chartOption } = useChartOption(() => {
    return {
      color: COLORS,
      grid: {
        left: 0,
        top: 20,
        bottom: 10,
        right: 270,
        containLabel: true,
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        right: '0',
        bottom: '28',
        width: 182,
        height: 250,
        itemWidth: 10, // 图例标记的图形宽度。
        itemGap: 4, // 图例每项之间的间隔。横向布局时为水平间隔，纵向布局时为纵向间隔
        icon: 'circle',
        textStyle: {
          color: '#4E5969',
          backgroundColor: '#FFFFFF',
          width: 164,
          height: 32,
          padding: [0, 8],
          borderRadius: 4,
          shadowColor: 'rgba(34 ,87, 188, 0.1)',
          shadowBlur: 1,
          shadowOffsetX: 1,
          shadowOffsetY: 1,
        },
        padding: [32, 12, 20, 8],
        // 右下左上
        backgroundColor: new graphic.LinearGradient(1, 1, 0, 0, [
          {
            offset: 0,
            color: '#FDFEFF',
          },
          {
            offset: 1,
            color: '#F4F7FC',
          },
        ]),
        borderRadius: 6,
        formatter(name) {
          return format.truncateText(name, 200, '14px Microsoft Yahei', '…', {});
        },
        data: legendData.value,
        selected: legendSelected.value,
        pageButtonGap: 20,
        pageButtonItemGap: 6,
        // animation: false,
        animationDurationUpdate: 200,
      },
      xAxis: {
        type: 'category',
        offset: 10,
        data: xAxis.value,
        boundaryGap: false,
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A9AEB8',
          },
        },
        axisTick: {
          show: false,
          alignWithLabel: true,
          lineStyle: {
            color: '#86909C',
          },
          interval(idx: number) {
            if (idx === 0) return false;
            if (idx === xAxis.value.length - 1) return false;
            return true;
          },
        },
        axisLabel: {
          color: '#86909C',
          formatter(value: number, idx: number) {
            if (idx === 0) return '';
            if (idx === xAxis.value.length - 1) return '';
            return `${value}`;
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#86909C',
        },
        splitLine: {
          lineStyle: {
            color: '#E5E6EB',
          },
        },
      },
      tooltip: {
        show: true,
        trigger: 'axis',
      },
      series: seriesData.value,
    };
  });
</script>

<style scoped lang="less">
  .chart-box {
    width: 100%;
    height: 230px;
  }
</style>
