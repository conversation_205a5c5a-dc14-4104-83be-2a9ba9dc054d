<template>
  <a-spin :loading="isLoading" style="width: 100%">
    <a-card :bordered="false" :style="cardStyle">
      <div class="content-wrap">
        <div class="content">
          <a-statistic
            :title="title"
            :value="renderData.count"
            :precision="type === 1 || type === 2 ? 2 : 0"
            :value-from="0"
            animation
            show-group-separator />
          <div v-if="type !== 0" class="desc">
            <a-typography-text type="secondary" class="label">环比</a-typography-text>
            <a-typography-text v-if="renderData.growth >= 0" type="danger" bold>
              {{ renderData.growth }}%
              <icon-arrow-rise />
            </a-typography-text>
            <a-typography-text v-else type="success" bold>
              {{ renderData.growth }}%
              <icon-arrow-fall />
            </a-typography-text>
          </div>
        </div>
        <img class="rig-img" :src="getAssetsImg(imgMap[type])" />
      </div>
    </a-card>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, CSSProperties } from 'vue';
  import { getAllBusCount, allPassengerFlow, allCardOrderCount, allFinclFlow } from '@/api/statistics';
  import { getAssetsImg } from '@/utils';

  const renderData = ref({
    count: 0,
    growth: 0,
    chartData: [],
  });
  const props = withDefaults(
    defineProps<{
      title: string;
      type: number;
      quota: string;
      cardStyle: CSSProperties;
    }>(),
    {
      title: '',
      type: 0,
      quota: '',
    }
  );
  const imgMap: Record<number, string> = {
    0: 'bus-count.png',
    1: 'income-money.png',
    2: 'member-ship.png',
    3: 'passenger-flow.png',
  };
  const apiMap: Record<number, any> = {
    0: getAllBusCount,
    1: allFinclFlow,
    2: allCardOrderCount,
    3: allPassengerFlow,
  };
  const { isLoading, execute: fetchData } = apiMap[props.type]();
  const dateRangeObj = inject('dateRangeObj');
  async function getInfo() {
    const { data } = await fetchData({ data: dateRangeObj });
    if (props.type === 0) {
      renderData.value.count = Number(data.value) || 0;
    } else {
      renderData.value.growth = Number(data.value.chain) || 0;
      renderData.value.count = Number(data.value.all_count) || 0;
    }
  }
  watch(
    () => dateRangeObj,
    () => {
      getInfo();
    },
    { immediate: true, deep: true }
  );
</script>

<style scoped lang="less">
  :deep(.arco-card) {
    border-radius: 4px;
  }
  :deep(.arco-card-body) {
    width: 100%;
    height: 134px;
    padding: 0;
  }
  .content-wrap {
    position: relative;
    width: 100%;
    padding: 16px;
    white-space: nowrap;
    overflow: hidden;
  }
  :deep(.content) {
    float: left;
    width: 160px;
    height: 102px;
  }
  :deep(.arco-statistic) {
    .arco-statistic-title {
      font-size: 16px;
      font-weight: bold;
      white-space: nowrap;
    }
    .arco-statistic-content {
      margin-top: 10px;
    }
  }

  .rig-img {
    position: absolute;
    right: 20px;
    bottom: 15px;
    width: 60px;
    height: 60px;
    vertical-align: bottom;
  }

  .label {
    padding-right: 8px;
    font-size: 12px;
  }
</style>
