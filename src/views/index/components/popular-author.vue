<template>
  <a-spin :loading="isLoading" style="width: 100%">
    <a-card class="general-card" :header-style="{ paddingBottom: '0' }" :bordered="false">
      <template #title>
        <slot name="title">
          <span style="font-weight: bold">{{ title }}</span>
        </slot>
      </template>
      <template v-if="selectMap[props.type]" #extra>
        <a-select v-model="selectedValue" style="width: 120px" size="mini" @change="onSelectChange">
          <a-option v-for="item in selectMap[props.type]" :key="item.value" :value="item.value">
            {{ item.name }}
          </a-option>
        </a-select>
      </template>
      <a-table
        :data="tableData"
        :pagination="false"
        :bordered="false"
        style="margin-bottom: 20px"
        :scroll="{ x: '100%', y: '450px' }">
        <template #columns>
          <a-table-column title="排名" data-index="avatar" :width="appStore.device !== 'mobile' ? 100 : undefined">
            <template #cell="{ rowIndex }">
              {{ rowIndex + 1 }}
            </template>
          </a-table-column>
          <a-table-column title="店铺名称" data-index="bus_name" ellipsis tooltip></a-table-column>
          <a-table-column
            v-if="type === 1"
            title="流水"
            data-index="total_amount"
            :width="appStore.device !== 'mobile' ? 120 : undefined"></a-table-column>
          <a-table-column
            v-if="type === 2"
            title="业绩"
            data-index="total_amount"
            :width="appStore.device !== 'mobile' ? 120 : undefined"></a-table-column>
          <a-table-column
            v-if="type === 3"
            title="客流量"
            data-index="all_count"
            :width="appStore.device !== 'mobile' ? 120 : undefined"></a-table-column>
        </template>
      </a-table>
    </a-card>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ranckFinclFlow, ranckCardOrder, allPassengerFlowList } from '@/api/statistics';
  import { useAppStore } from '@/store';

  const appStore = useAppStore();

  const tableData = ref([]);
  const props = withDefaults(
    defineProps<{
      title: string;
      type: number;
    }>(),
    {
      title: '',
      type: 1,
    }
  );
  const apiMap: Record<number, any> = {
    1: ranckFinclFlow,
    2: ranckCardOrder,
    3: allPassengerFlowList,
  };
  const selectMap: Record<number, any> = {
    2: [
      {
        value: 'all_count_list',
        name: '总业绩',
      },
      {
        value: 'member_count_list',
        name: '会籍业绩',
      },
      {
        value: 'pt_count_list',
        name: '教练业绩',
      },
      {
        value: 'swin_count_list',
        name: '泳教业绩',
      },
    ],
    3: [
      {
        value: 'all_count_list',
        name: '总客流',
      },
      {
        value: 'no_member_count_list',
        name: '散客客流',
      },
      {
        value: 'member_count_list',
        name: '会员客流',
      },
      {
        value: 'visit_count_list',
        name: '来访客流',
      },
    ],
  };
  const { isLoading, execute: fetchData } = apiMap[props.type]();
  const dateRangeObj = inject('dateRangeObj');
  const selectedValue = ref(props.type === 1 ? '' : selectMap[props.type][0].value);
  async function getInfo() {
    const { data } = await fetchData({ data: dateRangeObj });
    if (props.type === 1) {
      tableData.value = data.value;
    } else {
      tableData.value = data.value[selectedValue.value];
    }
  }
  function onSelectChange(val: any) {
    console.log(val);
    selectedValue.value = val;
    getInfo();
  }
  watch(
    () => dateRangeObj,
    () => {
      getInfo();
    },
    { immediate: true, deep: true }
  );
</script>

<style scoped lang="less">
  .general-card {
    max-height: 560px;
  }
</style>
