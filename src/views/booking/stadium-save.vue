<template>
  <div class="base-box">
    <Breadcrumb :items="breadcrumb" />
    <a-card class="general-card">
      <a-form ref="formRef" :rules="formRules" :model="formData" class="general-form" auto-label-width>
        <a-form-item field="type_id" label="场地类型名称">
          <a-select v-model="formData.type_id" :disabled="!!props.id" allow-clear allow-search>
            <a-option v-for="item in categoryList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
        </a-form-item>

        <a-form-item v-if="!!props.id" field="name" label="场地名称">
          <a-input v-model="formData.name" />
        </a-form-item>
        <a-form-item v-else field="names_string" label="场地">
          <a-input v-model="formData.names_string" />
        </a-form-item>
        <a-form-item v-if="!props.id">
          <a-alert type="warning">多个场地，名称中间请用 , 进行分割，例如：“3号场地，4号场地”</a-alert>
        </a-form-item>

        <a-form-item label="场次&定价">
          <a-select v-model="strategyId" allow-search @change="handleStrategyChange">
            <a-option v-for="item in strategyList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
        </a-form-item>

        <a-form-item field="max_hour" label="订场限制">
          <!-- <a-input-number
            ref="limitRef"
            v-model="formData.max_hour"
            :min="0.5"
            :step="0.5"
            :max="24"
            style="width: 280px"
          >
            <template #prefix>
              <span>用户单次订场时长不能大于</span>
            </template>
            <template #suffix>
              <span>小时</span>
            </template>
          </a-input-number> -->
          <span>用户单次订场时长不能大于</span>
          <a-input-number
            ref="limitRef"
            v-model="formData.max_hour"
            :min="0.5"
            :step="0.5"
            :max="24"
            style="width: 70px; margin: 0 8px"></a-input-number>
          <span>小时</span>
        </a-form-item>
        <a-form-item label="半场预定">
          <a-switch
            v-model="formData.is_half"
            :checked-value="1"
            :unchecked-value="0"
            checked-text="开"
            unchecked-text="关" />
        </a-form-item>
        <a-form-item label="场次安排以及定价">
          <weekly-strategy-tabs :url-map="formData.schedule_price" />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- <a-modal v-model:visible="showStrategyModal" title="切换方案" @cancel="handleStrategyCancel" @ok="handleStrategyOk">
      切换方案会覆盖场次和定价信息，确认切换？
    </a-modal> -->
  </div>
</template>

<script setup>
  import { Message, Modal } from '@arco-design/web-vue';
  import { useRouter, useRoute } from 'vue-router';
  import _ from 'lodash';
  import { getStadium, getStadiumCategoryList, getAllStrategyList, saveStadium } from '@/api/booking';
  import useBookingStore from '@/store/modules/booking';
  import { pushFile } from '@/request/requestOSS';
  import WeeklyStrategyTabs from './components/WeeklyStrategyTabs.vue';

  const router = useRouter();
  const route = useRoute();

  const bookingStore = useBookingStore();

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
  });

  // variables
  const categoryList = ref([]);
  const formData = ref({
    type_id: '',
    name: '',
    names: [],
    names_string: '',
    max_hour: 4,
    is_half: 0,
  });
  const isLoading = ref(false);
  const formRef = ref();
  const limitRef = ref();
  const formRules = {
    type_id: { required: true, message: '请选择场地类型' },
    name: { required: true, message: '请填写场馆名称' },
    names_string: { required: true, message: '请填写场馆名称' },
    max_hour: { required: true, message: '请填写用户单次订场时长' },
  };

  provide(
    'half',
    computed(() => formData.value.is_half === 1)
  );
  provide(
    'limit',
    computed(() => formData.value.max_hour)
  );
  provide('limitRef', limitRef);

  // event
  const handleSubmit = async () => {
    const valid = await formRef.value.validate();
    if (!valid) {
      isLoading.value = true;

      // check names
      if (!props.id) {
        let nameError = false;
        const names = [];
        const names_zh_array = formData.value.names_string.split('，').map((i) => i.trim()); // 砍中文逗号

        names_zh_array.forEach((zh_name) => {
          if (!zh_name) {
            nameError = true;
          } else {
            const names_en_array = zh_name.split(',').map((i) => i.trim()); // 砍英文逗号
            names_en_array.forEach((en_name) => {
              if (!en_name) {
                nameError = true;
              } else {
                names.push(en_name);
              }
            });
          }
        });

        if (nameError) {
          Message.warning('场馆名称有误, 请检查分隔符是否正确');
          isLoading.value = false;
          return;
        }

        formData.value.names = names;
      }

      // check list
      if (!bookingStore.checkAll(formData.value.max_hour, formData.value.is_half === 1)) {
        isLoading.value = false;
        return;
      }

      const monday = _.cloneDeep(bookingStore.monday);
      const tuesday = _.cloneDeep(bookingStore.tuesday);
      const wednesday = _.cloneDeep(bookingStore.wednesday);
      const thursday = _.cloneDeep(bookingStore.thursday);
      const friday = _.cloneDeep(bookingStore.friday);
      const saturday = _.cloneDeep(bookingStore.saturday);
      const sunday = _.cloneDeep(bookingStore.sunday);

      // push oss
      const today = Date.now();
      const schedule_price = {
        monday: await pushFile('', `${today}_monday.json`, { list: monday }),
        tuesday: await pushFile('', `${today}_tuesday.json`, { list: tuesday }),
        wednesday: await pushFile('', `${today}_wednesday.json`, { list: wednesday }),
        thursday: await pushFile('', `${today}_thursday.json`, { list: thursday }),
        friday: await pushFile('', `${today}_friday.json`, { list: friday }),
        saturday: await pushFile('', `${today}_saturday.json`, { list: saturday }),
        sunday: await pushFile('', `${today}_sunday.json`, { list: sunday }),
      };

      // submit
      saveStadium({
        ...formData.value,
        schedule_price: JSON.stringify(schedule_price),
      })
        .then(({ response }) => {
          if (response.value.errorcode === 0) {
            Message.success('保存成功');
            isLoading.value = false;
            router.back();
          }
        })
        .catch(() => {
          isLoading.value = false;
        });
    }
  };
  const getInfo = () => {
    return getStadium({ id: props.id }).then(({ response }) => {
      if (response.value.errorcode === 0) {
        Object.assign(formData.value, {
          ...response.value.data,
          max_hour: Number(response.value.data.max_hour),
          is_half: Number(response.value.data.is_half),
        });
      }
    });
  };

  // strategy
  const oldStrategyId = ref('');
  const strategyId = ref('');
  const strategyList = ref([]);
  // const showStrategyModal = ref(false);
  const loadStrategyList = () => {
    getAllStrategyList().then(({ response }) => {
      if (response.value.errorcode === 0) {
        strategyList.value = response.value.data.data;
      }
    });
  };
  const handleStrategyCancel = () => {
    strategyId.value = oldStrategyId.value;
    // showStrategyModal.value = false;
  };
  const handleStrategyOk = () => {
    const strategy = strategyList.value.find((item) => item.id === strategyId.value);
    if (!strategy) {
      return;
    }

    const { max_hour, is_half, schedule_price } = strategy;
    formData.value.max_hour = Number(max_hour);
    formData.value.is_half = Number(is_half);
    formData.value.schedule_price = schedule_price;

    oldStrategyId.value = strategyId.value;
    // showStrategyModal.value = false;
  };
  const handleStrategyChange = () => {
    // showStrategyModal.value = true;
    Modal.confirm({
      title: '切换方案',
      content: '切换方案会覆盖场次和定价信息，确认切换？',
      onOk: handleStrategyOk,
      onCancel: handleStrategyCancel,
    });
  };

  provide('loadStrategyList', loadStrategyList);

  // created
  const breadcrumb = ref(['订场']);
  if (props.id) {
    route.meta.locale = '编辑场地';
    breadcrumb.value.push('编辑场地');
    getInfo();
  } else {
    route.meta.locale = '添加场地';
    breadcrumb.value.push('添加场地');
  }

  getStadiumCategoryList().then(({ response }) => {
    if (response.value.errorcode === 0) {
      categoryList.value = response.value.data;
    }
  });
  loadStrategyList();
</script>

<style lang="less" scoped></style>
