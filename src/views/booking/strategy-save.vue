<template>
  <div class="base-box">
    <Breadcrumb :items="breadcrumb" />
    <a-card class="general-card">
      <a-form ref="formRef" :rules="formRules" :model="formData" class="general-form" auto-label-width>
        <a-form-item field="name" label="价格策略名称">
          <a-input v-model="formData.name" />
        </a-form-item>
        <a-form-item field="max_hour" label="订场限制">
          <!-- <a-input-number
            ref="limitRef"
            v-model="formData.max_hour"
            :min="0.5"
            :step="0.5"
            :max="24"
            style="width: 280px"
          >
            <template #prefix>
              <span>用户单次订场时长不能大于</span>
            </template>
            <template #suffix>
              <span>小时</span>
            </template>
          </a-input-number> -->
          <span>用户单次订场时长不能大于</span>
          <a-input-number
            ref="limitRef"
            v-model="formData.max_hour"
            :min="0.5"
            :step="0.5"
            :max="24"
            style="width: 70px; margin: 0 8px"></a-input-number>
          <span>小时</span>
        </a-form-item>
        <a-form-item label="半场预定">
          <a-switch
            v-model="formData.is_half"
            :checked-value="1"
            :unchecked-value="0"
            checked-text="开"
            unchecked-text="关" />
        </a-form-item>
        <a-form-item label="场次安排以及定价">
          <weekly-strategy-tabs :url-map="formData.schedule_price" :save-strategy="false" />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
  import { Message } from '@arco-design/web-vue';
  import { useRouter, useRoute } from 'vue-router';
  import _ from 'lodash';
  import { getStrategy, saveStrategy } from '@/api/booking';
  import useBookingStore from '@/store/modules/booking';
  import { pushFile } from '@/request/requestOSS';
  import WeeklyStrategyTabs from './components/WeeklyStrategyTabs.vue';

  const router = useRouter();
  const route = useRoute();

  const bookingStore = useBookingStore();

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
  });

  // variables
  const breadcrumb = ref(['订场']);
  const formData = ref({
    id: props.id,
    name: '',
    max_hour: 4,
    is_half: 0,
  });
  const isLoading = ref(false);
  const formRef = ref();
  const limitRef = ref();
  const formRules = {
    name: { required: true, message: '请填写场馆名称' },
    max_hour: { required: true, message: '请填写用户单次订场时长' },
  };

  provide(
    'half',
    computed(() => formData.value.is_half === 1)
  );
  provide(
    'limit',
    computed(() => formData.value.max_hour)
  );
  provide('limitRef', limitRef);

  // event
  const handleSubmit = async () => {
    const valid = await formRef.value.validate();
    if (!valid) {
      isLoading.value = true;

      // check list
      if (!bookingStore.checkAll(formData.value.max_hour, formData.value.is_half === 1)) {
        isLoading.value = false;
        return;
      }

      const monday = _.cloneDeep(bookingStore.monday);
      const tuesday = _.cloneDeep(bookingStore.tuesday);
      const wednesday = _.cloneDeep(bookingStore.wednesday);
      const thursday = _.cloneDeep(bookingStore.thursday);
      const friday = _.cloneDeep(bookingStore.friday);
      const saturday = _.cloneDeep(bookingStore.saturday);
      const sunday = _.cloneDeep(bookingStore.sunday);

      // push oss
      const today = Date.now();
      const schedule_price = {
        monday: await pushFile('strategy', `${today}_monday.json`, { list: monday }),
        tuesday: await pushFile('strategy', `${today}_tuesday.json`, { list: tuesday }),
        wednesday: await pushFile('strategy', `${today}_wednesday.json`, { list: wednesday }),
        thursday: await pushFile('strategy', `${today}_thursday.json`, { list: thursday }),
        friday: await pushFile('strategy', `${today}_friday.json`, { list: friday }),
        saturday: await pushFile('strategy', `${today}_saturday.json`, { list: saturday }),
        sunday: await pushFile('strategy', `${today}_sunday.json`, { list: sunday }),
      };

      // submit
      saveStrategy({
        ...formData.value,
        schedule_price: JSON.stringify(schedule_price),
      })
        .then(({ response }) => {
          if (response.value.errorcode === 0) {
            Message.success('保存成功');
            isLoading.value = false;
            router.back();
          }
        })
        .catch(() => {
          isLoading.value = false;
        });
    }
  };
  const getInfo = () => {
    return getStrategy({ id: props.id }).then(({ response }) => {
      if (response.value.errorcode === 0) {
        Object.assign(formData.value, {
          ...response.value.data,
          max_hour: Number(response.value.data.max_hour),
          is_half: Number(response.value.data.is_half),
        });
      }
    });
  };

  // created
  if (props.id) {
    route.meta.locale = '编辑排场&定价';
    breadcrumb.value.push('编辑排场&定价');
    getInfo();
  } else {
    route.meta.locale = '添加排场&定价';
    breadcrumb.value.push('添加排场&定价');
  }
</script>

<style lang="less" scoped></style>
