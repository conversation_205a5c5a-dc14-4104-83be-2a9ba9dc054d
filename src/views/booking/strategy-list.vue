<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="title" label="方案名称">
                  <a-input v-model="searchParam.name" placeholder="请输入" allow-clear @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-button type="primary" @click="$router.push('/booking/strategy-save')">新增排场&定价方案</a-button>
          <a-button type="outline" style="margin-left: 16px" @click="handleSetPriceModal">设置方案会员价</a-button>
          <a-button style="margin-left: 16px" @click="handleBatchRemove">批量删除</a-button>
        </a-col>
      </a-row>
      <a-table
        v-bind="tableProps"
        v-model:selectedKeys="selectedKeys"
        row-key="id"
        :row-selection="{
          type: 'checkbox',
          showCheckedAll: true,
          onlyCurrent: false,
        }"
        v-on="tableEvent">
        <template #columns>
          <a-table-column title="方案名称" data-index="name" />
          <a-table-column title="场次&定价">
            <template #cell="{ record }">
              <a-link @click="handleShowDetail(record)">查看详情</a-link>
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-link @click="$router.push(`/booking/strategy-save/${record.id}`)">编辑</a-link>
                <a-link status="danger" @click="handleRemove(record)">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:visible="isShowModal"
      title="排场&场次方案详情"
      :width="formData.half ? 1100 : 800"
      hide-cancel
      ok-text="关闭">
      <a-form :model="formData" auto-label-width>
        <a-form-item label="价格策略名称">{{ formData.name }}</a-form-item>
        <a-form-item label="订场限制">
          用户单次订场时长不能大于
          <span style="font-weight: bold; margin: 0 4px">{{ formData.limit }}</span>
          小时
        </a-form-item>
        <a-form-item label="半场预订">
          <a-switch
            v-model="formData.half"
            disabled
            unchecked-color="var(--color-fill-4)"
            unchecked-text="关"
            checked-text="开" />
        </a-form-item>
      </a-form>
      <weekly-strategy-tabs :url-map="formData.urlMap" disabled />
    </a-modal>

    <set-price-modal v-model:show="showSetPriceModal" />
  </div>
</template>

<script lang="ts" setup>
  import { Message, Modal } from '@arco-design/web-vue';
  import { getStrategyList, deleteStrategy } from '@/api/booking';
  import useTableProps from '@/hooks/table-props';
  import WeeklyStrategyTabs from './components/WeeklyStrategyTabs.vue';
  import SetPriceModal from './components/SetPriceModal.vue';

  defineOptions({
    name: 'StrategyList',
  });

  // variable
  const selectedKeys = ref([]);

  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList, dataPath } =
    useTableProps(getStrategyList);

  dataPath.value = {
    list: 'data',
    count: 'total',
  };

  setSearchParam({
    name: '',
  });

  const isShowModal = ref(false);
  const formData = reactive({
    name: '',
    urlMap: {},
    half: false,
    limit: 0,
  });
  const half = ref(false);
  const limit = ref(0);
  provide('half', half);
  provide('limit', limit);
  provide('limitRef', null);
  provide('loadStrategyList', null);

  // method
  const handleShowDetail = (record: any) => {
    formData.name = record.name;
    formData.limit = record.max_hour;
    formData.half = Number(record.is_half) === 1;
    formData.urlMap = record.schedule_price;

    half.value = formData.half;
    limit.value = formData.limit;
    isShowModal.value = true;
  };

  const handleRemove = (record: any) => {
    Modal.confirm({
      title: '提示',
      content: `确定删除方案 "${record.name}" 吗？`,
      okText: '删除',
      cancelText: '取消',
      onOk: async () => {
        const { response }: any = await deleteStrategy({ ids: [record.id] });
        if (response.value.errorcode === 0) {
          Message.success('删除成功');
          loadTableList();
        }
      },
    });
  };

  const handleBatchRemove = () => {
    Modal.confirm({
      title: '提示',
      content: '确定删除选中的方案吗？',
      okText: '删除',
      cancelText: '取消',
      onOk: async () => {
        const { response }: any = await deleteStrategy({ ids: selectedKeys.value });
        if (response.value.errorcode === 0) {
          Message.success('删除成功');
          loadTableList();
        }
      },
    });
  };

  // set price modal
  const showSetPriceModal = ref(false);
  const handleSetPriceModal = () => {
    showSetPriceModal.value = true;
  };

  // created
  loadTableList();
</script>
