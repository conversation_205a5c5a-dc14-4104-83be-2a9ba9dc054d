<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
        <a-form-item :label="userLabel">{{ formData.username }} {{ formData.phone }}</a-form-item>
        <a-form-item label="操作时间">
          {{ formData.create_time }}
        </a-form-item>
        <a-form-item label="长租时间">{{ formData.start_date }} ~ {{ formData.end_date }}</a-form-item>
        <a-form-item label="场地">{{ formData.type_name }}-{{ formData.space_name }}-{{ positionLabel }}</a-form-item>
        <a-form-item label="退订场次">
          <div style="width: 100%">
            <div style="display: flex; align-items: center">
              <a-tag>筛选</a-tag>
              <a-range-picker
                v-model="filterData.date"
                style="width: 300px; margin-left: 16px"
                @change="handleFilterDataChange" />
              <a-select
                v-model="filterData.status"
                allow-search
                style="width: 200px; margin-left: 16px"
                allow-clear
                placeholder="订单状态"
                @change="handleFilterDataChange">
                <a-option v-for="item in statusList" :key="item.value" :value="item.value">{{ item.label }}</a-option>
              </a-select>
            </div>
            <a-divider />
            <RentTimeTable
              ref="rentTimeTableRef"
              :list="rentTimeList"
              :columns="columns"
              remote
              :total="rentTimeTotal"
              :size="searchPost.page_size"
              @on-search="handleSearch" />
          </div>
        </a-form-item>
        <a-form-item label="共计场次" field="stats">
          <!-- <a-statistic title="总场次" :value="total" show-group-separator />
          <a-divider direction="vertical" />
          <a-statistic
            title="选中场次"
            :value="selectedKeys.length"
            :value-style="{ color: '#0fbf60' }"
            show-group-separator
          /> -->
          <div style="font-size: 14px; line-height: 32px">
            共
            <span style="font-weight: bold; font-size: 18px">{{ total }}</span>
            场次 ，选中
            <span style="color: red; font-weight: bold; font-size: 18px">{{ selectedKeys.length }}</span>
            场次
          </div>
        </a-form-item>
        <a-form-item label="应退金额">
          <h1 style="color: red">{{ refundTotal }}</h1>
        </a-form-item>
        <a-form-item label="实退金额" field="price">
          <a-input-number
            ref="priceInputRef"
            v-model="formData.price"
            placeholder="元"
            :min="0"
            :max="Infinity"
            :precision="2"
            @change="handlePriceChange" />
        </a-form-item>
        <a-form-item
          v-if="[null, '', undefined].includes(formData.price) || formData.price > 0"
          label="支付方式"
          field="pay_type">
          <PayTypeList
            v-model="formData.new_pay_type"
            :amount="formData.price"
            :bus-id="formData.bus_id + ''"
            :user-id="formData.user_id"
            :describe="`订场[${formData.space_name}]`"
            :sqb-option="{ describe: `订场[${formData.space_name}]`, serviceType: 2, isEqual: false }"
            :show-card-pay="!!formData.user_id"
            is-refund
            :is-max-amount="false" />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">退款</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <div v-if="isLoading" class="loading-mask"></div>
    <div v-if="isLoading" class="loading-box">
      <a-spin tip="订单处理中!" />
      <!-- <div class="loader"></div> -->
      <!-- <a-spin tip="订单处理中!" dot style="margin-top: 10px" /> -->
    </div>

    <a-modal v-model:visible="tipVisible" title="提示" :mask-closable="false">
      <a-alert type="success">数据已提交，处理大概需要1~ 5分钟 ，请到“长租任务”中查下执行结果</a-alert>
      <template #footer>
        <a-button type="primary" @click="handleTipGoTask">前往"长租任务"查看</a-button>
        <a-button @click="handleTipConfirm">完成</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  // @ts-ignore
  import Big from 'big.js';
  import PayTypeList from '@/components/form/PayTypeList.vue';
  import { useBusInfoStore } from '@/store';
  import { getLongTermRental, getBookingList, cancelLongTermRental, checkLongTermRentalStatus } from '@/api/booking';
  import RentTimeTable from './components/RentTimeTable.vue';

  const router = useRouter();

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
  });

  let originalList: any = [];
  const cacheList: any = [];

  const busInfo = useBusInfoStore();

  // variables
  const priceInputRef = ref();
  const formRef = ref();
  const formData = ref<any>({
    user_id: '',
    username: '',
    phone: '',
    create_time: '',
    start_date: '',
    end_date: '',
    space_name: '',
    type_name: '',
    position: 0,
    bus_id: busInfo.bus_id,
    remark: '',
    price: undefined,
    can_refund_total: 0,
    new_pay_type: [],
    batch_number: '',
    number: 0,
  });
  const isLoading = ref(false);
  const rentTimeTableRef = ref();
  const rentTimeList = ref<any>([]);
  const total = ref(0);
  const rentTimeTotal = ref(0);
  const columns = [
    {
      title: '订单编号',
      dataIndex: 'order_sn',
      width: 210,
    },
    {
      title: '日期',
      dataIndex: 'date',
    },
    {
      title: '场次时间',
      dataIndex: 'time',
    },
    {
      title: '订单状态',
      dataIndex: 'status_txt',
      width: 90,
      bodyCellStyle: (record: any) => {
        if ([4, 5].includes(Number(record.status))) {
          return {
            color: 'red',
          };
        }
        return {};
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
    },
  ];
  const searchPost = ref<any>({
    batch_number: '',
    start_date: '',
    end_date: '',
    status: '',
    page_no: 1,
    page_size: 100,
  });
  const filterData = ref<any>({
    date: null,
    status: '',
  });
  // 订单状态，0-未支付，1-已支付，2-已到场，3-已离场，4-已退款，5-已取消（未付款）
  const statusList = ref([
    // {
    //   label: '未支付',
    //   value: 0,
    // },
    {
      label: '已支付',
      value: 1,
    },
    {
      label: '已到场',
      value: 2,
    },
    {
      label: '已退款/已取消',
      value: 45,
    },
  ]);
  const tipVisible = ref(false);

  const userLabel = computed(() => {
    return formData.value.user_id ? '会员' : '散客';
  });
  const positionLabel = computed(() => {
    let label = '';
    if (formData.value.position === 0) {
      label = '全场';
    }
    if (formData.value.position === 1) {
      label = '上半场';
    }
    if (formData.value.position === 2) {
      label = '下半场';
    }
    return label;
  });
  const selectedKeys = computed(() => {
    return rentTimeTableRef.value?.selectedKeys || [];
  });
  // const refundTotal = computed(() => {
  //   const cancelAmountList = rentTimeList.value
  //     .filter((item: any) => {
  //       // return selectedKeys.value.includes(index);
  //       return selectedKeys.value.includes(item.id);
  //     })
  //     .map((item: any) => {
  //       return item.amount;
  //     });
  //   return cancelAmountList.length ? cancelAmountList.reduce((a: any, b: any) => new Big(a).plus(b)) : 0;
  // });

  // methods
  const handlePriceChange = (value: any) => {
    formData.value.price = value;

    if (formData.value.price > formData.value.can_refund_total) {
      Message.warning(`剩余可退金额${formData.value.can_refund_total}元`);
    }
  };
  // const isThisStatus = (local: number, remote: number) => {
  //   const flag012 = local === remote;
  //   const flag45 = local === 45 && (remote === 4 || remote === 5);
  //   return flag012 || flag45;
  // };
  // const getStatusText = (status: number) =>
  //   statusList.value.find((item) => isThisStatus(item.value, status))?.label || '';
  const getList = () => {
    return getBookingList(searchPost.value).then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        const { list, count } = response.value.data;
        if (Array.isArray(list)) {
          list.forEach((item) => {
            // item.sequence = index;
            item.sequence = item.id;
            item.time = `${item.start_time} - ${item.end_time}`;
            item.disabled = [2, 4, 5].includes(Number(item.status));
          });
          originalList = list;
        }
        // total.value = Number(response.value.data.count);
        rentTimeList.value = [...originalList];
        rentTimeTotal.value = Number(count || 0);

        originalList.forEach((item: any) => {
          const existIndex = cacheList.findIndex((cacheItem: any) => cacheItem.id === item.id);
          if (existIndex === -1) {
            cacheList.push(item);
          }
        });
      }
    });
  };
  const getInfo = () => {
    return getLongTermRental({
      id: props.id,
    }).then(({ response }: any) => {
      if (response.value.errorcode === 0) {
        const new_pay_type = response.value.data.pay_detail || response.value.data.new_pay_type;
        if (Array.isArray(new_pay_type)) {
          new_pay_type.forEach((item: any) => {
            item.amount = Number(item.amount || 0);
            item.card_user_id += '';
          });
        }
        const user_id = `${response.value.data.user_id}`;
        const can_refund_total = Number(response.value.data.can_refund_total || 0);
        Object.assign(formData.value, {
          ...response.value.data,
          new_pay_type,
          user_id,
          can_refund_total,
        });
        total.value = formData.value.number;
      }
    });
  };

  // event
  const handleFilterDataChange = () => {
    // local search
    // let list = [];
    // const { date, status }: any = filterData.value;
    // if (Array.isArray(date) && date.length === 2) {
    //   const [startTimestamp, endTimestamp] = [dayjs(date[0]).valueOf(), dayjs(date[1]).valueOf()];
    //   list = originalList.filter((item: any) => {
    //     const currentTimestamp = dayjs(item.date).valueOf();
    //     return currentTimestamp >= startTimestamp && currentTimestamp <= endTimestamp;
    //   });
    // } else {
    //   list = originalList;
    // }
    // if (status) {
    //   list = list.filter((item: any) => isThisStatus(status, item.status));
    // }
    // rentTimeList.value = list;
    // remote search
    if (
      Array.isArray(filterData.value.date) &&
      filterData.value.date?.length === 2 &&
      filterData.value.date[0] &&
      filterData.value.date[1]
    ) {
      searchPost.value.start_date = dayjs(filterData.value.date[0]).format('YYYY-MM-DD');
      searchPost.value.end_date = dayjs(filterData.value.date[1]).format('YYYY-MM-DD');
    } else {
      searchPost.value.start_date = '';
      searchPost.value.end_date = '';
    }
    if (filterData.value.status === 45) {
      searchPost.value.status = 4;
    } else {
      searchPost.value.status = filterData.value.status;
    }
    searchPost.value.page_no = 1;
    getList();
  };
  const handleSearch = (pageNo = 1, pageSize = 10) => {
    searchPost.value.page_no = pageNo;
    searchPost.value.page_size = pageSize;
    getList();
  };
  const handleSubmit = async () => {
    const errorcode = await checkLongTermRentalStatus()
      .then(({ response }: any) => response.value.errorcode)
      .catch((error) => error.errorcode);
    if (errorcode !== 0) {
      return;
    }

    if (!formData.value.price) {
      formData.value.new_pay_type = [];
    }

    formRef.value.validate().then(() => {
      const cancelIdList = cacheList
        .filter((item: any) => {
          // return selectedKeys.value.includes(index);
          return selectedKeys.value.includes(item.id);
        })
        .map((item: any) => {
          return item.id;
        });

      if (!cancelIdList.length) {
        Message.error('请选择要取消的订单');
        return;
      }

      if (formData.value.price > 0 && formData.value.new_pay_type.length === 0) {
        Message.error('请选择支付方式');
        return;
      }

      if (!formData.value.price && formData.value.new_pay_type.length > 0) {
        Message.error('请输入实际退款金额');
        priceInputRef.value.focus();
        return;
      }

      const refundTotal = formData.value.new_pay_type.reduce((sum: any, item: any) => {
        return sum + item.amount;
      }, 0);
      if (refundTotal !== formData.value.price) {
        Message.error('支付金额错误');
        return;
      }

      isLoading.value = true;

      const postData = {
        user_id: formData.value.user_id,
        batch_number: formData.value.batch_number,
        ids: cancelIdList.join(','),
        // number: formData.value.number,
        number: selectedKeys.value.length,
        amount: formData.value.price,
        new_pay_type: formData.value.new_pay_type,
        scene: 1,
      };
      cancelLongTermRental(postData)
        .then(({ response }: any) => {
          if (response.value.errorcode === 0) {
            Message.success('操作成功');
            // router.back();
            tipVisible.value = true;
          }
          isLoading.value = false;
        })
        .catch(() => {
          isLoading.value = false;
        });
    });
  };
  const handleTipGoTask = () => {
    tipVisible.value = false;
    router.push('/v2/Web/SpaceOrder/getList?active=3');
  };
  const handleTipConfirm = () => {
    tipVisible.value = false;
    router.back();
  };

  // watch
  const refundTotal = ref(0);
  watch(
    () => selectedKeys.value,
    () => {
      const cancelAmountList = cacheList
        .filter((item: any) => {
          // return selectedKeys.value.includes(index);
          return selectedKeys.value.includes(item.id);
        })
        .map((item: any) => {
          return item.amount;
        });
      refundTotal.value = cancelAmountList.length ? cancelAmountList.reduce((a: any, b: any) => new Big(a).plus(b)) : 0;
    },
    { immediate: true }
  );

  // created
  getInfo().then(() => {
    searchPost.value.batch_number = formData.value.batch_number;
    getList();
  });
</script>

<style lang="less" scoped>
  .loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #1d212999;
    opacity: 0.6;
    z-index: 998;
  }

  .loading-box {
    position: fixed;
    top: 50%;
    left: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 999;

    .loader {
      display: inline-grid;
      width: 80px;
      aspect-ratio: 1;
    }

    .loader:before,
    .loader:after {
      content: '';
      grid-area: 1/1;
      border-radius: 50%;
      animation: l3-0 2s alternate infinite ease-in-out;
    }

    .loader:before {
      margin: 25%;
      background: repeating-conic-gradient(#ff2351 0 60deg, pink 0 120deg);
      translate: 0 50%;
      rotate: -150deg;
    }

    .loader:after {
      padding: 10%;
      margin: -10%;
      background: repeating-conic-gradient(pink 0 30deg, #ff2351 0 60deg);
      mask: linear-gradient(#0000 50%, #000 0) content-box exclude, linear-gradient(#0000 50%, #000 0);
      rotate: -75deg;
      animation-name: l3-1;
    }

    @keyframes l3-0 {
      to {
        rotate: 150deg;
      }
    }

    @keyframes l3-1 {
      to {
        rotate: 75deg;
      }
    }
  }
</style>
