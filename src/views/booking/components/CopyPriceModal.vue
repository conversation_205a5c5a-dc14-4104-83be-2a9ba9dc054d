<template>
  <div class="copy-price-modal">
    <div title="复制价格">
      <a-button type="text" @click="handleCopy">
        <template #icon><icon-copy /></template>
      </a-button>
    </div>
    <a-modal v-model:visible="visible" title="复制价格" :width="720" :on-before-ok="handleConfirm">
      <a-alert type="warning" style="margin-bottom: 16px">
        将{{ dayMapRef[props.type] }} {{ props.day.start_time }}-{{ props.day.end_time }} 场次价格复制到
      </a-alert>
      <a-card v-for="(value, key) of weeklyMapRef" :key="key" style="margin-bottom: 16px">
        <template #title>
          <a-checkbox
            v-model="value.checkedAll"
            :indeterminate="value.indeterminate"
            :disabled="value.list.length === 0"
            @change="handleCheckedAll(key)">
            {{ dayMapRef[key] }}
          </a-checkbox>
        </template>
        <a-checkbox-group v-model="value.checkedList" @change="handleChecked(key)">
          <a-checkbox
            v-for="(item, index) in value.list"
            :key="item.idx"
            :value="index"
            style="width: 120px"
            :disabled="
              props.type === key && props.day.start_time === item.start_time && props.day.end_time === item.end_time
            ">
            <a-tag style="width: 90px" :color="value.checkedList.includes(index) ? 'red' : ''">
              {{ item.start_time }}-{{ item.end_time }}
            </a-tag>
          </a-checkbox>
        </a-checkbox-group>
      </a-card>
    </a-modal>
  </div>
</template>

<script setup>
  import _ from 'lodash';
  import useBookingStore from '@/store/modules/booking';
  import { dayMap } from '../types';

  const bookingStore = useBookingStore();

  const props = defineProps({
    day: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String,
      default: '',
    },
  });

  // variables
  const visible = ref(false);
  const dayMapRef = ref(dayMap);
  const weeklyMapRef = ref({});

  // methods
  const handleCopy = () => {
    weeklyMapRef.value = {
      monday: {
        checkedAll: false,
        indeterminate: false,
        checkedList: [],
        list: _.cloneDeep(bookingStore.monday),
      },
      tuesday: {
        checkedAll: false,
        indeterminate: false,
        checkedList: [],
        list: _.cloneDeep(bookingStore.tuesday),
      },
      wednesday: {
        checkedAll: false,
        indeterminate: false,
        checkedList: [],
        list: _.cloneDeep(bookingStore.wednesday),
      },
      thursday: {
        checkedAll: false,
        indeterminate: false,
        checkedList: [],
        list: _.cloneDeep(bookingStore.thursday),
      },
      friday: {
        checkedAll: false,
        indeterminate: false,
        checkedList: [],
        list: _.cloneDeep(bookingStore.friday),
      },
      saturday: {
        checkedAll: false,
        indeterminate: false,
        checkedList: [],
        list: _.cloneDeep(bookingStore.saturday),
      },
      sunday: {
        checkedAll: false,
        indeterminate: false,
        checkedList: [],
        list: _.cloneDeep(bookingStore.sunday),
      },
    };

    visible.value = true;
  };
  const handleCheckedAll = (key) => {
    const checkedDay = weeklyMapRef.value[key];
    checkedDay.indeterminate = false;

    if (checkedDay.checkedAll) {
      if (props.type === key) {
        const indexList = [];
        checkedDay.list.forEach((item, index) => {
          if (!(props.day.start_time === item.start_time && props.day.end_time === item.end_time)) {
            indexList.push(index);
          }
        });
        checkedDay.checkedList = indexList;
      } else {
        checkedDay.checkedList = weeklyMapRef.value[key].list.map((item, index) => index);
      }
    } else {
      checkedDay.checkedList = [];
    }
  };
  const handleChecked = (key) => {
    const checkedDay = weeklyMapRef.value[key];
    let count = checkedDay.list.length;
    if (props.type === key) {
      count = checkedDay.list.length - 1;
    }
    if (checkedDay.checkedList.length === count) {
      checkedDay.checkedAll = true;
      checkedDay.indeterminate = false;
    } else if (checkedDay.checkedList.length === 0) {
      checkedDay.checkedAll = false;
      checkedDay.indeterminate = false;
    } else {
      checkedDay.checkedAll = false;
      checkedDay.indeterminate = true;
    }
  };
  const handleConfirm = (done) => {
    Object.keys(weeklyMapRef.value).forEach((key) => {
      const checkedDay = weeklyMapRef.value[key];
      const { list, checkedList } = checkedDay;
      list.forEach((item, index) => {
        if (checkedList.includes(index)) {
          list[index] = _.cloneDeep({
            ...props.day,
            idx: item.idx,
            start_time: item.start_time,
            end_time: item.end_time,
          });
        }
      });

      bookingStore.setList(key, list);
    });
    done(true);
  };
</script>

<style lang="less" scoped></style>
