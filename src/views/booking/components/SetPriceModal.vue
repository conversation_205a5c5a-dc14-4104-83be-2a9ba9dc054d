<template>
  <a-modal
    v-model:visible="visible"
    title="设置方案会员价"
    width="820px"
    :mask-closable="false"
    :on-before-ok="handleSubmit"
    @cancel="handleCancel">
    <a-alert type="warning" style="margin-bottom: 10px">
      将卡种批量设置到方案模版的场次会员价中，若场次会员价已有对应卡种，新的会员价将覆盖原有价格
    </a-alert>
    <a-form ref="formRef" :model="formData" :rules="formRules" auto-label-width>
      <a-form-item label="卡种名称" field="card_ids" :rules="[{ validator: cardValidator }]">
        <a-tree-select
          v-model="formData.card_ids"
          :allow-search="true"
          :allow-clear="true"
          :tree-checkable="true"
          tree-checked-strategy="child"
          :data="cardTree"
          :filter-tree-node="filterTreeNode"
          :max-tag-count="5"
          @change="handleCardChange">
          <template #label="{ data }">
            <a-tooltip v-if="data.label.length > 20" :content="data.label">
              <span>{{ data.label.substring(0, 20) }}...</span>
            </a-tooltip>
            <span v-else>{{ data.label }}</span>
          </template>
          <template #tree-slot-title="{ title }">
            <a-tooltip v-if="title.length > 20" :content="title">
              <span>{{ title.substring(0, 20) }}...</span>
            </a-tooltip>
            <span v-else>{{ title }}</span>
          </template>
        </a-tree-select>
      </a-form-item>
      <a-form-item label="">
        <a-space direction="vertical">
          <a-card v-for="(sp, i) in stadiumPriceList" :key="sp.uid" style="margin-bottom: 10px">
            <a-row>
              <a-col span="3" style="text-align: right">
                <span style="font-size: 12px; margin-right: 10px; line-height: 32px">场次选择</span>
              </a-col>
              <a-col span="19">
                <a-form-item
                  :field="`stadiumPriceList[${i}].checked`"
                  :rules="[{ validator: planValidator(i) }]"
                  hide-label>
                  <treeselect
                    :ref="'treeselectRef_' + i"
                    v-model="stadiumPriceList[i].checked"
                    :limit="2"
                    :limit-text="(count) => `还有 ${count} 个`"
                    :options="sp.tree"
                    placeholder="请选择..."
                    no-results-text="无数据"
                    no-options-text="无数据"
                    no-children-text="无数据"
                    loading-text="加载中..."
                    value-consists-of="LEAF_PRIORITY"
                    :load-options="loadOptions"
                    multiple
                    @select="handleSelectNode(i, $event)"
                    @deselect="handleDeselectNode">
                    <template #value-label="{ node }">
                      <span>{{ node.raw.inputLabel }}</span>
                    </template>
                  </treeselect>
                </a-form-item>
              </a-col>
              <a-col span="2">
                <a-button v-if="stadiumPriceList.length > 1" type="text" @click="handleDeleteStadiumPrice(i)">
                  <icon-delete />
                </a-button>
              </a-col>
            </a-row>
            <a-row>
              <a-col span="3" style="text-align: right">
                <span style="font-size: 12px; margin-right: 10px; line-height: 32px">会员价</span>
              </a-col>
              <a-col span="19">
                <a-row v-if="stadiumPriceList[i].hasHalf">
                  <a-col span="2" style="line-height: 32px; font-weight: bold">半场</a-col>
                  <a-col span="3" style="line-height: 32px">平时价</a-col>
                  <a-col span="8">
                    <a-form-item
                      :field="`stadiumPriceList[${i}].price.half.common`"
                      :rules="[{ validator: priceValidator(i, 'half', 'common') }]"
                      hide-label>
                      <a-input-number
                        v-model="stadiumPriceList[i].price.half.common"
                        :min="0"
                        :step="0.01"
                        :precision="2"
                        :active-change="false"
                        size="small"
                        style="width: 90px" />
                      <span class="unit">元</span>
                    </a-form-item>
                  </a-col>
                  <a-col span="3" style="line-height: 32px">节假日</a-col>
                  <a-col span="8">
                    <a-form-item
                      :field="`stadiumPriceList[${i}].price.half.holiday`"
                      :rules="[{ validator: priceValidator(i, 'half', 'holiday') }]"
                      hide-label>
                      <a-input-number
                        v-model="stadiumPriceList[i].price.half.holiday"
                        :min="0"
                        :step="0.01"
                        :precision="2"
                        :active-change="false"
                        size="small"
                        style="width: 90px" />
                      <span class="unit">元</span>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col span="2" style="line-height: 32px; font-weight: bold">全场</a-col>
                  <a-col span="3" style="line-height: 32px">平时价</a-col>
                  <a-col span="8">
                    <a-form-item
                      :field="`stadiumPriceList[${i}].price.all.common`"
                      :rules="[{ validator: priceValidator(i, 'all', 'common') }]"
                      hide-label>
                      <a-input-number
                        v-model="stadiumPriceList[i].price.all.common"
                        :min="0"
                        :step="0.01"
                        :precision="2"
                        :active-change="false"
                        size="small"
                        style="width: 90px" />
                      <span class="unit">元</span>
                    </a-form-item>
                  </a-col>
                  <a-col span="3" style="line-height: 32px">节假日</a-col>
                  <a-col span="8">
                    <a-form-item
                      :field="`stadiumPriceList[${i}].price.all.holiday`"
                      :rules="[{ validator: priceValidator(i, 'all', 'holiday') }]"
                      hide-label>
                      <a-input-number
                        v-model="stadiumPriceList[i].price.all.holiday"
                        :min="0"
                        :step="0.01"
                        :precision="2"
                        :active-change="false"
                        size="small"
                        style="width: 90px" />
                      <span class="unit">元</span>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-col>
            </a-row>
          </a-card>
          <div v-if="stadiumPriceList.length === 0" style="text-align: center; margin: 40px">
            <a-empty />
          </div>
          <a-button type="outline" @click="handleAddStadiumPrice">
            <Icon type="ios-plus" />
            新增会员价
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-modal>
  <a-modal
    v-model:visible="loading"
    :mask-closable="false"
    class-name="vertical-center-modal"
    footer-hide
    @on-cancel="loading = false">
    <a-progress :percent="percent" />
    <h4 style="text-align: center; margin-top: 10px">正在设置会员价，请勿离开此页面...</h4>
  </a-modal>
</template>

<script setup>
  import { Message } from '@arco-design/web-vue';
  import _ from 'lodash';
  import Treeselect, { LOAD_CHILDREN_OPTIONS } from 'vue3-treeselect';
  import 'vue3-treeselect/dist/vue3-treeselect.css';
  import { getCardsForAll, getSpaceListByBusId, saveSpacePrice } from '@/api/one-time-pay';
  import { useBusInfoStore } from '@/store';
  import { fetchFile } from '@/request/requestOSS';

  const busInfo = useBusInfoStore();
  const formRef = ref(null);
  const ins = getCurrentInstance();

  // props & emits
  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['update:show']);

  // constants variable
  const SPACE_LIMIT = 20;

  // a-card tree
  const cardTree = ref([]);
  const initCardTree = async () => {
    try {
      const res = await getCardsForAll();
      const array = res.data.value.map((item) => {
        return {
          title: item.card_name,
          id: item.card_id,
          key: item.card_id,
          card_type_id: item.card_type_id,
          is_pt_time_limit_card: item.is_pt_time_limit_card,
        };
      });
      const getCardByType = (type) => {
        return array.filter((item) => item.card_type_id === type && item.is_pt_time_limit_card !== '1');
      };
      cardTree.value = [
        {
          title: '全部卡种',
          id: 'all',
          key: 'all',
          children: [
            {
              title: '期限卡',
              id: 'term',
              key: 'term',
              children: getCardByType('1'),
            },
            {
              title: '次卡',
              id: 'times',
              key: 'times',
              children: getCardByType('2'),
            },
            {
              title: '储值卡',
              id: 'value',
              key: 'value',
              children: getCardByType('3'),
            },
            {
              title: '私教课',
              id: 'private',
              key: 'private',
              children: getCardByType('4'),
            },
            {
              title: '泳教课',
              id: 'swim',
              key: 'swim',
              children: getCardByType('5'),
            },
          ].filter((item) => item.children.length > 0),
        },
      ];
    } catch (error) {
      console.error(error);
      cardTree.value = [];
    }
  };
  const filterTreeNode = (searchValue, nodeData) => {
    return nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  };
  const handleCardChange = () => {
    formRef.value.validateField('card_ids');
  };

  // stadium list, it's the options of the select a-tag, packed in a tree
  const stadiumList = ref([]);
  const getStadiumList = async () => {
    const res = await getSpaceListByBusId({ bus_id: busInfo.bus_id, type: 2 });

    res.data.value.forEach((item) => {
      stadiumList.value.push({
        id: item.id,
        label: item.name,
        level: 1,
        isDisabled: false,
        isLeaf: false,
        isHalf: Number(item.is_half),
        children: [
          {
            label: '周一',
            id: `${item.id}_monday`,
            url: item.schedule_price?.monday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
            isLeaf: false,
          },
          {
            label: '周二',
            id: `${item.id}_tuesday`,
            url: item.schedule_price?.tuesday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
            isLeaf: false,
          },
          {
            label: '周三',
            id: `${item.id}_wednesday`,
            url: item.schedule_price?.wednesday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
            isLeaf: false,
          },
          {
            label: '周四',
            id: `${item.id}_thursday`,
            url: item.schedule_price?.thursday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
            isLeaf: false,
          },
          {
            label: '周五',
            id: `${item.id}_friday`,
            url: item.schedule_price?.friday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
            isLeaf: false,
          },
          {
            label: '周六',
            id: `${item.id}_saturday`,
            url: item.schedule_price?.saturday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
            isLeaf: false,
          },
          {
            label: '周日',
            id: `${item.id}_sunday`,
            url: item.schedule_price?.sunday,
            parent: item.name,
            children: null,
            level: 2,
            isDisabled: false,
            isLeaf: false,
          },
        ],
      });
    });

    return res;
  };

  // stadium price list
  const stadiumPriceList = ref([]);
  const selectedIdList = computed(() => {
    return Array.from(new Set(stadiumPriceList.value.flatMap((item) => item.checked)));
  });
  const setDisable = () => {
    if (stadiumPriceList.value.length === 0) {
      return;
    }

    for (let index = 0; index < stadiumPriceList.value.length; index += 1) {
      const own_ids = stadiumPriceList.value[index].checked;
      const disable_ids = selectedIdList.value.filter((id) => !own_ids.includes(id));

      // for (const stadium of stadiumPriceList.value[index].tree) {
      stadiumPriceList.value[index].tree.forEach((stadium) => {
        let dayCount = 0;

        // for (const day of stadium.children) {
        stadium.children.forEach((day) => {
          if (Array.isArray(day.children)) {
            let timeCount = 0;

            // for (const time of day.children) {
            day.children.forEach((time) => {
              if (disable_ids.includes(time.id)) {
                time.isDisabled = true;
                timeCount += 1;
              } else {
                time.isDisabled = false;
              }
            });

            if (timeCount === day.children.length) {
              day.isDisabled = true;
              dayCount += 1;
            } else {
              day.isDisabled = false;
            }
          }
        });

        if (dayCount === stadium.children.length) {
          stadium.isDisabled = true;
        } else {
          stadium.isDisabled = false;
        }
      });
    }
  };

  let uid = 0;
  const handleAddStadiumPrice = () => {
    const tree = _.cloneDeep(stadiumList.value);
    stadiumPriceList.value.push({
      uid,
      checked: [],
      tree,
      price: {
        all: {
          common: null,
          holiday: null,
        },
        half: {
          common: null,
          holiday: null,
        },
      },
      checkedError: '',
      priceError: {
        all: {
          common: '',
          holiday: '',
        },
        half: {
          common: '',
          holiday: '',
        },
      },
      hasHalf: false,
    });

    uid += 1;
  };
  const handleDeleteStadiumPrice = (index) => {
    stadiumPriceList.value.splice(index, 1);

    nextTick(() => {
      setDisable();
    });
  };

  // pull data from oss
  const setTimeList = async (parentNode) => {
    if (!Array.isArray(parentNode.children) && parentNode.url) {
      const timeList = [];
      const res = await fetchFile(parentNode.url);
      res.list.forEach((item) => {
        const id = `${parentNode.id}_${item.start_time}_${item.end_time}`;
        timeList.push({
          inputLabel: `${parentNode.parent} / ${parentNode.label} / ${item.start_time}~${item.end_time}`,
          label: `${item.start_time} ~ ${item.end_time}`,
          id,
          level: 3,
          isDisabled: selectedIdList.value.includes(id),
          isLeaf: true,
        });
      });
      parentNode.children = timeList;
    }
  };
  const loadOptions = ({ action, parentNode, callback }) => {
    if (action === LOAD_CHILDREN_OPTIONS) {
      if (parentNode.url) {
        setTimeList(parentNode);
        callback();
      } else {
        parentNode.children = [];
        callback(new Error('未设置排场时间'));
      }
    }
  };

  // stadium price select
  const removeChecked = (index, node) => {
    // nextTick(() => {
    const treeRef = ins.proxy.$refs[`treeselectRef_${index}`][0];
    if (node.level === 1) {
      const removeIds = [node.id, ...node.children.map((item) => item.id)];
      // stadiumPriceList.value[index].checked = stadiumPriceList.value[index].checked.filter(
      //   (item) => !removeIds.includes(item)
      // );
      removeIds.forEach((id) => {
        const treeNode = treeRef.getNode(id);
        if (treeNode) {
          treeRef.removeValue(treeNode);
        }
      });
    } else {
      // stadiumPriceList.value[index].checked = stadiumPriceList.value[index].checked.filter(
      //   (item) => item !== node.id
      // );
      treeRef.removeValue(node);
    }
    // });
  };

  const handleSelectNode = async (index, node) => {
    // validate
    const spaceChecked = new Set();
    stadiumPriceList.value[index].checked.forEach((item) => {
      const [id] = item.split('_');
      spaceChecked.add(id);
    });

    if (spaceChecked.size >= SPACE_LIMIT) {
      Message.error(`最多选择${SPACE_LIMIT}个场地`);
      removeChecked(index, node);
      return;
    }

    const treeRef = ins.proxy.$refs[`treeselectRef_${index}`][0];

    if (node.level === 1) {
      // remove level 1 and 2
      removeChecked(index, node);

      // pull oss
      await Promise.all(node.children.map((item) => setTimeList(item)));

      // get time checked
      const newChecked = node.children
        .map((item) => item.children)
        .flat()
        .filter((item) => !!item)
        .filter((item) => !item.isDisabled)
        .map((item) => item.id);

      if (newChecked.length === 0) {
        Message.error(`"${node.label}"无可用排场时间!`);
      } else {
        // stadiumPriceList.value[index].checked = Array.from(
        //   new Set([...stadiumPriceList.value[index].checked.filter((item) => item), ...newChecked])
        // );
        newChecked.forEach((id) => {
          const values = treeRef.getValue();
          if (!values.includes(id)) {
            const treeNode = treeRef.getNode(id);
            if (treeNode) {
              // treeRef.addValue(treeNode);
              treeRef.select(treeNode);
            }
          }
        });
      }
    } else if (node.level === 2) {
      // remove current node
      removeChecked(index, node);

      // pull oss
      await setTimeList(node);

      // get time checked
      if (Array.isArray(node.children) && node.children.length) {
        const newChecked = node.children.filter((item) => !item.isDisabled).map((item) => item.id);
        // stadiumPriceList.value[index].checked = Array.from(
        //   new Set([...stadiumPriceList.value[index].checked.filter((item) => item), ...newChecked])
        // );
        newChecked.forEach((id) => {
          const values = treeRef.getValue();
          if (!values.includes(id)) {
            const treeNode = treeRef.getNode(id);
            if (treeNode) {
              treeRef.select(treeNode);
            }
          }
        });
      } else {
        Message.error(`"${node.parent} (${node.label})"无可用排场时间!`);
      }
    }

    nextTick(() => {
      setDisable();
    });
  };
  const handleDeselectNode = () => {
    nextTick(() => {
      setDisable();
    });
  };

  // const handleInputNode = (index, value) => {
  //   console.log('Input Node', value);
  //   // disable other stadium
  //   if (Array.isArray(value) && value.length === 0) {
  //     nextTick(() => {
  //       setDisable();
  //     });
  //   }
  //   // check the space half or all
  //   let hasHalf = false;
  //   const spaceIdList = stadiumPriceList.value[index].checked.map((item) => item.split('_')[0]);
  //   const spaceIdSet = new Set(spaceIdList);
  //   hasHalf = Array.from(spaceIdSet).some((id) => {
  //     const space = stadiumList.value.find((item) => item.id === id);
  //     return space && space.isHalf === 1;
  //   });
  //   stadiumPriceList.value[index].hasHalf = hasHalf;
  // };

  // variables
  const percent = ref(0);
  const loading = ref(false);
  const visible = ref(false);

  const NONE_FORM_DATA = {
    card_ids: [],
    schedule_price_data: [],
    type: 2,
  };
  const formData = ref(_.cloneDeep(NONE_FORM_DATA));

  // form validator
  const cardValidator = (value, callback) => {
    if (value.length === 0) {
      callback('请选择卡种');
    } else {
      callback();
    }
  };

  const planValidator = (index) => {
    return (value, callback) => {
      const item = stadiumPriceList.value[index];
      if (item.checked.length === 0) {
        callback('请选择场次');
      } else {
        callback();
      }
    };
  };

  const priceValidator = (index, size, date) => {
    return (value, callback) => {
      const item = stadiumPriceList.value[index];
      const price = item.price[size][date];
      if (price === undefined || price === null) {
        callback('请填写定价');
      } else {
        callback();
      }
    };
  };

  // methods
  const handleCancel = () => {
    emit('update:show', false);
  };
  const handleSubmit = async () => {
    const valid = await formRef.value.validate();

    if (!valid) {
      const list = [];

      // generate data
      stadiumPriceList.value.forEach((item) => {
        const spaceMap = new Map();

        item.checked.forEach((mix) => {
          const [id, day, start, end] = mix.split('_');

          if (spaceMap.has(id)) {
            const oldDayMap = spaceMap.get(id);

            if (oldDayMap.has(day)) {
              const dayValue = oldDayMap.get(day);
              dayValue.push({ start, end });

              const dayMap = new Map(oldDayMap);
              dayMap.set(day, dayValue);
              spaceMap.set(id, dayMap);
            } else {
              const dayMap = new Map(oldDayMap);
              dayMap.set(day, [{ start, end }]);
              spaceMap.set(id, dayMap);
            }
          } else {
            const dayMap = new Map();
            dayMap.set(day, [{ start, end }]);
            spaceMap.set(id, dayMap);
          }
        });

        const space = [];
        spaceMap.forEach((dayValue, id) => {
          const dayList = {};

          dayValue.forEach((timeValue, day) => {
            dayList[day] = timeValue;
          });

          space.push({
            tem_id: id,
            ...dayList,
          });
        });

        const { price } = item;

        list.push({
          space,
          price,
        });
      });

      loading.value = true;

      // split data and multiple request
      percent.value = 0;
      const chunk = 1 / list.length;

      const promises = [];
      let hasAuthority = true;
      // eslint-disable-next-line no-restricted-syntax
      for (const item of list) {
        formData.value.schedule_price_data = item;

        const promise = saveSpacePrice(formData.value)
          .then((res) => {
            percent.value = Number((percent.value + chunk).toFixed(2));
            return res;
          })
          // eslint-disable-next-line no-loop-func
          .catch((error) => {
            if (error.errorcode === 40014) {
              hasAuthority = false;
            }
          });

        promises.push(promise);
      }

      await Promise.all(promises).then(() => {
        if (hasAuthority) {
          Message.success({
            content: '会员价设置完成',
            duration: 2,
          });
        }
        setTimeout(() => {
          loading.value = false;
        }, 1000);
      });

      await new Promise((resolve) => {
        setTimeout(resolve, 2000);
      });

      if (hasAuthority) {
        handleCancel();
      }
      return hasAuthority;
      // eslint-disable-next-line no-else-return
    } else {
      loading.value = false;
      return false;
    }
  };

  // created
  initCardTree();
  getStadiumList();

  // watch
  watch(
    () => props.show,
    (val) => {
      visible.value = val;
      if (val) {
        loading.value = false;
        formData.value = _.cloneDeep(NONE_FORM_DATA);
        formRef.value?.resetFields();
        stadiumPriceList.value = [];
        handleAddStadiumPrice();
      } else {
        stadiumPriceList.value = [];
      }
    },
    { immediate: true }
  );
  // @input="handleInputNode(i, $event)" 在 vue 3 里未触发，所以使用 watch 监听
  watch(
    () => stadiumPriceList.value.map((item) => item.checked),
    (list) => {
      // disable other stadium
      if (Array.isArray(list)) {
        list.forEach((checked, index) => {
          if (checked.length === 0) {
            nextTick(() => {
              setDisable();
            });
          } else {
            let hasHalf = false;
            const spaceIdList = checked.map((item) => item.split('_')[0]);
            const spaceIdSet = new Set(spaceIdList);
            hasHalf = Array.from(spaceIdSet).some((id) => {
              const space = stadiumList.value.find((item) => item.id === Number(id));
              return space && space.isHalf === 1;
            });
            stadiumPriceList.value[index].hasHalf = hasHalf;
          }
        });
      }
      // check the space half or all
    },
    { deep: true }
  );
</script>

<style lang="less" scoped>
  ::v-deep(.vertical-center-modal) {
    display: flex;
    align-items: center;
    justify-content: center;

    .ivu-modal {
      top: 0;
    }
  }

  .unit {
    margin: auto 8px;
  }
</style>
