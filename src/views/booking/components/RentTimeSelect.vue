<template>
  <div class="rent-time-select">
    <a-checkbox v-model="selectValue.checked" class="checked" @change="handleCheckboxChange">
      {{ props.label }}
    </a-checkbox>
    <a-select
      v-if="selectValue.checked"
      v-model="selectValue.list"
      class="list"
      multiple
      :allow-search="false"
      placeholder="请选择"
      @change="handleSelectChange">
      <a-option v-for="time in timeList" :key="time" :value="time">{{ time }}</a-option>
      <template #header>
        <div style="padding: 6px 12px">
          <a-checkbox
            v-model:model-value="checkedAll"
            value="1"
            :indeterminate="indeterminate"
            @change="handleSelectAll">
            全选
          </a-checkbox>
        </div>
      </template>
    </a-select>
  </div>
</template>

<script setup lang="ts">
  import { fetchFile } from '@/request/requestOSS';

  const props = defineProps({
    type: {
      type: String,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
    url: {
      type: String,
      required: true,
    },
  });
  const emit = defineEmits(['update:modelValue']);

  // variable
  const timeList = ref<string[]>([]);
  const selectValue = ref<any>({
    checked: false,
    list: [],
  });
  const checkedAll = ref(false);
  const indeterminate = ref(false);

  // request oss
  watch(
    () => props.url,
    () => {
      if (!props.url) {
        timeList.value = [];
        selectValue.value = {
          checked: false,
          list: [],
        };
        return;
      }
      fetchFile(props.url).then((res: any) => {
        selectValue.value = {
          checked: false,
          list: [],
        };
        timeList.value = [];
        const { list } = res;
        if (Array.isArray(list)) {
          list.forEach((item) => {
            timeList.value.push(`${item.start_time}-${item.end_time}`);
          });
        }
      });
    },
    { immediate: true }
  );

  // event
  const handleCheckboxChange = () => {
    // if (selectValue.value.checked) {
    //   selectValue.value.list = timeList.value;
    //   checkedAll.value = true;
    //   indeterminate.value = false;
    // } else {
    //   selectValue.value.list = [];
    //   checkedAll.value = false;
    //   indeterminate.value = false;
    // }
    selectValue.value.list = [];
    checkedAll.value = false;
    indeterminate.value = false;
    emit('update:modelValue', selectValue.value);
  };
  const handleSelectChange = () => {
    if (selectValue.value.list.length === timeList.value.length) {
      checkedAll.value = true;
      indeterminate.value = false;
    } else if (selectValue.value.list.length === 0) {
      checkedAll.value = false;
      indeterminate.value = false;
    } else {
      checkedAll.value = false;
      indeterminate.value = true;
    }
    emit('update:modelValue', selectValue.value);
  };
  const handleSelectAll = () => {
    indeterminate.value = false;
    if (checkedAll.value) {
      selectValue.value.list = timeList.value;
    } else {
      selectValue.value.list = [];
    }
    emit('update:modelValue', selectValue.value);
  };
</script>

<style lang="less" scoped>
  .rent-time-select {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    padding: 6px 0;

    .checked {
      width: 100px;
    }
  }
</style>
