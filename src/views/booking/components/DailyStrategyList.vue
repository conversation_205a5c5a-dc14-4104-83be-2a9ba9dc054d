<template>
  <div class="daily-box">
    <transition-group v-if="list.length > 0" name="list" tag="div" class="list">
      <div v-for="(item, index) in list" :key="item.idx" class="line">
        <div class="title-box">
          <span class="label">场次{{ index + 1 }}</span>
          <span v-if="item.start_time && item.end_time" class="value">{{ item.start_time }}-{{ item.end_time }}</span>
          <span v-else class="value">未设置</span>
        </div>
        <div>
          <transition name="slide-fade" mode="out-in">
            <daily-strategy-price
              v-if="half"
              :is-whole="false"
              :item="item"
              :style="{ width: half ? '270px' : '0px' }" />
          </transition>
          <daily-strategy-price :is-whole="true" :item="item" />
        </div>
        <a-button-group v-if="!disabled" type="outline" class="buttons">
          <weekly-strategy-price-modal :day="item" />
          <copy-price-modal :day="item" :type="type" />
          <div title="删除">
            <a-button type="text" @click="handleTimeDelete(index)">
              <template #icon><icon-delete /></template>
            </a-button>
          </div>
        </a-button-group>
      </div>
    </transition-group>
    <a-empty v-else style="margin-top: 100px" />
  </div>
</template>

<script setup>
  import useBookingStore from '@/store/modules/booking';
  import DailyStrategyPrice from './DailyStrategyPrice.vue';
  import CopyPriceModal from './CopyPriceModal.vue';
  import WeeklyStrategyPriceModal from './WeeklyStrategyPriceModal.vue';

  const bookingStore = useBookingStore();

  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
  });

  // variables
  const list = computed(() => bookingStore[props.type]);
  const half = inject('half');
  const disabled = inject('disabled');

  // methods
  const handleTimeDelete = (index) => {
    list.value.splice(index, 1);

    bookingStore.setList(props.type, list.value);
  };
</script>

<style lang="less" scoped>
  .daily-box {
    padding: 20px;
    min-height: 302px;
    overflow-y: auto;

    .list {
      width: 100%;
      border-radius: 4px;
      border: 1px solid #f0f0f0;
      border-bottom: none;
    }

    .line {
      padding: 13px 20px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
    }

    .title-box {
      display: inline-block;
      vertical-align: middle;
      width: 140px;
      white-space: nowrap;
    }

    .label {
      margin-right: 10px;
      color: black;
      font-size: 14px;
      font-weight: bold;
    }

    .value {
      font-size: 14px;
      color: #333333;
    }

    .buttons {
      margin-left: 20px;
      width: 80px;
      display: inline-flex;
      justify-content: space-between;
      float: right;
    }
  }

  // .list-enter-active,
  // .list-leave-active {
  //   transition: all 0.1s ease;
  // }
  // .list-enter-from,
  // .list-leave-to {
  //   opacity: 0;
  //   transform: translateX(30px);
  // }

  /*
  Enter and leave animations can use different
  durations and timing functions.
*/
  // .slide-fade-enter-active {
  //   transition: all 0.1s ease-out 0.2s;
  // }

  // .slide-fade-leave-active {
  //   transition: all 0.1s cubic-bezier(1, 0.5, 0.8, 1);
  // }

  // .slide-fade-enter-from,
  // .slide-fade-leave-to {
  //   transform: translateX(20px);
  //   opacity: 0;
  // }
</style>
