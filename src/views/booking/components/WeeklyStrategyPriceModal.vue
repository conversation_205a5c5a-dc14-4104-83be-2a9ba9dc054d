<template>
  <div>
    <a-button v-if="!props.day" type="outline" @click="visible = true">设置价格</a-button>
    <div v-else title="设置价格">
      <a-button type="text" @click="visible = true">
        <template #icon><icon-edit /></template>
      </a-button>
    </div>
    <a-modal
      v-model:visible="visible"
      title="设置价格"
      :mask-closable="false"
      width="600px"
      @cancel="visible = false"
      @before-ok="handleBeforeOk">
      <a-alert type="warning" style="margin-bottom: 16px">
        节假日价格会随国家法定节假日执行；储值卡会员价仅限于使用储值卡支付，微信支付等方式不享受此价格
      </a-alert>
      <a-form ref="formRef" :model="formData" :rules="formRules" auto-label-width>
        <a-form-item field="stadium" label="场次">
          <a-tree-select
            v-model="formData.stadium"
            :tree-props="{
              defaultExpandAll: false,
              virtualListProps: {
                height: 200,
              },
            }"
            :allow-search="true"
            :allow-clear="true"
            :tree-checkable="true"
            tree-checked-strategy="child"
            :data="timeTreeData"
            :max-tag-count="2"
            placeholder="请选择">
            <template #tree-slot-title="{ title }">
              {{ title.split(' ')[1] }}
            </template>
          </a-tree-select>
        </a-form-item>
        <a-form-item field="price" label="非会员价">
          <a-card style="width: 100%">
            <div v-if="half" class="line">
              <span class="title">半场</span>
              <!-- <a-input-number
                v-model="noMember.half.common"
                placeholder="请输入"
                :min="0"
                :max="99999"
                :step="0.01"
                :precision="2"
                style="width: 150px; margin: 0 8px"
              >
                <template #prefix>
                  <span>平时价</span>
                </template>
                <template #suffix>
                  <span>元</span>
                </template>
              </a-input-number>
              <a-input-number
                v-model="noMember.half.holiday"
                placeholder="请输入"
                :min="0"
                :max="99999"
                :step="0.01"
                :precision="2"
                style="width: 150px; margin: 0 8px"
              >
                <template #prefix>
                  <span>节假日</span>
                </template>
                <template #suffix>
                  <span>元</span>
                </template>
              </a-input-number> -->
              <input-price v-model:price="noMember.half.common" prefix="平时价" />
              <input-price v-model:price="noMember.half.holiday" prefix="节假日" />
            </div>
            <div class="line">
              <span class="title">全场</span>
              <!-- <a-input-number
                v-model="noMember.all.common"
                placeholder="请输入"
                :min="0"
                :max="99999"
                :step="0.01"
                :precision="2"
                style="width: 150px; margin: 0 8px"
              >
                <template #prefix>
                  <span>平时价</span>
                </template>
                <template #suffix>
                  <span>元</span>
                </template>
              </a-input-number> -->
              <!-- <a-input-number
              v-model="noMember.all.holiday"
              placeholder="请输入"
              :min="0"
              :max="99999"
              :step="0.01"
              :precision="2"
              style="width: 150px; margin: 0 8px"
              >
              <template #prefix>
                <span>节假日</span>
              </template>
              <template #suffix>
                <span>元</span>
              </template>
            </a-input-number> -->
              <input-price v-model:price="noMember.all.common" prefix="平时价" />
              <input-price v-model:price="noMember.all.holiday" prefix="节假日" />
            </div>
          </a-card>
        </a-form-item>
        <a-form-item
          v-for="(member, index) in memberList"
          :key="member.common + '_' + member.holiday"
          field="price"
          :label="'会员价 ' + (index + 1)">
          <a-card style="width: 100%">
            <div style="display: flex; align-items: center; margin-bottom: 16px">
              <!-- <TreeCardSelect v-model="memberList[index].card_ids" :data="cardTreeData"></TreeCardSelect> -->
              <a-tree-select
                v-model="memberList[index].card_ids"
                :tree-props="{
                  virtualListProps: {
                    height: 200,
                  },
                }"
                :allow-search="true"
                :allow-clear="true"
                :tree-checkable="true"
                tree-checked-strategy="child"
                :data="cardTree"
                :filter-tree-node="filterTreeNode"
                :max-tag-count="2"
                placeholder="请选择"></a-tree-select>
              <a-button type="text" style="margin-left: 8px" @click="handleCardDelete(index)">
                <template #icon><icon-delete /></template>
              </a-button>
            </div>
            <div v-if="half" class="line">
              <span class="title">半场</span>
              <!-- <a-input-number
                v-model="memberList[index].half.common"
                placeholder="请输入"
                :min="0"
                :max="99999"
                :step="0.01"
                :precision="2"
                style="width: 150px; margin: 0 8px"
              >
                <template #prefix>
                  <span>平时价</span>
                </template>
                <template #suffix>
                  <span>元</span>
                </template>
              </a-input-number>
              <a-input-number
                v-model="memberList[index].half.holiday"
                placeholder="请输入"
                :min="0"
                :max="99999"
                :step="0.01"
                :precision="2"
                style="width: 150px; margin: 0 8px"
              >
                <template #prefix>
                  <span>节假日</span>
                </template>
                <template #suffix>
                  <span>元</span>
                </template>
              </a-input-number> -->
              <input-price v-model:price="memberList[index].half.common" prefix="平时价" />
              <input-price v-model:price="memberList[index].half.holiday" prefix="节假日" />
            </div>
            <div class="line">
              <span class="title">全场</span>
              <!-- <a-input-number
                v-model="memberList[index].all.common"
                placeholder="请输入"
                :min="0"
                :max="99999"
                :step="0.01"
                :precision="2"
                style="width: 150px; margin: 0 8px"
              >
                <template #prefix>
                  <span>平时价</span>
                </template>
                <template #suffix>
                  <span>元</span>
                </template>
              </a-input-number>
              <a-input-number
                v-model="memberList[index].all.holiday"
                placeholder="请输入"
                :min="0"
                :max="99999"
                :step="0.01"
                :precision="2"
                style="width: 150px; margin: 0 8px"
              >
                <template #prefix>
                  <span>节假日</span>
                </template>
                <template #suffix>
                  <span>元</span>
                </template>
              </a-input-number> -->
              <input-price v-model:price="memberList[index].all.common" prefix="平时价" />
              <input-price v-model:price="memberList[index].all.holiday" prefix="节假日" />
            </div>
          </a-card>
        </a-form-item>
        <a-form-item>
          <a-button id="vaporFly" type="outline" @click="handleAdd">
            <template #icon>
              <icon-plus />
            </template>
            新增会员价
          </a-button>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
  import _ from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import useBookingStore from '@/store/modules/booking';
  import { getCardsForAll } from '@/api/activity';
  // import TreeCardSelect from '@/components/form/tree-card-select.vue';
  import { dayMap } from '../types';
  import InputPrice from './InputPrice.vue';

  const bookingStore = useBookingStore();

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    day: {
      type: Object,
      default: () => null,
    },
  });

  const emit = defineEmits(['update:show']);

  const NONE = {
    all: {
      common: null,
      holiday: null,
    },
    half: {
      common: null,
      holiday: null,
    },
  };

  // variables
  const visible = ref(false);
  const formRef = ref();
  const formData = ref({
    stadium: [],
  });
  const formRules = ref({
    stadium: {
      required: true,
      validator: (value, cb) => {
        if (Array.isArray(value) && value.length > 0) {
          cb();
        } else {
          cb('请选择场次');
        }
      },
    },
  });
  const noMember = ref(_.cloneDeep(NONE));
  const memberList = ref([]);
  const timeTreeData = ref([]);
  const half = inject('half');

  // weekly tree select
  // const loadMore = (node) => {
  //   node.children = node.shitChildren;
  //   return Promise.resolve();
  // };
  // const handleTreeChange = (value) => {
  //   if (value.includes('monday')) {
  //     const day = timeTreeData.value.find((item) => item.value === 'monday');
  //     day.children = day.shitChildren;
  //   }
  // };

  // cards
  const { cardTree } = storeToRefs(bookingStore);
  const initCardTree = async () => {
    try {
      const { response } = await getCardsForAll();
      const list = response.value?.data.map((item) => {
        return {
          title: item.card_name,
          value: item.card_id,
          key: item.card_id,
          card_type_id: item.card_type_id,
          is_pt_time_limit_card: item.is_pt_time_limit_card,
        };
      });
      const getCardByType = (type) => {
        return list.filter((item) => item.card_type_id === type && item.is_pt_time_limit_card !== '1');
      };
      cardTree.value = [
        {
          title: '全部卡种',
          value: 'all',
          key: 'all',
          children: [
            {
              title: '期限卡',
              value: 'term',
              key: 'term',
              children: getCardByType('1'),
            },
            {
              title: '次卡',
              value: 'times',
              key: 'times',
              children: getCardByType('2'),
            },
            {
              title: '储值卡',
              value: 'value',
              key: 'value',
              children: getCardByType('3'),
            },
            {
              title: '私教课',
              value: 'private',
              key: 'private',
              children: getCardByType('4'),
            },
            {
              title: '泳教课',
              value: 'swim',
              key: 'swim',
              children: getCardByType('5'),
            },
          ].filter((item) => item.children.length > 0),
        },
      ];
    } catch (error) {
      console.error(error);
      cardTree.value = [];
    } finally {
      bookingStore.setCardTree(cardTree.value);
    }
  };
  const filterTreeNode = (searchValue, nodeData) => {
    return nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  };

  // methods
  const handleBeforeOk = async (done) => {
    const valid = await formRef.value?.validate();
    if (!valid) {
      if (memberList.value.length > 0) {
        try {
          memberList.value.forEach((item) => {
            if (!item.card_ids.length) {
              Message.error('会员卡不能为空');
              throw new Error();
            }
          });
        } catch (error) {
          done(false);
          return;
        }
      }

      formData.value.stadium.forEach((idx) => {
        const key = idx.split('_')[1];
        const list = bookingStore[key];

        const index = list.findIndex((item) => item.idx === idx);
        list[index].no_member_price = noMember.value;
        list[index].member_price = memberList.value;

        bookingStore.setList(key, list);
      });
      done(true);
    } else {
      done(false);
    }
  };
  const handleAdd = () => {
    memberList.value.push({
      card_ids: [],
      ..._.cloneDeep(NONE),
    });

    nextTick(() => {
      window.document.querySelector('#vaporFly')?.scrollIntoView({ behavior: 'smooth' });
    });
  };
  const handleCardDelete = (index) => {
    memberList.value.splice(index, 1);
  };

  // created
  if (cardTree.value.length === 0) {
    initCardTree();
  }

  // watch
  watch(
    () => visible.value,
    () => {
      if (visible.value) {
        if (props.day) {
          formData.value.stadium = [`${props.day.idx}`];
          noMember.value = _.cloneDeep(props.day.no_member_price || NONE);
          memberList.value = _.cloneDeep(props.day.member_price || []);
        } else {
          formData.value.stadium = [];
          noMember.value = _.cloneDeep(NONE);
          memberList.value = [];
        }

        const tree = [];
        Object.keys(dayMap).forEach((key) => {
          const list = bookingStore[key];
          tree.push({
            title: `${key} ${dayMap[key]}`,
            value: key,
            key,
            disabled: list.length === 0,
            isLeaf: false,
            children: list.map((item) => {
              const time = `${item.start_time}-${item.end_time}`;
              return {
                // title: time,
                title: `${dayMap[key]} ${time}`,
                value: item.idx,
                key: item.idx,
                isLeaf: true,
              };
            }),
          });
        });

        timeTreeData.value = tree;
      }
    },
    { immediate: true }
  );
  watch(
    () => props.show,
    () => {
      if (props.show) {
        visible.value = true;
        emit('update:show', false);
      }
    },
    { immediate: true }
  );
</script>

<style lang="less" scoped>
  .line {
    margin-bottom: 8px;

    .title {
      font-weight: bold;
    }
  }
</style>
