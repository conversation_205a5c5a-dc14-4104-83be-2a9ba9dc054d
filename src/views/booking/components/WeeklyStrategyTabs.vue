<template>
  <div class="weekly-box" :class="tabsClass">
    <div ref="target"></div>
    <div v-if="!disabled" class="options">
      <weekly-strategy-modal @emit-price-modal-show="priceModalShow = true" />
      <weekly-strategy-price-modal v-model:show="priceModalShow" />
      <weekly-template-modal v-if="saveStrategy" />
    </div>
    <div class="weekly">
      <a-tabs v-model:active-key="activeKey" direction="vertical" type="card-gutter">
        <a-tab-pane v-for="(value, key) of dayMapRef" :key="key" :title="value">
          <daily-strategy-list :type="key" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup>
  import { useIntersectionObserver } from '@vueuse/core';
  import { fetchFile } from '@/request/requestOSS';
  import useBookingStore from '@/store/modules/booking';
  import DailyStrategyList from './DailyStrategyList.vue';
  import WeeklyStrategyModal from './WeeklyStrategyModal.vue';
  import WeeklyStrategyPriceModal from './WeeklyStrategyPriceModal.vue';
  import WeeklyTemplateModal from './WeeklyTemplateModal.vue';
  import { dayMap } from '../types';

  const bookingStore = useBookingStore();

  const props = defineProps({
    urlMap: {
      type: Object,
      default: () => null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    saveStrategy: {
      type: Boolean,
      default: true,
    },
  });

  provide('disabled', props.disabled);

  // variable
  const activeKey = ref('monday');
  const dayMapRef = ref(dayMap);
  const priceModalShow = ref(false);
  const half = inject('half');
  const tabsClass = computed(() => {
    if (props.disabled) {
      return '';
    }
    return half.value ? 'incline' : 'decline';
  });

  // method
  const getOssFiles = () => {
    const names = Object.keys(props.urlMap);
    names.forEach(async (key) => {
      const url = props.urlMap[key];
      let { list } = await fetchFile(url);

      // set idx for refresh key
      if (Array.isArray(list)) {
        list = list.map((item, index) => {
          return {
            ...item,
            idx: `org_${key}_${index}`,
          };
        });
      } else {
        list = [];
      }

      bookingStore.setList(key, list);
    });
  };
  const setNoneList = () => {
    Object.keys(dayMapRef.value).forEach((key) => {
      bookingStore.setList(key, []);
    });
  };

  watch(
    () => props.urlMap,
    () => {
      if (props.urlMap) {
        activeKey.value = 'monday';
        getOssFiles();
      } else {
        activeKey.value = 'monday';
        setNoneList();
      }
    },
    { immediate: true, deep: true }
  );

  // intersection observer
  const target = ref(null);

  useIntersectionObserver(target, ([{ isIntersecting }]) => {
    if (!isIntersecting) {
      const element = document.querySelector('.arco-tabs-nav-tab');
      element.style.position = 'fixed';
      element.style.top = '60px';
    } else {
      const element = document.querySelector('.arco-tabs-nav-tab');
      element.style.position = 'relative';
      element.style.top = '0';
    }
  });
</script>

<style lang="less" scoped>
  .weekly-box {
    .options {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      width: 350px;
    }

    .options > * {
      margin-right: 10px;
    }
  }

  .incline {
    width: 670px;
    animation-name: incline-width;
    animation-duration: 0.2s;
    animation-fill-mode: forwards;
  }
  .decline {
    width: 960px;
    animation-name: decline-width;
    animation-duration: 0.2s;
    animation-delay: 0.1s;
    animation-fill-mode: forwards;
  }

  @keyframes incline-width {
    0% {
      width: 670px;
    }
    100% {
      width: 960px;
    }
  }

  @keyframes decline-width {
    0% {
      width: 960px;
    }
    100% {
      width: 670px;
    }
  }

  ::v-deep .arco-tabs-vertical.arco-tabs-type-card-gutter > .arco-tabs-content {
    border: 1px solid var(--color-neutral-3);
  }

  ::v-deep .arco-tabs-nav::before {
    background-color: transparent;
    visibility: hidden;
  }

  ::v-deep .arco-tabs-nav-vertical.arco-tabs-nav-type-card-gutter .arco-tabs-tab-active {
    border-right: none;
    background-color: white;
  }

  ::v-deep .arco-tabs-nav-vertical {
    margin-right: -1px;
    min-width: 60px;
    min-height: 60px;
  }
</style>
