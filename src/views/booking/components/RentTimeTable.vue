<template>
  <div class="rent-time-table">
    <a-button
      v-if="!props.remote"
      :type="isAll ? 'secondary' : 'primary'"
      shape="round"
      style="margin-bottom: 16px"
      :disabled="props.list.length === 0"
      @click="handleAll">
      {{ isAll ? '已' : '' }}选中全部场次
    </a-button>
    <a-table
      ref="tableRef"
      v-model:selectedKeys="selectedKeys"
      row-key="sequence"
      :columns="props.columns"
      :data="props.list"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: false,
      }"
      scrollbar
      :scroll="{ maxHeight: '600px' }"
      :pagination="{
        current: pageNo,
        pageSize,
        total: props.remote ? props.total : props.list.length,
        pageSizeOptions: [10, 50, 100, 500, 1000],
        showTotal: true,
        showJumper: false,
        showPageSize: true,
      }"
      @page-change="onPageChange"
      @page-size-change="onPageSizeChange" />
  </div>
</template>

<script setup lang="ts">
  const emit = defineEmits(['onSearch']);

  const props: any = defineProps({
    list: {
      type: Array,
      default: () => [],
      required: true,
    },
    columns: {
      type: Array,
      default: () => [],
      required: true,
    },
    remote: {
      type: Boolean,
      default: false,
    },
    total: {
      type: Number,
      default: 9999,
    },
    size: {
      type: Number,
      default: 10,
    },
  });

  // variable
  const tableRef = ref();
  const pageNo = ref(1);
  const pageSize = ref(props.size);
  const selectedKeys = ref([]);
  const isAll = computed(() => selectedKeys.value.length === props.list.length);

  // event
  const onPageChange = (current: number) => {
    pageNo.value = current;

    if (props.remote) {
      emit('onSearch', pageNo.value, pageSize.value);
    }
  };
  const onPageSizeChange = (size: number) => {
    pageNo.value = 1;
    pageSize.value = size;

    if (props.remote) {
      emit('onSearch', pageNo.value, pageSize.value);
    }
  };
  const handleAll = () => {
    selectedKeys.value = props.list.map((item: any) => item.sequence);
  };

  // watch
  watch(
    () => props.list,
    () => {
      if (Array.isArray(props.list) && props.list.length > 0 && !props.remote) {
        handleAll();
      }
    },
    { immediate: true }
  );

  // expose
  defineExpose({ selectedKeys });
</script>

<style scoped></style>
