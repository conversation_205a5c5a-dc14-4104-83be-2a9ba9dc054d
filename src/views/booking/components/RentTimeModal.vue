<template>
  <div class="rent-time-modal">
    <!-- <a-popover
      v-model:popup-visible="popupVisible"
      :title="stadiumName"
      @popup-visible-change="handlePopupVisibleChange"
    >
      <a-button @click="modalVisible = true">{{ buttonLabel }}</a-button>
      <template #content>
        <h4>{{ dateRange }}</h4>
        <p v-if="modalFormData.monday.checked">周一: {{ modalFormData.monday.list.join('、') }}</p>
        <p v-if="modalFormData.tuesday.checked">周二: {{ modalFormData.tuesday.list.join('、') }}</p>
        <p v-if="modalFormData.wednesday.checked">周三: {{ modalFormData.wednesday.list.join('、') }}</p>
        <p v-if="modalFormData.thursday.checked">周四: {{ modalFormData.thursday.list.join('、') }}</p>
        <p v-if="modalFormData.friday.checked">周五: {{ modalFormData.friday.list.join('、') }}</p>
        <p v-if="modalFormData.saturday.checked">周六: {{ modalFormData.saturday.list.join('、') }}</p>
        <p v-if="modalFormData.sunday.checked">周日: {{ modalFormData.sunday.list.join('、') }}</p>
      </template>
    </a-popover> -->
    <a-button v-if="Array.isArray(attrs.modelValue) && attrs.modelValue.length === 0" @click="modalVisible = true">
      进行筛选
    </a-button>
    <a-button v-else class="btn-box" @click="modalVisible = true">
      <span class="btn-label">{{ buttonLabel }}</span>
      <span style="margin-left: 8px; color: red">重新筛选</span>
    </a-button>
    <a-modal
      v-model:visible="modalVisible"
      title="场次筛选"
      :mask-closable="false"
      @cancel="modalVisible = false"
      @before-ok="submitForm">
      <a-form ref="modalFormRef" :model="modalFormData" :rules="modalFormRules">
        <a-form-item field="date" label="时间">
          <a-range-picker
            v-model="modalFormData.date"
            shortcuts-position="left"
            :shortcuts="rangeShortcuts"
            :disabled-date="disabledDate"
            style="width: 100%"
            :allow-clear="false"
            @select="onSelect"
            @popup-visible-change="onPopupVisibleChange" />
        </a-form-item>
        <a-form-item field="stadiumId" label="场地">
          <a-select v-model="modalFormData.stadiumId" allow-search placeholder="请选择" @change="handleStadiumChange">
            <a-option v-for="item in stadiumList" :key="item.value" :value="item.value">{{ item.label }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="周期场次" required>
          <div style="width: 100%">
            <RentTimeSelect
              v-for="item in modalFormData.dayList"
              :key="item.type"
              v-model="modalFormData[item.type]"
              :type="item.type"
              :label="item.label"
              :url="item.url" />
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';
  import _ from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import { getSchedule, getStadiumList } from '@/api/booking';
  import { useBusInfoStore } from '@/store';
  import { dayMap, weekdayMap } from '../types';
  import RentTimeSelect from './RentTimeSelect.vue';

  const busInfo = useBusInfoStore();

  const attrs = useAttrs();
  const emit = defineEmits(['update:modelValue', 'update:stadium']);

  // variable
  const NONE_DAY_LIST = [
    {
      type: 'monday',
      label: dayMap.monday,
      url: '',
    },
    {
      type: 'tuesday',
      label: dayMap.tuesday,
      url: '',
    },
    {
      type: 'wednesday',
      label: dayMap.wednesday,
      url: '',
    },
    {
      type: 'thursday',
      label: dayMap.thursday,
      url: '',
    },
    {
      type: 'friday',
      label: dayMap.friday,
      url: '',
    },
    {
      type: 'saturday',
      label: dayMap.saturday,
      url: '',
    },
    {
      type: 'sunday',
      label: dayMap.sunday,
      url: '',
    },
  ];
  const NONE_FORM_DATA = {
    date: null,
    stadiumId: '',
    dayList: _.cloneDeep(NONE_DAY_LIST),
    monday: {},
    tuesday: {},
    wednesday: {},
    thursday: {},
    friday: {},
    saturday: {},
    sunday: {},
  };
  const modalFormRef = ref();
  const modalFormRules = {
    date: {
      required: true,
      message: '请选择日期',
    },
    stadiumId: {
      required: true,
      message: '请选择场地',
    },
  };
  const modalFormData = ref<any>(_.cloneDeep(NONE_FORM_DATA));
  const modalVisible = ref(false);
  // const popupVisible = ref(false);
  const stadiumList = ref<any>([]);
  const stadium = computed(() => {
    return stadiumList.value.find((item: any) => item.value === modalFormData.value.stadiumId);
  });
  const stadiumName = computed(() => {
    let name = '场地';
    if (stadium.value) {
      name = stadium.value.label;
    }
    return name;
  });
  const dateRange = computed(() => {
    const { date } = modalFormData.value;
    let range = '时间';
    if (Array.isArray(date) && date.length > 0) {
      range = `${dayjs(date[0]).format('YYYY-MM-DD')} ~ ${dayjs(date[1]).format('YYYY-MM-DD')}`;
    }
    return range;
  });
  const rangeShortcuts = ref<any>([
    {
      label: '一周',
      value: () => [dayjs(), dayjs().add(1, 'week').subtract(1, 'day')],
    },
    {
      label: '一月',
      value: () => [dayjs(), dayjs().add(1, 'month').subtract(1, 'day')],
    },
    {
      label: '半年',
      value: () => [dayjs(), dayjs().add(6, 'month').subtract(1, 'day')],
    },
    {
      label: '一年',
      value: () => [dayjs(), dayjs().add(1, 'year').subtract(1, 'day')],
    },
  ]);

  // utility
  let dates: any = [];
  const onSelect = (valueString: any, value: any) => {
    dates = value;
  };
  const onPopupVisibleChange = (visible: any) => {
    if (!visible) {
      dates = [];
    }
  };
  const disabledDate = (current: any) => {
    // 当天之前的日期不可选
    if (dayjs(current).add(1, 'day').valueOf() < dayjs().valueOf()) {
      return true;
    }
    // 365天之外的日期不可选
    if (dates && dates.length) {
      const oneDay = 24 * 60 * 60 * 1000;
      const tooLate = dates[0] && Math.abs((dayjs(current).valueOf() - dates[0]) / oneDay) > 365;
      const tooEarly = dates[1] && Math.abs((dayjs(current).valueOf() - dates[1]) / oneDay) > 365;
      return tooEarly || tooLate;
    }
    return false;
  };
  const generateDateList = (date: string[]): string[] => {
    const [startDateStr, endDateStr] = date;
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);
    const dateList = [];

    const currentDate = new Date(startDate);

    while (currentDate.getTime() <= endDate.getTime()) {
      dateList.push(dayjs(currentDate).format('YYYY-MM-DD'));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dateList;
  };
  const generateRentTimeList = (dateList: string[], unavailableList?: any): any => {
    let list: any = [];
    if (Array.isArray(dateList) && dateList.length > 0) {
      dateList.forEach((date) => {
        const whatWeekday = dayjs(date).day();
        const weekday = weekdayMap[whatWeekday];
        const item = modalFormData.value[weekday];
        const oneDayList: any = [];
        if (item.checked) {
          item.list.forEach((time: string) => {
            const [start_time, end_time] = time.split('-');
            const startTimestamp = dayjs(`${date} ${start_time}`).valueOf();
            const endTimestamp = dayjs(`${date} ${end_time}`).valueOf();

            // TODO: if current date not in unavailable list, then skip
            let skipFlag = false;
            if (Array.isArray(unavailableList) && unavailableList.length > 0) {
              unavailableList.forEach((unavailable: any) => {
                const [unavailableStartTimestamp, unavailableEndTimestamp] = unavailable;

                const unavailableStartTimestampInRange =
                  startTimestamp <= unavailableStartTimestamp && unavailableEndTimestamp <= endTimestamp;
                const unavailableEndTimestampInRange =
                  startTimestamp >= unavailableStartTimestamp && unavailableEndTimestamp >= endTimestamp;

                if (unavailableStartTimestampInRange || unavailableEndTimestampInRange) {
                  skipFlag = true;
                }
              });
            }

            if (!skipFlag) {
              oneDayList.push({
                date,
                time,
                start_time,
                end_time,
              });
            }
          });
        }
        list = [...list, ...oneDayList];
      });

      // set sequence
      list = list.map((item: any, index: number) => ({ ...item, sequence: index }));
    }
    return list;
  };
  const getWeekdayLabel = (weekday: string, list: []) => {
    let label = '';
    if (Array.isArray(list) && list.length > 0) {
      label = `${weekday} ${list.join('、')}`;
    }
    return label;
  };

  // join weekday in a string
  const buttonLabel = computed(() => {
    const mondayValue = getWeekdayLabel('周一', modalFormData.value.monday.list);
    const tuesdayValue = getWeekdayLabel('周二', modalFormData.value.tuesday.list);
    const wednesdayValue = getWeekdayLabel('周三', modalFormData.value.wednesday.list);
    const thursdayValue = getWeekdayLabel('周四', modalFormData.value.thursday.list);
    const fridayValue = getWeekdayLabel('周五', modalFormData.value.friday.list);
    const saturdayValue = getWeekdayLabel('周六', modalFormData.value.saturday.list);
    const sundayValue = getWeekdayLabel('周日', modalFormData.value.sunday.list);
    const joinValue = [
      mondayValue,
      tuesdayValue,
      wednesdayValue,
      thursdayValue,
      fridayValue,
      saturdayValue,
      sundayValue,
    ]
      .filter((item) => item)
      .join(', ');
    return `${dateRange.value}, ${stadiumName.value}, ${joinValue}`;
  });

  // popup event
  // const handlePopupVisibleChange = (val: boolean) => {
  //   if (val) {
  //     if (modalFormData.value.date && modalFormData.value.stadiumId) {
  //       popupVisible.value = true;
  //     } else {
  //       popupVisible.value = false;
  //     }
  //   } else {
  //     popupVisible.value = false;
  //   }
  // };

  // form event
  const submitForm = async (done: any) => {
    const valid = await modalFormRef.value?.validate();
    if (valid) {
      done(false);
      return;
    }

    let strideFlag = false;
    const weekdays: string[] = Object.values(weekdayMap);
    weekdays.forEach((weekday: string) => {
      if (modalFormData.value[weekday].checked) {
        strideFlag = true;
      }
    });
    if (!strideFlag) {
      Message.error({ content: '请至少选择一天' });
      done(false);
      return;
    }

    // except: ordered and disabled
    const unavailableList: number[] = [];
    // getUnavailableList().then((res: any) => {
    // })

    // date range transform to date list
    const { date } = modalFormData.value;
    const dateList = generateDateList(date);

    // generate rent time list
    const list = generateRentTimeList(dateList, unavailableList);
    emit('update:modelValue', list);

    // emit stadium
    emit('update:stadium', stadium.value);
    // close modal
    done(true);
  };

  // stadium select initialization
  getStadiumList({ bus_id: busInfo.bus_id }).then((res) => {
    const { data: list }: any = res.response.value;
    if (Array.isArray(list)) {
      list.forEach((item) => {
        stadiumList.value.push({
          value: item.id,
          label: item.name,
          space_id: item.space_id,
          space_name: item.space_name,
          position: item.position,
        });
      });
    }
  });

  // stadium change
  const handleStadiumChange = () => {
    getSchedule({ space_id: stadium.value.space_id }).then((res: any) => {
      const { data } = res.response.value;
      const schedules = data?.schedules;
      const list: any = [];
      Object.keys(schedules).forEach((key) => {
        const url = schedules[key];
        list.push({
          type: key,
          label: dayMap[key],
          url,
        });
      });
      modalFormData.value.dayList = list;
    });
  };
</script>

<style lang="less" scoped>
  .btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn-label {
      text-align: left;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 618px;
    }
  }
</style>
