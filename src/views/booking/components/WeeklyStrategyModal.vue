<template>
  <div>
    <a-button type="outline" @click="handleEdit">编辑场次</a-button>
    <a-modal
      v-model:visible="visible"
      title="编辑场次"
      :mask-closable="false"
      width="50%"
      @cancel="handleCancel"
      @before-ok="handleBeforeOk">
      <div v-for="(value, key) of dayMapRef" :key="key" style="margin-bottom: 20px">
        <daily-strategy-card :type="key" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
  import { Message, Modal } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import _ from 'lodash';
  import useBookingStore from '@/store/modules/booking';
  import DailyStrategyCard from './DailyStrategyCard.vue';
  import { dayMap } from '../types';

  const bookingStore = useBookingStore();

  const limit = inject('limit');
  const limitRef = inject('limitRef');

  const emit = defineEmits(['emitPriceModalShow']);

  // variables
  const dayMapRef = ref(dayMap);
  const visible = ref(false);
  // cache data
  let monday = [];
  let tuesday = [];
  let wednesday = [];
  let thursday = [];
  let friday = [];
  let saturday = [];
  let sunday = [];

  // methods
  const handleEdit = () => {
    if (!limit.value) {
      Message.warning('订场限制未设置!');
      limitRef?.value?.focus();
    } else {
      monday = _.cloneDeep(bookingStore.monday);
      tuesday = _.cloneDeep(bookingStore.tuesday);
      wednesday = _.cloneDeep(bookingStore.wednesday);
      thursday = _.cloneDeep(bookingStore.thursday);
      friday = _.cloneDeep(bookingStore.friday);
      saturday = _.cloneDeep(bookingStore.saturday);
      sunday = _.cloneDeep(bookingStore.sunday);

      visible.value = true;
    }
  };
  const checkDayList = () => {
    let limitError = false;
    const LIMIT_ERROR = `单次订场时长不能大于${limit.value}个小时`;
    let repeatError = false;
    const REPEAT_ERROR = '请勿重复排场！';
    let noneError = true;
    const NONE_ERROR = '没有设置场次!';
    let noTimeCount = 0;
    const NO_TIME_ERROR = '没有设置时间!';

    const limitTimestamp = limit.value * 60 * 60 * 1000;
    const date = dayjs().format('YYYY-MM-DD');
    Object.keys(dayMap).forEach((key) => {
      const noRepeatList = [];

      const list = bookingStore[key];
      list.forEach((item) => {
        noneError = false;

        const { start_time, end_time } = item;
        const startTimestamp = dayjs(`${date} ${start_time}`).valueOf();
        let endTimestamp = dayjs(`${date} ${end_time}`).valueOf();
        item.error = false;

        if (end_time === '00:00') {
          endTimestamp = dayjs(`${date} 24:00:00`).valueOf();
        }

        // no time
        if (!start_time || !end_time) {
          item.error = true;
          noTimeCount += 1;
        }

        // less than limit
        if (endTimestamp - startTimestamp > limitTimestamp) {
          item.error = true;
          limitError = true;
        }

        // not in noRepeatList, between start and end
        const inRange = noRepeatList.findIndex(({ start, end }) => {
          const startInRange = startTimestamp >= start && startTimestamp < end;
          const endInRange = endTimestamp > start && endTimestamp <= end;
          return startInRange || endInRange;
        });

        if (inRange !== -1) {
          item.error = true;
          repeatError = true;
        }

        noRepeatList.push({
          start: startTimestamp,
          end: endTimestamp,
        });
      });
      bookingStore.setList(key, list);
    });

    if (limitError) {
      Message.warning(LIMIT_ERROR);
    }

    if (repeatError) {
      Message.warning(REPEAT_ERROR);
    }

    if (noneError) {
      Message.warning(NONE_ERROR);
    }

    if (noTimeCount) {
      Message.warning(NO_TIME_ERROR);
    }

    return !limitError && !repeatError && !noneError && !noTimeCount;
  };
  const handleBeforeOk = (done) => {
    if (checkDayList()) {
      done(true);
      Modal.confirm({
        title: '操作成功',
        content: '是否进行场次价格设置',
        okText: '进行价格设置',
        cancelText: '关闭',
        maskClosable: false,
        onOk: () => {
          emit('emitPriceModalShow');
        },
      });
    } else {
      done(false);
    }
  };
  const handleCancel = () => {
    bookingStore.setList('monday', monday);
    bookingStore.setList('tuesday', tuesday);
    bookingStore.setList('wednesday', wednesday);
    bookingStore.setList('thursday', thursday);
    bookingStore.setList('friday', friday);
    bookingStore.setList('saturday', saturday);
    bookingStore.setList('sunday', sunday);

    visible.value = false;
  };
</script>

<style lang="less" scoped></style>
