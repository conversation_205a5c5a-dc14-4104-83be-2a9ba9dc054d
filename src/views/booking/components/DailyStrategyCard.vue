<template>
  <a-card>
    <template #title>
      <div class="title-box">
        <span>{{ title }}</span>
        <span style="font-size: 14px; margin-left: 20px">
          共
          <span style="color: #ff2351; font-size: 16px">{{ list.length }}</span>
          场次
        </span>
      </div>
    </template>
    <transition-group name="list" tag="div" class="time-box">
      <div v-for="(item, index) in list" :key="item.idx" class="time">
        <a-time-picker
          :key="updateKey"
          :default-value="[item.start_time, item.end_time]"
          format="HH:mm"
          type="time-range"
          :step="{ hour: 1, minute: 30 }"
          style="width: 200px"
          disable-confirm
          :error="item.error"
          @change="handleTimeChange(index, $event)">
          <template #extra>
            <a-button type="text" :disabled="!item.start_time" @click="handleTimeSetZero(index)">至凌晨 24:00</a-button>
          </template>
        </a-time-picker>
        <div title="删除">
          <a-button type="text" @click="handleTimeDelete(index)">
            <template #icon><icon-delete /></template>
          </a-button>
        </div>
      </div>
    </transition-group>
    <div class="btn-box">
      <a-space>
        <a-button type="primary" size="mini" style="margin-left: 10px" @click="handleTimeAdd">
          <template #icon>
            <icon-plus />
          </template>
          新增场次
        </a-button>
        <daily-strategy-copy-modal :type="type" />
      </a-space>
    </div>
  </a-card>
</template>

<script setup>
  import dayjs from 'dayjs';
  import { Message } from '@arco-design/web-vue';
  import useBookingStore from '@/store/modules/booking';
  import DailyStrategyCopyModal from './DailyStrategyCopyModal.vue';
  import { dayMap } from '../types';

  const bookingStore = useBookingStore();

  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
  });

  // variables
  const list = computed(() => bookingStore[props.type]);
  const title = ref(dayMap[props.type]);

  // methods
  const handleTimeChange = (index, value) => {
    if (value?.length === 2) {
      const [start_time, end_time] = value;
      list.value[index].start_time = start_time;
      list.value[index].end_time = end_time;
      list.value[index].error = false;
    } else {
      list.value[index].start_time = null;
      list.value[index].end_time = null;
    }

    bookingStore.setList(props.type, list.value);
  };

  const updateKey = ref(0);
  const handleTimeSetZero = (index) => {
    list.value[index].end_time = '00:00';
    list.value[index].error = false;

    bookingStore.setList(props.type, list.value);
    updateKey.value += 1;
  };

  const handleTimeDelete = (index) => {
    list.value.splice(index, 1);

    bookingStore.setList(props.type, list.value);
  };

  let idx = 0;
  const handleTimeAdd = () => {
    let start_time = '07:00';
    let end_time = '08:00';
    if (list.value.length) {
      const last = list.value[list.value.length - 1];

      if (last.start_time && last.end_time) {
        const date = dayjs().format('YYYY-MM-DD');
        const lastDistance = dayjs(`${date} ${last.end_time}`).diff(`${date} ${last.start_time}`, 'minute');

        start_time = last.end_time;
        if (start_time === '00:00') {
          Message.error('已到最后一场，无法再添加新场次');
          return;
        }

        const endDate = dayjs(`${date} ${last.end_time}`).add(lastDistance, 'minute');
        const zeroDate = dayjs(`${date} 24:00`);

        if (endDate.isBefore(zeroDate)) {
          end_time = endDate.format('HH:mm');
        } else {
          end_time = zeroDate.format('HH:mm');
        }
      } else {
        // start_time = '';
        // end_time = '';
        Message.error('请先设置上一个场次的时间');
        return;
      }
    }
    list.value.push({
      idx: `add_${props.type}_${idx}`,
      start_time,
      end_time,
    });

    idx += 1;

    bookingStore.setList(props.type, list.value);
  };
</script>

<style lang="less" scoped>
  .title-box {
    display: flex;
    align-items: center;
  }

  .time-box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .time {
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 250px;
      margin-bottom: 10px;
    }
  }

  .btn-box {
    display: flex;
    align-items: center;
  }

  // .list-enter-active,
  // .list-leave-active {
  //   transition: all 0.2s ease;
  // }
  // .list-enter-from,
  // .list-leave-to {
  //   opacity: 0;
  //   transform: translateX(30px);
  // }
</style>
