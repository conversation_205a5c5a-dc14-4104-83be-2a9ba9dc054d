<template>
  <div>
    <a-button type="primary" @click="visible = true">保存方案为模版</a-button>
    <a-modal
      v-model:visible="visible"
      title="保存模板"
      :mask-closable="false"
      width="30%"
      @cancel="visible = false"
      @before-ok="handleBeforeOk">
      <a-form ref="formRef" :model="formData" :rules="formRules" auto-label-width>
        <a-form-item field="name" label="方案名称">
          <a-input v-model="formData.name" placeholder="请输入方案名称" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
  import { Message } from '@arco-design/web-vue';
  import _ from 'lodash';
  import useBookingStore from '@/store/modules/booking';
  import { saveStrategy } from '@/api/booking';
  import { pushFile } from '@/request/requestOSS';

  const bookingStore = useBookingStore();

  const limit = inject('limit');
  const half = inject('half');
  const loadStrategyList = inject('loadStrategyList');

  // variable
  const formData = ref({
    name: '',
  });
  const formRef = ref();
  const formRules = ref({
    name: { required: true, message: '请输入方案名称' },
  });
  const visible = ref(false);

  // methods
  const handleBeforeOk = async (done) => {
    const valid = await formRef.value?.validate();
    if (valid) {
      done(false);
    } else {
      // check list
      if (!bookingStore.checkAll(formData.value.max_hour, formData.value.is_half === 1)) {
        done(false);
        return;
      }

      const monday = _.cloneDeep(bookingStore.monday);
      const tuesday = _.cloneDeep(bookingStore.tuesday);
      const wednesday = _.cloneDeep(bookingStore.wednesday);
      const thursday = _.cloneDeep(bookingStore.thursday);
      const friday = _.cloneDeep(bookingStore.friday);
      const saturday = _.cloneDeep(bookingStore.saturday);
      const sunday = _.cloneDeep(bookingStore.sunday);

      // push oss
      const today = Date.now();
      const schedule_price = {
        monday: await pushFile('strategy', `${today}_monday.json`, { list: monday }),
        tuesday: await pushFile('strategy', `${today}_tuesday.json`, { list: tuesday }),
        wednesday: await pushFile('strategy', `${today}_wednesday.json`, { list: wednesday }),
        thursday: await pushFile('strategy', `${today}_thursday.json`, { list: thursday }),
        friday: await pushFile('strategy', `${today}_friday.json`, { list: friday }),
        saturday: await pushFile('strategy', `${today}_saturday.json`, { list: saturday }),
        sunday: await pushFile('strategy', `${today}_sunday.json`, { list: sunday }),
      };

      // submit
      saveStrategy({
        name: formData.value.name,
        max_hour: limit.value,
        is_half: half.value ? 1 : 0,
        schedule_price: JSON.stringify(schedule_price),
      }).then(({ response }) => {
        if (response.value.errorcode === 0) {
          Message.success({
            content: '模版保存成功！',
          });

          formData.value.name = '';
          if (loadStrategyList) {
            loadStrategyList();
          }
          done(true);
        }
      });
    }
  };
</script>

<style lang="less" scoped></style>
