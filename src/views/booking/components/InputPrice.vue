<template>
  <div class="input-price">
    <span>{{ props.prefix }}</span>
    <a-input-number
      v-model="value"
      :min="0"
      :max="Infinity"
      :step="0.01"
      :precision="2"
      style="width: 90px; margin: 0 4px"
      placeholder="请输入"
      @change="handleChange" />
    <span>{{ props.suffix }}</span>
  </div>
</template>

<script setup>
  const props = defineProps({
    prefix: {
      type: String,
      default: '',
    },
    suffix: {
      type: String,
      default: '元',
    },
    price: {
      type: Number,
      default: 0,
    },
  });

  const emits = defineEmits(['update:price']);

  const value = ref(props.price);

  const handleChange = () => {
    if (value.value === null || value.value === undefined) {
      value.value = null;
      emits('update:price', null);
    } else {
      emits('update:price', value.value);
    }
  };

  watch(
    () => props.price,
    (newValue) => {
      if (newValue === null || newValue === undefined) {
        value.value = null;
      } else {
        value.value = newValue;
      }
    },
    { immediate: true }
  );
</script>

<style lang="less" scoped>
  .input-price {
    display: inline-flex;
    align-items: center;
    margin: 0 8px;
  }
</style>
