<template>
  <div>
    <a-button type="primary" size="mini" @click="handleCopy">
      <template #icon>
        <icon-copy />
      </template>
      复制场次信息
    </a-button>
    <a-modal
      v-model:visible="visible"
      title="复制场次信息"
      :mask-closable="false"
      width="30%"
      @cancel="visible = false"
      @before-ok="handleBeforeOk">
      <a-form ref="formRef" :model="formData" :rules="formRules" auto-label-width>
        <a-form-item field="from" label="复制">
          <a-select v-model="formData.from" placeholder="请选择" allow-clear allow-search>
            <a-option v-for="(value, key) of dayMapRef" :key="key" :value="key">{{ value }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="to" label="粘贴到">
          <a-select v-model="formData.to" placeholder="请选择" allow-clear allow-search multiple>
            <a-option v-for="(value, key) of dayMapRef" :key="key" :value="key" :disabled="key === formData.from">
              {{ value }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-alert type="warning">注意: 粘贴的周期内已有场次将被全部覆盖替换</a-alert>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
  import useBookingStore from '@/store/modules/booking';
  import { dayMap } from '../types';

  const bookingStore = useBookingStore();

  // props
  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
  });

  // variable
  const dayMapRef = ref(dayMap);
  const formData = ref({
    from: '',
    to: [],
  });
  const formRef = ref();
  const formRules = ref({
    from: { required: true, message: '请选择' },
    to: { required: true, message: '请选择' },
  });
  const visible = ref(false);

  // methods
  const handleCopy = () => {
    formData.value.from = '';
    formData.value.to = [];
    visible.value = true;
  };
  const handleBeforeOk = async (done) => {
    const valid = await formRef.value?.validate();
    if (valid) {
      done(false);
    } else {
      const fromList = bookingStore[formData.value.from];
      formData.value.to.forEach((key) => {
        const copyList = fromList.map((item) => {
          return {
            ...item,
            idx: item.idx.replace(formData.value.from, key),
          };
        });
        bookingStore.setList(key, copyList);
      });
      done(true);
    }
  };

  // watch
  watch(
    () => visible.value,
    (val) => {
      if (val) {
        formData.value.from = props.type;
        formData.value.to = [];
      }
    }
  );
</script>

<style lang="less" scoped></style>
