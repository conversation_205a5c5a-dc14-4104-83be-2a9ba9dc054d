<template>
  <div class="price-box" :style="attrs.style">
    <a-popover :title="title" position="lt">
      <div class="overflow-hidden">
        <span class="title">{{ title }}</span>
        <span v-if="noMember.common === null || noMember.holiday === null" class="desc" style="color: red">
          未设置价格
        </span>
        <template v-else>
          <span v-if="noMember" class="desc">非会员:{{ noMember?.common || 0 }}元/{{ noMember?.holiday || 0 }}元;</span>
          <span v-for="(m, i) in memberList" :key="i + '_' + m.common + '_' + m.holiday" class="desc">
            会员价{{ i + 1 }}:{{ m?.common || 0 }}元/{{ m?.holiday || 0 }}元;
          </span>
        </template>
      </div>
      <template #content>
        <a-list>
          <a-list-item>
            <a-tag color="red" style="width: 60px">非会员:</a-tag>
            <a-tag style="margin-left: 8px">
              平时价: {{ noMember?.common || 0 }}元，节假日: {{ noMember?.holiday || 0 }}元
            </a-tag>
          </a-list-item>
          <a-list-item v-for="(m, i) in memberList" :key="m.common + '_' + m.holiday + '_' + i">
            <a-tag color="green" style="min-width: 60px">会员价{{ i + 1 }}:</a-tag>
            <a-tag style="margin-left: 8px">平时价: {{ m?.common || 0 }}元，节假日: {{ m?.holiday || 0 }}元</a-tag>
          </a-list-item>
        </a-list>
      </template>
    </a-popover>
  </div>
</template>

<script setup>
  const attrs = useAttrs();

  const props = defineProps({
    isWhole: {
      type: Boolean,
      default: true,
    },
    item: {
      type: Object,
      default: () => ({}),
    },
  });

  const titleMap = {
    half: '半场',
    all: '全场',
  };

  // variables
  const title = ref('');
  const noMember = computed(() => {
    if (props.isWhole) {
      return (
        props.item?.no_member_price?.all || {
          common: null,
        }
      );
    }
    return (
      props.item?.no_member_price?.half || {
        common: null,
      }
    );
  });
  const memberList = computed(() => {
    if (props.isWhole) {
      return props.item?.member_price?.map((item) => item.all)?.filter((item) => item.common !== null);
    }
    return props.item?.member_price?.map((item) => item.half)?.filter((item) => item.common !== null);
  });

  // watch
  watch(
    () => props.isWhole,
    (val) => {
      if (val) {
        title.value = titleMap.all;
      } else {
        title.value = titleMap.half;
      }
    },
    { immediate: true }
  );
</script>

<style lang="less" scoped>
  .overflow-hidden {
    width: 270px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .price-box {
    display: inline-block;
    vertical-align: middle;

    .title {
      color: black;
      font-size: 14px;
      font-weight: bold;
    }

    .desc {
      font-size: 14px;
      color: #333333;
      margin-left: 4px;
    }
  }
</style>
