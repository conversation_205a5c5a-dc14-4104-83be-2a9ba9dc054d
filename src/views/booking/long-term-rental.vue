<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :rules="formRules" :model="formData" class="general-form" auto-label-width>
        <a-form-item field="user_type" label="客户类型" :rules="{ required: true, message: '请填写' }">
          <a-radio-group v-model="formData.user_type" type="button" @change="handleUserTypeChange">
            <a-radio :value="1">会员</a-radio>
            <a-radio :value="2">散客</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="formData.user_type === 1"
          field="user_id"
          label="会员"
          :rules="{ required: true, message: '请填写' }">
          <user-search
            v-model="formData.user_id"
            :bus-id="formData.bus_id"
            :from="5"
            placeholder="姓名/电话"
            @change="handleUserChange" />
        </a-form-item>
        <a-form-item v-else label="电话" field="phone" :rules="{ required: true, validator: checkMobile }">
          <a-input v-model.trim="formData.phone" />
        </a-form-item>
        <a-form-item label="预订场次" required>
          <div style="width: 100%">
            <RentTimeModal v-model="rentTimeList" v-model:stadium="stadium" />
            <a-divider />
            <RentTimeTable ref="rentTimeTableRef" :list="rentTimeList" :columns="columns" />
          </div>
        </a-form-item>
        <a-form-item label="场次统计">
          <!-- <a-statistic
            title="总共选择场次"
            :value="selectedKeys.length"
            :value-style="{ color: '#0fbf60' }"
            show-group-separator
          /> -->
          <div style="font-size: 14px; line-height: 32px">
            总共选择
            <span style="color: red; font-weight: bold; font-size: 18px">{{ selectedKeys.length }}</span>
            场次
          </div>
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea
            v-model="formData.remark"
            :auto-size="{
              minRows: 5,
              maxRows: 10,
            }"
            placeholder="请输入"
            allow-clear />
        </a-form-item>
        <a-form-item label="实际支付">
          <a-input-number
            v-model="formData.amount"
            placeholder="元"
            :min="0"
            :max="Infinity"
            :precision="2"
            @input="handleAmountChange" />
        </a-form-item>
        <a-form-item v-if="[null, '', undefined].includes(formData.amount) || formData.amount > 0" label="支付方式">
          <PayTypeList
            v-model="formData.new_pay_type"
            :amount="formData.amount"
            :bus-id="formData.bus_id"
            :user-id="formData.user_id"
            :describe="`订场[${stadium?.space_name}]`"
            :sqb-option="sqbOption" />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <div v-if="isLoading" class="loading-mask"></div>
    <div v-if="isLoading" class="loading-box">
      <a-spin tip="订单处理中!" />
      <!-- <div class="loader"></div> -->
      <!-- <a-spin tip="订单处理中!" dot style="margin-top: 10px" /> -->
    </div>

    <a-modal v-model:visible="tipVisible" title="提示" :mask-closable="false">
      <a-alert type="success">数据已提交，处理大概需要1~ 5分钟 ，请到“长租任务”中查下执行结果</a-alert>
      <template #footer>
        <a-button type="primary" @click="handleTipGoTask">前往"长租任务"查看</a-button>
        <a-button @click="handleTipConfirm">完成</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import PayTypeList from '@/components/form/PayTypeList.vue';
  import { useBusInfoStore } from '@/store';
  import UserSearch from '@/components/user/user-search.vue';
  import { saveLongTermRental, saveBookingList, checkLongTermRentalStatus } from '@/api/booking';
  import { pushFile } from '@/request/requestOSS';
  import RentTimeTable from './components/RentTimeTable.vue';
  import RentTimeModal from './components/RentTimeModal.vue';

  const router = useRouter();
  const busInfo = useBusInfoStore();

  // form
  const formRef = ref();
  const formData = ref<any>({
    user_type: 1,
    user_id: '',
    bus_id: busInfo.bus_id,
    remark: '',
    amount: undefined,
    new_pay_type: [],
    phone: '',
  });
  const formRules = {
    user_type: { required: true, message: '请选择客户类型' },
    user_id: { required: true, message: '请选择客户' },
  };
  const checkMobile = (value: string, cb: any) => {
    if (!value) {
      cb('请输入手机号');
    }
    if (!/^1\d{10}$/.test(value)) {
      cb('手机号码错误');
    }
    cb();
  };
  const isLoading = ref(false);

  // table
  const rentTimeTableRef = ref();
  const rentTimeList = ref<any>([]);
  const columns = [
    {
      title: '日期',
      dataIndex: 'date',
    },
    {
      title: '场次时间',
      dataIndex: 'time',
    },
  ];
  const selectedKeys = computed(() => {
    return rentTimeTableRef.value?.selectedKeys || [];
  });

  // modal
  const stadium = ref<any>();
  const tipVisible = ref(false);

  // event
  const handleAmountChange = (value: any) => {
    formData.value.amount = value;
  };
  const handleUserTypeChange = () => {
    formData.value.user_id = '';
    formData.value.phone = '';
  };
  const userObject = ref();
  const handleUserChange = (info: any) => {
    formData.value.user_id = info?.user_id || '';
    formData.value.phone = info?.phone || '';
    userObject.value = info || null;
  };
  const handleTipGoTask = () => {
    tipVisible.value = false;
    router.push('/v2/Web/SpaceOrder/getList?active=3');
  };
  const handleTipConfirm = () => {
    tipVisible.value = false;
    router.back();
  };
  const handleSubmit = async () => {
    const errorcode = await checkLongTermRentalStatus()
      .then(({ response }: any) => response.value.errorcode)
      .catch((error) => error.errorcode);
    console.log('errorcode: ', errorcode);
    if (errorcode !== 0) {
      return;
    }

    if (selectedKeys.value.length === 0) {
      Message.warning('请选择场次');
      return;
    }

    if (selectedKeys.value.length > 1000) {
      Message.warning('单次最多选择1000场次');
      return;
    }

    if (formData.value.amount > 0 && formData.value.new_pay_type.length === 0) {
      Message.warning('请选择支付方式');
      return;
    }

    const valid = await formRef.value?.validate();
    if (!valid) {
      isLoading.value = true;

      const bookingList = rentTimeList.value
        .filter((item: any, index: number) => {
          return selectedKeys.value.includes(index);
        })
        .map((item: any) => {
          return {
            date: item.date,
            start_time: item.start_time,
            end_time: item.end_time,
          };
        })
        .sort((a: any, b: any) => {
          return dayjs(`${a.date} ${a.start_time}`).valueOf() - dayjs(`${b.date} ${b.start_time}`).valueOf();
        });

      const first = bookingList[0];
      const last = bookingList[bookingList.length - 1];

      const postData = {
        ...formData.value,
        start_date: first.date,
        end_date: last.date,
        space_id: stadium.value?.space_id,
        position: stadium.value?.position,
        phone: formData.value?.phone,
        num: selectedKeys.value.length,
      };

      // settings
      const LIMIT_NUMBER = 200;

      try {
        // save form
        const formRes = await saveLongTermRental(postData);
        const { batch_number } = formRes.data.value;
        // const batch_number = 'test';

        // push file
        let weeklyFile: string | boolean = '';
        const files = [];
        if (batch_number) {
          // push recent 7 days first
          const afterSevenDaysTimestamp = dayjs().add(7, 'day').valueOf();
          const afterSevenDaysIndex = bookingList.findIndex((item: any) => {
            return dayjs(item.date).valueOf() >= afterSevenDaysTimestamp;
          });

          if (afterSevenDaysIndex > 0) {
            // part in 7 days
            const recentSevenDaysList = bookingList.splice(0, afterSevenDaysIndex);
            weeklyFile = await pushFile('booking', `${batch_number}_7days.json`, recentSevenDaysList);
          } else if (afterSevenDaysIndex === 0) {
            // out of 7 days
            weeklyFile = '';
          } else {
            // all in 7 days
            weeklyFile = await pushFile('booking', `${batch_number}_7days.json`, bookingList);
          }

          // create files with 100 objects from bookingList, use Promise.all
          if (afterSevenDaysIndex >= 0 && bookingList.length > 0) {
            const fileNum = Math.ceil(bookingList.length / LIMIT_NUMBER);
            const promises = [];
            for (let i = 0; i < fileNum; i += 1) {
              const file = bookingList.slice(i * LIMIT_NUMBER, (i + 1) * LIMIT_NUMBER);
              promises.push(pushFile('booking', `${batch_number}_${i}.json`, file));
            }
            const results = await Promise.all(promises);
            for (let i = 0; i < results.length; i += 1) {
              if (results[i]) {
                files.push(results[i]);
              }
            }
          }
        }

        // save oss url
        if (weeklyFile || files.length > 0) {
          await saveBookingList({
            batch_number,
            week_resources: weeklyFile,
            org_content: files,
            space_id: stadium.value?.space_id,
          });

          isLoading.value = false;
          tipVisible.value = true;
        }
      } catch (error: any) {
        isLoading.value = false;
        // Modal.error({
        //   title: '出错了',
        //   content: error.errormsg,
        // });
      }
    }
  };

  // watch
  const sqbOption = ref({});
  watch(
    () => stadium.value,
    () => {
      sqbOption.value = { describe: `订场[${stadium.value?.space_name}]`, serviceType: 2, isEqual: false };
    },
    { immediate: true }
  );
</script>

<style lang="less" scoped>
  .loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #1d212999;
    opacity: 0.6;
    z-index: 998;
  }

  .loading-box {
    position: fixed;
    top: 50%;
    left: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 999;

    .loader {
      display: inline-grid;
      width: 80px;
      aspect-ratio: 1;
    }
    .loader:before,
    .loader:after {
      content: '';
      grid-area: 1/1;
      border-radius: 50%;
      animation: l3-0 2s alternate infinite ease-in-out;
    }
    .loader:before {
      margin: 25%;
      background: repeating-conic-gradient(#ff2351 0 60deg, pink 0 120deg);
      translate: 0 50%;
      rotate: -150deg;
    }
    .loader:after {
      padding: 10%;
      margin: -10%;
      background: repeating-conic-gradient(pink 0 30deg, #ff2351 0 60deg);
      mask: linear-gradient(#0000 50%, #000 0) content-box exclude, linear-gradient(#0000 50%, #000 0);
      rotate: -75deg;
      animation-name: l3-1;
    }
    @keyframes l3-0 {
      to {
        rotate: 150deg;
      }
    }
    @keyframes l3-1 {
      to {
        rotate: 75deg;
      }
    }
  }
</style>
