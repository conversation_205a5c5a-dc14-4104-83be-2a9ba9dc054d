<template>
  <div class="base-box">
    <Breadcrumb />

    <a-card v-if="isBrandSite" class="general-card">
      <a-row style="margin-bottom: 16px">
        <a-col :flex="1">
          <a-tabs :default-active-key="1" :active-key="tabSequence" @change="handleTabChange">
            <a-tab-pane :key="1" title="商品配置">
              <product-setting :modify-authority="modifyAuthority" :remove-authority="removeAuthority" />
            </a-tab-pane>
            <a-tab-pane v-if="storeAuthority" :key="2" title="门店ID配置">
              <store-setting />
            </a-tab-pane>
          </a-tabs>
        </a-col>
      </a-row>
    </a-card>

    <a-card v-else class="general-card">
      <div class="empty-container">
        <a-empty description="您没有权限访问此页面" />
        <a-typography-text type="secondary" style="margin-top: 16px">请联系管理员获取相应权限</a-typography-text>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import Breadcrumb from '@/components/breadcrumb/index.vue';
  import { getModifyAuthority } from '@/api/marketing';
  import ProductSetting from './components/product-setting.vue';
  import StoreSetting from './components/store-setting.vue';

  defineOptions({
    name: 'GroupOnline',
  });

  const IS_BRAND_SITE = (window as any).IS_BRAND_SITE ?? false;
  const isBrandSite = ref(IS_BRAND_SITE);

  const tabSequence = ref(1);
  const handleTabChange = async (key: number | string) => {
    tabSequence.value = Number(key);
  };

  // fetch the authority about modify the product
  const modifyAuthority = ref(false);
  const removeAuthority = ref(false);
  const storeAuthority = ref(false);
  getModifyAuthority().then((res) => {
    modifyAuthority.value = res.data.value.save_auth;
    removeAuthority.value = res.data.value.del_auth;
    storeAuthority.value = res.data.value.third_update_auth;
  });
</script>

<style lang="less" scoped>
  .empty-container {
    height: 60vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>
