<template>
  <a-row>
    <a-col :flex="1">
      <a-form :model="searchParam" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" label-align="left">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="bus_id" label="门店">
              <BusSelectAdmin v-model="searchParam.bus_id" placeholder="请选择" allow-clear />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-col>

    <a-divider style="height: 32px" direction="vertical" />

    <a-col :flex="'86px'" style="text-align: right">
      <a-space direction="vertical" :size="18">
        <a-button type="primary" @click="handleSearch">
          <template #icon>
            <icon-search />
          </template>
          搜索
        </a-button>
      </a-space>
    </a-col>
  </a-row>

  <a-divider style="margin-top: 0" />

  <a-row style="margin-bottom: 16px">
    <a-col :span="24" style="display: flex; align-items: center; justify-content: flex-end">
      <ExportExcel>
        <template #default="{ handleExport }">
          <a-button :loading="isLoading" @click="handleClickExport(handleExport)">导出</a-button>
        </template>
      </ExportExcel>
    </a-col>
  </a-row>

  <a-divider style="margin-top: 0" />

  <a-table v-bind="tableProps" v-on="tableEvent">
    <template #columns>
      <a-table-column title="门店" data-index="bus_name" />
      <a-table-column title="美团 ID" data-index="meituan_shop_id" ellipsis tooltip>
        <template #cell="{ record }">
          <a-link v-if="record.meituan_shop_id" color="blue">{{ record.meituan_shop_id }}</a-link>
          <span v-else>未设置</span>
        </template>
      </a-table-column>
      <a-table-column title="大众点评 ID" data-index="dianping_shop_id" ellipsis tooltip>
        <template #cell="{ record }">
          <a-link v-if="record.dianping_shop_id" color="blue">{{ record.dianping_shop_id }}</a-link>
          <span v-else>未设置</span>
        </template>
      </a-table-column>
      <a-table-column title="抖音门店 ID" data-index="douyin_shop_id" ellipsis tooltip>
        <template #cell="{ record }">
          <a-link v-if="record.douyin_shop_id" color="blue">{{ record.douyin_shop_id }}</a-link>
          <span v-else>未设置</span>
        </template>
      </a-table-column>
      <a-table-column title="抖音商家 ID" data-index="douyin_account_id" ellipsis tooltip>
        <template #cell="{ record }">
          <a-link v-if="record.douyin_account_id" color="blue">{{ record.douyin_account_id }}</a-link>
          <span v-else>未设置</span>
        </template>
      </a-table-column>
      <a-table-column title="操作">
        <template #cell="{ record }">
          <a-button
            type="text"
            style="color: #03b6ff"
            @click="
              () => {
                openEditModal(record);
              }
            ">
            编辑
          </a-button>
        </template>
      </a-table-column>
    </template>
  </a-table>

  <!-- Add this modal dialog at the end of your template section, just before </template> -->
  <a-modal v-model:visible="modalVisible" title="编辑" width="600px" @cancel="closeModal">
    <a-form :model="editForm" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" label-align="right">
      <a-form-item field="meituan_shop_id" label="美团ID">
        <a-input v-model="editForm.meituan_shop_id" placeholder="请输入美团ID" />
      </a-form-item>
      <a-form-item field="dianping_shop_id" label="大众点评ID">
        <a-input v-model="editForm.dianping_shop_id" placeholder="请输入大众点评ID" />
      </a-form-item>
      <a-form-item field="douyin_shop_id" label="抖音ID">
        <a-input v-model="editForm.douyin_shop_id" placeholder="请输入抖音门店ID" />
      </a-form-item>
      <a-form-item field="douyin_account_id" label="抖音商家ID">
        <a-input v-model="editForm.douyin_account_id" placeholder="请输入抖音商家ID" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="closeModal">取消</a-button>
      <a-button type="primary" :loading="saveLoading" @click="saveEdit">保存</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getStoreSettingList, updateStoreSetting } from '@/api/marketing';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import useTableProps from '@/hooks/table-props';
  import ExportExcel from '@/components/exportExcel.vue';

  defineOptions({
    name: 'ProductSetting',
  });

  const { isLoading, tableProps, dataPath, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getStoreSettingList);

  // 设置接口返回的数据映射关系
  dataPath.value = {
    list: 'list',
    count: 'count',
  };

  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: '',
  });

  // 导出
  const handleClickExport = (cb: any) => {
    loadTableList(true).then((list) => {
      cb({
        filename: '门店ID配置',
        columns: [
          {
            title: '门店',
            dataIndex: 'bus_name',
          },
          {
            title: '美团 ID',
            dataIndex: 'meituan_shop_id',
          },
          {
            title: '大众点评 ID',
            dataIndex: 'dianping_shop_id',
          },
          {
            title: '抖音门店 ID',
            dataIndex: 'douyin_shop_id',
          },
          {
            title: '抖音商家 ID',
            dataIndex: 'douyin_account_id',
          },
        ],
        data: list,
      });
    });
  };

  // Add these variables and functions to your script section
  // 编辑弹窗相关
  const modalVisible = ref(false);
  const saveLoading = ref(false);
  const editForm = ref({
    bus_id: '',
    meituan_shop_id: '',
    dianping_shop_id: '',
    douyin_shop_id: '',
    douyin_account_id: '',
  });

  const openEditModal = (record: any) => {
    editForm.value = {
      bus_id: record.bus_id,
      meituan_shop_id: record.meituan_shop_id || '',
      dianping_shop_id: record.dianping_shop_id || '',
      douyin_shop_id: record.douyin_shop_id || '',
      douyin_account_id: record.douyin_account_id || '',
    };
    modalVisible.value = true;
  };

  const closeModal = () => {
    modalVisible.value = false;
  };

  const saveEdit = () => {
    saveLoading.value = true;
    // 这里添加保存逻辑，需要调用API
    updateStoreSetting(editForm.value)
      .then(() => {
        saveLoading.value = false;
        modalVisible.value = false;
        Message.success('保存成功');
        loadTableList(); // 重新加载表格数据
      })
      .catch(() => {
        saveLoading.value = false;
      });
  };

  loadTableList();

  onActivated(() => {
    // 加载表格数据
    loadTableList();
  });
</script>

<style lang="less" scoped></style>
