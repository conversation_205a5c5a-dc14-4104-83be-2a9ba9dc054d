<template>
  <a-row>
    <a-col :flex="1">
      <a-form :model="searchParam" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" label-align="left">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="bus_id" label="门店">
              <BusSelectAdmin v-model="searchParam.bus_id" placeholder="请选择" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="rule_type" label="类型">
              <a-select v-model="searchParam.rule_type" placeholder="请选择" style="width: 100%" allow-clear>
                <a-option v-for="item in MEIPING_EXCHANGE_TYPE" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="name" label="商品">
              <a-input v-model="searchParam.name" placeholder="请输入" allow-clear @press-enter="handleSearch" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-col>

    <a-divider style="height: 32px" direction="vertical" />

    <a-col :flex="'86px'" style="text-align: right">
      <a-space direction="vertical" :size="18">
        <a-button type="primary" @click="handleSearch">
          <template #icon>
            <icon-search />
          </template>
          搜索
        </a-button>
      </a-space>
    </a-col>
  </a-row>

  <a-divider style="margin-top: 0" />

  <a-row style="margin-bottom: 16px">
    <a-col :span="12">
      <a-button v-if="props.modifyAuthority" type="primary" @click="$router.push('/admin/sale-add')">新增关联</a-button>
      <a-button
        v-if="props.removeAuthority"
        style="margin-left: 16px"
        :disabled="selectedIds.length === 0"
        @click="handleBatchDelete">
        批量删除
      </a-button>
      <!-- <a-tooltip v-if="selectedIds.length > 0" :content="`已选择的id：${selectedIds.join(', ')}`">
        <span style="margin-left: 16px">已选择{{ selectedIds.length }}个</span>
      </a-tooltip> -->
      <span v-if="selectedIds.length > 0" style="margin-left: 16px">已选择{{ selectedIds.length }}个</span>
    </a-col>
    <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
      <ExportExcel>
        <template #default="{ handleExport }">
          <a-button :loading="isLoading" @click="handleClickExport(handleExport)">导出</a-button>
        </template>
      </ExportExcel>
    </a-col>
  </a-row>

  <a-divider style="margin-top: 0" />

  <a-table v-bind="tableProps" v-model:selectedKeys="selectedIds" v-on="tableEvent">
    <template #columns>
      <a-table-column title="类型" data-index="rule_type">
        <template #cell="{ record }">
          {{ MEIPING_EXCHANGE_TYPE.find((item) => item.value == record.rule_type)?.label }}
        </template>
      </a-table-column>
      <a-table-column title="商品" data-index="name" />
      <a-table-column title="门店" data-index="bus_name" />
      <a-table-column title="美团" data-index="is_show_meituan" ellipsis tooltip>
        <template #cell="{ record }">
          <a-link v-if="record.is_show_meituan != 0" type="primary">关联 ID: {{ record.meituan_deal_id }}</a-link>
          <span v-else>未开通</span>
        </template>
      </a-table-column>
      <a-table-column title="大众点评" data-index="is_show_dianping" ellipsis tooltip>
        <template #cell="{ record }">
          <a-link v-if="record.is_show_dianping != 0" color="blue">关联 ID: {{ record.dianping_deal_id }}</a-link>
          <span v-else>未开通</span>
        </template>
      </a-table-column>
      <a-table-column title="抖音" data-index="is_show_tiktok" ellipsis tooltip>
        <template #cell="{ record }">
          <a-link v-if="record.is_show_tiktok != 0" color="blue">关联 ID: {{ record.tiktok_deal_id }}</a-link>
          <span v-else>未开通</span>
        </template>
      </a-table-column>
      <a-table-column v-if="props.modifyAuthority || props.removeAuthority" title="操作" :width="160">
        <template #cell="{ record }">
          <a-button
            v-if="props.modifyAuthority"
            type="text"
            style="color: #03b6ff"
            @click="
              () => {
                $router.push('/admin/sale-add/' + record.id);
              }
            ">
            编辑
          </a-button>
          <a-button
            v-if="props.removeAuthority"
            type="text"
            @click="
              () => {
                handleDelete(record.id, record.name);
              }
            ">
            删除
          </a-button>
        </template>
      </a-table-column>
    </template>
  </a-table>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { getProductSettingList, removeProductSetting } from '@/api/marketing';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import useTableProps from '@/hooks/table-props';
  import ExportExcel from '@/components/exportExcel.vue';
  import { MEIPING_EXCHANGE_TYPE } from '@/store/constants';

  defineOptions({
    name: 'ProductSetting',
  });

  const props = defineProps({
    modifyAuthority: {
      type: Boolean,
      default: false,
    },
    removeAuthority: {
      type: Boolean,
      default: false,
    },
  });

  const { isLoading, tableProps, dataPath, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getProductSettingList);

  // 表格多选
  tableProps.value['row-selection'] = {
    type: 'checkbox',
    showCheckedAll: true,
  };

  // 设置接口返回的数据映射关系
  dataPath.value = {
    list: 'list',
    count: 'count',
  };

  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: '',
    rule_type: '',
    name: '',
  });

  // 删除
  const handleDelete = (id: number, name: string) => {
    const ids = [id];
    Modal.confirm({
      title: '提示',
      content: `确认删除 "${name}" 吗？`,
      onOk: () => {
        removeProductSetting({ ids }).then(() => {
          Message.success('删除成功');
          loadTableList();
        });
      },
    });
  };

  // 批量删除
  const selectedIds = ref([]);
  const handleBatchDelete = () => {
    if (selectedIds.value.length === 0) {
      Message.warning('请选择要删除的记录');
      return;
    }
    Modal.confirm({
      title: '提示',
      content: `确认删除 ${selectedIds.value.length} 条商品配置吗？`,
      onOk: () => {
        removeProductSetting({ ids: selectedIds.value }).then(() => {
          Message.success('删除成功');
          loadTableList();
        });
      },
    });
  };

  // 导出
  const handleClickExport = (cb: any) => {
    loadTableList(true).then((list) => {
      const data = list.map((item: any) => ({
        rule_type: MEIPING_EXCHANGE_TYPE.find((option) => option.value === String(item.rule_type))?.label || '',
        name: item.name || '',
        bus_name: item.bus_name || '',
        meituan: Number(item.is_show_meituan) !== 0 ? `关联 ID: ${item.meituan_deal_id}` : '未开通',
        dianping: Number(item.is_show_dianping) !== 0 ? `关联 ID: ${item.dianping_deal_id}` : '未开通',
        tiktok: Number(item.is_show_tiktok) !== 0 ? `关联 ID: ${item.tiktok_deal_id}` : '未开通',
      }));
      cb({
        filename: '商品配置',
        columns: [
          {
            title: '类型',
            dataIndex: 'rule_type',
          },
          {
            title: '商品',
            dataIndex: 'name',
          },
          {
            title: '门店',
            dataIndex: 'bus_name',
          },
          {
            title: '美团',
            dataIndex: 'meituan',
          },
          {
            title: '大众点评',
            dataIndex: 'dianping',
          },
          {
            title: '抖音',
            dataIndex: 'tiktok',
          },
        ],
        data,
      });
    });
  };

  onActivated(() => {
    // 加载表格数据
    loadTableList();
  });
</script>

<style lang="less" scoped></style>
