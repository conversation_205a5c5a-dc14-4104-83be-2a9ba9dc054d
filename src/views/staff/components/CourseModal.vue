<template>
  <a-modal
    v-model:visible="modalVisible"
    title="课程设置"
    :width="720"
    @ok="handleCourseConfirm"
    @cancel="handleCourseCancel">
    <div class="chose-panel">
      <a-space wrap>
        <span style="font-size: 12px">已选:</span>
        <a-tag
          v-for="card in checkedCardUnionList"
          :key="card.card_id"
          size="small"
          closable
          @close="handleCardRemove(card)">
          {{ String(card.card_name).substring(0, 10) }}
        </a-tag>
      </a-space>
    </div>
    <a-tabs
      v-if="cardGroup.length && cardList.length"
      v-model:active-key="groupKey"
      position="left"
      @tab-click="handleTabClick">
      <a-tab-pane v-for="group in cardGroup" :key="'group_' + group.id" :title="group.title">
        <div class="tab-box">
          <div>
            <a-checkbox
              v-model="allCardChecked"
              :indeterminate="indeterminate"
              @change="handleAllCardChecked(group.id)">
              全选
            </a-checkbox>
          </div>
          <a-checkbox-group v-model="checkedCardIdList" @change="handleCardChecked(group.id)">
            <a-checkbox
              v-for="card in cardList.filter((item) => item.group_id === group.id)"
              :key="'card_' + card.card_id"
              :value="card.card_id">
              {{ card.card_name }}
            </a-checkbox>
          </a-checkbox-group>
        </div>
      </a-tab-pane>
    </a-tabs>
    <a-skeleton v-else animation>
      <a-space direction="vertical" :style="{ width: '100%' }" size="large">
        <a-skeleton-line :rows="2" />
        <a-skeleton-shape />
        <a-skeleton-line :rows="4" />
        <a-skeleton-shape />
        <a-skeleton-line :rows="2" />
      </a-space>
    </a-skeleton>
  </a-modal>
</template>

<script setup>
  import { useBusInfoStore } from '@/store';
  import { getCardGroup, getCardList } from '@/api/staff';

  const busInfo = useBusInfoStore();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    isSwimCoach: {
      type: Boolean,
      required: true,
    },
    allowedCourses: {
      type: Array,
      default: () => [],
    },
  });

  const emit = defineEmits(['update:visible', 'confirm']);

  const modalVisible = ref(props.visible);

  const groupKey = ref('');
  const cardGroup = ref([]);
  const cardList = ref([]);
  const allCardChecked = ref(false);
  const indeterminate = ref(false);
  const checkedCardIdList = ref([]);
  const checkedCardIdUnionList = ref([]);
  const checkedCardUnionList = computed(() => {
    return cardList.value.filter((card) => checkedCardIdUnionList.value.includes(card.card_id));
  });

  const handleAllCardChecked = (groupId) => {
    indeterminate.value = false;
    const currentGroupList = cardList.value.filter((item) => item.group_id === groupId).map((item) => item.card_id);

    if (allCardChecked.value) {
      checkedCardIdList.value = currentGroupList;

      // push the checked card to checkedCardIdUnionList
      const listSet = new Set(checkedCardIdList.value);
      const unionSet = new Set(checkedCardIdUnionList.value);
      const newUnionSet = unionSet.union(listSet);
      checkedCardIdUnionList.value = Array.from(newUnionSet);
    } else {
      checkedCardIdList.value = [];

      // remove the checked card from checkedCardIdUnionList
      const listSet = new Set(currentGroupList);
      const unionSet = new Set(checkedCardIdUnionList.value);
      const newUnionSet = unionSet.difference(listSet);
      checkedCardIdUnionList.value = Array.from(newUnionSet);
    }
  };
  const handleCardChecked = (groupId) => {
    const list = cardList.value.filter((item) => item.group_id === groupId);
    if (checkedCardIdList.value.length === 0) {
      allCardChecked.value = false;
      indeterminate.value = false;
    } else if (list.length === checkedCardIdList.value.length) {
      allCardChecked.value = true;
      indeterminate.value = false;
    } else {
      allCardChecked.value = false;
      indeterminate.value = true;
    }

    // checked the checkedCardIdUnionList
    const allListSet = new Set(list.map((item) => item.card_id));
    const checkedListSet = new Set(checkedCardIdList.value);
    const unionSet = new Set(checkedCardIdUnionList.value);
    let newUnionSet = unionSet.difference(allListSet);
    newUnionSet = newUnionSet.union(checkedListSet);
    checkedCardIdUnionList.value = Array.from(newUnionSet);
  };

  const handleTabClick = () => {
    checkedCardIdList.value = [];
    allCardChecked.value = false;
    indeterminate.value = false;

    // set the checkedCardIdList from checkedCardIdUnionList
    const groupId = groupKey.value.split('_')[1];
    if (groupId !== undefined && checkedCardIdUnionList.value.length > 0) {
      const list = cardList.value.filter((item) => item.group_id === groupId).map((item) => item.card_id);
      const listSet = new Set(list);
      const unionSet = new Set(checkedCardIdUnionList.value);
      const intersection = unionSet.intersection(listSet);

      checkedCardIdList.value = Array.from(intersection);
      handleCardChecked(groupId);
    }
  };

  const handleCardRemove = (card) => {
    checkedCardIdList.value = checkedCardIdList.value.filter((item) => item !== card.card_id);
    checkedCardIdUnionList.value = checkedCardIdUnionList.value.filter((id) => id !== card.card_id);

    handleCardChecked(card.group_id);
  };

  const handleCourseConfirm = () => {
    emit('confirm', checkedCardIdUnionList.value);
    emit('update:visible', false);
  };
  const handleCourseCancel = () => {
    checkedCardIdUnionList.value = [...props.allowedCourses];

    const groupId = groupKey.value.split('_')[1];
    const list = cardList.value.filter((item) => item.group_id === groupId).map((item) => item.card_id);
    const listSet = new Set(list);
    const unionSet = new Set(checkedCardIdUnionList.value);
    const newUnionSet = unionSet.intersection(listSet);
    checkedCardIdList.value = Array.from(newUnionSet);

    handleCardChecked(groupId);
    emit('update:visible', false);
  };

  // created
  const loadCardGroup = () => {
    return getCardGroup({
      bus_id: busInfo.bus_id,
      type: props.isSwimCoach ? 3 : '',
    }).then((res) => {
      cardGroup.value = res.data.value.list;
      return res;
    });
  };
  const loadCardList = () => {
    return getCardList({
      type: props.isSwimCoach ? 3 : '',
    }).then((res) => {
      cardList.value = res.data.value;
      return res;
    });
  };

  watch(
    () => props.visible,
    (val) => {
      if (val) {
        loadCardGroup();
        loadCardList();
        modalVisible.value = true;
      } else {
        modalVisible.value = false;
      }
    },
    { immediate: true }
  );

  let initFlag = true;
  watch(
    () => props.allowedCourses,
    (val) => {
      checkedCardIdUnionList.value = [...val];
      if (initFlag && val?.length) {
        loadCardGroup();
        loadCardList();
        initFlag = false;
      }
    },
    { immediate: true }
  );

  defineExpose({
    checkedCardUnionList,
    handleCardRemove,
  });
</script>

<style lang="less" scoped></style>
