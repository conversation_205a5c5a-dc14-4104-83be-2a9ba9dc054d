<template>
  <div>
    <a-space wrap>
      <a-button
        v-for="item in list"
        :key="item.id"
        type="dashed"
        size="mini"
        :status="item.checked ? 'success' : 'normal'"
        @click="handleClick(item)"
        @dblclick="handleDoubleClick(item)">
        <template #icon>
          <icon-subscribed v-if="item.checked" />
          <icon-subscribe v-else />
        </template>
        {{ item.name }}
      </a-button>
      <a-button v-if="!showInput" size="mini" @click="handleAdd">
        <template #icon>
          <icon-plus style="color: red" />
        </template>
      </a-button>
      <div v-else>
        <a-space>
          <a-input ref="inputRef" v-model="inputValue" size="mini" @press-enter="handleAddSubmit" />
          <a-button size="mini" @click="handleAddSubmit">
            <template #icon>
              <icon-check />
            </template>
          </a-button>
          <a-button size="mini" @click="showInput = false">
            <template #icon>
              <icon-undo />
            </template>
          </a-button>
        </a-space>
      </div>
      <a-button v-if="checkedIds.length && !showInput" size="mini" @click="handleRemove">
        <template #icon>
          <icon-delete />
        </template>
      </a-button>
    </a-space>
  </div>
</template>

<script setup>
  import { Message, Modal } from '@arco-design/web-vue';

  const props = defineProps({
    list: {
      type: Array,
      default: () => [],
    },
    allowEdit: {
      type: Boolean,
      default: true,
    },
  });
  const emit = defineEmits(['update:checkedIds', 'save', 'remove']);

  const inputRef = ref();
  const showInput = ref(false);
  let inputId = null;
  const inputValue = ref('');
  const checkedIds = computed(() => {
    return props.list.filter((item) => item.checked).map((item) => item.id);
  });

  const handleClick = (item) => {
    item.checked = !item.checked;
    emit('update:checkedIds', checkedIds.value);
  };
  const handleDoubleClick = (item) => {
    if (!props.allowEdit) {
      return;
    }

    item.checked = false;
    inputId = item.id;
    inputValue.value = item.name;
    showInput.value = true;
    nextTick(() => {
      inputRef.value.focus();
    });
  };
  const handleAdd = () => {
    inputId = null;
    inputValue.value = '';
    showInput.value = true;
    nextTick(() => {
      inputRef.value.focus();
    });
  };
  const handleAddSubmit = () => {
    const value = inputValue.value.trim();
    if (!value) {
      Message.error('请输入名称!');
      return;
    }

    showInput.value = false;
    emit('save', { id: inputId, name: value });
  };
  const handleRemove = () => {
    const removeIds = checkedIds.value;
    if (!removeIds.length) {
      Message.error('请选择要删除的标签');
      return;
    }

    const removeNames = props.list
      .filter((item) => removeIds.includes(item.id))
      .map((item) => item.name)
      .join('"、"');

    Modal.confirm({
      title: '提示',
      content: `确定要删除 "${removeNames}" 吗？`,
      onOk: () => {
        emit('remove', removeIds.join(','));
      },
    });
  };
</script>

<style lang="less" scoped></style>
