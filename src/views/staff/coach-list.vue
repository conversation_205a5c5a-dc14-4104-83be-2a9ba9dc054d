<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <div class="tree-table">
        <div class="tree-box">
          <a-button-group style="margin-bottom: 20px">
            <a-button :type="expandedKeys?.length ? 'primary' : 'secondary'" @click="toggleExpanded">
              {{ expandedKeys?.length ? '全部折叠' : '全部展开' }}
            </a-button>
          </a-button-group>
          <a-tree
            v-model:expanded-keys="expandedKeys"
            :selected-keys="checkedKeys"
            :data="treeData"
            show-line
            @select="onSelect">
            <template #switcher-icon="node">
              <icon-folder v-if="node.level === 1" />
              <icon-user-group v-if="node.level === 2" />
              <icon-user v-if="node.level === 3" />
            </template>
            <template #extra="node">
              <div class="tree-extra">
                <a-button v-if="node.level !== 3" size="mini" @click="handleNodeAdd(node)">
                  <template #icon>
                    <icon-plus />
                  </template>
                </a-button>
                <div v-else></div>
                <a-button v-if="node.level !== 1" size="mini" @click="handleNodeEdit(node)">
                  <template #icon>
                    <icon-edit />
                  </template>
                </a-button>
                <div v-else></div>
                <a-button v-if="node.level !== 1" size="mini" @click="handleNodeDelete(node)">
                  <template #icon>
                    <icon-delete />
                  </template>
                </a-button>
                <div v-else></div>
              </div>
            </template>
          </a-tree>
          <a-modal v-model:visible="visibleNode" :title="titleNode">
            <a-form ref="refNode" :model="formNode" :rules="ruleNode" auto-label-width>
              <a-form-item label="组名称" field="name">
                <a-input v-model="formNode.name" placeholder="请输入" allow-clear @press-enter="handleNodeSave" />
              </a-form-item>
            </a-form>
            <template #footer>
              <a-button @click="visibleNode = false">取消</a-button>
              <a-button type="primary" @click="handleNodeSave">确定</a-button>
            </template>
          </a-modal>
        </div>
        <a-divider direction="vertical" />
        <div class="table-box">
          <a-row>
            <a-col :flex="1">
              <a-form
                :model="searchParam"
                :label-col-props="{ span: 6 }"
                :wrapper-col-props="{ span: 18 }"
                label-align="left">
                <a-row :gutter="24">
                  <a-col :span="6">
                    <a-form-item field="title" label="姓名/电话">
                      <a-input
                        v-model="searchParam.param"
                        placeholder="请输入"
                        allow-clear
                        @press-enter="handleSearch" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item field="title" label="职务">
                      <a-select v-model="searchParam.position_id" placeholder="请选择" allow-clear>
                        <a-option v-for="item in positionList" :key="item.id" :value="item.id">
                          {{ item.name }}
                        </a-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item field="title" label="教练类型">
                      <a-select v-model="searchParam.coach_type" placeholder="请选择" allow-clear>
                        <a-option value="私教教练">私教教练</a-option>
                        <a-option value="操课教练">操课教练</a-option>
                        <a-option value="游泳教练">游泳教练</a-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item field="title" label="教练端">
                      <a-select v-model="searchParam.use_coach_manage" placeholder="请选择" allow-clear>
                        <a-option :value="1">已开通</a-option>
                        <a-option :value="0">未开通</a-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form>
            </a-col>
            <a-divider style="height: 32px" direction="vertical" />
            <a-col :flex="'86px'" style="text-align: right">
              <a-space direction="vertical" :size="18">
                <a-button type="primary" @click="handleSearch">
                  <template #icon>
                    <icon-search />
                  </template>
                  搜索
                </a-button>
              </a-space>
            </a-col>
          </a-row>
          <a-divider style="margin-top: 0" />
          <a-row style="margin-bottom: 16px">
            <a-col :span="12">
              <a-space direction="horizontal">
                <a-button type="primary" @click="$router.push('/staff/coach-save')">添加</a-button>
                <a-button @click="handleBatchSetCourse">批量设置允许教授课程</a-button>
              </a-space>
            </a-col>
            <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
              <ExportExcel>
                <template #default="{ handleExport }">
                  <a-button :loading="isLoading" @click="handleClickExport(handleExport)">导出</a-button>
                </template>
              </ExportExcel>
            </a-col>
          </a-row>

          <a-table
            v-bind="tableProps"
            v-model:selectedKeys="selectedKeys"
            row-key="id"
            :row-selection="{
              type: 'checkbox',
              showCheckedAll: true,
              onlyCurrent: false,
            }"
            v-on="tableEvent"
            @selection-change="handleSelectionChange">
            <template #columns>
              <a-table-column title="形象照" data-index="avatar">
                <template #cell="{ record }">
                  <a-image :width="30" class="avatar-zoom" :src="record.avatar" />
                </template>
              </a-table-column>
              <a-table-column title="头像" data-index="face_avatar">
                <template #cell="{ record }">
                  <a-image
                    :width="30"
                    class="avatar-zoom"
                    :src="record.face_avatar"
                    :preview="false"
                    @click="unbindFace(record)" />
                </template>
              </a-table-column>
              <a-table-column title="姓名" data-index="name">
                <template #cell="{ record }">
                  <a-space>
                    <div>{{ record.name }}</div>
                    <a-tooltip v-if="record.is_bindSfinger == 1" content="指静脉已录入">
                      <div class="bind-finger" @click="unbindFinger(record)" />
                    </a-tooltip>
                    <a-tooltip v-if="record.rfid" content="RFID 已绑定">
                      <div class="bind-rfid" @click="unbindRfid(record)" />
                    </a-tooltip>
                  </a-space>
                </template>
              </a-table-column>
              <a-table-column title="电话" data-index="phone" />
              <a-table-column title="职务" data-index="position" />
              <a-table-column title="教练类型" data-index="coach_type">
                <template #cell="{ record }">
                  <a-space>
                    <a-tag v-if="record.coach_type.includes('私')" color="blue">私</a-tag>
                    <a-tag v-if="record.coach_type.includes('泳')" color="gold">泳</a-tag>
                    <a-tag v-if="record.coach_type.includes('操')" color="green">操</a-tag>
                  </a-space>
                </template>
              </a-table-column>
              <a-table-column title="授课/跟进会员" data-index="member">
                <template #cell="{ record }">
                  <router-link style="color: #1890ff" :to="`/v2/member?class_coach_id=${record.id}&curMenu=search`">
                    {{ record.member }}
                  </router-link>
                  <span style="margin: 0 2px">/</span>
                  <router-link style="color: #1890ff" :to="`/v2/member?followup_coach_id=${record.id}&curMenu=search`">
                    {{ record.followup }}
                  </router-link>
                </template>
              </a-table-column>
              <a-table-column title="私教端" data-index="use_coach_manage">
                <template #cell="{ record }">
                  <a-space direction="horizontal">
                    <div v-if="record.use_coach_manage == 1" class="bind-wechat">
                      <div v-if="record.data_authority === '经理'" class="pin" style="color: green">
                        {{ record.data_authority }}
                      </div>
                      <div v-else-if="record.data_authority === '组长'" class="pin" style="color: blue">
                        {{ record.data_authority }}
                      </div>
                    </div>
                    <div v-else class="unbind-wechat">
                      <div v-if="record.data_authority === '经理'" class="pin" style="color: green">
                        {{ record.data_authority }}
                      </div>
                      <div v-else-if="record.data_authority === '组长'" class="pin" style="color: blue">
                        {{ record.data_authority }}
                      </div>
                    </div>
                  </a-space>
                </template>
              </a-table-column>
              <a-table-column title="教练介绍" data-index="id">
                <template #cell="{ record }">
                  <a-link @click="handleShowDetail(record)">详情</a-link>
                </template>
              </a-table-column>

              <a-table-column title="操作">
                <template #cell="{ record }">
                  <a-dropdown :popup-max-height="false" trigger="hover" @select="handleAction($event, record)">
                    <a-button>
                      操作
                      <icon-down style="margin-left: 8px" />
                    </a-button>
                    <template #content>
                      <a-doption value="edit">编辑</a-doption>
                      <a-doption value="remove">删除</a-doption>
                      <a-doption value="avatar">传头像</a-doption>
                      <a-doption value="bind">绑 RFID</a-doption>
                    </template>
                  </a-dropdown>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </div>
    </a-card>

    <a-modal
      v-model:visible="detailModal"
      :esc-to-close="false"
      :mask-closable="false"
      :footer="false"
      hide-title
      body-style="padding: 0">
      <div class="coach-detail">
        <a-card>
          <template #cover>
            <div>
              <img style="width: 100%" alt="avatar" :src="detailFormData?.avatar" />
            </div>
          </template>
          <a-card-meta>
            <template #title>
              <div class="detail-title">{{ detailFormData?.name }}</div>
            </template>
            <template #description>
              <a-form :model="detailFormData">
                <a-form-item label="职务">
                  {{ detailFormData?.position }}
                </a-form-item>
                <a-form-item label="擅长">
                  <a-tag v-for="tag in detailFormData?.specialty.split(',')" :key="tag" color="blue">{{ tag }}</a-tag>
                </a-form-item>
                <a-form-item label="课程">
                  {{ detailFormData?.courses_content }}
                </a-form-item>
                <a-form-item label="荣誉">
                  <div v-html="detailFormData?.aptitude"></div>
                </a-form-item>
              </a-form>
              <div class="rich-text" v-html="detailFormData?.other_intr"></div>
            </template>
          </a-card-meta>
        </a-card>
      </div>
      <div class="close-x">
        <icon-close-circle size="30" @click="detailModal = false" />
      </div>
    </a-modal>

    <a-modal v-model:visible="removeModal" title="删除教练">
      <a-collapse v-if="removeModal" :default-active-key="['remove']">
        <a-collapse-item key="remove" :header="`确定要删除教练 &quot;${removeFormData?.name}&quot; 吗？`">
          <a-space direction="vertical">
            <a-alert type="normal">
              该教练名下有上课会员
              <a-tag color="red">{{ removeFormData?.member }}</a-tag>
              名
            </a-alert>
            <a-alert type="normal">
              该教练名下有跟进会员
              <a-tag color="red">{{ removeFormData?.followup }}</a-tag>
              名
            </a-alert>
            <a-alert v-if="forceRemoveFlag" type="warning" :show-icon="false">
              请将会员指派给其他教练。选择“直接删除教练”后，这些会员会自动解除和该教练的跟进关系
            </a-alert>
          </a-space>
        </a-collapse-item>
      </a-collapse>
      <template v-if="forceRemoveFlag" #footer>
        <a-button @click="handleAssign">指派给其他教练</a-button>
        <a-button type="primary" @click="handleRemove($event, '1')">直接删除教练</a-button>
      </template>
      <template v-else #footer>
        <a-button @click="removeModal = false">取消</a-button>
        <a-button type="primary" @click="handleRemove">删除</a-button>
      </template>
    </a-modal>

    <a-modal v-model:visible="bindModal" title="RFID 设备绑定">
      <a-form ref="bindFormRef" :model="bindFormData" :rules="bindFormRules">
        <a-form-item label="RFID 编号" field="bind_data">
          <a-input v-model="bindFormData.bind_data" placeholder="请输入" allow-clear @press-enter="handleBind" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="bindModal = false">取消</a-button>
        <a-button type="primary" @click="handleBind">确定</a-button>
      </template>
    </a-modal>

    <course-modal
      ref="courseModalRef"
      v-model:visible="showCourseModal"
      :is-swim-coach="isSwimCoach"
      @confirm="handleCourseConfirm" />
    <img-upload
      v-model="bindFaceModal"
      :options="{ fixedNumber: [900, 900] }"
      :allow-template="false"
      :show-tips="true"
      :tips="{ widthHeight: '900X900', maxSize: '100kb', format: 'jpg、png' }"
      allow-camera
      @on-change="handleBindFace" />
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  import ExportExcel from '@/components/exportExcel.vue';
  import {
    getCoachList,
    removeCoach,
    getCoachGroup,
    getCoachPosition,
    addCoachGroup,
    updateCoachGroup,
    removeCoachGroup,
    unbind,
    bind,
    clearFingerPrint,
    setAllowedCourses,
  } from '@/api/staff';
  import useTableProps from '@/hooks/table-props';
  import ImgUpload from '@/components/form/img-upload.vue';
  import CourseModal from './components/CourseModal.vue';

  defineOptions({
    name: 'CoachList',
  });

  const router = useRouter();

  // variable
  const detailModal = ref(false);
  const detailFormData = ref();
  const showCourseModal = ref(false);
  const selectedKeys = ref([]);
  let selectedRows: any[] = [];
  const isSwimCoach = ref(false);
  const removeModal = ref(false);
  const removeFormData = ref();
  const bindModal = ref(false);
  const bindFormRef = ref();
  const bindFormData = ref({
    bind_type: 2,
    coach_id: '',
    bind_data: '',
  });
  const bindFormRules = {
    bind_data: [
      {
        required: true,
        message: '请输入 RFID 编号',
      },
    ],
  };
  const forceRemoveFlag = computed(() => {
    return Number(removeFormData.value?.member) || Number(removeFormData.value?.followup);
  });
  const bindFaceModal = ref(false);
  const bindFaceFormData = ref({
    bind_type: 1,
    coach_id: '',
    bind_data: '',
  });

  const { isLoading, tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getCoachList);

  setSearchParam({
    id: '',
    param: '',
    position_id: '',
    coach_type: '',
    use_coach_manage: '',
  });

  // table
  const unbindFace = (record: any) => {
    Modal.confirm({
      title: '解绑头像',
      content: `确定解绑 "${record.name}" 的人脸头像吗？`,
      onOk: async () => {
        const { response }: any = await unbind({
          bind_type: 1,
          coach_id: record.id,
        });
        if (response.value.errorcode === 0) {
          Message.success('解绑成功');
          loadTableList();
        }
      },
    });
  };
  const unbindFinger = (record: any) => {
    Modal.confirm({
      title: '清除指静脉',
      content: `确定清除 "${record.name}" 的指静脉信息吗？`,
      onOk: async () => {
        const { response }: any = await clearFingerPrint({
          user_id: record.id,
          type: record.coach_type ? 2 : 1,
        });
        if (response.value.errorcode === 0) {
          Message.success('清除成功');
          loadTableList();
        }
      },
    });
  };
  const unbindRfid = (record: any) => {
    Modal.confirm({
      title: '解绑RFID',
      content: `确定解绑 "${record.name}" 的 RFID 吗？`,
      onOk: async () => {
        const { response }: any = await unbind({
          bind_type: 2,
          coach_id: record.id,
        });
        if (response.value.errorcode === 0) {
          Message.success('解绑成功');
          loadTableList();
        }
      },
    });
  };
  const handleRemove = (event: any, type = '') => {
    removeCoach({ id: removeFormData.value.id, type }).then(() => {
      Message.success('删除成功');
      loadTableList();
    });
  };
  const handleAssign = () => {
    // this.$router.push({ path: '/member', query: { followup_coach_id: this.coachInfo.id, curMenu: 'search' }})
    router.push(`/v2/member?followup_coach_id=${removeFormData.value.id}&curMenu=search`);
  };
  const bindRfid = (record: any) => {
    bindFormData.value.coach_id = record.id;
    bindFormData.value.bind_data = record.rfid || '';
    bindModal.value = true;
  };
  const handleBind = async () => {
    const valid = await bindFormRef.value?.validate();
    if (!valid) {
      bindModal.value = false;
      const { response }: any = await bind(bindFormData.value);
      if (response.value.errorcode === 0) {
        Message.success('绑定成功');
        loadTableList();
      }
    }
  };
  const bindFace = (record: any) => {
    bindFaceFormData.value.coach_id = record.id;
    bindFaceFormData.value.bind_data = '';
    bindFaceModal.value = true;
  };
  const handleBindFace = async (path = '') => {
    bindFaceFormData.value.bind_data = path;
    const { response }: any = await bind(bindFaceFormData.value);
    if (response.value.errorcode === 0) {
      Message.success('绑定成功');
      loadTableList();
      bindFaceModal.value = false;
    }
  };
  const handleShowDetail = (record: any) => {
    detailFormData.value = record;
    detailModal.value = true;
  };
  const handleClickExport = (cb: any) => {
    loadTableList(true).then((list) => {
      list.forEach((item: any) => {
        item.member_label = `${item.member} / ${item.followup}`;
        item.use_coach_manage_label = Number(item.use_coach_manage) === 1 ? '已开通' : '未开通';
      });
      cb({
        filename: '教练列表',
        columns: [
          {
            title: '形象照',
            dataIndex: 'avatar',
          },
          {
            title: '头像',
            dataIndex: 'face_avatar',
          },
          {
            title: '姓名',
            dataIndex: 'name',
          },
          {
            title: '电话',
            dataIndex: 'phone',
          },
          {
            title: '职务',
            dataIndex: 'position',
          },
          {
            title: '教练类型',
            dataIndex: 'coach_type',
          },
          {
            title: '授课/跟进会员',
            dataIndex: 'member_label',
          },
          {
            title: '私教端',
            dataIndex: 'use_coach_manage_label',
          },
        ],
        data: list,
      });
    });
  };
  const handleSelectionChange = (keys: any) => {
    const pageSet = new Set(tableProps.value.data.map((item: any) => item.id));
    const selectedPageSet = new Set(keys.filter((id: string) => pageSet.has(id)));

    // preview: keys remove pageSet
    const preview = selectedRows.filter((item) => !pageSet.has(item.id));
    // current: selectedPageSet
    const current = tableProps.value.data.filter((item: any) => selectedPageSet.has(item.id));
    // union preview and current
    selectedRows = preview.concat(current);
  };

  const handleBatchSetCourse = () => {
    if (!selectedKeys.value.length) {
      Message.warning('请先选择教练');
      return;
    }
    // 批量设置不能同时选中泳教和私教
    try {
      let isSwimming = false;
      let isPrivate = false;
      selectedRows.forEach((item) => {
        if (item.coach_type.includes('泳')) {
          isSwimming = true;
        } else if (item.coach_type.includes('私')) {
          isPrivate = true;
        }
        if (isSwimming && isPrivate) {
          throw new Error('不能同时选中泳教和私教');
        } else {
          isSwimCoach.value = isSwimming;
        }
      });
    } catch (error: any) {
      Message.warning(error.message);
      return;
    }

    Modal.confirm({
      title: '提示',
      content: '确定要批量设置教练的课程吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        showCourseModal.value = true;
      },
    });
  };
  const handleCourseConfirm = async (list: any) => {
    const { response }: any = await setAllowedCourses({
      is_swim: isSwimCoach.value ? 1 : 0,
      coach_ids: selectedKeys.value,
      permitted_class: list,
    });
    if (response.value.errorcode === 0) {
      Message.success('设置成功');
      selectedKeys.value = [];
      showCourseModal.value = false;
      loadTableList();
    }
  };
  const handleAction = (key: any, record: any) => {
    if (key === 'edit') {
      router.push(`/staff/coach-save/${record.id}`);
    } else if (key === 'remove') {
      removeModal.value = true;
      removeFormData.value = record;
    } else if (key === 'avatar') {
      bindFace(record);
    } else if (key === 'bind') {
      bindRfid(record);
    }
  };

  // position
  const positionList = ref<any>([]);
  const loadPositionList = () => {
    getCoachPosition().then((res: any) => {
      positionList.value = res.data.value;
    });
  };

  // tree
  interface Node {
    id: string;
    pid: string;
    key: string;
    title: string;
    level: number;
    children?: Node[];
  }
  const checkedKeys = ref([]);
  let allExpandedKeys: string[] = [];
  const expandedKeys = ref<any>([]);
  const treeData = ref<Node[]>([]);
  const toggleExpanded = () => {
    expandedKeys.value = expandedKeys?.value.length ? [] : allExpandedKeys;
  };
  const onSelect = (newCheckedKeys: any, event: any) => {
    searchParam.id = event.node.key;
    checkedKeys.value = newCheckedKeys;
    loadTableList();
  };
  const arrayToTree = (data: Node[]): Node[] => {
    const map = new Map<string, Node>();
    const roots: Node[] = [];

    // Step 1: Initialize each node with an empty `children` array and add to map
    data.forEach((item) => {
      map.set(item.id, { ...item, children: [] });
    });

    // Step 2: Iterate over the array and build the tree
    data.forEach((item) => {
      const node = map.get(item.id)!;

      if (item.pid === '0') {
        // If the node has no parent, it's a root node
        roots.push(node);
      } else {
        // If it has a parent, find the parent and add this node to its `children`
        const parent = map.get(item.pid);
        if (parent) {
          parent.children?.push(node);
        }
      }
    });

    return roots;
  };
  const loadTree = () => {
    getCoachGroup().then((res) => {
      const list = res.data.value.map((item: any) => {
        return {
          id: item.id,
          pid: item.pid,
          key: item.id,
          title: item.name,
          level: item.group_level,
        };
      });
      expandedKeys.value = list.filter((item: any) => item.level !== 3).map((item: any) => item.key);
      allExpandedKeys = list.map((item: any) => item.key);
      treeData.value = arrayToTree(list);

      if (checkedKeys.value.length === 0) {
        checkedKeys.value = list.filter((item: any) => item.pid === '0').map((item: any) => item.key);
      }
    });
  };

  // tree node modify by a modal
  const visibleNode = ref(false);
  const titleNode = ref('');
  const refNode = ref();
  const formNode = ref<any>({
    id: '',
    name: '',
    pid: null,
    group_level: null,
    action: '',
  });
  const ruleNode = ref({
    name: [{ required: true, message: '请输入组别名称', trigger: 'blur' }],
  });
  const handleNodeAdd = (node: Node) => {
    titleNode.value = '组别创建';
    formNode.value.id = node.id;
    formNode.value.name = '';
    formNode.value.pid = node.pid;
    formNode.value.group_level = node.level;
    formNode.value.action = 'add';
    visibleNode.value = true;

    nextTick(() => {
      refNode.value?.resetFields();
    });
  };
  const handleNodeEdit = (node: Node) => {
    titleNode.value = '组别编辑';
    formNode.value.id = node.id;
    formNode.value.name = node.title;
    formNode.value.pid = null;
    formNode.value.group_level = null;
    formNode.value.action = 'update';
    visibleNode.value = true;
  };
  const handleNodeDelete = (node: Node) => {
    Modal.confirm({
      title: '组别删除',
      content: `确认删除 "${node.title}" 及其下属组别信息吗?`,
      okText: '删除',
      cancelText: '取消',
      onOk: async () => {
        const { response }: any = await removeCoachGroup({
          id: node.id,
          group_level: node.level,
        });
        if (response.value.errorcode === 0) {
          Message.success('删除成功');
          loadTree();
        }
      },
    });
  };
  const handleNodeSave = async () => {
    const valid = await refNode.value?.validate();
    if (valid) {
      return;
    }

    if (formNode.value.action === 'update') {
      const { response }: any = await updateCoachGroup(formNode.value);
      if (response.value.errorcode === 0) {
        Message.success('修改成功');
        visibleNode.value = false;
        loadTree();
      } else {
        Message.error(response.value.errormsg);
      }
    } else {
      const { response }: any = await addCoachGroup(formNode.value);
      if (response.value.errorcode === 0) {
        Message.success('创建成功');
        visibleNode.value = false;
        loadTree();
      } else {
        Message.error(response.value.errormsg);
      }
    }
  };

  // created
  loadPositionList();
  loadTree();
  loadTableList();
</script>

<style lang="less" scoped>
  .bind-icon {
    width: 14px;
    height: 14px;
    background-repeat: no-repeat;
    background-size: cover;
    cursor: pointer;
  }

  .bind-mobile {
    width: 30px;
    height: 30px;
    background-image: url(data:image/png;base64,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);
    background-repeat: no-repeat;
    background-size: cover;

    .pin {
      position: absolute;
      margin-top: -10px;
      margin-left: 20px;
      font-size: 10px;
    }
  }

  .coach-detail {
    background-color: #f5f7f9;
    background-image: url(https://imagecdn.rocketbird.cn/minprogram/member/image/coach-bg.png);
    background-position: center top;
    background-repeat: no-repeat;
    background-size: contain;
    padding: 30px 40px;

    .detail-title {
      font-size: 16px;
      font-weight: bold;
      color: #313131;
      text-align: center;
      text-decoration: underline;
      text-decoration-color: #ca2e53;
      text-decoration-thickness: 4px;
      text-underline-offset: 12px;
      line-height: 50px;
      margin-bottom: 20px;
    }
  }

  .rich-text {
    font-size: 12px;
    color: #898989;
    line-height: 1.4;
    padding-top: 16px;
    padding-bottom: 25px;

    :deep(img) {
      max-width: 100%;
    }
  }

  .close-x {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    color: darkgray;
    transition: all 0.4s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-x:hover {
    color: lightgray;
    transform: rotate(90deg) scale(1.3);
  }

  .tree-table {
    display: flex;

    .tree-box {
      width: max(360px, 15%);
    }

    .table-box {
      width: 100%;

      .avatar-zoom:hover {
        transition: all 0.7s ease;
        transform: scale(2.7);
        cursor: pointer;
      }

      .bind-finger {
        .bind-icon;
        background-image: url(../../assets/img/s_finger.png);
      }

      .bind-rfid {
        .bind-icon;
        background-image: url(../../assets/img/rfid.png);
      }

      .bind-wechat {
        .bind-mobile;
        background-position: -248px 2px;
      }

      .unbind-wechat {
        .bind-mobile;
        background-position: -290px 2px;
      }
    }
  }

  .tree-extra {
    margin-left: auto;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
  }
</style>
