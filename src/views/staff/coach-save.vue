<template>
  <div class="base-box">
    <Breadcrumb :items="breadcrumb" />

    <img-upload
      v-model="showAvatarModal"
      :options="{ fixedNumber: [100, 100] }"
      :allow-template="false"
      :show-tips="true"
      :tips="{ widthHeight: '100X100', maxSize: '100kb', format: 'jpg、png' }"
      @on-change="handleAvatarUpload" />
    <img-upload
      v-model="showPhotoModal"
      :options="{ fixedNumber: [660, 660] }"
      :allow-template="false"
      :show-tips="true"
      :tips="{ widthHeight: '660X660', maxSize: '100kb', format: 'jpg、png' }"
      :allow-camera="false"
      @on-change="handlePhotoUpload" />

    <a-card class="general-card">
      <a-form ref="formRef" :rules="formRules" :model="formData" class="general-form" auto-label-width>
        <a-form-item field="photo" label="形象照">
          <a-space direction="vertical">
            <a-image mode="widthFix" width="200" height="200" :src="formData.avatar" />
            <a-button type="outline" @click="showAvatarModal = true">选择图片</a-button>
          </a-space>
          <template #extra>
            <div class="tip-box">
              <p class="tip">上传图片建议尺寸: 100X100</p>
              <p class="tip">格式限制: jpg、png</p>
              <p class="tip">推荐大小: 小于100kb</p>
            </div>
          </template>
        </a-form-item>
        <a-form-item field="name" label="姓名">
          <a-input v-model="formData.name" />
        </a-form-item>
        <a-form-item field="gender" label="性别">
          <a-radio-group v-model="formData.sex">
            <a-radio :value="1">男</a-radio>
            <a-radio :value="2">女</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item field="phone" label="手机号">
          <a-input v-model.trim="formData.phone" />
        </a-form-item>
        <a-form-item field="group_id" label="所在组别">
          <a-select v-model="formData.group_id" allow-search>
            <a-option v-for="item in coachGroup" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="coach_type" label="教练类型">
          <a-checkbox-group v-model="formData.coach_type" @change="handleTypeChange">
            <a-checkbox value="私教教练" :disabled="isSwimCoach">私教教练</a-checkbox>
            <a-checkbox value="游泳教练" :disabled="isPrivateCoach">游泳教练</a-checkbox>
            <a-checkbox value="团操课教练">团操课教练</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item field="permitted_class" label="允许教授课程">
          <a-button type="outline" :disabled="loading" @click="showCourseModal = true">选择课程</a-button>
          <template #extra>
            <a-space v-if="checkedCardUnionList.length" wrap>
              <span style="margin-left: 10px">已选:</span>
              <a-tag
                v-for="card in checkedCardUnionList"
                :key="card.card_id"
                size="small"
                closable
                @close="handleCourseRemove(card)">
                {{ card.card_name }}
              </a-tag>
            </a-space>
          </template>
        </a-form-item>
        <!-- coach_type 选择了 私教教练 或者 游泳教练 时, 才显示 -->
        <a-form-item
          v-if="formData.coach_type.includes('私教教练') || formData.coach_type.includes('游泳教练')"
          label="单节付费课方案"
          field="pt_charge_plan_id">
          <template #label>
            <a-space>
              <div>单节付费课方案</div>
              <a-tooltip content="会员直接付费预约教练上课，不需提前购课，不同课程不同教练每次支付费用不同">
                <icon-question-circle />
              </a-tooltip>
            </a-space>
          </template>
          <a-select v-model="formData.pt_charge_plan_id" allow-clear allow-search>
            <a-option v-for="item in planList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="specialty_id" label="擅长">
          <tag-box
            v-model:checkedIds="formData.specialty_id"
            :list="coachSpecialty"
            :allow-edit="false"
            @save="handleSaveCoachSpecial"
            @remove="handleRemoveCoachSpecial" />
          <template #extra>
            <div>不能编辑, 只允许添加和删除</div>
          </template>
        </a-form-item>
        <a-form-item field="position_id" label="职务">
          <tag-box
            v-model:checkedIds="formData.position_id"
            :list="coachPosition"
            @save="handleSaveCoachPosition"
            @remove="handleRemoveCoachPosition" />
          <template #extra>
            <div>双击进行编辑</div>
          </template>
        </a-form-item>
        <a-form-item label="课程项目">
          <a-input v-model="formData.courses_content" placeholder="例如: 普拉提机械康复、动感单车" />
        </a-form-item>
        <a-form-item label="荣誉资质">
          <a-textarea
            v-model="formData.aptitude"
            placeholder="例如: 健美协会全能私人教练认证"
            :max-length="{ length: 500, errorOnly: true }"
            allow-clear
            show-word-limit
            :auto-size="{
              minRows: 4,
              maxRows: 8,
            }" />
        </a-form-item>
        <a-form-item label="其他介绍">
          <Editor v-model="formData.other_intr" style="width: 100%" />
        </a-form-item>
        <a-form-item label="向会员展示">
          <a-switch
            v-model="formData.display"
            :checked-value="1"
            :unchecked-value="0"
            checked-text="展示"
            unchecked-text="不展示" />
        </a-form-item>
        <a-form-item v-if="formData.display === 1" label="展示排位">
          <a-input-number v-model="formData.sort" :min="0" placeholder="数值越小, 会员端排位越靠前" />
        </a-form-item>
        <a-form-item label="教练端使用">
          <a-switch
            v-model="formData.use_coach_manage"
            :checked-value="1"
            :unchecked-value="0"
            checked-text="允许使用"
            unchecked-text="不允许使用" />
        </a-form-item>
        <a-form-item label="教练端权限">
          <a-select v-model="formData.data_authority" allow-search>
            <a-option v-for="item in authorityList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="formData.coach_type.includes('团操课教练')" label="团操教练端权限">
          <a-select v-model="formData.class_data_authority" allow-search>
            <a-option v-for="item in classAuthorityList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="photo" label="宣传照">
          <a-space direction="vertical">
            <a-image mode="widthFix" width="200" height="200" :src="formData.promotional_photo" />
            <a-button type="outline" @click="showPhotoModal = true">选择图片</a-button>
          </a-space>
          <template #extra>
            <div class="tip-box">
              <p class="tip">上传图片建议尺寸: 660X660</p>
              <p class="tip">格式限制: jpg、png</p>
              <p class="tip">推荐大小: 小于100kb</p>
            </div>
          </template>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button :loading="isLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <course-modal
      ref="courseModalRef"
      v-model:visible="showCourseModal"
      :is-swim-coach="isSwimCoach"
      :allowed-courses="formData.permitted_class"
      @confirm="handleCourseConfirm" />
  </div>
</template>

<script setup>
  import { Message } from '@arco-design/web-vue';
  import { useRouter, useRoute } from 'vue-router';
  import {
    getCoachGroup,
    getCoachSpecial,
    saveCoachSpecial,
    removeCoachSpecial,
    getCoachPosition,
    addCoachPosition,
    updateCoachPosition,
    removeCoachPosition,
    getCoach,
    addCoach,
    updateCoach,
  } from '@/api/staff';
  import ImgUpload from '@/components/form/img-upload.vue';
  import Editor from '@/components/form/editor.vue';
  import { getAllPlanList } from '@/api/one-time-pay';
  import TagBox from './components/TagBox.vue';
  import CourseModal from './components/CourseModal.vue';

  defineOptions({
    name: 'CoachSave',
  });

  const router = useRouter();
  const route = useRoute();

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
  });

  // form
  const isLoading = ref(false);

  const formRef = ref();
  const formData = ref({
    name: '',
    sex: 1,
    phone: '',
    coach_type: ['私教教练'],
    permitted_class: [],
    group_id: '',
    specialty_id: '',
    position_id: '',
    courses_content: '',
    aptitude: '',
    other_intr: '',
    avatar: '',
    display: 1,
    sort: null,
    use_coach_manage: 0,
    data_authority: 1,
    class_data_authority: 1,
    promotional_photo: '',
    pt_charge_plan_id: '',
  });
  const formRules = {
    name: {
      required: true,
      message: '请输入姓名',
    },
    phone: {
      required: true,
      message: '请输入手机号',
    },
    group_id: {
      required: true,
      message: '请选择所在组别',
    },
    coach_type: {
      required: true,
      message: '请选择教练类型',
    },
    permitted_class: {
      required: true,
      validator: (value, callback) => {
        if (Array.isArray(value) && value.length > 0) {
          callback();
        } else {
          callback('请选择允许教授的课程');
        }
      },
    },
    specialty_id: {
      required: true,
      validator: (value, callback) => {
        if (Array.isArray(value) && value.length > 0) {
          callback();
        } else {
          callback('请选择教练擅长');
        }
      },
    },
    position_id: {
      required: true,
      validator: (value, callback) => {
        if (Array.isArray(value) && value.length > 0) {
          callback();
        } else {
          callback('请选择教练职位');
        }
      },
    },
  };

  // avatar and photo modal
  const showAvatarModal = ref(false);
  const showPhotoModal = ref(false);
  const handleAvatarUpload = (path = '') => {
    formData.value.avatar = path;
  };
  const handlePhotoUpload = (path = '') => {
    formData.value.promotional_photo = path;
  };

  // group
  const coachGroup = ref([]);
  getCoachGroup().then((res) => {
    coachGroup.value = res.data.value;
  });

  // plan list
  const planList = ref([]);
  const pullAllPlanList = () => {
    let type = '';
    if (formData.coach_type.includes('私教教练') && formData.coach_type.includes('游泳教练')) {
      type = 0;
    } else if (formData.coach_type.includes('私教教练')) {
      type = 1;
    } else if (formData.coach_type.includes('游泳教练')) {
      type = 2;
    }
    return getAllPlanList({
      type,
    }).then((res) => {
      planList.value = res.data.value.list;
    });
  };

  // type
  const isPrivateCoach = computed(() => {
    return formData.value?.coach_type?.includes('私教教练');
  });
  const isSwimCoach = computed(() => {
    return formData.value?.coach_type?.includes('游泳教练');
  });
  const handleTypeChange = () => {
    if (!isPrivateCoach.value && !isSwimCoach.value) {
      formData.value.permitted_class = [];
    }
    pullAllPlanList();
  };

  // course
  const showCourseModal = ref(false);
  const courseModalRef = ref();
  const checkedCardUnionList = computed(() => {
    return courseModalRef.value?.checkedCardUnionList || [];
  });
  const handleCourseConfirm = (list) => {
    formData.value.permitted_class = [...list];
    formRef.value.validateField('permitted_class');
  };
  const handleCourseRemove = (card) => {
    courseModalRef.value?.handleCardRemove(card);
    formData.value.permitted_class = checkedCardUnionList.value.map((item) => item.card_id);
    formRef.value.validateField('permitted_class');
  };

  // good at
  const coachSpecialty = ref([]);
  const specialPromise = getCoachSpecial().then((res) => {
    coachSpecialty.value = res.data.value;
    return res;
  });

  const handleSaveCoachSpecial = (params) => {
    saveCoachSpecial({
      name: params.name,
    }).then(() => {
      getCoachSpecial().then((res) => {
        const list = res.data.value;
        list.forEach((item, index) => {
          const csIndex = coachSpecialty.value.findIndex((cs) => cs.id === item.id);
          if (csIndex !== -1) {
            const { checked } = coachSpecialty.value[index];
            list[index].checked = checked || false;
          }
        });

        coachSpecialty.value = list;
      });
      Message.success('保存成功');
    });
  };
  const handleRemoveCoachSpecial = (ids) => {
    removeCoachSpecial({
      ids,
    }).then(() => {
      const idsList = ids.split(',');
      coachSpecialty.value = coachSpecialty.value.filter((item) => !idsList.includes(item.id));
      Message.success('删除成功');

      formData.value.specialty_id = [];
    });
  };

  // position
  const coachPosition = ref([]);
  const positionPromise = getCoachPosition().then((res) => {
    coachPosition.value = res.data.value;
    return res;
  });

  const handleSaveCoachPosition = async (params) => {
    if (params.id) {
      await updateCoachPosition(params);
    } else {
      await addCoachPosition(params);
    }

    const res = await getCoachPosition();
    const list = res.data.value;

    list.forEach((item, index) => {
      const cpIndex = coachPosition.value.findIndex((cp) => cp.id === item.id);
      if (cpIndex !== -1) {
        const { checked } = coachPosition.value[index];
        list[index].checked = checked || false;
      }
    });

    coachPosition.value = list;
    Message.success('保存成功');
  };
  const handleRemoveCoachPosition = (ids) => {
    const promiseList = [];
    const idsList = ids.split(',');
    idsList.forEach((id) => {
      const promise = removeCoachPosition({ id });
      promiseList.push(promise);
    });

    Promise.all(promiseList).then(() => {
      coachPosition.value = coachPosition.value.filter((item) => !idsList.includes(item.id));
      Message.success('删除成功');

      formData.value.position_id = [];
    });
  };

  // authority
  const authorityList = ref([
    { id: 1, name: '教练（仅管理自己会员）' },
    { id: 2, name: '教练组长（查看组别内会员）' },
    { id: 3, name: '教练经理（管理全部会员）' },
  ]);

  // class authority
  const classAuthorityList = ref([
    { id: 1, name: '团操教练（查看团操课安排）' },
    { id: 2, name: '团操经理（管理全部团操课）' },
  ]);

  // event
  const handleSubmit = async () => {
    const valid = await formRef.value.validate();
    if (!valid) {
      isLoading.value = true;

      const postData = {
        ...formData.value,
        specialty_id: formData.value.specialty_id.join(','),
        position_id: formData.value.position_id.join(','),
        coach_type: formData.value.coach_type.join(','),
        aptitude: formData.value.aptitude.replace(new RegExp(/(\n)/g), '<br/>'),
        other_intr: formData.value.other_intr.replace(new RegExp(/(\n)/g), '<br/>'),
      };
      Reflect.deleteProperty(postData, 'specialty');
      Reflect.deleteProperty(postData, 'position');

      // submit
      setTimeout(() => {
        isLoading.value = false;
      }, 1000);
      if (props.id) {
        await updateCoach({
          id: props.id,
          ...postData,
        });
      } else {
        await addCoach(postData);
      }

      Message.success('保存成功');
      isLoading.value = false;
      router.back();
    }
  };
  const loading = ref(false);
  const getInfo = () => {
    return getCoach({ id: props.id }).then((res) => {
      const coach = res.data.value;
      const specialty_id = coach.specialty_id ? coach.specialty_id.split(',') : [];
      const position_id = coach.position_id ? coach.position_id.split(',') : [];

      specialPromise.then(() => {
        coachSpecialty.value.forEach((item) => {
          if (specialty_id.includes(item.id)) {
            item.checked = true;
          }
        });
      });

      positionPromise.then(() => {
        coachPosition.value.forEach((item) => {
          if (position_id.includes(item.id)) {
            item.checked = true;
          }
        });
      });

      Object.assign(formData.value, {
        ...coach,
        specialty_id,
        position_id,
        coach_type: coach.coach_type ? coach.coach_type.split(',') : [],
        aptitude: coach.aptitude ? coach.aptitude.replace(/<br\/>/g, '\n') : '',
        other_intr: coach.other_intr ? coach.other_intr.replace(/<br\/>/g, '\n') : '',
        sex: Number(coach.sex),
        sort: Number(coach.sort),
        display: Number(coach.display),
        use_coach_manage: Number(coach.use_coach_manage),
        data_authority: Number(coach.data_authority),
        class_data_authority: Number(coach.class_data_authority),
      });

      loading.value = false;
    });
  };

  // created
  // pullAllPlanList();
  const breadcrumb = ref(['人员管理', '教练']);
  if (props.id) {
    route.meta.locale = '编辑教练';
    breadcrumb.value.push('编辑教练');
    loading.value = true;
    getInfo();
  } else {
    route.meta.locale = '添加教练';
    breadcrumb.value.push('添加教练');
  }
</script>

<style lang="less" scoped>
  .tip-box {
    margin-top: 10px;

    .tip {
      font-size: 12px;
      color: #999999;
      line-height: 18px;
    }
  }

  .tab-box {
    height: 500px;
    overflow-y: scroll;
  }

  .chose-panel {
    margin-bottom: 20px;
    overflow: hidden;
  }
</style>
