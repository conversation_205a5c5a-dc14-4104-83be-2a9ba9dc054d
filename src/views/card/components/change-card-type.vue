<template>
  <a-modal
    v-model:visible="isShowModal"
    :title="cardType === 2 ? '私教转泳教' : '泳教转私教'"
    :width="720"
    :on-before-ok="hansleConfirm">
    <a-form ref="formRef" :model="postData" auto-label-width>
      <a-form-item field="card_ids" label="需要转换的卡种" :rules="{ required: true, message: '请选择需要转换的卡种' }">
        <a-select v-model="postData.card_ids" allow-clear allow-search multiple>
          <a-option v-for="item in cardList" :key="item.card_id" :value="item.card_id">
            {{ item.name }}
          </a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { changeCardToSwim } from '@/api/member-card';

  const props = defineProps<{
    modelValue?: boolean;
    cardType: number;
    cardList: any[];
  }>();

  const postData = reactive({
    card_ids: [],
    type: props.cardType === 2 ? 0 : 1, // 0私转泳 1泳转私
  });
  const emits = defineEmits(['update:modelValue', 'onSuccess']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const formRef = ref();
  async function hansleConfirm() {
    try {
      const errors = await formRef.value.validate();
      if (errors) return false;
      await changeCardToSwim({
        ...postData,
        card_ids: postData.card_ids.join(','),
      });
      Message.success('设置成功！');
      postData.card_ids = [];
      emits('onSuccess');
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }
</script>
