<template>
  <a-modal v-model:visible="isShowModal" title="储值卡续充设置" :width="720" :on-before-ok="hansleConfirm">
    <a-form ref="formRef" :model="postData" auto-label-width>
      <a-form-item v-if="IS_BRAND_SITE" field="bus_id" label="场馆">
        <BusSelectAdmin v-model="postData.bus_id" placeholder="请选择" />
      </a-form-item>
      <RechargeFormItem
        v-model:model-value="postData.recharge_package"
        v-model:self-recharge="postData.self_recharge" />
      <a-form-item field="card_ids" label="卡种" :rules="{ required: true, message: '请选择需要续费的卡种' }">
        <a-tree-select
          v-model="postData.card_ids"
          :allow-search="true"
          :allow-clear="true"
          :tree-checkable="true"
          tree-checked-strategy="child"
          :filter-tree-node="filterTreeNode"
          :data="treeData"
          placeholder="请选择"
          style="width: 100%"></a-tree-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getMerchantCardList } from '@/api/card-class';
  import { getCardList, allStoredValueCardSet } from '@/api/card';
  import { changeCardToSwim } from '@/api/member-card';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { useBusInfoStore } from '@/store';
  import RechargeFormItem from './recharge-form-item.vue';

  const { IS_BRAND_SITE } = window;
  const props = defineProps<{
    modelValue?: boolean;
  }>();
  const busInfo = useBusInfoStore();
  const postData = reactive({
    bus_id: busInfo.bus_id,
    card_ids: [],
    self_recharge: '1', // 开启关闭储值卡自主续充
    recharge_package: [
      // 储值卡专用，充值套餐设置
      {
        id: Date.now(),
        amount: 0,
        number: 0,
        gift_number: 0,
        delay_number: 0,
      },
    ],
  });
  const emits = defineEmits(['update:modelValue', 'onSuccess']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const treeData = ref<Record<string, any>[]>([]);
  const { execute: getList } = IS_BRAND_SITE ? getMerchantCardList() : getCardList();
  async function getTreeList() {
    const res = await getList({
      data: {
        bus_id: postData.bus_id,
        page_size: 9999,
        card_type: 1, // 会籍卡
        card_class: 3, // 储值卡
      },
    });
    postData.card_ids = [];
    treeData.value = [
      {
        key: -1,
        title: '储值卡',
        children: res.data.value.list.map((item: any) => {
          return {
            ...item,
            title: item.name,
            key: item.id,
          };
        }),
      },
    ];
  }
  watch(
    () => isShowModal.value,
    (newValue) => {
      if (newValue) {
        getTreeList();
      } else {
        postData.card_ids = [];
      }
    }
  );

  const formRef = ref();
  const { execute: setInfo } = allStoredValueCardSet();
  async function hansleConfirm() {
    try {
      const errors = await formRef.value.validate();
      if (errors) return false;
      const { self_recharge, recharge_package, card_ids } = postData;
      const params = {
        card_ids: card_ids.join(),
        recharge_package: '',
        self_recharge,
      };
      // 开启设置，才覆盖，关闭不覆盖
      if (self_recharge === '1') {
        const rechargeList = recharge_package.map(({ amount, number, gift_number, delay_number }) => {
          return { amount, number, gift_number, delay_number };
        });
        params.recharge_package = JSON.stringify(rechargeList);
      }
      await setInfo({
        data: params,
      });
      Message.success('设置成功！');
      emits('onSuccess');
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  function filterTreeNode(searchValue: string, nodeData: any) {
    return nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  }
</script>
