<template>
  <a-modal
    v-model:visible="isShowModal"
    :title="name || '课种'"
    :footer="false"
    :width="720"
    @before-close="popupVisibleChange">
    <a-table row-key="index" :data="tableData" :pagination="pagination" @page-change="onPageChange">
      <template #columns>
        <a-table-column title="课程名称" data-index="card_name" />
        <a-table-column title="授课方式" data-index="user_no">
          <template #cell="{ record }">1对{{ record.user_no }}</template>
        </a-table-column>
        <a-table-column title="时长" data-index="class_duration">
          <template #cell="{ record }">{{ record.class_duration }}分钟</template>
        </a-table-column>
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { Pagination } from '@/types/global';

  const props = defineProps<{
    modelValue?: boolean;
    tableData: any[];
    name: string;
  }>();

  const emits = defineEmits(['update:modelValue']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const pagination = reactive<Pagination>({
    current: 1,
    pageSize: 10,
  });
  const onPageChange = (current: number) => {
    pagination.current = current;
  };
  function popupVisibleChange() {
    pagination.current = 1;
  }
</script>
