<template>
  <div style="width: 100%">
    <a-card class="settlement-card" style="width: 100%" dis-hover>
      <template #extra>
        <a-link @click.prevent="isShowModal = true">编辑</a-link>
      </template>
      <a-table :data="tableData" :pagination="false" :hoverable="false">
        <template #columns>
          <a-table-column title="节数范围" data-index="min">
            <template #cell="{ record }">
              <span v-if="record.min && record.max">{{ record.min }}~{{ record.max }}</span>
              <span v-else-if="record.min">{{ `>=${record.min}节` }}</span>
              <span v-else-if="record.max">{{ `<=${record.max}节` }}</span>
            </template>
          </a-table-column>
          <a-table-column title="结算价格" data-index="price">
            <template #cell="{ record }">{{ record.price }}/节</template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <a-modal v-model:visible="isShowModal" title="结算价格设置" :width="720" :on-before-ok="handleBeforeClose">
      <a-row style="margin-bottom: 8px">
        <a-col :span="10">消课节数</a-col>
        <a-col :span="10" :offset="4">结算单价</a-col>
      </a-row>
      <a-row v-for="(item, index) in saleModalData" :key="index" style="margin-bottom: 8px">
        <a-col :span="10">
          <a-space>
            <a-input-number v-model="saleModalData[index].min" :min="1" :precision="0" disabled />
            <span>~</span>
            <a-input-number v-model="saleModalData[index].max" :min="saleModalData[index].min + 1" :precision="0" />
          </a-space>
        </a-col>
        <a-col :span="10" :offset="4">
          <a-space>
            <a-input-number v-model="saleModalData[index].price" :precision="2" :style="{ width: '140px' }" />
            <span>元/节</span>
            <icon-delete
              v-if="index !== 0 && index === saleModalData.length - 1"
              style="cursor: pointer; color: red"
              size="18"
              title="删除"
              @click="handleDeleteClick(index)" />
          </a-space>
        </a-col>
      </a-row>
      <a-link @click="handleAddClick">+添加</a-link>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';

  interface SetInfo {
    min: number;
    max: number | undefined;
    price: number | undefined;
  }
  const props = withDefaults(
    defineProps<{
      modelValue: SetInfo[];
    }>(),
    {
      modelValue: () => [
        {
          min: 1,
          max: undefined,
          price: undefined,
        },
      ],
    }
  );
  const emits = defineEmits(['update:modelValue']);
  const tableData = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const saleModalData = ref<SetInfo[]>([]);
  const isShowModal = ref(false);
  watch(
    () => isShowModal.value,
    (newValue) => {
      if (newValue) {
        saleModalData.value = JSON.parse(
          JSON.stringify(
            tableData.value.length
              ? tableData.value
              : [
                  {
                    min: 1,
                    max: undefined,
                    price: undefined,
                  },
                ]
          )
        );
      }
    }
  );
  function handleAddClick() {
    const lastArr = saleModalData.value[saleModalData.value.length - 1];
    const price = `${lastArr.price}`;
    if (!lastArr.max || price === 'null' || price === 'undefined') {
      Message.error('请先填写完成上一阶段数据');
      return;
    }
    saleModalData.value.push({
      min: lastArr.max + 1,
      max: undefined,
      price: undefined,
    });
  }
  function handleDeleteClick(index: number) {
    saleModalData.value.splice(index, 1);
  }
  function handleBeforeClose() {
    const lastArr = saleModalData.value[saleModalData.value.length - 1];
    if (lastArr.price === undefined) {
      Message.error('结算单价不能为空');
      return false;
    }
    tableData.value = saleModalData.value;
    return true;
  }
</script>
