<template>
  <a-form-item field="name" :label="msPtSwim === 1 ? '会籍卡标签' : msPtSwim == 2 ? '私教课标签' : '泳教课标签'">
    <CheckboxTag
      v-model="checkboxTag"
      v-model:data="checkboxTagData"
      :disabled="disabled"
      style="width: 100%"
      radio
      :type="msPtSwim === 1 ? 2 : msPtSwim === 2 ? 1 : 3"
      :before-tag-add="onTagAdd"
      :before-tag-delete="onTagDelete"></CheckboxTag>
  </a-form-item>
</template>

<script lang="ts" setup>
  import CheckboxTag from '@/components/form/checkbox-tag.vue';
  import { addCardGroup, deleteCardGroup, getCardGroupList } from '@/api/card-group';
  import { useBusInfoStore } from '@/store';

  const props = defineProps({
    msPtSwim: {
      type: Number,
      default: 1,
    },
    modelValue: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const busInfo = useBusInfoStore();
  const busId = computed(() => {
    return busInfo.bus_id;
  });

  const emits = defineEmits(['update:modelValue']);
  const checkboxTag = computed({
    get: () => [props.modelValue],
    set: (value) => {
      emits('update:modelValue', value[0] || '');
    },
  });
  const checkboxTagData = ref([]);
  function getSortData() {
    getCardGroupList({ bus_id: busId.value, type: props.msPtSwim === 1 ? 2 : props.msPtSwim === 2 ? 1 : 3 }).then(
      (res) => {
        checkboxTagData.value = res.data.value
          .filter((item: any) => item.id !== '0')
          .map((item: any) => ({ ...item, name: item.title }));
      }
    );
  }
  getSortData();
  function onTagAdd(title: string, type: number) {
    return addCardGroup({
      bus_id: busId.value,
      title,
      type,
    }).then(() => {
      getSortData();
      return Promise.resolve();
    });
  }
  function onTagDelete(id: string) {
    if (id === '0') return Promise.reject();
    return deleteCardGroup({
      bus_id: busId.value,
      id,
    }).then(() => {
      const index = checkboxTag.value.indexOf(id);
      if (index !== -1) {
        checkboxTag.value = checkboxTag.value.filter((i) => i !== id); // 赋值新数组，触发 set 方法
      }
      return Promise.resolve();
    });
  }
</script>
