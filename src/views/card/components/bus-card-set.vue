<template>
  <div>
    <div v-if="cardType == 2" :label-width="160">
      <a-select
        v-if="!addType"
        v-model="postDataArr[0].bus_id"
        allow-clear
        multiple
        allow-search
        @change="getSelectedBusIds">
        <a-option
          v-for="bus in busList"
          :key="bus.id"
          :disabled="state.selectedBusIds.indexOf(bus.id) !== -1"
          :value="bus.id">
          {{ bus.name }}
        </a-option>
      </a-select>
      <a-select v-else v-model="postDataArr[0].level_id" allow-clear multiple allow-search @change="getSelectedBusIds">
        <a-option
          v-for="group in groupList"
          :key="group.level_id"
          :disabled="state.selectedBusIds.indexOf(group.level_id) !== -1"
          :value="group.level_id">
          {{ group.level_name }}
        </a-option>
      </a-select>
    </div>
    <a-card
      v-for="(postData, index) in postDataArr"
      v-else
      :key="index"
      :class="isCustomize ? 'mgb15' : 'bus-card'"
      dis-hover>
      <template #title>
        <p>{{ addType ? postDataArr[index].level_name : postDataArr[index].bus_name }}</p>
      </template>
      <template #extra>
        <a-link @click.prevent="handleBusDel(index)">删除场馆</a-link>
      </template>

      <slot v-if="state.isCustomize" name="customize" :store-data="postData"></slot>
      <a-form v-else :ref="'busSetForm' + index" :model="postDataArr[index]" :label-width="140">
        <a-form-item v-if="!addType" v-show="cardType != 2" label="会员手机端购买" field="status">
          <a-radio-group v-model="postDataArr[index].status">
            <a-radio :value="1">支持</a-radio>
            <a-radio :value="0">不支持</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="cardType != 2 && ((!addType && postDataArr[index].status == 1) || addType)"
          label="到店激活限制"
          field="activation_restriction">
          <a-select v-model="postDataArr[index].activation_restriction">
            <a-option v-for="(item, index) in state.activationRestrictionList" :key="index" :value="item.value">
              {{ item.name }}
            </a-option>
          </a-select>
          <template #extra>该限制只适用于勤鸟+授权的会员端小程序</template>
        </a-form-item>
        <a-form-item v-show="cardType != 2 && subCardType != 4 && subCardType != 5" label="售价" field="current_price">
          <a-input-number
            v-model="postDataArr[index].current_price"
            :min="0"
            :step="0.01"
            :disabled="postDataArr[index].currentPriceFlag" />
          <a-checkbox
            v-if="postDataArr[index].alipay_sale_status != 1"
            v-show="cardType != 2 && subCardType != 7"
            v-model="postDataArr[index].currentPriceFlag"
            style="width: 130px; margin-left: 20px"
            @change="handlePriceToFace(index)">
            价格面议
          </a-checkbox>
        </a-form-item>
        <a-form-item
          v-show="subCardType == 3"
          label="签到扣费折扣"
          tooltip="用该储值卡种进行到馆签到可享受到的折扣"
          field="sign_discount">
          <a-input-number v-model="postDataArr[index].sign_discount" :min="0.1" :max="10.0" :step="0.1" />
          <template #extra>请输入折扣值 0.1~10.0，10表示不打折</template>
        </a-form-item>
        <a-form-item
          v-show="(subCardType == 1 || subCardType == 2) && cardType != 2"
          label="时段限制"
          :content-flex="false">
          <div v-for="(item, i) in postDataArr[index].weekTimes" :key="i" class="flex-time">
            <a-checkbox-group v-model="item.weeks" style="width: 100%; margin-bottom: 8px">
              <a-checkbox v-for="index in ['0', '1', '2', '3', '4', '5', '6']" :key="index" :value="index">
                周{{ state.dict[index] }}
              </a-checkbox>
            </a-checkbox-group>
            <a-row style="width: 100%" align="center">
              开始时间
              <a-time-picker v-model="item.start_time" format="HH:mm" style="width: 80px" />
              ~ 截止时间
              <a-time-picker v-model="item.end_time" format="HH:mm" style="width: 80px" />
              <a-button type="text" style="color: #ed3f14" @click="postDataArr[index].weekTimes.splice(i, 1)">
                删除
              </a-button>
            </a-row>
          </div>
          <a-button type="text" size="small" @click="confirmAddTimeLimit(index)">添加</a-button>
        </a-form-item>

        <a-form-item v-show="subCardType == 4 || subCardType == 5" label="单节售价（元）" field="single_price">
          <a-input-number v-model="postDataArr[index].single_price" :min="0" :step="0.01" />
        </a-form-item>
        <a-form-item
          v-show="subCardType == 4 || subCardType == 5 || subCardType == 7"
          :label="subCardType == 7 ? '赠送天数' : '赠送节数（节）'"
          field="gift_number">
          <a-input-number v-model="postDataArr[index].gift_number" :min="0" :step="1" />
        </a-form-item>
        <a-form-item
          v-show="(postDataArr[index].status == 1 || addType) && (subCardType == 4 || subCardType == 5)"
          label="起购节数（节）"
          field="buy_min_value">
          <a-input-number v-model="postDataArr[index].buy_min_value" :min="1" :step="1" />
        </a-form-item>
        <a-form-item v-if="cardType !== 2" label="购卡规则" :content-flex="false">
          <div class="card-rules-box">
            <a-switch
              v-model="postDataArr[index].is_open_rule"
              :disabled="cardRuleList.length === 0"
              checked-value="1"
              unchecked-value="0"
              @change="handleChangeOpenRule($event, index)" />
            <div class="select-box">
              <a-select
                v-if="cardRuleList.length"
                v-model="postDataArr[index].sale_rule_id"
                placeholder="请选择"
                style="width: 100%"
                :disabled="postDataArr[index].is_open_rule === '0'">
                <a-option v-for="(item, index) in cardRuleList" :key="index" :value="item.id">
                  {{ item.rule_name }}
                </a-option>
              </a-select>
              <a-alert v-else type="warning">该类型卡暂未添加规则</a-alert>
            </div>
          </div>
          <div
            v-if="postDataArr[index].is_open_rule === '1' && cardRuleList.length && state.range[index]"
            class="rule-info-box">
            <p>{{ rangelabel.sale + (state.range[index].sale_range || '') }}</p>
            <p v-show="subCardType === 1 || subCardType === 2 || subCardType === 3">
              {{ rangelabel.buy + (state.range[index].buy_max || '') }}
            </p>
            <p>{{ rangelabel.gift + (state.range[index].gift_max || '') }}</p>
          </div>
        </a-form-item>
      </a-form>
    </a-card>
    <div v-if="cardType !== 2">
      <a-button type="outline" @click="handleBusShow">添加{{ addType ? '场馆组' : '场馆' }}</a-button>
    </div>

    <a-modal
      v-model:visible="state.showAddBusModal"
      :on-before-ok="confirmAddBus"
      :title="addType ? '添加场馆组' : '添加场馆'"
      @before-close="cancelAddBus">
      <a-form-item :label="`场馆${addType ? '组' : ''}名称`">
        <a-select v-if="!addType" v-model="state.addBusModalData" allow-clear multiple allow-search>
          <a-option
            v-for="bus in busList"
            :key="bus.id"
            :disabled="state.selectedBusIds.indexOf(bus.id) !== -1"
            :value="bus.id">
            {{ bus.name }}
          </a-option>
        </a-select>
        <a-select v-else v-model="state.addBusModalData" allow-clear multiple allow-search>
          <a-option
            v-for="group in groupList"
            :key="group.level_id"
            :disabled="state.selectedBusIds.indexOf(group.level_id) !== -1"
            :value="group.level_id">
            {{ group.level_name }}
          </a-option>
        </a-select>
      </a-form-item>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getRange } from '@/api/card-sale-rule';
  import { useBusInfoStore } from '@/store';

  const props = withDefaults(
    defineProps<{
      addType?: number;
      cardType: number;
      subCardType: number;
      modelValue: Record<string, any>[];
      busList?: Record<string, any>[];
      groupList?: Record<string, any>[];
      isCustomize?: boolean;
      cardRuleList?: Record<string, any>[];
      formValidate: any;
      cardId: number;
    }>(),
    {
      addType: 0,
      cardType: 0,
      subCardType: 1,
      modelValue: () => [],
      busList: () => [],
      groupList: () => [],
      formValidate: () => {},
      isCustomize: false,
      cardRuleList: () => [],
      cardId: 0,
    }
  );
  const emits = defineEmits(['update:modelValue']);
  const postDataArr = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emits('update:modelValue', value);
    },
  });
  const rangelabel = computed(() => {
    const { subCardType } = props;
    let sale = '售价';
    let buy = '购买天数';
    let gift = '赠送天数';
    switch (+subCardType) {
      case 2:
        buy = '购买次数';
        gift = '赠送次数';
        break;
      case 3:
        buy = '价值金额';
        gift = '赠送金额';
        break;
      case 4:
      case 5:
        sale = '单节售价';
        gift = '赠送节数';
        break;
      default:
    }
    return {
      sale: `${sale}可调整范围：`,
      buy: `${buy}可调整上限：`,
      gift: `${gift}可调整上限：`,
    };
  });
  const state = reactive({
    cardThreeType: 1,
    activationRestrictionList: [
      {
        value: 0,
        name: '无限制',
      },
      {
        value: 1,
        name: '仅支持立即开卡',
      },
      {
        value: 7,
        name: '7天后自动激活',
      },
      {
        value: 15,
        name: '15天后自动激活',
      },
      {
        value: 30,
        name: '30天后自动激活',
      },
      {
        value: 45,
        name: '45天后自动激活',
      },
      {
        value: 60,
        name: '2个月(60)天后自动激活',
      },
      {
        value: 90,
        name: '3个月(90)天后自动激活',
      },
      {
        value: 120,
        name: '4个月(120)天后自动激活',
      },
      {
        value: 150,
        name: '5个月(150)天后自动激活',
      },
      {
        value: 180,
        name: '6个月(180)天后自动激活',
      },
      {
        value: 210,
        name: '7个月(210)天后自动激活',
      },
      {
        value: 240,
        name: '8个月(240)天后自动激活',
      },
      {
        value: 270,
        name: '9个月(270)天后自动激活',
      },
      {
        value: 300,
        name: '10个月(300)天后自动激活',
      },
      {
        value: 330,
        name: '11个月(330)天后自动激活',
      },
      {
        value: 360,
        name: '12个月(360)天后自动激活',
      },
    ],
    dict: {
      0: '日',
      1: '一',
      2: '二',
      3: '三',
      4: '四',
      5: '五',
      6: '六',
    },
    enableTimeLimit: false,
    timeLimits: [],
    timeLimitIndex: 0,
    timeLimitModalData: {
      weeks: [],
      begin_time: '',
      end_time: '',
    },
    showAddBusIndex: 0,
    selectedBusIds: [] as Record<string, any>[],
    addBusModalData: [] as string[],
    showAddBusModal: false,
    range: [
      /* {
          sale_range: "",
          buy_max: "",
          gift_max: "",
        } */
    ] as Record<string, any>[],
  });

  function getSelectedBusIds() {
    const ids: Record<string, any>[] = [];
    postDataArr.value.forEach((v: Record<string, any>) => {
      ids.push(props.addType ? v.level_id : v.bus_id);
    });
    state.selectedBusIds = ids;
  }
  function getInfoById(id: string) {
    if (!props.addType) {
      const foundBusItem = props.busList.find((item) => id === item.id);
      if (foundBusItem) {
        return {
          bus_id: id,
          bus_name: foundBusItem.name,
        };
      }
    }
    const foundItem = props.groupList.find((item) => id === item.level_id);
    if (foundItem) {
      return {
        level_id: foundItem.level_id,
        level_name: foundItem.level_name,
      };
    }
    return {
      level_id: '',
      level_name: '',
    };
  }
  function confirmAddBus() {
    if (state.addBusModalData.length) {
      const curIndexArr = { ...postDataArr.value[state.showAddBusIndex] };
      if (curIndexArr.sale_rule_id !== undefined) curIndexArr.sale_rule_id = '';
      if (curIndexArr.is_open_rule !== undefined) curIndexArr.is_open_rule = '0';
      state.addBusModalData.forEach((busId) => {
        const data = JSON.parse(
          JSON.stringify({
            ...curIndexArr,
            ...getInfoById(busId),
          })
        );
        postDataArr.value.push(data);
      });
      state.showAddBusModal = false;
    }
  }
  function cancelAddBus() {
    state.addBusModalData = [];
  }
  function handleBusShow() {
    state.showAddBusIndex = postDataArr.value.length - 1;
    state.addBusModalData = [];
    getSelectedBusIds();
    state.showAddBusModal = true;
  }
  function handleBusDel(index: number) {
    if (postDataArr.value && postDataArr.value.length === 1) {
      Message.error('至少需要保留一个归属场馆');
      return;
    }
    postDataArr.value.splice(index, 1);
  }
  function confirmAddTimeLimit(index: number) {
    postDataArr.value[index].weekTimes.push({
      weeks: [],
      begin_time: '',
      end_time: '',
    });
  }
  function handlePriceToFace(index: number) {
    if (postDataArr.value[index].currentPriceFlag) {
      postDataArr.value[index].current_price = 0;
    }
  }
  function handleChangeOpenRule(val: string, index: number) {
    if (val === '0') {
      postDataArr.value[index].sale_rule_id = '';
    }
  }

  function getRuleInfoParams(info: Record<string, any>) {
    const { end_time, number, gift_number: giftNumber } = props.formValidate;
    const { sale_rule_id, single_price, current_price, gift_number, bus_id } = info;
    // 默认1 期限卡的传值
    const params = {
      bus_id,
      rule_id: sale_rule_id, // 规则id
      sale_amount: current_price, // 售价金额
      buy_num: end_time, // 购买量
      gift_num: giftNumber, // 赠送量
    };
    switch (props.subCardType) {
      case 2:
      case 3:
        params.buy_num = number;
        break;
      case 4:
      case 5:
        params.sale_amount = single_price;
        params.gift_num = gift_number;
        delete params.buy_num;
        break;
      case 7:
        params.gift_num = gift_number;
        delete params.buy_num;
        break;
      default:
    }
    return params;
  }
  function getCardRuleRangeInfo(params: Record<string, any>, index: number) {
    if (!params.rule_id) {
      state.range[index] = {};
      return;
    }
    // 获取选择的购卡规则范围信息
    const { execute: getRangeInfo } = getRange();
    getRangeInfo({ data: params }).then((res) => {
      state.range[index] = res.data.value;
    });
  }
  const paramsInfoArr = ref<Record<string, any>[]>([]);
  function getCardRuleRange() {
    postDataArr.value.forEach((item, index) => {
      const params = getRuleInfoParams(item);
      // params与上次不一样时更新卡规则范围
      if (JSON.stringify(paramsInfoArr.value[index]) !== JSON.stringify(params)) {
        paramsInfoArr.value[index] = params;
        getCardRuleRangeInfo(params, index);
      }
    });
  }
  watch(
    () => postDataArr.value,
    () => {
      getCardRuleRange();
    },
    { deep: true, immediate: true }
  );
</script>

<style lang="less" scoped>
  .across-row {
    display: flex;
    flex-direction: row;
  }
  .flex-time {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
  }
  .bodybuilding {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .w-red {
      font-size: 16px;
      color: red;
    }

    .w-blue {
      font-size: 16px;
      color: blue;
    }

    .word {
      height: 169px;
      width: 242px;
    }
  }
  .bus-card {
    margin: 0 0 15px 0;
    width: 100%;

    .card-rules-box {
      box-sizing: border-box;
      padding: 0 16px;
      display: flex;
      align-items: center;
      width: 100%;
      height: 34px;
      .select-box {
        margin-left: 20px;
        flex: 1;
        .ivu-alert-warning {
          margin-bottom: 0;
          padding-right: 16px;
        }
      }
    }
    .rule-info-box {
      box-sizing: border-box;
      margin-top: 18px;
      padding: 8px;
      width: 100%;
      line-height: 1.75;
      border: 1px solid #dcdee2;
      border-color: #e8eaec;
    }
  }

  .rig-gray {
    color: gray;
    margin-left: 10px;
  }
  .mgb15 {
    margin-bottom: 15px;
  }
</style>
