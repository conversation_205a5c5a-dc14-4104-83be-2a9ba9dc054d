<template>
  <a-modal v-model:visible="isShowModal" title="新增组别" :width="720" :on-before-ok="hansleConfirm">
    <a-form ref="formRef" :model="postData" auto-label-width>
      <a-form-item field="bus_id" label="场馆" :rules="{ required: true, message: '请选择场馆' }">
        <BusSelectAdmin v-model="postData.bus_id" placeholder="请选择" />
      </a-form-item>
      <a-form-item label="组别类型">
        <a-radio-group v-model="postData.type">
          <a-radio :value="1">私教课组别</a-radio>
          <a-radio :value="2">泳教课组别</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="name" label="组别名称" :rules="{ required: true, message: '请输入组别名称' }">
        <a-input v-model="postData.name" placeholder="请输入组别名称" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { addCardClassGroup } from '@/api/card-class-group';
  import { merchantCardClassGroupAdd } from '@/api/card-class';
  import { useBusInfoStore } from '@/store';

  const props = defineProps<{
    modelValue?: boolean;
    busId: string;
  }>();
  const { IS_BRAND_SITE } = window;
  const busInfo = useBusInfoStore();
  const initBusId = computed(() => props.busId || busInfo.bus_id);
  const postData = reactive({
    bus_id: '',
    type: 1, // 1 私教 2 泳教
    name: '',
  });

  const emits = defineEmits(['update:modelValue', 'onSuccess']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  watch(
    () => isShowModal.value,
    (val) => {
      postData.bus_id = initBusId.value;
    }
  );

  const formRef = ref();
  const { execute: addGroup } = IS_BRAND_SITE ? merchantCardClassGroupAdd() : addCardClassGroup();
  async function hansleConfirm() {
    try {
      const errors = await formRef.value.validate();
      if (errors) return false;
      await addGroup({ data: postData });
      Message.success('设置成功！');
      formRef.value.resetFields();
      emits('onSuccess');
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }
</script>
