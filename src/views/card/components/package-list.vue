<template>
  <a-card class="general-card">
    <a-row>
      <a-col :flex="1">
        <a-form
          :model="searchParam"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="belong_bus_id" label="场馆">
                <BusSelectAdmin v-model="searchParam.bus_id" placeholder="请选择" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="search" label="包名称">
                <a-input v-model="searchParam.name" allow-clear @press-enter="handleSearch" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="search" label="在售状态">
                <a-select v-model="searchParam.status" allow-clear placeholder="请选择">
                  <a-option :value="1">在售</a-option>
                  <a-option :value="0">下架</a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
      <a-divider style="height: 32px" direction="vertical" />
      <a-col :flex="'86px'" style="text-align: right">
        <a-space direction="vertical" :size="18">
          <a-button type="primary" @click="handleSearch">
            <template #icon>
              <icon-search />
            </template>
            搜索
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-divider style="margin-top: 0" />
    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-space>
          <a-button type="primary" @click="router.push('/v2/bundlePackage/save')">新增套餐包</a-button>
        </a-space>
      </a-col>
      <a-col :span="12" align="end">
        <ExportExcel>
          <template #default="{ handleExport }">
            <a-button @click="handleClickExport(handleExport as Callback<ExportData>)">导出</a-button>
          </template>
        </ExportExcel>
      </a-col>
    </a-row>
    <a-table v-bind="tableProps" :data="tableProps.data" v-on="tableEvent">
      <template #columns>
        <a-table-column title="套餐包名称" data-index="name" />
        <a-table-column title="售价" data-index="amount" align="right" />
        <a-table-column title="内容" data-index="contend" :width="300" ellipsis tooltip />
        <a-table-column title="在售状态" data-index="sale_status_str">
          <template #cell="{ record }">
            <a-switch
              v-model="record.sale_status"
              :loading="record.loading"
              checked-value="1"
              unchecked-value="0"
              @change="
                (e) => {
                  handleStatusChange(e, record);
                }
              " />
          </template>
        </a-table-column>
        <a-table-column title="会员端售卖" data-index="phonePay">
          <template #cell="{ record }">
            <a-switch
              v-model="record.is_member"
              :loading="record.statusLoading"
              checked-value="1"
              unchecked-value="0"
              @change="
                (e) => {
                  handleSetStatusChange(e, record);
                }
              " />
          </template>
        </a-table-column>
        <a-table-column title="操作">
          <template #cell="{ record }">
            <a-space>
              <a-link @click="goEdit(record.id)">编辑</a-link>
              <a-link status="danger" @click="handleDelete(record)">删除</a-link>
            </a-space>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-card>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { useBusInfoStore } from '@/store';
  import ExportExcel from '@/components/exportExcel.vue';
  import { getCardClass, checkSurportOnline } from '@/api/card';
  import { getPackageList, swicthSaleStatus, swicthMemberStatus, delPackage } from '@/api/package';
  import useTableProps from '@/hooks/table-props';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { Callback, ExportData } from '@/types/global';

  const router = useRouter();
  const { tableProps, tableEvent, searchParam, setSearchParam, handleSearch, loadTableList } = useTableProps(
    getPackageList,
    (list: Record<string, any>[]) => {
      return list.map((item: Record<string, any>) => {
        item.sale_status_str = item.sale_status === '1' ? '是' : '否';
        item.is_member_str = item.is_member === '1' ? '是' : '否';
        item.amount = +item.amount;
        return item;
      });
    }
  );
  const cardTypeList = ref<{ name: string; value: number | string }[]>([]);
  const busInfo = useBusInfoStore();

  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: busInfo.bus_id,
    name: '',
    status: 1,
  });

  const supportMobileAuthority = ref(false);
  onMounted(() => {
    loadTableList();
    getCardClass().then((res) => {
      cardTypeList.value = res.data.value;
    });
    checkSurportOnline().then((res) => {
      supportMobileAuthority.value = res.data.value.status === 1;
    });
  });

  function handleSupportMobile(info: any) {
    if (!supportMobileAuthority.value && Number(info.status) === 1) {
      Modal.confirm({
        title: '提示',
        content: '启用在线支付购卡购课功能，需要先申请微信特约服务商',
        okText: '查看文档',
        cancelText: '取消',
        onOk: () => {
          window.open(
            'https://imagecdn.rocketbird.cn/minprogram/web-fe-v2/%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98%E7%89%B9%E7%BA%A6%E5%95%86%E6%88%B7%E7%9A%84%E7%94%B3%E8%AF%B7%E5%92%8C%E9%85%8D%E7%BD%AE.pdf'
          );
        },
      });
      setTimeout(() => {
        info.status = '0';
      }, 200);
      return false;
    }
    return true;
  }

  async function handleStatusChange(status: string | number | boolean, info: any) {
    const { id } = info;
    if (status === '0') info.is_member = '0';
    const { isLoading, execute: setInfo } = swicthSaleStatus();
    info.loading = isLoading;
    try {
      await setInfo({ data: { id, status } });
      Message.success('设置成功！');
    } catch (error) {
      console.error(error);
      loadTableList();
    }
  }
  async function handleSetStatusChange(status: string | number | boolean, info: any) {
    const { id } = info;
    if (handleSupportMobile(info)) {
      const { isLoading, execute: setInfo } = swicthMemberStatus();
      info.statusLoading = isLoading;
      if (status === '1') info.sale_status = '1';
      try {
        await setInfo({ data: { id, status } });
        Message.success('设置成功！');
      } catch (error) {
        console.error(error);
        loadTableList();
      }
    }
  }

  async function postToDelete(id: string, usable_group_id: string) {
    await delPackage({ id, usable_group_id });
    loadTableList();
    Message.success('删除成功');
  }

  function goEdit(id: string) {
    router.push(`/v2/bundlePackage/save/${id}`);
  }

  function handleDelete(info: Record<string, any>) {
    const { id, usable_group_id } = info;
    if (info.delete_notice_bus_name) {
      Modal.confirm({
        title: '删除',
        content: `删除操作将影响${info.delete_notice_bus_name}正常使用此卡，是否删除？`,
        onOk: () => {
          postToDelete(id, usable_group_id);
        },
      });
    } else {
      postToDelete(id, usable_group_id);
    }
  }
  const handleClickExport = (cb: Callback<ExportData>) => {
    loadTableList(true).then((list) => {
      cb({
        filename: '套餐包',
        columns: [
          {
            title: '套餐包名称',
            dataIndex: 'name',
          },
          {
            title: '售价',
            dataIndex: 'amount',
          },
          {
            title: '内容',
            dataIndex: 'contend',
          },
          {
            title: '在售状态',
            dataIndex: 'sale_status_str',
          },
          {
            title: '会员端售卖',
            dataIndex: 'phonePay',
          },
        ],
        data: list,
      });
    });
  };
</script>
