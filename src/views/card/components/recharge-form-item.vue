<template>
  <div>
    <a-form-item field="self_recharge" label="自主续充" tooltip="开启后会员可以在会员端自主充值续费">
      <a-switch v-model="selefRecharge" checked-value="1" unchecked-value="0" />
    </a-form-item>
    <a-form-item
      v-if="selfRecharge === '1'"
      field="recharge_package"
      label="充值套餐设置"
      :content-flex="false"
      :rules="[{ validator: checkRechargePackage }]">
      <a-row :gutter="16" align="center" justify="space-between">
        <a-col flex="60px"></a-col>
        <a-col flex="1">售价</a-col>
        <a-col flex="1">储值价值</a-col>
        <a-col flex="1">赠送价值</a-col>
        <a-col flex="1">储值卡延期天数</a-col>
        <a-col flex="30px"></a-col>
      </a-row>
      <a-row
        v-for="(item, index) in rechargePackage"
        :key="index"
        :gutter="16"
        align="center"
        justify="space-between"
        style="margin-bottom: 16px">
        <a-col flex="70px">方案{{ index + 1 }}</a-col>
        <a-col flex="1">
          <a-form-item
            :field="`recharge_package.${index}.amount`"
            :rules="{ required: true, min: 0.01, type: 'number', message: '售价需要大于0' }"
            no-style>
            <a-input-number v-model="item.amount" :min="0.01" :precision="2" placeholder="请填写" />
          </a-form-item>
        </a-col>
        <a-col flex="1">
          <a-form-item
            :field="`recharge_package.${index}.number`"
            :rules="{ required: true, message: '请填写数据' }"
            no-style>
            <a-input-number v-model="item.number" :min="0" :precision="2" placeholder="请填写" />
          </a-form-item>
        </a-col>
        <a-col flex="1">
          <a-form-item :field="`recharge_package.${index}.gift_number`" no-style>
            <a-input-number
              v-model="item.gift_number"
              :min="0"
              :precision="2"
              placeholder="请填写"
              :rules="{ required: true, message: '请填写数据' }" />
          </a-form-item>
        </a-col>
        <a-col flex="1">
          <a-form-item :field="`recharge_package.${index}.delay_number`" no-style>
            <a-input-number
              v-model="item.delay_number"
              :min="0"
              :precision="0"
              placeholder="请填写"
              :rules="{ required: true, message: '请填写数据' }" />
          </a-form-item>
        </a-col>
        <a-col flex="30px">
          <icon-delete
            v-if="index > 0"
            style="cursor: pointer"
            size="18"
            title="删除"
            @click.prevent="handleDeleteListClick(index)" />
        </a-col>
      </a-row>
      <a-button type="outline" @click="addRechargePackage">新增方案</a-button>
    </a-form-item>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';

  const props = withDefaults(
    defineProps<{
      modelValue?: {
        id: string | number;
        amount: number;
        number: number;
        gift_number: number;
        delay_number: number;
      }[];
      selfRecharge?: string;
    }>(),
    {
      modelValue: () => [],
      selfRecharge: '1',
    }
  );
  const emits = defineEmits(['update:modelValue', 'update:selfRecharge', 'onSuccess']);
  const rechargePackage = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const selefRecharge = computed({
    get: () => props.selfRecharge,
    set: (value) => {
      emits('update:selfRecharge', value);
    },
  });

  function addRechargePackage() {
    if (rechargePackage.value.length >= 50) {
      Message.error('已达最大可设置上限');
      return;
    }
    rechargePackage.value.push({
      id: Date.now(),
      amount: 0,
      number: 0,
      gift_number: 0,
      delay_number: 0,
    });
  }

  function handleDeleteListClick(index: number) {
    rechargePackage.value.splice(index, 1);
  }

  const checkRechargePackage = (value: string, cb: (arg0?: string) => void) => {
    if (!rechargePackage.value.length) {
      cb('请设置充值套餐');
    }
    // 除开id可以不同外 不能存在相同方案
    if (
      new Set(
        rechargePackage.value.map((item) =>
          JSON.stringify({
            amount: item.amount,
            number: item.number,
            gift_number: item.gift_number,
            delay_number: item.delay_number,
          })
        )
      ).size !== rechargePackage.value.length
    ) {
      cb('存在相同方案');
    }
    cb();
  };
</script>
