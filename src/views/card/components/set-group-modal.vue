<template>
  <a-modal v-model:visible="isShowModal" title="课种分组设置" :width="720" :on-before-ok="hansleConfirm">
    <a-spin style="display: block" :loading="treeLoading">
      <a-form ref="formRef" :model="postData" auto-label-width>
        <a-form-item field="bus_id" label="场馆" :rules="{ required: true, message: '请选择场馆' }">
          <BusSelectAdmin v-model="postData.bus_id" placeholder="请选择" @change="getTreeList()" />
        </a-form-item>
        <a-form-item label="组别类型">
          <a-radio-group v-model="postData.type" @change="getTreeList()">
            <a-radio :value="1">私教课组别</a-radio>
            <a-radio :value="2">泳教课组别</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item field="card_ids" label="选择卡种" :rules="{ required: true, message: '请选择卡种' }">
          <a-tree-select
            v-model="postData.card_ids"
            :allow-search="true"
            :allow-clear="true"
            :tree-checkable="true"
            :filter-tree-node="filterTreeNode"
            :data="treeData"
            tree-checked-strategy="child"
            placeholder="请选择"
            style="width: 100%"></a-tree-select>
        </a-form-item>
        <a-form-item field="card_class_group_id" label="新的分组" :rules="{ required: true, message: '请选择分组' }">
          <a-select v-model="postData.card_class_group_id" allow-clear placeholder="请选择">
            <a-option v-for="item in groupList" :key="item.card_class_group_id" :value="item.card_class_group_id">
              {{ item.card_class_group_name }}
            </a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { getCardClassGroupCardList, setCardClassGroup } from '@/api/card-class-group';
  import { setMerchantCardClassGroup } from '@/api/card-class';
  import { useBusInfoStore } from '@/store';

  const props = defineProps<{
    modelValue?: boolean;
    busId?: string;
  }>();
  const { IS_BRAND_SITE } = window;
  const busInfo = useBusInfoStore();
  const initBusId = computed(() => props.busId || busInfo.bus_id);
  const postData = reactive({
    bus_id: '',
    card_ids: [],
    type: 1, // 1 私教 2 泳教
    card_class_group_id: '', // 课程分组id
  });
  const emits = defineEmits(['update:modelValue', 'onSuccess']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const { isLoading: treeLoading, execute: getTree } = getCardClassGroupCardList();
  const treeData = ref<Record<string, any>[]>([]);
  const groupList = ref<Record<string, any>[]>([]);
  async function getTreeList() {
    const res = await getTree({ data: { bus_id: postData.bus_id, type: postData.type } });
    postData.card_class_group_id = '';
    postData.card_ids = [];
    groupList.value = res.data.value;
    treeData.value = res.data.value.map((item: any) => {
      return {
        ...item,
        title: item.card_class_group_name || '',
        key: `${item.card_class_group_id}`, // 未分组后端返回的是数字0 造成搜索不到
        children: item.card_list.map((child: any) => {
          return {
            ...child,
            title: child.card_name,
            key: child.card_id,
          };
        }),
      };
    });
  }

  function filterTreeNode(searchValue: string, nodeData: any) {
    return nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  }

  watch(
    () => isShowModal.value,
    (newValue) => {
      if (newValue) {
        postData.bus_id = initBusId.value;
        getTreeList();
      }
    }
  );

  const formRef = ref();
  const { execute: setGroup } = IS_BRAND_SITE ? setMerchantCardClassGroup() : setCardClassGroup();
  async function hansleConfirm() {
    try {
      const errors = await formRef.value.validate();
      if (errors) return false;
      await setGroup({ data: postData });
      Message.success('设置成功！');
      formRef.value.resetFields();
      emits('onSuccess');
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }
</script>
