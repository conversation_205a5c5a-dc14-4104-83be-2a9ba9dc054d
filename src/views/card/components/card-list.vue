<template>
  <a-card class="general-card">
    <a-row>
      <a-col :flex="1">
        <a-form
          :model="searchParam"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item field="belong_bus_id" label="场馆">
                <BusSelectAdmin v-model="searchParam.bus_id" placeholder="请选择" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="search" label="卡名称">
                <a-input v-model="searchParam.card_name" allow-clear @press-enter="handleSearch" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="search" label="单店/通店">
                <a-select v-model="searchParam.is_universal_card" allow-clear placeholder="请选择">
                  <a-option :value="0">单店</a-option>
                  <a-option :value="1">通店</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="search" label="卡类型">
                <a-select v-model="searchParam.card_class" allow-clear placeholder="请选择">
                  <a-option v-for="item in cardTypeList" :key="item.value" :value="item.value">
                    {{ item.name }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item field="search" label="在售状态">
                <a-select v-model="searchParam.sale_status" allow-clear placeholder="请选择">
                  <a-option :value="1">在售</a-option>
                  <a-option :value="0">下架</a-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-col>
      <a-divider style="height: 32px" direction="vertical" />
      <a-col :flex="'86px'" style="text-align: right">
        <a-space direction="vertical" :size="18">
          <a-button type="primary" @click="handleSearch">
            <template #icon>
              <icon-search />
            </template>
            搜索
          </a-button>
        </a-space>
      </a-col>
    </a-row>
    <a-divider style="margin-top: 0" />
    <a-row style="margin-bottom: 16px">
      <a-col :span="12">
        <a-space>
          <a-button type="primary" @click="$router.push(`/admin/card-add/0/${cardType}`)">
            新增{{ cardType === 1 ? '会籍卡' : cardType === 2 ? '私教课' : '泳教课' }}
          </a-button>
        </a-space>
      </a-col>
      <a-col :span="12" align="end">
        <a-space>
          <a-button v-if="(cardType === 2 || cardType === 3) && gruopAuth" @click="goCardGroup()">课程组管理</a-button>
          <a-button v-if="cardRuleAuth" @click="goCardRules()">浮动售价</a-button>
          <a-button v-if="cardType === 1" @click="isShowRechargeModel = true">储值卡续充规则</a-button>
          <a-button v-if="adminInfo.is_admin && [2, 3].includes(cardType)" @click="handleCanChangeCard()">
            转化课程类型
          </a-button>
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button @click="handleClickExport(handleExport as Callback<ExportData>)">导出</a-button>
            </template>
          </ExportExcel>
        </a-space>
      </a-col>
    </a-row>
    <a-table v-bind="tableProps" v-on="tableEvent">
      <template #columns>
        <a-table-column title="会员卡名称" data-index="name" />
        <a-table-column title="类型" data-index="cardTypeName" />
        <a-table-column v-if="cardType === 2 || cardType === 3" title="方式" data-index="userNoStr" />
        <a-table-column title="使用限制" data-index="numberStr" />
        <a-table-column title="售价" data-index="current_price" align="right">
          <template #cell="{ record }">
            <span
              v-if="
                record.is_pt_time_limit_card !== '1' &&
                (record.card_type_id === '4' || record.card_type_id === '5' || record.experience_card === '1')
              ">
              -
            </span>
            <span v-else-if="record.current_price === 0">价格面议</span>
            <span v-else>{{ record.current_price }}</span>
          </template>
        </a-table-column>
        <a-table-column title="售卖时间" data-index="sale_time" />
        <a-table-column title="在售状态" data-index="sale_status_str">
          <template #cell="{ record }">
            <a-switch
              v-if="hasAuth(record)"
              v-model="record.sale_status"
              :loading="record.loading"
              checked-value="1"
              unchecked-value="0"
              @change="handleStatusChange($event, record)" />
            <div v-else>-</div>
          </template>
        </a-table-column>
        <a-table-column title="会员端售卖" data-index="phonePay">
          <template #cell="{ record }">
            <a-switch
              v-if="record.experience_card !== '1' && hasAuth(record)"
              v-model="record.status"
              :loading="record.statusLoading"
              checked-value="1"
              unchecked-value="0"
              @change="handleSetStatusChange($event, record)" />
            <div v-else>-</div>
          </template>
        </a-table-column>
        <a-table-column v-if="cardType === 2 || cardType === 3" title="课程组" data-index="user_no">
          <template #title>
            课程组
            <a-tooltip
              content="1对多私教课程在同一课程组内可以跨课种进行预约上课，但是只能人数少课程上人数多课程，不能人数多去上人数少的课程，例如1对1课程 可以去预约1对2、1对3、1对n课程，1对3就不能去预约1对2、1对1课程">
              <icon-question-circle style="color: #ff6323" />
            </a-tooltip>
          </template>
          <template #cell="{ record }">
            <a-link v-if="record.card_class_group_name" @click="handleGroupDetail(record)">
              {{ record.card_class_group_name }}
            </a-link>
            <span v-else>-</span>
          </template>
        </a-table-column>
        <a-table-column title="操作">
          <template #cell="{ record }">
            <a-space>
              <a-link @click="goEdit(record)">编辑</a-link>
              <a-link status="danger" :disabled="!hasAuth(record)" @click="handleDelete(record)">删除</a-link>
            </a-space>
          </template>
        </a-table-column>
      </template>
    </a-table>
    <ChangeCardType
      v-model="isShowChangeCardType"
      :card-list="totalCanChangeCard"
      :card-type="cardType"
      @on-success="loadTableList()" />

    <RechargeModal v-model="isShowRechargeModel" />
    <GroupDetailModal v-model="isShowGroupDetail" :table-data="curCardGroup" :name="curCardGroupName" />
  </a-card>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { getMerchantCardList, merchantCardClassGroupList } from '@/api/card-class';
  import { getGroupCardList, getCardClassGroup } from '@/api/card-class-group';
  import { useBusInfoStore, useCardStore, useAdminInfoStore } from '@/store';
  import ExportExcel from '@/components/exportExcel.vue';
  import { Callback, ExportData } from '@/types/global';
  import {
    getCardClass,
    getCardList,
    setCardSaleStatus,
    setCardStatus,
    checkSurportOnline,
    deleteCards,
  } from '@/api/card';
  import useTableProps from '@/hooks/table-props';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { goCardRules } from '@/utils/router-go';
  import ChangeCardType from './change-card-type.vue';
  import RechargeModal from './recharge-modal.vue';
  import GroupDetailModal from './group-detail-modal.vue';

  const props = defineProps({
    cardType: {
      type: Number,
      default: 1,
    },
  });
  // 权限
  const cardStore = useCardStore();
  cardStore.setCardRuleAuth();
  const cardRuleAuth = computed(() => {
    return cardStore.cardRuleAuth;
  });
  const { cardSettingAuth } = storeToRefs(cardStore);
  function hasAuth(card: Record<string, any>) {
    const { experience_card, universal_card, card_type_id } = card;
    const { singleCard, multiCard, expCard, multiPtCard, swimCard } = cardSettingAuth.value;
    let hasAuthStatus = false;
    if (card_type_id === '5') {
      // 泳教单独控制权限
      hasAuthStatus = swimCard;
    } else {
      hasAuthStatus =
        (multiCard && universal_card === '1' && card_type_id !== '4') ||
        (multiCard && universal_card === '1' && card_type_id === '4' && multiPtCard) ||
        (expCard && experience_card === '1') ||
        (singleCard && experience_card === '0' && universal_card === '0');
    }
    return hasAuthStatus;
  }

  const adminInfo = useAdminInfoStore();
  const { IS_BRAND_SITE } = window;
  const router = useRouter();
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    IS_BRAND_SITE ? getMerchantCardList : getCardList,
    (list: Record<string, any>[]) => {
      return list.map((item: Record<string, any>) => {
        item.numberStr =
          item.number +
          (item.card_type_id === '1' || item.is_pt_time_limit_card === '1'
            ? '天'
            : item.card_type_id === '3'
            ? '元'
            : '次');
        item.sale_time =
          !item.start_end_time || !item.start_end_time.start
            ? '-'
            : `${item.start_end_time.start}~${item.start_end_time.end}`;
        item.sale_status_str = item.sale_status === '1' ? '是' : '否';
        item.phonePay = item.status === '1' ? '是' : '否';
        item.userNoStr = item.experience_card === '1' ? '-' : `1对${item.user_no}`;
        item.cardTypeName = `${item.universal_card === '1' ? '多店通用' : ''}${
          item.experience_card === '1' ? '体验卡' : ''
        }${
          item.card_type_id === '1'
            ? '期限卡'
            : item.card_type_id === '2'
            ? '次卡'
            : item.card_type_id === '3'
            ? '储值卡'
            : item.is_pt_time_limit_card === '1'
            ? '私教包月'
            : item.card_type_id === '4'
            ? '私教课'
            : '泳教课'
        }`;
        if (item.is_pt_time_limit_card !== '1' && item.card_type_id === '4' && item.experience_card === '0') {
          item.numberStr = '-';
        }
        item.current_price = +item.current_price;
        return item;
      });
    }
  );
  const cardTypeList = ref<{ name: string; value: number | string }[]>([]);
  const busInfo = useBusInfoStore();

  // 设置除分页外的其它属性值
  setSearchParam({
    card_type: props.cardType,
    bus_id: busInfo.bus_id,
    page_no: 1,
    page_size: 10,
    card_name: '',
    card_class: '',
    is_universal_card: '',
    sale_status: 1,
  });

  const supportMobileAuthority = ref(false);
  const hasMountedLife = ref(false);
  onMounted(() => {
    // 初次渲染使用mount生命周期 防止tab切换后activated已经触发 请求的数据为第一个tab的
    hasMountedLife.value = true;
    loadTableList();
    getCardClass({ card_type: props.cardType }).then((res) => {
      cardTypeList.value = res.data.value;
    });
    checkSurportOnline().then((res) => {
      supportMobileAuthority.value = res.data.value.status === 1;
    });
  });
  onActivated(() => {
    // 更新表格
    if (!hasMountedLife.value) {
      loadTableList();
    }
    hasMountedLife.value = false;
  });

  function goEdit(info: Record<string, any>) {
    router.push(`/admin/card-add/${info.id}/${props.cardType}?bus=${info.bus_id}`);
  }

  function handleSupportMobile(info: Record<string, any>) {
    if (!supportMobileAuthority.value && info.status === '1') {
      Modal.confirm({
        title: '提示',
        content: '启用在线支付购卡购课功能，需要先申请微信特约服务商',
        okText: '查看文档',
        cancelText: '取消',
        onOk: () => {
          window.open(
            'https://imagecdn.rocketbird.cn/minprogram/web-fe-v2/%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98%E7%89%B9%E7%BA%A6%E5%95%86%E6%88%B7%E7%9A%84%E7%94%B3%E8%AF%B7%E5%92%8C%E9%85%8D%E7%BD%AE.pdf'
          );
        },
      });
      setTimeout(() => {
        info.status = '0';
      }, 200);
      return false;
    }
    return true;
  }

  async function handleStatusChange(sale_status: string, info) {
    const { id, bus_id } = info;
    if (sale_status === '0') info.status = '0';
    const { isLoading, execute: setInfo } = setCardSaleStatus();
    info.loading = isLoading;
    try {
      await setInfo({ data: { id, bus_id, sale_status } });
      Message.success('设置成功！');
    } catch (error) {
      console.error(error);
      loadTableList();
    }
  }
  async function handleSetStatusChange(status: string, info) {
    const { id, bus_id } = info;
    if (handleSupportMobile(info)) {
      const { isLoading, execute: setInfo } = setCardStatus();
      info.statusLoading = isLoading;
      if (status === '1') info.sale_status = '1';
      try {
        await setInfo({ data: { id, bus_id, status } });
        Message.success('设置成功！');
      } catch (error) {
        console.error(error);
        loadTableList();
      }
    }
  }

  async function postToDelete(id: string) {
    await deleteCards(id);
    loadTableList();
    Message.success('删除成功');
  }

  function handleDelete(info: Record<string, any>) {
    const { id } = info;
    if (info.delete_notice_bus_name) {
      Modal.confirm({
        title: '删除',
        content: `删除操作将影响${info.delete_notice_bus_name}正常使用此卡，是否删除？`,
        onOk: () => {
          postToDelete(id);
        },
      });
    } else {
      postToDelete(id);
    }
  }
  // 卡种转换
  const totalCanChangeCard = ref([]);
  const isShowChangeCardType = ref(false);
  function handleCanChangeCard() {
    loadTableList(true).then((res) => {
      totalCanChangeCard.value = res ? res.filter((item) => item.is_pt_time_limit_card !== '1') : [];
      isShowChangeCardType.value = true;
    });
  }
  // 课程组
  const gruopAuth = ref(false);
  function handleGroupAuth() {
    const { execute: getGroupAuth } = IS_BRAND_SITE ? merchantCardClassGroupList(false) : getCardClassGroup(false);
    getGroupAuth({
      data: {
        check: 1,
      },
    }).then((res) => {
      gruopAuth.value = res.response.value?.errorcode === 0;
    });
  }
  handleGroupAuth();
  const isShowGroupDetail = ref(false);
  const curCardGroupName = ref('');
  const curCardGroup = ref([]);
  function handleGroupDetail(info: Record<string, any>) {
    getGroupCardList({
      bus_id: info.bus_id,
      card_class_group_id: info.card_class_group_id,
    }).then((res) => {
      curCardGroupName.value = info.card_class_group_name;
      curCardGroup.value = res.data.value;
      isShowGroupDetail.value = true;
    });
  }

  const isShowRechargeModel = ref(false);
  function goCardGroup() {
    router.push('/admin/card-group');
  }
  // 导出
  const handleClickExport = (cb: Callback<ExportData>) => {
    loadTableList(true).then((list) => {
      let columns = [
        {
          title: '场馆',
          dataIndex: 'bus_name',
        },
        {
          title: '会员卡名称',
          dataIndex: 'name',
        },
        {
          title: '卡类型',
          dataIndex: 'cardTypeName',
        },
        {
          title: '使用限制',
          dataIndex: 'numberStr',
        },
        {
          title: '售价',
          dataIndex: 'current_price',
        },
        {
          title: '售卖时间',
          dataIndex: 'sale_time',
        },
        {
          title: '在售状态',
          dataIndex: 'sale_status_str',
        },
        {
          title: '会员端售卖',
          dataIndex: 'phonePay',
        },
      ];
      if (props.cardType === 2 || props.cardType === 3) {
        columns = columns.concat([
          {
            title: '方式',
            dataIndex: 'userNoStr',
          },
          {
            title: '课程组',
            dataIndex: 'card_class_group_name',
          },
        ]);
      }
      cb({
        filename: props.cardType === 1 ? '会籍卡' : props.cardType === 2 ? '私教课' : '泳教课',
        columns,
        data: list,
      });
    });
  };
</script>
