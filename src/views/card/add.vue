<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-form ref="formRef" :model="formData" class="general-form" auto-label-width>
        <a-form-item field="cardType" label="会员卡类型">
          <a-radio-group v-model="formData.cardType" type="button" :disabled="disabledEdit" @change="handleCardType">
            <a-radio v-if="hasAuth('singleCard')" :value="0" :disabled="cardId !== 0">单店</a-radio>
            <a-radio
              v-if="msPtSwim === 2 ? hasAuth('multiCard') && showMultiPtClass : hasAuth('multiCard')"
              :value="1"
              :disabled="cardId !== 0">
              多店通用
            </a-radio>
            <a-radio v-if="hasAuth('expCard') && formData.subCardType !== 7" :value="2" :disabled="cardId !== 0">
              体验
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item field="subCardType" label="">
          <a-radio-group
            v-model="formData.subCardType"
            type="button"
            :disabled="disabledEdit"
            @change="handleSubCardType">
            <a-radio v-if="msPtSwim === 1" :value="1" :disabled="cardId !== 0">期限卡</a-radio>
            <a-radio v-if="msPtSwim === 1" :value="2" :disabled="cardId !== 0">次卡</a-radio>
            <a-radio v-if="msPtSwim === 1 && formData.cardType !== 2" :value="3" :disabled="cardId !== 0">
              储值卡
            </a-radio>
            <a-radio v-if="msPtSwim === 2" :value="4" :disabled="cardId !== 0">私教课</a-radio>
            <a-radio v-if="msPtSwim === 2 && formData.cardType !== 2" :value="7" :disabled="cardId !== 0">
              私教包月
            </a-radio>
            <a-radio v-if="showSwimClass" :value="5" :disabled="cardId !== 0">泳教课</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          field="name"
          :label="msPtSwim === 1 ? '会员卡名称' : '课程名称'"
          :rules="{ required: true, message: '名称不能为空' }">
          <a-input v-model="formData.name" :disabled="disabledEdit" />
        </a-form-item>
        <a-form-item
          v-if="(msPtSwim === 2 || msPtSwim === 3) && formData.cardType !== 2"
          field="user_no"
          label="授课方式"
          :rules="{ required: true, message: '授课方式不能为空' }">
          <a-space>
            1对
            <a-input-number
              v-model="formData.user_no"
              style="width: 100px"
              :precision="0"
              :min="1"
              :max="999"
              :disabled="!cardSettingAuth.hasUserNo"
              :step="1" />
            授课（1 位教练对
            {{ formData.user_no }}
            位会员上课）
          </a-space>
        </a-form-item>
        <CardTag v-model="formData.group_id" :disabled="disabledEdit" :ms-pt-swim="msPtSwim" />

        <a-form-item
          v-if="formData.subCardType === 2"
          label="次数"
          field="number"
          :rules="{
            match: /^[1-9]\d*$/,
            message: '次数为正整数',
          }">
          <a-input-number v-model="formData.number" :precision="0" :min="1" :step="1" :disabled="disabledEdit" />
        </a-form-item>
        <a-form-item
          v-if="formData.subCardType === 2 && formData.cardType !== 2"
          label="赠送次数"
          field="gift_number"
          :rules="{
            match: /^\d+$/,
            message: '赠送次数为正整数或0',
          }">
          <a-input-number v-model="formData.gift_number" :min="0" :step="1" :disabled="disabledEdit" />
        </a-form-item>
        <a-form-item
          v-if="(formData.subCardType === 4 || formData.subCardType === 5) && formData.cardType === 2"
          label="节数"
          field="number"
          :rules="{
            required: true,
            match: /^[1-9]\d*$/,
            message: '节数只能为正整数',
          }">
          <a-input-number v-model="formData.number" :min="1" :step="1" :disabled="disabledEdit" />
        </a-form-item>
        <a-form-item v-if="formData.subCardType === 3" label="价值金额" field="number">
          <a-input-number v-model="formData.number" :min="0" :step="0.01" :disabled="disabledEdit" />
          <template #extra>购卡时可根据实际情况进行修改</template>
        </a-form-item>
        <a-form-item
          v-if="formData.subCardType === 3 && formData.cardType !== 2"
          label="赠送金额"
          field="gift_number"
          :rules="{
            match: /^[0-9]+(.[0-9]{1,2})?$/,
            message: '金额必须大于等于0且最多只能保留两位小数',
          }">
          <a-input-number v-model="formData.gift_number" :min="0" :step="0.01" :disabled="disabledEdit" />
        </a-form-item>

        <a-form-item
          v-if="formData.cardType !== 2 && formData.subCardType !== 1 && formData.subCardType !== 7"
          :label="
            (msPtSwim === 2 || msPtSwim === 3) && formData.subCardType !== 7 && formData.cardType !== 2
              ? '单节有效期（天）'
              : '有效时限（天）'
          "
          field="end_time"
          :disabled="disabledEdit"
          :rules="{
            match: /^\d+$/,
            message: '正整数或0',
          }">
          <a-input-number v-model="formData.end_time" :precision="0" :min="0" :step="1" />
          <template #extra>为0表示永久有效</template>
        </a-form-item>
        <a-form-item
          v-else
          :label="
            (msPtSwim === 2 || msPtSwim === 3) && formData.subCardType !== 7 && formData.cardType !== 2
              ? '单节有效期（天）'
              : '有效时限（天）'
          "
          field="end_time"
          :rules="{
            match: /^[1-9]\d*$/,
            message: '正整数',
          }">
          <a-input-number v-model="formData.end_time" :min="1" :step="1" :disabled="disabledEdit" />
        </a-form-item>
        <a-form-item
          v-if="formData.subCardType === 1 && formData.cardType !== 2"
          label="赠送天数"
          field="gift_number"
          :rules="{
            match: /^\d+$/,
            message: '赠送次数为正整数或0',
          }">
          <a-input-number v-model="formData.gift_number" :min="0" :step="1" :disabled="disabledEdit" />
        </a-form-item>
        <a-form-item v-if="formData.subCardType === 7" label="每日最多上课节数" field="per_day_pt_class_num">
          <a-input-number v-model="formData.per_day_pt_class_num" :min="1" :step="1" />
        </a-form-item>
        <a-form-item v-if="formData.subCardType === 7" label="有效期内最多上课节数" field="per_all_pt_class_num">
          <a-input-number v-model="formData.per_all_pt_class_num" :min="1" :step="1" placeholder="不限" />
        </a-form-item>

        <a-form-item
          v-if="formData.subCardType === 7"
          label="消课结算单价"
          field="pt_settlement_unit_price"
          tooltip="举例：如果1~10节的结算单价是40.00/节，>10节的结算单价是20.00/节，表示的是10节以后每节单价是20.00/节">
          <PtSettlementUnitPrice v-model="formData.pt_settlement_unit_price" />
        </a-form-item>

        <a-form-item v-if="formData.cardType !== 2" label="售卖时间" field="start_end_time">
          <a-range-picker
            v-model="duringDate"
            style="width: 100%"
            :placeholder="['开始时间', '结束时间']"
            @change="handleDateChange" />
          <template #extra>不填代表无限制</template>
        </a-form-item>
        <a-form-item v-if="formData.cardType !== 2" label="允许请假" class="pedro-input-center">
          <a-space>
            <a-input-number
              v-model="formData.enable_suspend_num"
              :min="0"
              :precision="0"
              :active-change="false"
              style="width: 80px" />
            次，共
            <a-input-number
              v-model="formData.enable_suspend_day"
              :min="0"
              :precision="0"
              :active-change="false"
              style="width: 80px" />
            天
          </a-space>
        </a-form-item>
        <a-form-item v-if="formData.subCardType === 2 && formData.cardType !== 2" label="会员分享">
          <a-radio-group v-model="formData.member_share">
            <a-radio value="0">不允许</a-radio>
            <a-radio value="1">允许分享给朋友使用</a-radio>
          </a-radio-group>
          <template #extra>会员自己将卡拆分出1次分享给朋友使用</template>
        </a-form-item>
        <a-form-item
          v-show="formData.subCardType === 4 || formData.subCardType === 5 || formData.subCardType === 7"
          label="单节时长（分钟）"
          field="class_duration">
          <a-input-number v-model="formData.class_duration" :min="1" :max="9999" :disabled="disabledEdit" />
        </a-form-item>
        <template v-if="formData.cardType === 2">
          <a-form-item v-if="msPtSwim === 1" label="会籍发放" required field="membership_grant">
            <a-radio-group v-model="formData.membership_grant">
              <a-radio value="1">允许</a-radio>
              <a-radio value="0">不允许</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="转赠他人" required field="give">
            <a-radio-group v-model="formData.give">
              <a-radio value="1">允许</a-radio>
              <a-radio value="0">不允许</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            label="领取有效期（天）"
            field="activation_end"
            :rules="{ required: true, match: /^[0-9]\d*$/, message: '0或正整数' }">
            <a-input-number
              v-model="formData.activation_end"
              :min="0"
              :max="360"
              :step="1"
              :disabled="disabledEdit"
              placeholder="0~360天，0代表永久有效" />
          </a-form-item>
        </template>
        <a-form-item label="发放/使用场馆">
          <a-radio-group v-model="formData.deal_type">
            <a-radio :value="0" :disabled="cardId !== 0">按场馆添加</a-radio>
            <a-radio v-if="(cardId === 0 && adminType === '1') || cardId !== 0" :value="1" :disabled="cardId !== 0">
              按场馆组添加
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="">
          <BusCardSet
            v-if="adminBusList && formData.deal_type === 0"
            v-model="busBelongData"
            :bus-list="adminBusList"
            :add-type="formData.deal_type"
            :card-type="formData.cardType"
            :card-id="cardId"
            :sub-card-type="formData.subCardType"
            :form-validate="formData"
            :card-rule-list="cardRuleList"
            style="width: 100%"></BusCardSet>
          <BusCardSet
            v-if="busGroupList && formData.deal_type === 1 && busGroupList.length"
            v-model="busGroupBelongData"
            :group-list="busGroupList"
            :add-type="formData.deal_type"
            :card-type="formData.cardType"
            :card-id="cardId"
            :sub-card-type="formData.subCardType"
            :form-validate="formData"
            :card-rule-list="cardRuleList"
            style="width: 100%"></BusCardSet>
        </a-form-item>
        <a-form-item v-if="busGroupList && formData.deal_type === 1 && !busGroupList.length">
          暂无场馆组数据，去编辑场馆资料页面添加吧！
        </a-form-item>
        <a-form-item
          v-if="
            formData.subCardType !== 4 &&
            formData.subCardType !== 5 &&
            formData.subCardType !== 7 &&
            formData.cardType !== 2
          "
          label="代售场馆"
          field="help_sale_bus_id">
          <BusSelectMerchant
            v-model="formData.help_sale_bus_list"
            :disabled="disabledEdit"
            :max-tag-count="2"
            allow-clear
            allow-search
            multiple />
        </a-form-item>
        <RechargeFormItem
          v-if="formData.subCardType === 3"
          v-model:self-recharge="formData.self_recharge"
          v-model="formData.recharge_package" />
        <a-form-item :label="msPtSwim === 1 ? '会员卡描述' : '课程描述'" field="description">
          <FormEditor v-model="formData.description" style="width: 100%" />
        </a-form-item>
        <a-form-item label="课程封面" prop="thumb">
          <div class="image-description">
            <img v-show="formData.thumb" :src="formData.thumb" />
            <a-button type="outline" @click="uploadModal = true">选择图片</a-button>
          </div>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</a-button>
            <a-button type="secondary" @click="$router.back()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
    <ImgUpload
      v-model="uploadModal"
      title-tips=" ( 只能添加jpg，jpeg，gif，png，svg，大小不超过1MB。)"
      :options="{ fixedNumber: [690, 400] }"
      :allow-camera="false"
      @on-change="handleUpload" />

    <a-modal
      v-model:visible="visibleResultModal"
      title="卡课创建成功！"
      ok-text="关闭"
      hide-cancel
      width="720px"
      :mask-closable="false"
      @close="handleCloseResultModal"
      @ok="handleOkResultModal">
      <a-space>
        <router-link v-if="visibleAuthority.space" to="/booking/strategy-list" target="_blank">
          <a-result :status="null" title="设置排场&定价方案">
            <template #icon>
              <icon-calendar-clock style="color: #ff696a" />
            </template>
          </a-result>
        </router-link>
        <a-result v-else :status="null" title="设置排场&定价方案" @click="Message.error('没有设置排场&定价方案权限')">
          <template #icon>
            <icon-calendar-clock style="color: #ff696a" />
          </template>
        </a-result>
        <router-link v-if="visibleAuthority.open_class" to="/class/charge-plane" target="_blank">
          <a-result :status="null" title="设置团课收费方案">
            <template #icon>
              <icon-calendar style="color: #ff696a" />
            </template>
          </a-result>
        </router-link>
        <a-result v-else :status="null" title="设置团课收费方案" @click="Message.error('没有设置团课收费方案权限')">
          <template #icon>
            <icon-calendar style="color: #ff696a" />
          </template>
        </a-result>
        <router-link v-if="visibleAuthority.pt_class" :to="`/one-time-pay/list/${cardId}`" target="_blank">
          <a-result :status="null" title="设置单节付费课方案">
            <template #icon>
              <icon-bookmark style="color: #ff696a" />
            </template>
          </a-result>
        </router-link>
        <a-result v-else :status="null" title="设置单节付费课方案" @click="Message.error('没有设置单节付费课方案权限')">
          <template #icon>
            <icon-bookmark style="color: #ff696a" />
          </template>
        </a-result>
      </a-space>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { FormInstance } from '@arco-design/web-vue/es/form';
  import { Message, Modal } from '@arco-design/web-vue';
  import { getLevelList } from '@/api/business';
  import { getRuleList } from '@/api/card-sale-rule';
  import { useBusInfoStore, useCardStore } from '@/store';
  import { getCardDetail, addCard, updateCard } from '@/api/card';
  import BusSelectMerchant from '@/components/bus-select/merchant.vue';
  import FormEditor from '@/components/form/editor.vue';
  import ImgUpload from '@/components/form/img-upload.vue';
  import { CardState } from '@/store/modules/card/types';
  import { unescapeHTML } from '@/utils';
  import { getAuthority } from '@/api/one-time-pay';
  import CardTag from './components/card-tag.vue';
  import BusCardSet from './components/bus-card-set.vue';
  import RechargeFormItem from './components/recharge-form-item.vue';
  import PtSettlementUnitPrice from './components/pt-settlement-unit-price.vue';

  const route = useRoute();
  const router = useRouter();
  const busInfo = useBusInfoStore();
  const { admin_type: adminType, bus_id: busId, bus_name: busName, adminBusList } = storeToRefs(busInfo);
  const { id: paramsId, msPtSwimType } = route.params;
  const { bus } = route.query;
  const cardId = ref(Number(paramsId || 0));
  const msPtSwim = ref(Number(msPtSwimType || 1));
  const formRef = ref<FormInstance>();
  const duringDate = ref([]);
  const formData = reactive({
    is_pt_time_limit_card: '0',
    user_no: 1,
    per_day_pt_class_num: 1,
    per_all_pt_class_num: 1,
    pt_settlement_unit_price: [],
    enable_suspend_num: 0,
    enable_suspend_day: 0,
    member_share: '0',
    cardType: 0, // 会员卡类型
    subCardType: 1,
    name: '',
    group_id: '0',
    gift_number: 0,
    number: 1,
    end_time: 1,
    description: '',
    status: '0',
    commission: 0,
    sign_discount: 10,
    booking_discount: 10,
    class_duration: 60,
    buy_min_value: 1,
    help_sale_bus_list: [],
    help_sale_bus_id: '',
    belong: '',
    deal_type: 0,
    start_end_time: {
      start: '',
      end: '',
    },
    single_price: 0,
    membership_grant: '1', // 体验卡是否允许会籍发放 0不允许 1允许
    give: '1', // 体验卡是否允许转赠 0不允许 1允许
    activation_end: 0, // 体验卡有效期/天, 0 永久
    // 课程封面-选择上传图片路径
    thumb: 'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png',
    self_recharge: '0', // 开启关闭储值卡自主续充
    recharge_package: [
      // 储值卡专用，充值套餐设置
      {
        // id: ''
        key: Date.now(), // 没有id 则使用时间戳，有则为id
        isSame: false, // 用于显示隐藏校验提示
        amount: 0, // 售价
        number: 0, // 储值卡价值
        gift_number: 0, // 赠送价值
        delay_number: 0, // 延长到期天数
      },
    ],
  });
  // 卡权限
  const cardStore = useCardStore();
  cardStore.setCardAuth();
  const { cardSettingAuth } = storeToRefs(cardStore);

  function handleDateChange(val: any) {
    const [start, end] = val || ['', ''];
    formData.start_end_time.start = start;
    formData.start_end_time.end = end;
  }

  const busGroupList = ref([]);
  const cardRuleList = ref([]);
  const busBelongData = ref([
    {
      bus_id: '',
      bus_name: '',
      currentPriceFlag: false,
      enable_time_limit: 0,
      weekTimes: [],
      current_price: 0,
      status: 0,
      activation_restriction: 0,
      alipay_sale_status: 0,
      gift_number: 0,
      sign_discount: 10,
      booking_discount: 10,
      buy_min_value: 1,
      single_price: 0,
      sale_rule_id: '', // 购卡规则id
      is_open_rule: '0', // 是否开启规则 0否1是
    },
  ]);
  const busGroupBelongData = ref([
    {
      level_id: '',
      level_name: '',
      gift_number: 0,
      currentPriceFlag: false,
      enable_time_limit: 0,
      weekTimes: [],
      current_price: 0,
      sign_discount: 10,
      booking_discount: 10,
      buy_min_value: 1,
      single_price: 0,
      sale_rule_id: '', // 购卡规则id
      is_open_rule: '0', // 是否开启规则 0否1是
    },
  ]);
  function initBelongBusData() {
    const data = [
      {
        bus_id: busId,
        bus_name: busName,
        level_id: (busGroupList.value[0] && busGroupList.value[0].level_id) || '',
        level_name: (busGroupList.value[0] && busGroupList.value[0].level_name) || '',
        currentPriceFlag: false,
        enable_time_limit: 0,
        weekTimes: [],
        current_price: 0,
        alipay_sale_status: 0,
        sign_discount: 10,
        gift_number: 0,
        booking_discount: 10,
        buy_min_value: 1,
        single_price: 0,
        activation_restriction: 0,
        sale_rule_id: '', // 购卡规则id
        is_open_rule: '0', // 是否开启规则 0否1是
      },
    ];
    busGroupBelongData.value = data;
    busBelongData.value = [
      {
        ...data[0],
        status: 0,
      },
    ];
  }

  // 获取卡规则
  function getCardRuleList() {
    const { subCardType, is_pt_time_limit_card } = formData;
    const params = {
      card_type_id: subCardType === 7 ? 4 : subCardType,
      is_pt_time_limit_card,
    };
    getRuleList(params).then((res) => {
      cardRuleList.value = res.data.value.list;
    });
  }

  function handleCardType() {
    if (cardId.value === 0) {
      formData.subCardType = msPtSwim.value === 1 ? 1 : msPtSwim.value === 2 ? 4 : 5;
      initBelongBusData();
    }
  }
  function handleSubCardType(val: string | number | boolean) {
    if (cardId.value === 0) {
      if (val === 1) {
        // date of card
        formData.end_time = 1;
      } else if (val === 2) {
        // count of card
        if (formData.cardType === 2) {
          // experience of card
          formData.end_time = 1;
        } else {
          formData.end_time = 0;
        }
        formData.number = 1;
      } else if (val === 3) {
        // store of card
        formData.end_time = 0;
      } else if (val === 4) {
        formData.is_pt_time_limit_card = '0';
        formData.end_time = 1;
      } else if (val === 7) {
        formData.is_pt_time_limit_card = '1';
        formData.end_time = 30;
      }
      initBelongBusData();
    }
  }

  function getBusGroupList() {
    getLevelList().then((res) => {
      const resList = res.data.value.list;
      busGroupList.value = resList;
      busGroupBelongData.value[0].level_id = resList[0].level_id || '';
      busGroupBelongData.value[0].level_name = resList[0].level_name || '';
    });
  }
  const uploadModal = ref(false);
  // 课程封面-选择图片-确定按钮事件
  function handleUpload(path: string) {
    formData.thumb = path;
  }

  function getCardTypeName() {
    if (formData.cardType === 2) {
      return 'expCard';
    }
    if (formData.cardType === 1) {
      return 'multiCard';
    }
    if (formData.cardType === 1 && formData.subCardType === 4) {
      return 'multiPtCard';
    }
    if (formData.subCardType === 6) {
      return 'packageCard';
    }
    if (formData.subCardType === 5) {
      return 'swimCard';
    }
    return 'singleCard';
  }

  const showMultiPtClass = computed(() => {
    return cardSettingAuth.value.multiPtCard && msPtSwim.value === 2;
  });
  const showSwimClass = computed(() => {
    return cardSettingAuth.value.swimCard && msPtSwim.value === 3;
  });
  const disabledEdit = computed(() => {
    return cardId.value !== 0 && formData.is_edt !== 1;
  });
  const authorization = computed(() => {
    const cardTypeName = getCardTypeName();
    if (cardId.value === 0) {
      // 新增
      return cardSettingAuth.value[cardTypeName];
    } // 编辑
    return formData.is_edt === 1 && cardSettingAuth.value[cardTypeName];
  });
  function hasAuth(type: keyof CardState['cardSettingAuth']) {
    return cardSettingAuth.value[type] || cardId.value !== 0;
  }
  function getCardInfo(): Promise<any> {
    if (cardId.value === 0) {
      return Promise.resolve();
    }
    return getCardDetail(bus, cardId.value).then((res) => {
      const card = res.data.value;
      Object.assign(formData, card);
      if (card.is_pt_time_limit_card === '1') {
        formData.pt_settlement_unit_price = card.pt_settlement_unit_price.map((obj) => {
          const { max, min, price } = obj;
          return {
            max: max === '' ? undefined : Number(max),
            min: min === '' ? undefined : Number(min),
            price: price === '' ? undefined : Number(price),
          };
        });
      }
      if (card.universal_card === '0' && card.experience_card === '0') {
        formData.cardType = 0;
      } else if (card.universal_card === '1') {
        formData.cardType = 1;
      } else if (card.experience_card === '1') {
        formData.cardType = 2;
      }

      formData.subCardType = card.is_pt_time_limit_card === '1' ? 7 : +card.card_type_id;
      if (card.card_type_id !== '3') {
        formData.number = +card.number;
        formData.gift_number = +card.gift_number;
      } else {
        formData.number = parseFloat(card.number);
        formData.gift_number = parseFloat(card.gift_number);
        formData.self_recharge = card.self_recharge || '0'; // 储值卡自主续充
        // 续充设置数据
        const resRechargeList = card.recharge_package && JSON.parse(card.recharge_package);
        if (Array.isArray(resRechargeList) && resRechargeList.length) {
          resRechargeList.forEach((v, i) => {
            v.key = v.id || Date.now() + i;
            v.isSame = false; // 用于显示隐藏校验提示
          });
          formData.recharge_package = resRechargeList;
        } else {
          formData.recharge_package = [
            {
              // id: ''
              key: Date.now(), // 有则为id，没有id则使用时间戳
              isSame: false, // 用于显示隐藏校验提示
              amount: 0, // 售价
              number: 0, // 储值卡价值
              gift_number: 0, // 赠送价值
              delay_number: 0, // 延长到期天数
            },
          ];
        }
      }

      formData.per_day_pt_class_num = +card.per_day_pt_class_num;
      formData.per_all_pt_class_num =
        card.per_all_pt_class_num && +card.per_all_pt_class_num > 0 ? +card.per_all_pt_class_num : '';
      formData.end_time = +card.end_time;
      formData.enable_suspend_num = Number(card.enable_suspend_num);
      formData.enable_suspend_day = Number(card.enable_suspend_day);
      formData.class_duration = parseFloat(card.class_duration);
      formData.buy_min_value = parseFloat(card.buy_min_value);
      formData.activation_end = +card.activation_end;
      formData.user_no = +card.user_no;
      formData.description = unescapeHTML(card.description);
      duringDate.value = [card.start_end_time.start || '', card.start_end_time.end || ''];

      let belongBusInfo = card.belong;
      if (Array.isArray(belongBusInfo)) {
        belongBusInfo.forEach((item) => {
          if (Array.isArray(item.weekTimes)) {
            item.weekTimes.forEach((time: any) => {
              time.weeks = time.weeks.split(',');
            });
          }
          if (item.bus_id && `${item.bus_id}`.indexOf(',') !== -1) {
            item.bus_id = item.bus_id.split(',');
            item.bus_name = item.bus_name.split(',');
          }
          if (item.level_id && `${item.level_id}`.indexOf(',') !== -1) {
            item.level_id = item.level_id.split(',');
            item.level_name = item.level_name.split(',');
          }
          item.sale_rule_id = +item.sale_rule_id ? item.sale_rule_id : '';
        });
      } else {
        belongBusInfo = '';
      }
      if (formData.deal_type) {
        busGroupBelongData.value = belongBusInfo;
      } else {
        busBelongData.value = belongBusInfo;
      }

      if (!authorization.value) {
        Message.error('可查看,无编辑权限');
      }
      return card;
    });
  }

  const visibleResultModal = ref(false);
  const visibleAuthority = ref<any>({
    open_class: false,
    pt_class: false,
    space: false,
  });

  const { isLoading: submitLoading, execute: submitInfo } = cardId.value ? updateCard() : addCard();
  async function handleSubmit() {
    const errors = await formRef.value?.validate();
    if (!errors) {
      /**
       * @description
       * 01 普通期限卡 card_type_id=1
       * 02 普通次卡 card_type_id=2
       * 03 普通储值卡 card_type_id=3
       * 04 普通私教卡 card_type_id=4
       * 11 多店通用期限卡 universal_card
       * 12 多店通用次卡 universal_card
       * 13 多店通用储值卡 universal_card
       * 21 体验期限卡 experience_card
       * 22 体验次卡 experience_card
       * */
      formData.card_type_id = formData.subCardType === 7 ? 4 : formData.subCardType;
      if (formData.cardType === 1) {
        formData.universal_card = '1';
      } else {
        formData.universal_card = '0';
      }
      if (formData.cardType === 2) {
        formData.experience_card = '1';
      } else {
        formData.experience_card = '0';
        // 不是体验卡就重置体验卡专用参数， 后台默认为0
        formData.give = '0';
        formData.membership_grant = '0';
        formData.activation_end = 0;
      }
      formData.is_pt_time_limit_card = formData.subCardType === 7 ? '1' : '0';

      // 期限卡或者私教包月
      if (formData.subCardType === 1 || formData.subCardType === 7) {
        formData.number = formData.end_time;
      }
      formData.help_sale_bus_id = Array.isArray(formData.help_sale_bus_list)
        ? formData.help_sale_bus_list.join(',')
        : '';

      const postData = { ...formData };
      // 不是储值卡，删除自主续充相关数据
      if (postData.subCardType !== 3) {
        delete postData.self_recharge;
        delete postData.recharge_package;
      } else if (postData.self_recharge !== '1') {
        // 储值卡，未开启自主续充，
        delete postData.recharge_package;
      } else {
        // 开启后
        const rechargeList = postData.recharge_package.map(({ id, amount, number, gift_number, delay_number }) => {
          return { id, amount, number, gift_number, delay_number };
        });
        postData.recharge_package = JSON.stringify(rechargeList);
      }

      let belongData = JSON.parse(JSON.stringify(formData.deal_type ? busGroupBelongData.value : busBelongData.value));
      let errMsg = '';
      if (Array.isArray(belongData)) {
        belongData.forEach((item) => {
          if (item.is_open_rule === '1' && ['', null, undefined].includes(item.sale_rule_id)) {
            errMsg = '已开启购卡规则，请选择';
          }
          if (formData.cardType === 2) {
            if (formData.deal_type) {
              item.level_id = Array.isArray(item.level_id) ? item.level_id.join(',') : item.level_id;
            } else {
              item.bus_id = Array.isArray(item.bus_id) ? item.bus_id.join(',') : item.bus_id;
            }
          }
          if (
            (formData.subCardType === 4 || formData.subCardType === 5) &&
            !/^\d+$/.test(item.gift_number) &&
            formData.cardType !== 2
          ) {
            errMsg = '赠送节数只能为正整数或0';
          }
          if (formData.cardType !== 2 && Array.isArray(item.weekTimes)) {
            item.weekTimes.forEach((time) => {
              const { weeks, start_time, end_time } = time;
              if (!weeks.length || !start_time || !end_time) {
                errMsg = '请选择使用时间段';
              }
              const beginNum = +start_time.split(':').join('');
              const endNum = +end_time.split(':').join('');
              if (endNum - beginNum <= 0) {
                errMsg = '时间段错误';
              }
              time.weeks = time.weeks.join(',');
            });
          }
        });
        if (errMsg) {
          Message.error(errMsg);
          return;
        }
      } else {
        belongData = '';
      }
      postData.belong = JSON.stringify(belongData);
      submitInfo({ data: postData })
        .then((res) => {
          // if (formData.cardType === 2) {
          //   Message.success({
          //     content: '保存成功！',
          //     onClose() {
          //       router.push({ path: '/admin/card-list', query: { ttp: Date.now() } });
          //     },
          //   });
          // } else if (!cardId.value) {
          //   Message.success('保存成功！');
          //   cardId.value = res.data.value;
          //   getAuthority().then((auth) => {
          //     visibleAuthority.value = auth.data.value;
          //     visibleResultModal.value = true;
          //   });
          // } else {
          //   Message.success({
          //     content: '保存成功！',
          //     onClose() {
          //       router.push({ path: '/admin/card-list', query: { ttp: Date.now() } });
          //     },
          //   });
          // }
          Message.success({
            content: '保存成功！',
            onClose() {
              router.push({ path: '/admin/card-list', query: { ttp: Date.now() } });
            },
          });
        })
        .catch((error) => {
          console.log(error);
          if (error.errorcode === 50000) {
            Modal.confirm({
              title: '提示',
              content: '在线支付功能未开通，请联系勤鸟工作人员！',
            });
          }
        });
    }
  }

  // result modal
  const handleOkResultModal = () => {
    visibleResultModal.value = false;
  };
  const handleCloseResultModal = () => {
    router.push({ path: '/admin/card-list', query: { ttp: Date.now() } });
  };

  onMounted(async () => {
    if (busBelongData.value.length) {
      busBelongData.value[0].bus_id = busId;
      busBelongData.value[0].bus_name = busName;
    }
    formData.subCardType = msPtSwim.value === 1 ? 1 : msPtSwim.value === 2 ? 4 : 5;
    /* 如果不是会籍卡，不允许会籍发放 */
    if (msPtSwim.value !== 1) formData.membership_grant = '0';
    if (cardId.value === 0) {
      if (!cardSettingAuth.value) {
        router.push({ path: '/v2/management/card' });
        return;
      }
      const tabs = ['singleCard', 'multiCard', 'expCard'];
      const index = tabs.findIndex((value) => cardSettingAuth.value[value]);
      if (index !== -1) {
        formData.cardType = index;
      }
    }
    await getBusGroupList();
    if (!adminBusList.value.length) {
      busInfo.setAdminBusList().then(async () => {
        await getCardInfo();
      });
    } else {
      await getCardInfo();
    }
    watch(
      () => [formData.subCardType, formData.is_pt_time_limit_card],
      (val) => {
        getCardRuleList();
      },
      { immediate: true }
    );
  });
</script>

<style lang="less" scoped>
  .image-description {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    img {
      width: 345px;
      height: 200px;
      margin-bottom: 10px;
    }
  }
</style>
