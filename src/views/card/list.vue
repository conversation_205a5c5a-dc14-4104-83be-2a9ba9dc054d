<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-tabs v-model:active-key="activeKey" lazy-load @change="handleTabChange">
        <a-tab-pane key="0" title="会籍卡">
          <cardList :card-type="1" />
        </a-tab-pane>
        <a-tab-pane key="1" title="私教课">
          <cardList :card-type="2" />
        </a-tab-pane>
        <a-tab-pane v-if="cardSettingAuth.swimCard" key="2" title="泳教课">
          <cardList :card-type="3" />
        </a-tab-pane>
        <a-tab-pane v-if="cardSettingAuth.packageCard" key="3" title="套餐包">
          <packageList />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { useCardStore } from '@/store';
  import cardList from './components/card-list.vue';
  import packageList from './components/package-list.vue';

  defineOptions({
    name: 'CardList',
  });
  const cardStore = useCardStore();
  cardStore.setCardAuth();
  const { cardSettingAuth } = storeToRefs(cardStore);
  const activeKey = ref('0');
  const handleTabChange = (key: string) => {
    activeKey.value = key;
    sessionStorage.setItem('cardListActive', key);
  };
  onMounted(() => {
    const index = sessionStorage.getItem('cardListActive');
    if (index) {
      activeKey.value = index;
    }
  });
</script>
