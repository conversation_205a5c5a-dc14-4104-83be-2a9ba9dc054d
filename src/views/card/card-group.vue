<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="belong_bus_id" label="场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" placeholder="请选择" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="课程组名称">
                  <a-input v-model="searchParam.search" allow-clear @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="课程组类型">
                  <a-select v-model="searchParam.type" allow-clear placeholder="请选择">
                    <a-option :value="1">私教课组别</a-option>
                    <a-option :value="2">泳教课组别</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="isShowSetGroupModal = true">设置课种分组</a-button>
            <a-button type="primary" @click="isShowAddGroupModal = true">新增组别</a-button>
          </a-space>
        </a-col>
        <a-col :span="12" align="end">
          <a-space>
            <ExportButton
              :data="exportPostParams"
              :url="IS_BRAND_SITE ? '/Merchant/CardClass/card_class_group_excel' : '/Web/CardClassGroup/excel'" />
          </a-space>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column title="场馆" data-index="bus_name" :width="150" ellipsis />
          <a-table-column title="课程组名称" data-index="name" :width="150" />
          <a-table-column title="类型" data-index="type_name" :width="120" />
          <a-table-column title="课种" data-index="card_names" ellipsis>
            <template #cell="{ record }">
              <a-link @click="handleShowGroupDetail(record)">{{ record.card_names }}</a-link>
            </template>
          </a-table-column>
          <a-table-column title="操作" :width="120">
            <template #cell="{ record }">
              <a-space>
                <a-link status="danger" @click="handleDelete(record)">删除</a-link>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <AddGroupModal v-model="isShowAddGroupModal" :bus-id="searchParam.bus_id" @on-success="loadTableList()" />
    <SetGroupModal v-model="isShowSetGroupModal" :bus-id="searchParam.bus_id" @on-success="loadTableList()" />
    <GroupDetailModal v-model="isShowGroupDetail" :table-data="curCardGroup" :name="curCardGroupName" />
  </div>
</template>

<script lang="ts" setup>
  import { Modal, Message } from '@arco-design/web-vue';
  import { useBusInfoStore } from '@/store';
  import { getCardClassGroup, delCardClassGroup } from '@/api/card-class-group';
  import { merchantCardClassGroupList, delMerchantCardClassGroup } from '@/api/card-class';
  import useTableProps from '@/hooks/table-props';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import ExportButton from '@/components/form/export-button.vue';
  import SetGroupModal from './components/set-group-modal.vue';
  import AddGroupModal from './components/add-group-modal.vue';
  import GroupDetailModal from './components/group-detail-modal.vue';

  const { IS_BRAND_SITE } = window;
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    IS_BRAND_SITE ? merchantCardClassGroupList : getCardClassGroup,
    (list) => {
      return list.map((item) => {
        item.type_name = item.type === '1' ? '私教课组别' : '泳教课组别';
        item.card_names = item.card_list?.map((item2: any) => item2.card_name).join(',');
        return item;
      });
    }
  );
  const busInfo = useBusInfoStore();

  // 设置除分页外的其它属性值
  setSearchParam({
    bus_id: busInfo.bus_id,
    type: '',
    search: '',
  });
  const exportPostParams = computed(() => {
    return {
      ...searchParam,
      current: 1,
      pageSize: tableProps.value.pagination.total,
    };
  });
  onMounted(() => {
    loadTableList();
  });

  async function postToDelete(card_class_group_id: string, bus_id = '') {
    if (IS_BRAND_SITE) {
      await delMerchantCardClassGroup({ card_class_group_id, bus_id });
    } else {
      await delCardClassGroup({ card_class_group_id, bus_id });
    }
    loadTableList();
    Message.success('删除成功');
  }

  function handleDelete(info: Record<string, any>) {
    const { id, bus_id } = info;
    Modal.confirm({
      title: '删除组别',
      content: '组别下全部课种将被设置成未分组，是否要删除？',
      onOk: () => {
        postToDelete(id, bus_id);
      },
    });
  }
  // 添加课程组
  const isShowAddGroupModal = ref(false);
  // 设置课程组
  const isShowSetGroupModal = ref(false);
  // 课种详情
  const isShowGroupDetail = ref(false);
  const curCardGroup = ref([]);
  const curCardGroupName = ref('');
  function handleShowGroupDetail(info: Record<string, any>) {
    curCardGroup.value = info.card_list;
    curCardGroupName.value = info.name;
    isShowGroupDetail.value = true;
  }
</script>
