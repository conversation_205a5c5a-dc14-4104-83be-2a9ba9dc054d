<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="bus_id" label="场馆">
                  <BusSelectAdmin v-model="formModel.bus_id" placeholder="请选择" />
                  <!-- <a-select
                    v-model="formModel.bus_id"
                    allow-search
                    allow-clear
                    :options="storeList"
                    :virtual-list-props="{ height: 200 }"
                    :threshold="200"
                    placeholder="请选择"
                  >
                  </a-select> -->
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="查询">
                  <a-input v-model="formModel.search" placeholder="业务单号/姓名/电话" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="rangeValue" label="时间">
                  <a-range-picker
                    v-model="formModel.rangeValue"
                    :allow-clear="false"
                    format="YYYY-MM-DD"
                    shortcuts-position="left"
                    :shortcuts="shortcuts"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="type" label="类型">
                  <a-select v-model="formModel.type" allow-clear placeholder="请选择">
                    <a-option value="1">支出</a-option>
                    <a-option value="0">收入</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="pay_status" label="线上/线下">
                  <a-select v-model="formModel.pay_status" allow-clear placeholder="请选择">
                    <a-option value="0">线下</a-option>
                    <a-option value="1">线上</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="pay_type" label="支付方式">
                  <PayTypeSelect
                    v-model="formModel.pay_type"
                    show-card-pay
                    :bus-id="formModel.bus_id"
                    placeholder="请选择"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="related" label="业务关联">
                  <a-select v-model="formModel.related" allow-clear placeholder="请选择">
                    <a-option value="1">已关联</a-option>
                    <a-option value="0">未关联</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>

        <a-divider style="height: 120px" direction="vertical" />

        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" :loading="isLoading" @click="search">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <!-- <a-button @click="reset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button> -->
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />

      <template v-if="totalStatList.length">
        <a-scrollbar type="track" style="height: 98px; overflow-x: auto">
          <a-space :size="10">
            <a-statistic
              v-for="(item, index) in totalStatList"
              :key="index"
              class="finance-statistics"
              :class="{
                'finance-income': index === 0 && item.name === '收入',
                'finance-expenditure': index === 1 && item.name === '支出',
              }"
              :title="item.name"
              :value="Number(item.amount)"
              :start="false"
              :precision="2"
              :animation-duration="800"
              show-group-separator
              animation
            ></a-statistic>
          </a-space>
        </a-scrollbar>
        <a-divider style="margin-top: 0" />
      </template>

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space> </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button :loading="isLoading" @click="handleClickExport(handleExport as Callback<ExportData>)">
                导出
              </a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />

      <a-table
        row-key="id"
        :loading="isLoading"
        :pagination="tablePagination"
        :columns="(cloneColumns as TableColumnData[])"
        :data="renderData"
        :bordered="false"
        :size="size"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #flow_sn="{ record }">
          <template v-if="record.flow_sn">
            <a-link
              v-for="(item, index) in record.flow_sn.split(',')"
              :key="index"
              style="margin: 2px; white-space: normal; color: #07abf2"
              @click="handleToDetail(record, item, index)"
            >
              {{ item }}
            </a-link>
          </template>
          <span v-else>-</span>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { computed, ref, reactive, watch, nextTick } from 'vue';
  import cloneDeep from 'lodash/cloneDeep';
  import Sortable from 'sortablejs';

  // import useLoading from '@/hooks/loading';

  import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import type { PaginationProps } from '@arco-design/web-vue/es/pagination/interface';
  import type { ShortcutType } from '@arco-design/web-vue/es/date-picker/interface';
  import { getReceiveLog } from '@/api/statistics';
  import ExportExcel from '@/components/exportExcel.vue';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import PayTypeSelect from '@/components/form/payTypeSelect.vue';
  import { AnyObject, Callback, ExportData } from '@/types/global';

  type SizeProps = 'mini' | 'small' | 'medium' | 'large';
  type Column = TableColumnData & { checked?: true };

  const route = useRoute();

  const generateFormModel = () => {
    const today = new Date();
    return {
      bus_id: '',
      search: '', // 业务单号/姓名/电话
      type: '', // 0.收入 1.支出
      pay_type: '',
      pay_status: '', // 是否为线上支付方式，0：线下；1：线上
      related: '', // 业务关联 0 已关联 1 未关联
      begin_date: '',
      end_date: '',
      rangeValue: [today, today], //
    };
  };

  const renderData = ref<AnyObject[]>([]);
  const totalStatList = ref<{ name: string; amount: string }[]>([]);
  const formModel = ref(generateFormModel());
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);

  const size = ref<SizeProps>('medium');

  const pagination: PaginationProps = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const tablePagination = computed(() => ({
    ...pagination,
    showPageSize: true,
    showTotal: true,
  }));

  const shortcuts = ref<ShortcutType[]>([
    {
      label: '今天',
      value: () => [dayjs(), dayjs()],
    },
    {
      label: '一周',
      value: () => [dayjs().subtract(1, 'week'), dayjs()],
    },
    {
      label: '一个月',
      value: () => [dayjs().subtract(30, 'day'), dayjs()],
    },
  ]);

  const columns = computed<TableColumnData[]>(() => [
    {
      title: '流水单号',
      dataIndex: 'serial_number',
    },
    {
      title: '渠道单号',
      dataIndex: 'order_no_text',
    },
    {
      title: '时间',
      dataIndex: 'create_time',
    },
    {
      title: '金额',
      dataIndex: 'amount',
    },
    {
      title: '线上/线下',
      dataIndex: 'is_online_text',
    },
    {
      title: '支付方式',
      dataIndex: 'pay_type_name',
    },
    {
      title: '会员',
      dataIndex: 'username_text',
    },
    {
      title: '关联业务单号',
      dataIndex: 'flow_sn',
      slotName: 'flow_sn',
      width: 200,
    },
  ]);

  const { isLoading, execute } = getReceiveLog();

  const fetchData = async (params: any, isExport = false) => {
    if (params.rangeValue.length) {
      params.begin_date = dayjs(params.rangeValue[0]).format('YYYY-MM-DD');
      params.end_date = dayjs(params.rangeValue[1]).format('YYYY-MM-DD');
      delete params.rangeValue;
    }
    const { data } = await execute({
      data: params,
    });

    const list: AnyObject[] = data.value.list || [];
    list.forEach((v) => {
      v.order_no_text = v.order_no ? v.order_no : '-';
      v.is_online_text = v.is_online === '1' ? '线上' : '线下';
      v.username_text = v.username || '-';
    });

    if (isExport) {
      return Promise.resolve(list);
    }

    pagination.total = +data.value.count;
    renderData.value = list;
    totalStatList.value = data.value.total_stat;

    return Promise.resolve(list);
  };

  const handleClickExport = (cb: Callback<ExportData>) => {
    fetchData(
      {
        ...formModel.value,
        current: 1,
        pageSize: pagination.total,
      },
      true
    ).then((list) => {
      cb({
        filename: (route.matched[route.matched.length - 1].meta.locale as string | undefined) || '收银流水',
        columns: columns.value,
        data: list,
      });
    });
  };

  const search = () => {
    pagination.current = 1;
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...formModel.value,
    });
  };
  const onPageChange = (current: number) => {
    pagination.current = current;
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const onPageSizeChange = (pageSize: number) => {
    pagination.current = 1;
    pagination.pageSize = pageSize;
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const reset = () => {
    formModel.value = generateFormModel();
  };

  const handleSelectDensity = (val: string | number | Record<string, any> | undefined, e: Event) => {
    size.value = val as SizeProps;
  };

  const handleChange = (checked: boolean | (string | boolean | number)[], column: Column, index: number) => {
    if (!checked) {
      cloneColumns.value = showColumns.value.filter((item) => item.dataIndex !== column.dataIndex);
    } else {
      cloneColumns.value.splice(index, 0, column);
    }
  };

  const exchangeArray = <T extends Array<any>>(array: T, beforeIdx: number, newIdx: number, isDeep = false): T => {
    const newArray = isDeep ? cloneDeep(array) : array;
    if (beforeIdx > -1 && newIdx > -1) {
      // 先替换后面的，然后拿到替换的结果替换前面的
      newArray.splice(beforeIdx, 1, newArray.splice(newIdx, 1, newArray[beforeIdx]).pop());
    }
    return newArray;
  };

  const popupVisibleChange = (val: boolean) => {
    if (val) {
      nextTick(() => {
        const el = document.getElementById('tableSetting') as HTMLElement;
        const sortable = new Sortable(el, {
          onEnd(e: any) {
            const { oldIndex, newIndex } = e;
            exchangeArray(cloneColumns.value, oldIndex, newIndex);
            exchangeArray(showColumns.value, oldIndex, newIndex);
          },
        });
      });
    }
  };

  const router = useRouter();
  const handleToDetail = (record: any, flowSn: string, index: number) => {
    router.push({
      // 业务流水
      name: 'Inventory',
      query: {
        search: flowSn,
        beginTime: record.deal_time && record.deal_time[index],
        endTime: record.deal_time && record.deal_time[index],
        busId: formModel.value.bus_id,
      },
    });
  };

  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );

  formModel.value.bus_id = (route.params.busId as string) || '';
  if (route.params.beginDate && route.params.endDate) {
    formModel.value.rangeValue = [route.params.beginDate as any, route.params.endDate as any];
  }
  fetchData({
    ...formModel.value,
    current: pagination.current,
    pageSize: pagination.pageSize,
  });
</script>

<style scoped lang="less">
  .base-box {
    padding: 0 20px 20px 20px;
  }
</style>
