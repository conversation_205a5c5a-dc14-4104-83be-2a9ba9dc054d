<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="search" label="场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" placeholder="场馆" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="查询">
                  <a-input
                    v-model="searchParam.search"
                    placeholder="业务单号/姓名/电话"
                    allow-clear
                    @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="时间">
                  <a-range-picker
                    v-model="rangeValue"
                    :allow-clear="false"
                    shortcuts-position="left"
                    :shortcuts="shortcuts"
                    @change="handleTimeChange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="类型">
                  <a-select v-model="searchParam.flow_type" class="option-select" placeholder="请选择" allow-clear>
                    <a-option v-for="item in flowTypeList" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="operate_type" label="事项">
                  <a-select v-model="searchParam.operate_type" class="option-select" placeholder="请选择" allow-clear>
                    <a-option v-for="item in operateTypeList" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="sale_id" label="销售人员">
                  <AdminRegion
                    v-model="searchParam.sale_id"
                    url="/Web/Statistics/getGroupSales"
                    :bus-id="searchParam.bus_id"
                    :multiple="false"
                    :has-store="true"
                    :singular-primary-key="true"
                    allow-clear
                    placeholder="请选择" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="pay_type" label="付费方式">
                  <PayTypeSelect
                    v-model="searchParam.pay_type"
                    show-card-pay
                    :bus-id="searchParam.bus_id"
                    placeholder="请选择" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <template v-if="totalStat.length">
        <a-scrollbar type="track" style="height: 98px; overflow-x: auto">
          <a-space :size="10">
            <a-statistic
              v-for="(item, index) in totalStat"
              :key="index"
              class="finance-statistics"
              :title="item.pay_name"
              :value="Number(item.pay_amount)"
              :start="false"
              :precision="2"
              :animation-duration="800"
              show-group-separator
              animation></a-statistic>
          </a-space>
        </a-scrollbar>
        <a-divider style="margin-top: 0" />
      </template>
      <a-row style="margin-bottom: 16px">
        <a-col :span="12"></a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportButton v-if="IS_BRAND_SITE" :data="exportPostParams" url="/merchant/statistics/getFinancialFlowNew" />
          <ExportExcel v-else ref="exportExcelRef">
            <a-button :loading="isExporting || isExportingTwo" @click="handleClickExport()">导出</a-button>
          </ExportExcel>
        </a-col>
      </a-row>
      <a-table v-bind="tableProps" v-on="tableEvent">
        <template #columns>
          <a-table-column title="业务单号" data-index="flow_sn" />
          <a-table-column title="时间" data-index="deal_time" />
          <a-table-column title="事项" data-index="operate_type" />
          <a-table-column title="会员" data-index="username">
            <template #cell="{ record }">
              <div v-if="!record.is_real_user">{{ record.username || '散客' }}</div>
              <a-link
                v-else
                :href="goSubDetail(record.user_id, record.bus_id, false)"
                @click.prevent="goSubDetail(record.user_id, record.bus_id)">
                {{ record.username }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="应收金额" data-index="amount" align="right" />
          <a-table-column title="定金抵扣" data-index="pre_payment" align="right" />
          <a-table-column title="实收" data-index="income_amount" align="right" />
          <a-table-column title="描述" data-index="description" ellipsis tooltip />
          <a-table-column title="销售人员" data-index="sale">
            <template #cell="{ record }">
              <a-link @click="handleViewSales(record)">{{ record.sale }}</a-link>
            </template>
          </a-table-column>
          <a-table-column title="付费方式" data-index="sale">
            <template #cell="{ record }">
              <a-link @click="handleViewPay(record)">{{ record.payType }}</a-link>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <OrderDetailModal v-model="isShowOrderDetail" :table-data="orderDetailList" />
    <PayDetailModal v-model="isShowPayDetail" :table-data="payDetailList" />
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import type { ShortcutType } from '@arco-design/web-vue/es/date-picker/interface';
  import { goSubDetail } from '@/utils/router-go';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import { getFinancialFlow, getBusinessLog, exportFlow, tableTwo } from '@/api/statistics';
  import AdminRegion from '@/components/form/adminRegion.vue';
  import PayTypeSelect from '@/components/form/payTypeSelect.vue';
  import useTableProps from '@/hooks/table-props';
  import { useBusInfoStore, usePayStore } from '@/store';
  import ExportExcel from '@/components/exportExcel.vue';
  import ExportButton from '@/components/form/export-button.vue';
  import OrderDetailModal from './components/order-detail-modal.vue';
  import PayDetailModal from './components/pay-detail-modal.vue';

  defineOptions({
    name: 'Inventory',
  });
  const { IS_BRAND_SITE } = window;
  const shortcuts = ref<ShortcutType[]>([
    {
      label: '今天',
      value: () => [dayjs(), dayjs()],
    },
    {
      label: '一周',
      value: () => [dayjs().subtract(1, 'week'), dayjs()],
    },
    {
      label: '一个月',
      value: () => [dayjs().subtract(30, 'day'), dayjs()],
    },
  ]);
  const totalStat = ref([]);
  const busInfo = useBusInfoStore();
  const { bus_id: storeBusId, bus_name: busName } = storeToRefs(busInfo);
  const saleCount = ref(0);
  const payInfo = usePayStore();
  const { payTypes } = storeToRefs(payInfo);
  const hasAmountPayTypeIds = ref([]);
  function packRow(item, isExp) {
    const saleArr = {};
    if (item.marketers_detail && item.marketers_detail.length > saleCount.value) {
      saleCount.value = item.marketers_detail.length;
    }
    let sale = '';
    if (item.marketers_detail) {
      item.marketers_detail.forEach((m, i) => {
        saleArr[`sale${i}`] = m.name;
        saleArr[`saleRate${i}`] = m.percent;
        saleArr[`saleAmount${i}`] = m.amount;
      });
      item.marketers_detail.forEach((m, i) => {
        sale += m.name + m.percent;
        if (i + 1 !== item.marketers_detail.length) {
          sale += '; ';
        }
      });
    }

    let payType = '';
    if (isExp) {
      payTypes.value.forEach((p, i) => {
        saleArr[Number(p.pay_type_id)] = 0;
        item.pay_detail.forEach((subP, subI) => {
          if (hasAmountPayTypeIds.value.indexOf(Number(subP.pay_type_id)) === -1 && subP.pay_type_id) {
            hasAmountPayTypeIds.value.push(Number(subP.pay_type_id));
          }
          if (Number(p.pay_type_id) === Number(subP.pay_type_id)) {
            saleArr[Number(p.pay_type_id)] = Number(saleArr[Number(p.pay_type_id)])
              ? (Number(saleArr[Number(p.pay_type_id)]) + Number(subP.amount)).toFixed(2)
              : subP.amount;
          }
        });
      });
    } else {
      item.pay_detail.forEach((p, i) => {
        saleArr[Number(p.pay_type_id)] = p.amount || 0;
        payType += p.pay_type + p.amount;
        if (i + 1 !== item.pay_detail.length) {
          payType += '; ';
        }
      });
    }
    return {
      ...item,
      payType,
      ...saleArr,
      sale,
    };
  }
  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } = useTableProps(
    IS_BRAND_SITE ? getBusinessLog : getFinancialFlow,
    (list) => {
      return list.map((item) => {
        return packRow(item, false);
      });
    },
    (res) => {
      totalStat.value = res.data.value.total_stat;
    }
  );

  const flowTypeList = [
    { value: 1, label: '收入' },
    { value: 2, label: '支出' },
  ];
  const operateTypeList = [
    { value: 0, label: '购卡' },
    { value: 1, label: '续卡' },
    { value: 21, label: '购私教' },
    { value: 22, label: '续私教' },
    { value: 23, label: '购泳教' },
    { value: 24, label: '续泳教' },
    { value: 2, label: '升卡' },
    { value: 3, label: '转入卡' },
    { value: 5, label: '请假' },
    { value: 6, label: '销卡' },
    { value: 7, label: '租柜' },
    { value: 8, label: '商品售卖' },
    { value: 27, label: '商品退款' },
    { value: 9, label: '定金' },
    { value: 10, label: '退定金' },
    { value: 11, label: '押金' },
    { value: 12, label: '退押金' },
    { value: 13, label: '付费团课' },
    { value: 15, label: '补卡' },
    { value: 16, label: '付费活动' },
    { value: 17, label: '拆分' },
    { value: 18, label: '跨店购卡' },
    { value: 19, label: '场地预订' },
    { value: 20, label: '场地退订' },
    { value: 29, label: '票务' },
  ];
  const route = useRoute();
  const rangeValue = ref<string[]>([]);
  const exportPostParams = computed(() => {
    const params: any = {
      ...searchParam,
      is_export: 1,
      current: 1,
      pageSize: tableProps.value.pagination.total,
    };
    return params;
  });
  rangeValue.value = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
  setSearchParam({
    bus_id: storeBusId.value || '',
    begin_date: dayjs().format('YYYY-MM-DD'),
    end_date: dayjs().format('YYYY-MM-DD'),
    flow_type: '',
    pay_type: '',
    search: '',
    sale_id: '',
    operate_type: '',
  });
  onActivated(() => {
    // 判断是否有参数
    if (route.query && Object.keys(route.query).length > 0) {
      const { beginTime, endTime, busId, search: searchText } = route.query || {};
      rangeValue.value = [beginTime || searchParam.begin_date, endTime || searchParam.end_date];
      searchParam.begin_date = beginTime || searchParam.begin_date;
      searchParam.end_date = endTime || searchParam.end_date;
      searchParam.bus_id = busId || searchParam.bus_id;
      searchParam.search = searchText || searchParam.search;
    }

    loadTableList();
  });

  function handleTimeChange(params: any) {
    const [begin, end] = params || ['', ''];
    searchParam.begin_date = begin;
    searchParam.end_date = end;
  }

  const isShowOrderDetail = ref(false);
  const orderDetailList = ref([]);
  function handleViewSales(record: Record<string, any>) {
    orderDetailList.value = record.marketers_detail;
    isShowOrderDetail.value = true;
  }

  const isShowPayDetail = ref(false);
  const payDetailList = ref([]);
  function handleViewPay(record: Record<string, any>) {
    payDetailList.value = record.pay_detail;
    isShowPayDetail.value = true;
  }
  const exportExcelRef = ref();
  const { isLoading: isExporting, execute: exportData } = exportFlow();
  const { isLoading: isExportingTwo, execute: exportDataTwo } = tableTwo();
  function getOneData(params) {
    return exportData({ data: params }).then((res) => {
      const resData = res.data.value;
      if (!Array.isArray(resData.list)) {
        return { exColumns: [], data: [] };
      }
      const arr = [];
      resData.list.forEach((item) => {
        if (item.flow_category === '定金') {
          item.marketers_detail.forEach((little) => {
            little.percent = '';
            little.amount = '';
          });
        }
        arr.push(packRow(item, true));
      });

      const all = resData.total_stat;
      const totalObject = {};

      all.forEach((item) => {
        if (item.pay_type_id) {
          totalObject[item.pay_type_id] = item.pay_amount;
        }
      });
      arr.push({});
      arr.push({
        ...totalObject,
        deal_time: '总计',
        flow_type: `实收:${all[0].pay_amount}; 支出:${all[1].pay_amount}`,
        income_amount: all[0].pay_amount,
      });
      const exColumns = [
        { title: '时间', dataIndex: 'deal_time' },
        { title: '业务单号', dataIndex: 'flow_sn' },
        { title: '流水类型', dataIndex: 'flow_category' },
        { title: '合同编号', dataIndex: 'custom_order_sn' },
        { title: '支出/收入', dataIndex: 'flow_type' },
        { title: '事项', dataIndex: 'operate_type' },
        { title: '卡种', dataIndex: 'card_name' },
        { title: '会员', dataIndex: 'username' },
        { title: '应收金额', dataIndex: 'amount' },
        { title: '定金抵扣', dataIndex: 'pre_payment' },
        { title: '实收', dataIndex: 'income_amount' },
        { title: '描述', dataIndex: 'description' },
        { title: '备注', dataIndex: 'remark' },
      ];
      payTypes.value.forEach((item) => {
        if (item.usable !== 0 || hasAmountPayTypeIds.value.indexOf(Number(item.pay_type_id)) !== -1) {
          exColumns.push({ title: item.pay_type_name, dataIndex: item.pay_type_id });
        }
      });

      if (saleCount.value > 0) {
        for (let i = 0; i < saleCount.value; i += 1) {
          exColumns.push({ title: '销售人员', dataIndex: `sale${i}` });
          exColumns.push({ title: '比例', dataIndex: `saleRate${i}` });
          exColumns.push({ title: '金额', dataIndex: `saleAmount${i}` });
        }
      }
      return { exColumns, data: arr };
    });
  }
  function getTwoData(params) {
    return exportDataTwo({ data: params }).then((res) => {
      const resData = res.response.value;
      const list = [];
      if (!Array.isArray(resData.data)) {
        return { exColumnsTwo: [], dataTwo: [] };
      }
      const curBetweenDate = `${searchParam.begin_date}至${searchParam.end_date}`;
      resData.data.forEach((item) => {
        item.curBetweenDate = curBetweenDate;
        list.push(packRow(item, true));
      });
      list.push({});
      const all = resData.total.pay_type_amount;
      const totalObject = {};
      all.forEach((item) => {
        if (item.pay_type_id) {
          totalObject[item.pay_type_id] = item.amount;
        }
      });
      list.push({
        ...resData.total,
        ...totalObject,
        curBetweenDate: '总计',
      });

      const exColumnsTwo = [
        { title: '日期', dataIndex: 'curBetweenDate' },
        { title: '事项', dataIndex: 'name' },
        { title: '会员卡', dataIndex: 'card_name' },
        { title: '数量', dataIndex: 'count' },
        { title: '应收', dataIndex: 'amount' },
        { title: '定金抵扣', dataIndex: 'pre_payment' },
        { title: '实收', dataIndex: 'income_amount' },
      ];
      payTypes.value.forEach((item) => {
        if (item.usable !== 0 || hasAmountPayTypeIds.value.indexOf(Number(item.pay_type_id)) !== -1) {
          exColumnsTwo.push({ title: item.pay_type_name, dataIndex: item.pay_type_id });
        }
      });
      return { exColumnsTwo, dataTwo: list };
    });
  }
  const handleClickExport = () => {
    const params = {
      ...searchParam,
      page_no: 1,
      page_size: tableProps.value.pagination.total,
    };
    getOneData(params).then(({ exColumns, data }) => {
      exportExcelRef.value.handleExport({
        filename: `日报流水-${dayjs().format('YYYY-MM-DD')}`,
        columns: exColumns,
        data,
      });
    });
    if (busName.value.indexOf('上海交通大学') !== -1) {
      getTwoData(params).then(({ exColumnsTwo, dataTwo }) => {
        exportExcelRef.value.handleExport({
          filename: `上海交大-日报流水-${dayjs().format('YYYY-MM-DD')}`,
          columns: exColumnsTwo,
          data: dataTwo,
        });
      });
    }
  };
</script>

<style lang="less" scoped>
  .box-body-total {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .t-label {
      width: 200px;
      padding: 0 40px;
      font-size: 16px;
      font-weight: bold;
    }

    .t-desc {
      width: 70%;
      max-width: 650px;
      font-size: 16px;
      display: flex;
      flex-wrap: wrap;
      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
        width: 25%;
        display: flex;
      }
    }
  }
</style>
