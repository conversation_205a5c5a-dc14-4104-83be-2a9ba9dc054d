<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="rangeValue" label="时间">
                  <a-range-picker
                    v-model="formModel.rangeValue"
                    :allow-clear="false"
                    format="YYYY-MM-DD"
                    shortcuts-position="left"
                    :shortcuts="shortcuts"
                    :disabled-date="getDisabledDate"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="bus_id" label="商家场馆">
                  <BusSelectAdmin
                    v-model="formModel.bus_id"
                    placeholder="请选择"
                    @update:model-value="handleBusChange"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" :loading="isLoading" @click="search">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" :loading="isLoading" @click="editTableFlag = true"> 设置表格样式 </a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <a-space>
            <ExportExcel>
              <template #default="{ handleExport }">
                <a-button :loading="isLoading" @click="handleClickExport(handleExport as Callback<ExportData>)">
                  导出
                </a-button>
              </template>
            </ExportExcel>
          </a-space>
        </a-col>
      </a-row>

      <a-table
        :loading="isLoading"
        :pagination="false"
        :columns="(cloneColumns as TableColumnData[])"
        :data="renderData"
        :bordered="false"
        :size="size"
        @page-change="onPageChange"
      >
      </a-table>
    </a-card>

    <a-modal
      v-model:visible="editTableFlag"
      title="更改表格样式"
      :width="800"
      :modal-style="{ margin: '120px 0' }"
      @ok="handleSaveClick"
    >
      <a-table :columns="defaultVerticalRowColumn" :data="showRowData" :pagination="false">
        <template #optionalName="{ record }">
          <span v-show="!record.editFlag">{{ record.group_name }}</span>
          <a-input v-show="record.editFlag" v-model="record.group_name" />
        </template>
        <template #optionalValue="{ record }">
          <span v-if="!record.editFlag">{{ record.value }}</span>
          <template v-else>
            <a-select v-model="record.ids" multiple placeholder="请选择统计项" @change="rowComb()">
              <template v-for="item in rowArr" :key="item.id">
                <a-option
                  :value="item.id"
                  :label="item.name"
                  :disabled="record.ids.indexOf(item.id) === -1 && item.disabled"
                ></a-option>
              </template>
            </a-select>
          </template>
        </template>
        <template #optional="{ record, rowIndex }">
          <div style="display: flex; justify-content: space-around">
            <a-button v-show="record.editFlag" type="primary" @click="handleComplete(1, record)">完成</a-button>
            <a-button v-show="!record.editFlag" type="primary" @click="record.editFlag = true">编辑</a-button>
            <a-button type="primary" @click="handleRowDelete(record, rowIndex)">删除</a-button>
          </div>
        </template>
      </a-table>
      <a-button type="primary" style="margin: 20px 0" @click="handleAddRowClick">添加</a-button>
      <a-table :columns="defaultHorizontalRowColumn" :data="showColData" :pagination="false">
        <template #optionalName="{ record }">
          <span v-show="!record.editFlag">{{ record.group_name }}</span>
          <a-input v-show="record.editFlag" v-model="record.group_name" />
        </template>
        <template #optionalCategory="{ record }">
          <span v-if="!record.editFlag">{{ record.value }}</span>
          <template v-else>
            <a-select v-model="record.ids" multiple placeholder="请选择统计项" @change="colComb()">
              <template v-for="item in colArr" :key="item.id">
                <a-option
                  :value="item.id"
                  :label="item.name"
                  :disabled="record.ids.indexOf(item.id) === -1 && item.disabled"
                ></a-option>
              </template>
            </a-select>
          </template>
        </template>
        <template #optionalIsSum="{ record }">
          <span v-if="!record.editFlag">{{ record.is_sum === '0' ? '不汇总' : '汇总' }}</span>
          <template v-else>
            <a-select v-model="record.is_sum" placeholder="请选择规则">
              <a-option value="1" label="汇总"></a-option>
              <a-option value="0" label="不汇总"></a-option>
            </a-select>
          </template>
        </template>
        <template #optional="{ record, rowIndex }">
          <div style="display: flex; justify-content: space-around">
            <a-button v-show="record.editFlag" type="primary" @click="handleComplete(2, record)">完成</a-button>
            <a-button v-show="!record.editFlag" type="primary" @click="record.editFlag = true">编辑</a-button>
            <a-button type="primary" @click="handleColDelete(record, rowIndex)">删除</a-button>
          </div>
        </template>
      </a-table>
      <a-button type="primary" style="margin: 20px 0" @click="handleAddColClick">添加</a-button>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import dayjs from 'dayjs';
  import cloneDeep from 'lodash/cloneDeep';
  // import Sortable from 'sortablejs';
  import { Message } from '@arco-design/web-vue';

  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import type { ShortcutType } from '@arco-design/web-vue/es/date-picker/interface';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import ExportExcel from '@/components/exportExcel.vue';

  // import useLoading from '@/hooks/loading';
  import { useAdminInfoStore, useBusInfoStore } from '@/store';
  import { getFlowLogAll, getSummaryGroup, getSummaryGroupList, putSummaryGroup } from '@/api/statistics';
  // import { Pagination } from '@/types/global';
  // import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface';
  import { Callback, ExportData } from '@/types/global';

  defineOptions({
    name: 'FlowLogAll',
  });
  type SizeProps = 'mini' | 'small' | 'medium' | 'large';
  type Column = TableColumnData & { checked?: true };

  // const TYPE_EXPORT_COLS = ['exportOpen', 'exportTeam', 'exportPrivate'];
  // const LI_HEIGHT = 20;

  // const route = useRoute();
  const busInfo = useBusInfoStore();

  const generateFormModel = () => {
    const today = new Date();

    return {
      start_time: '',
      end_time: '',
      rangeValue: [dayjs().subtract(30, 'day').$d, today],
      bus_id: busInfo.bus_id,
    };
  };

  const renderData = ref<[]>([]);
  const formModel = ref(generateFormModel());
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);

  const size = ref<SizeProps>('medium');

  const shortcuts = ref<ShortcutType[]>([
    {
      label: '今天',
      value: () => [new Date(), new Date()],
    },
    {
      label: '本周',
      value() {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        return [start, end];
      },
    },
    {
      label: '本月',
      value() {
        const end = new Date();
        const start = dayjs().date(1);
        return [start, end];
      },
    },
  ]);
  const adminInfo = useAdminInfoStore();
  const getDisabledDate = (current: Date) => {
    const days = adminInfo.financeCheckDays;
    return (
      (days ? current.getTime() - Date.now() < days * -24 * 60 * 60 * 1000 : false) || current.valueOf() > Date.now()
    );
  };

  // update table construct.
  const editTableFlag = ref(false);
  interface arrType {
    id: string;
    name: string;
    disabled?: boolean;
  }
  const rowArr = ref<arrType[]>([]);
  const colArr = ref<arrType[]>([]);
  const showRowData = ref();
  const showColData = ref();
  const deleteRowList = ref();
  const deleteColList = ref();
  const updateRowList = ref();
  const updateColList = ref();

  const columns = ref<TableColumnData[]>([{ title: '类型 \\ 支付', dataIndex: '-1' }]);

  const defaultVerticalRowColumn = ref<TableColumnData[]>([
    {
      width: 120,
      title: '类型名称',
      dataIndex: 'group_name',
      slotName: 'optionalName',
    },
    {
      title: '统计项',
      dataIndex: 'value',
      slotName: 'optionalValue',
    },
    {
      width: 180,
      title: '操作',
      dataIndex: 'option',
      slotName: 'optional',
      align: 'right',
    },
  ]);

  const defaultHorizontalRowColumn = ref<TableColumnData[]>([
    {
      width: 120,
      title: '类型名称',
      dataIndex: 'group_name',
      slotName: 'optionalName',
    },
    {
      title: '统计项',
      dataIndex: 'category',
      slotName: 'optionalCategory',
    },
    {
      title: '规则',
      dataIndex: 'is_sum',
      slotName: 'optionalIsSum',
    },
    {
      width: 180,
      title: '操作',
      dataIndex: 'option',
      slotName: 'optional',
      align: 'right',
    },
  ]);
  const { isLoading, execute: executeFlowLogAll } = getFlowLogAll();

  const fetchData = async (params: any) => {
    if (params.rangeValue.length) {
      params.start_time = dayjs(params.rangeValue[0]).format('YYYY-MM-DD');
      params.end_time = dayjs(params.rangeValue[1]).format('YYYY-MM-DD');
      delete params.rangeValue;
    }
    const { data } = await executeFlowLogAll({
      data: params,
    });
    renderData.value = data.value || [];
  };

  const route = useRoute();
  const handleClickExport = (cb: Callback<ExportData>) => {
    const list = renderData.value;
    cb({
      filename: (route.matched[route.matched.length - 1].meta.locale as string | undefined) || '流水汇总表',
      columns: cloneColumns.value,
      data: list,
    });
  };

  // update table construct.
  const { execute: executeFlowLogGroup } = getSummaryGroup();
  const getHeadArr = async (type: number) => {
    const { data } = await executeFlowLogGroup({
      data: {
        bus_id: formModel.value.bus_id,
        type,
      },
    });
    if (type === 1) {
      rowArr.value = data.value || [];
    } else {
      colArr.value = data.value || [];
    }
    return Promise.resolve();
  };

  const { execute: executeFlowLogGroupList } = getSummaryGroupList();
  async function getDefineArr() {
    const { data } = await executeFlowLogGroupList({
      data: {
        bus_id: formModel.value.bus_id,
      },
    });
    const defineRowArr = data.value.operate_type_list;
    const defineColArr = data.value.pay_type_list;

    updateRowList.value = [];
    updateColList.value = [];
    deleteRowList.value = [];
    deleteColList.value = [];

    showRowData.value = [];
    showColData.value = [];
    columns.value = [{ title: '类型 \\ 支付', dataIndex: '-1' }];

    function setOptionPack(item: any, index: number) {
      return {
        ...item,
        editFlag: false,
        entityId: index + 1,
      };
    }
    if (Array.isArray(defineRowArr)) {
      defineRowArr.forEach((item, index) => {
        showRowData.value.push(setOptionPack(item, index));
      });
    }
    if (Array.isArray(defineColArr)) {
      defineColArr.forEach((item, index) => {
        showColData.value.push(setOptionPack(item, index));
        columns.value.push({
          title: item.group_name,
          dataIndex: item.group_id,
          align: 'right',
        });
      });
      columns.value.push({ title: '汇总', dataIndex: '-2', align: 'right' });
    }
    return Promise.resolve();
  }

  /**
   * 1.拿出showRowData/showColData数据中选中的id放在Set结构中
   * 2.匹配rowArr/colArr数据设置disabled
   * 3.本身行数据中选中的is不禁用 见98/126行
   */
  /**
   * 设置表格样式-row梳理
   */
  const rowComb = () => {
    const optionSet = new Set();
    showRowData.value.forEach((item: any) => {
      item.ids.forEach((id: any) => {
        optionSet.add(id);
      });
    });
    rowArr.value.forEach((item: any) => {
      if (optionSet.has(item.id)) {
        item.disabled = true;
      } else {
        item.disabled = false;
      }
    });
  };
  /**
   * 设置表格样式-col梳理
   */
  const colComb = () => {
    const optionSet = new Set();
    showColData.value.forEach((item: any) => {
      item.ids.forEach((id: any) => {
        optionSet.add(id);
      });
    });
    colArr.value.forEach((item: any) => {
      if (optionSet.has(item.id)) {
        item.disabled = true;
      } else {
        item.disabled = false;
      }
    });
  };

  /**
   * 设置表格样式-通用-完成方法
   * @param type 1/2 1:row 2:col
   * @param record 编辑行对象
   */
  const handleComplete = (type: number, record: any) => {
    if (!record.group_name) {
      Message.error('请输入类型名称！');
      return;
    }
    if (!record.ids || record.ids.length < 1) {
      Message.error('请选择统计项！');
      return;
    }
    let name = '';
    rowArr.value.forEach((item) => {
      if (record.ids.indexOf(item.id) !== -1) {
        name += `${item.name}、`;
      }
    });
    record.value = name.slice(0, name.length - 1);
    record.editFlag = false;
    // 装进/更新updateRowList/updateColList
    if (type === 1) {
      const itemIdx = updateRowList.value.findIndex((item: any) => item.entityId === record.entityId);
      if (itemIdx === -1) {
        updateRowList.value.push(record);
      } else {
        updateRowList.value[itemIdx] = record;
      }
    } else {
      const itemIdx = updateColList.value.findIndex((item: any) => item.entityId === record.entityId);
      if (itemIdx === -1) {
        updateColList.value.push(record);
      } else {
        updateColList.value[itemIdx] = record;
      }
    }
  };

  /**
   * 设置表格样式-row删除
   * @param record 编辑行对象
   */
  const handleRowDelete = (record: any, rowIndex: number) => {
    showRowData.value.splice(rowIndex, 1);
    // 装进/更新deleteRowList
    deleteRowList.value.push({
      ...record,
      group_name: record.group_id ? record.group_name : '',
      is_del: 1,
    });

    rowComb();
  };
  /**
   * 设置表格样式-col删除
   * @param record 编辑行对象
   */
  const handleColDelete = (record: any, rowIndex: number) => {
    showColData.value.splice(rowIndex, 1);
    // 装进/更新deleteColList
    deleteColList.value.push({
      ...record,
      group_name: record.group_id ? record.group_name : '',
      is_del: 1,
    });

    colComb();
  };

  function handleBusChange() {
    getHeadArr(1)
      .then(() => getHeadArr(2))
      .then(() => getDefineArr())
      .then(() => fetchData({ ...formModel.value }))
      .then(() => {
        rowComb();
        colComb();
      });
  }

  const { execute: executePutGroup } = putSummaryGroup();
  /**
   * 设置表格样式-保存
   */
  async function handleSaveClick() {
    const type1arr = updateRowList.value.concat(deleteRowList.value).filter((item: any) => !!item.group_name);
    const type2arr = updateColList.value.concat(deleteColList.value).filter((item: any) => !!item.group_name);

    if (type1arr.length === 0 && type2arr.length === 0) {
      return false;
    }

    const { response }: { response: { value: any } } = await executePutGroup({
      data: {
        bus_id: formModel.value.bus_id,
        operate_type_list: type1arr,
        pay_type_list: type2arr,
      },
    });
    if (response.value?.errorcode === 0) {
      Message.success('表格样式编辑成功！');
      updateRowList.value = [];
      updateColList.value = [];
      deleteRowList.value = [];
      deleteColList.value = [];

      showRowData.value = [];
      showColData.value = [];
      columns.value = [{ title: '类型 \\ 支付', dataIndex: '-1' }];
    } else {
      Message.error(response.value?.errormsg);
    }

    return getDefineArr()
      .then(() => fetchData({ ...formModel.value }))
      .then(() => {
        rowComb();
        colComb();
      });
  }

  function handleAddRowClick() {
    let lastEntityId = 0;
    if (Array.isArray(showRowData.value) && showRowData.value.length > 0) {
      lastEntityId = showRowData.value[showRowData.value.length - 1].entityId;
    }
    showRowData.value.push({
      group_id: '',
      group_name: '',
      ids: [],
      editFlag: true,
      entityId: lastEntityId + 1,
    });
  }

  function handleAddColClick() {
    let lastEntityId = 0;
    if (Array.isArray(showColData.value) && showColData.value.length > 0) {
      lastEntityId = showColData.value[showColData.value.length - 1].entityId;
    }
    showColData.value.push({
      group_id: '',
      group_name: '',
      ids: [],
      editFlag: true,
      entityId: lastEntityId + 1,
      is_sum: '0',
    });
  }

  const search = () => {
    fetchData({
      ...formModel.value,
    });
  };

  const onPageChange = (current: number) => {
    fetchData({ ...formModel.value, current });
  };

  // const reset = () => {
  //   formModel.value = generateFormModel();
  // };

  // const handleSelectDensity = (val: string | number | Record<string, any> | undefined, e: Event) => {
  //   size.value = val as SizeProps;
  // };

  // const handleChange = (checked: boolean | (string | boolean | number)[], column: Column, index: number) => {
  //   if (!checked) {
  //     cloneColumns.value = showColumns.value.filter((item) => item.dataIndex !== column.dataIndex);
  //   } else {
  //     cloneColumns.value.splice(index, 0, column);
  //   }
  // };

  // const exchangeArray = <T extends Array<any>>(array: T, beforeIdx: number, newIdx: number, isDeep = false): T => {
  //   const newArray = isDeep ? cloneDeep(array) : array;
  //   if (beforeIdx > -1 && newIdx > -1) {
  //     // 先替换后面的，然后拿到替换的结果替换前面的
  //     newArray.splice(beforeIdx, 1, newArray.splice(newIdx, 1, newArray[beforeIdx]).pop());
  //   }
  //   return newArray;
  // };

  // const popupVisibleChange = (val: boolean) => {
  //   if (val) {
  //     nextTick(() => {
  //       const el = document.getElementById('tableSetting') as HTMLElement;
  //       const sortable = new Sortable(el, {
  //         onEnd(e: any) {
  //           const { oldIndex, newIndex } = e;
  //           exchangeArray(cloneColumns.value, oldIndex, newIndex);
  //           exchangeArray(showColumns.value, oldIndex, newIndex);
  //         },
  //       });
  //     });
  //   }
  // };

  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );

  handleBusChange();
</script>

<style scoped lang="less">
  .base-box {
    padding: 0 20px 20px 20px;
  }
</style>
