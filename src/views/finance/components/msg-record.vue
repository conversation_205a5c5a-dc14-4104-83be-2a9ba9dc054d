<template>
  <a-row>
    <a-col :flex="1">
      <a-form :model="searchParam" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" label-align="left">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="keyword" label="主题名称">
              <a-input
                v-model="searchParam.keyword"
                allow-clear
                placeholder="请输入主题名称"
                @press-enter="handleSearch"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="begin_date" label="发送时间">
              <a-range-picker :default-value="rangeValue" style="width: 100%" @change="handleCreatedTimeChange" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-col>
    <a-divider style="height: 32px" direction="vertical" />
    <a-col :flex="'86px'" style="text-align: right">
      <a-space direction="vertical" :size="18">
        <a-button type="primary" @click="handleSearch">
          <template #icon>
            <icon-search />
          </template>
          搜索
        </a-button>
      </a-space>
    </a-col>
  </a-row>
  <a-divider style="margin-top: 0" />
  <a-row style="margin-bottom: 16px">
    <a-col :span="12">
      <a-space> </a-space>
    </a-col>
    <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
      <ExportExcel>
        <template #default="{ handleExport }">
          <a-button :loading="isLoading" @click="handleClickExport(handleExport as Callback<ExportData>)">
            导出
          </a-button>
        </template>
      </ExportExcel>
    </a-col>
  </a-row>
  <a-table v-bind="tableProps" v-on="tableEvent">
    <template #columns>
      <a-table-column title="时间" data-index="send_time" />
      <a-table-column title="通知对象" data-index="receiver" />
      <a-table-column title="消耗短信条数" data-index="sms_num" />
      <a-table-column title="内容" data-index="amount" ellipsis>
        <template #cell="{ record }">
          <div style="width: 100%; overflow: hidden; text-overflow: ellipsis">
            <span style="padding-right: 15px; font-weight: bold">{{ record.title }}</span>
            <span style="color: #666" :title="record.content.replace('，拒收请回复R', '')">
              {{ record.content.replace('，拒收请回复R', '') }}
            </span>
          </div>
        </template>
      </a-table-column>
      <a-table-column title="操作账号" data-index="sender" />
    </template>
  </a-table>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import type { CalendarValue } from '@arco-design/web-vue/es/date-picker/interface';
  import { sendMsgList } from '@/api/send-message';
  import useTableProps from '@/hooks/table-props';
  import { Callback, ExportData } from '@/types/global';
  import ExportExcel from '@/components/exportExcel.vue';

  const { isLoading, tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(sendMsgList);
  const rangeValue = ref<CalendarValue>([
    dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ]);
  // 设置除分页外的其它属性值
  setSearchParam({
    keyword: '',
    begin_date: rangeValue.value[0],
    end_date: rangeValue.value[1],
  });
  function handleCreatedTimeChange(params: (CalendarValue | undefined)[] | undefined) {
    const [begin_date, end_date] = params || ['', ''];
    setSearchParam({
      begin_date,
      end_date,
    });
  }
  loadTableList();
  const handleClickExport = (cb: Callback<ExportData>) => {
    const columns = [
      {
        title: '时间',
        dataIndex: 'send_time',
      },
      {
        title: '通知对象',
        dataIndex: 'receiver',
      },
      {
        title: '消耗短信条数',
        dataIndex: 'sms_num',
      },
      {
        title: '内容',
        dataIndex: 'content',
      },
    ];
    loadTableList(true).then((list) => {
      cb({
        filename: '短信消耗',
        columns,
        data: list,
      });
    });
  };
</script>
