<template>
  <a-row>
    <a-col :flex="1">
      <a-form :model="searchParam" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" label-align="left">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="begin_date" label="时间">
              <a-range-picker :default-value="rangeValue" style="width: 100%" @change="handleCreatedTimeChange" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="item_type" label="事项">
              <a-select v-model="searchParam.item_type" allow-clear placeholder="请选择">
                <a-option v-for="item in operateTypeList" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="item_type" label="操作结果">
              <a-select v-model="searchParam.status" allow-clear placeholder="请选择">
                <a-option value="1">成功</a-option>
                <a-option value="2">失败</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-col>
    <a-divider style="height: 32px" direction="vertical" />
    <a-col :flex="'86px'" style="text-align: right">
      <a-space direction="vertical" :size="18">
        <a-button type="primary" @click="handleSearch">
          <template #icon>
            <icon-search />
          </template>
          搜索
        </a-button>
      </a-space>
    </a-col>
  </a-row>
  <a-divider style="margin-top: 0" />
  <a-row style="margin-bottom: 16px">
    <a-col :span="12">
      <a-button v-if="transferContractNumberAuth" type="primary" @click="showAllocationModal = true">
        划拨合同券
      </a-button>
    </a-col>
    <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
      <ExportExcel>
        <template #default="{ handleExport }">
          <a-button :loading="isLoading" @click="handleClickExport(handleExport as Callback<ExportData>)">
            导出
          </a-button>
        </template>
      </ExportExcel>
    </a-col>
  </a-row>
  <a-table v-bind="tableProps" v-on="tableEvent">
    <template #columns>
      <a-table-column title="时间" data-index="create_time" />
      <a-table-column title="操作人" data-index="username" />
      <a-table-column title="合同订单" data-index="order_sn" />
      <a-table-column title="事项" data-index="item_type">
        <template #cell="{ record }">
          <div>
            {{ operateTypeList.find((item) => item.value === record.item_type)?.label }}
          </div>
        </template>
      </a-table-column>
      <a-table-column title="操作结果" data-index="status">
        <template #cell="{ record }">
          <div :style="{ color: record.status === '1' ? '#52c41a' : record.status === '2' ? '#f5222d' : '' }">
            {{ record.status === '1' ? '成功' : record.status === '2' ? '失败' : '' }}
          </div>
        </template>
      </a-table-column>
      <a-table-column title="数量" data-index="use_contract_number" />
    </template>
  </a-table>
  <AllocationContractModal v-model="showAllocationModal" @on-success="loadTableList" />
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import type { CalendarValue } from '@arco-design/web-vue/es/date-picker/interface';
  import { getContractRecord } from '@/api/consumption-record';
  import useTableProps from '@/hooks/table-props';
  import { Callback, ExportData } from '@/types/global';
  import ExportExcel from '@/components/exportExcel.vue';
  import AllocationContractModal from './allocation-contract-modal.vue';

  const { isLoading, tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getContractRecord);
  const showAllocationModal = ref(false);
  const operateTypeList = [
    {
      label: '签署',
      value: '2',
    },
    {
      label: '划拨',
      value: '3',
    },
    {
      label: '姓名身份证实名认证',
      value: '4',
    },
    {
      label: '运营商三要素实名认证',
      value: '5',
    },
    {
      label: '人脸识别实名认证',
      value: '6',
    },
  ];
  const rangeValue = ref<CalendarValue>([
    dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ]);
  // 设置除分页外的其它属性值
  setSearchParam({
    username: '',
    status: '',
    item_type: '',
    begin_date: rangeValue.value[0],
    end_date: rangeValue.value[1],
  });
  function handleCreatedTimeChange(params: (CalendarValue | undefined)[] | undefined) {
    const [begin_date, end_date] = params || ['', ''];
    setSearchParam({
      begin_date,
      end_date,
    });
  }

  const transferContractNumberAuth = ref(false);
  loadTableList().then((res) => {
    transferContractNumberAuth.value = res.data?.value.transfer_contract_number;
  });

  const handleClickExport = (cb: Callback<ExportData>) => {
    const columns = [
      {
        title: '时间',
        dataIndex: 'create_time',
      },
      {
        title: '操作人',
        dataIndex: 'username',
      },
      {
        title: '合同订单',
        dataIndex: 'order_sn',
      },
      {
        title: '事项',
        dataIndex: 'item_type_name',
      },
      {
        title: '操作结果',
        dataIndex: 'statusName',
      },
      {
        title: '数量',
        dataIndex: 'use_contract_number',
      },
    ];
    loadTableList(true).then((list) => {
      cb({
        filename: '电子合同消费记录',
        columns,
        data: list.map((item) => {
          const itemName = operateTypeList.find((subItem) => subItem.value === item.item_type)?.label;
          return {
            ...item,
            item_type_name: itemName,
            statusName: item.status === '1' ? '成功' : item.status === '2' ? '失败' : '',
          };
        }),
      });
    });
  };
</script>
