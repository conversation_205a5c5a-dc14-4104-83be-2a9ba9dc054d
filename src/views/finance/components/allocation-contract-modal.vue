<template>
  <a-modal
    v-model:visible="isShowModal"
    title="场馆"
    :width="720"
    :ok-loading="isLoading"
    :on-before-ok="handleOk"
    unmount-on-close
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="postData" auto-label-width>
      <a-form-item label="当前合同卷"> {{ nowContractNumber }} </a-form-item>
      <a-form-item
        field="contract_number"
        label="划拨数量"
        :rules="[
          {
            required: true,
            message: '请填写划拨数量',
          },
        ]"
      >
        <a-input-number v-model="postData.contract_number" :min="1" :max="nowContractNumber" :precision="0" />
      </a-form-item>
      <a-form-item
        field="to_bus_id"
        label="划拨场馆"
        :rules="[
          {
            required: true,
            message: '请选择划拨场馆',
          },
        ]"
      >
        <BusSelectMerchant v-model="postData.to_bus_id" />
      </a-form-item>
      <a-form-item label="划拨后剩余">
        {{ remainingNumber }}
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  /* 划拨合同券 */
  import { computed } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useAdminInfoStore } from '@/store';
  import BusSelectMerchant from '@/components/bus-select/merchant.vue';
  import { transferContractNumber } from '@/api/business';

  const props = defineProps<{
    modelValue?: boolean;
  }>();
  const adminInfo = useAdminInfoStore();
  const nowContractNumber = computed(() => {
    return Number(adminInfo.getInfoState.contract_number || 0);
  });
  const postData = reactive({
    now_contract_number: nowContractNumber.value,
    to_bus_id: '',
    contract_number: 1,
  });
  const remainingNumber = computed(() => {
    return nowContractNumber.value - Number(postData.contract_number || 0);
  });
  const { isLoading, execute } = transferContractNumber();

  const emits = defineEmits(['update:modelValue', 'onSuccess']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const formRef = ref();
  const handleOk = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      return execute({ data: postData })
        .then(() => {
          Message.success({
            content: '操作成功',
          });
          adminInfo.setInfoByServe();
          isShowModal.value = false;
          formRef.value.resetFields();
          emits('onSuccess');
          return true;
        })
        .catch(() => {
          return false;
        });
    }
    return false;
  };
  const handleCancel = () => {
    isShowModal.value = false;
  };
</script>
