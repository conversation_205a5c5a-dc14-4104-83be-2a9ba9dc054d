<template>
  <a-modal v-model:visible="isShowModal" title="场馆" :footer="false" :width="720">
    <a-table row-key="index" :data="tableData" :pagination="false">
      <template #columns>
        <a-table-column title="姓名" data-index="name" />
        <a-table-column title="充当角色" data-index="role" />
        <a-table-column title="贡献占比" data-index="percent" />
        <a-table-column title="业绩金额" data-index="amount" />
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  const props = defineProps<{
    modelValue?: boolean;
    tableData: any[];
  }>();

  const emits = defineEmits(['update:modelValue']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
</script>
