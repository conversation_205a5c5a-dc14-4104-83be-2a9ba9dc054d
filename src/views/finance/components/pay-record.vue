<template>
  <a-row>
    <a-col :flex="1">
      <a-form :model="searchParam" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }" label-align="left">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="begin_date" label="充值时间">
              <a-range-picker :default-value="rangeValue" style="width: 100%" @change="handleCreatedTimeChange" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-col>
    <a-divider style="height: 32px" direction="vertical" />
    <a-col :flex="'86px'" style="text-align: right">
      <a-space direction="vertical" :size="18">
        <a-button type="primary" @click="handleSearch">
          <template #icon>
            <icon-search />
          </template>
          搜索
        </a-button>
      </a-space>
    </a-col>
  </a-row>
  <a-divider style="margin-top: 0" />
  <a-table v-bind="tableProps" v-on="tableEvent">
    <template #columns>
      <a-table-column title="时间" data-index="create_time" />
      <a-table-column title="事项" data-index="type" />
      <a-table-column :width="120" title="充值金额" data-index="amount" align="right" />
    </template>
  </a-table>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import type { CalendarValue } from '@arco-design/web-vue/es/date-picker/interface';
  import { getRechargeRecord } from '@/api/consumption-record';
  import useTableProps from '@/hooks/table-props';

  const { tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getRechargeRecord);
  const rangeValue = ref<CalendarValue>([
    dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ]);
  // 设置除分页外的其它属性值
  setSearchParam({
    begin_date: rangeValue.value[0],
    end_date: rangeValue.value[1],
  });
  function handleCreatedTimeChange(params: (CalendarValue | undefined)[] | undefined) {
    const [begin_date, end_date] = params || ['', ''];
    setSearchParam({
      begin_date,
      end_date,
    });
  }
  loadTableList();
</script>
