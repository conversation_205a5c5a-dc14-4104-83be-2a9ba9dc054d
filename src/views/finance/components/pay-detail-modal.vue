<template>
  <a-modal v-model:visible="isShowModal" title="付费方式" :footer="false" :width="720">
    <a-table row-key="index" :data="tableData" :pagination="false">
      <template #columns>
        <a-table-column title="支付方式" data-index="pay_type" />
        <a-table-column title="支付金额" data-index="amount" />
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  const props = defineProps<{
    modelValue?: boolean;
    tableData: any[];
  }>();

  const emits = defineEmits(['update:modelValue']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
</script>
