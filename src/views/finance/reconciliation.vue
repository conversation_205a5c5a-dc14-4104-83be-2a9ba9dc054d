<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="searchParam"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="rangeValue" label="时间">
                  <a-range-picker v-model="rangeValue" :allow-clear="false" value-format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="username" label="名称">
                  <a-input v-model="searchParam.username" placeholder="名称" allow-clear @press-enter="handleSearch" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="bus_id" label="购买场馆">
                  <BusSelectAdmin v-model="searchParam.bus_id" placeholder="购买场馆" allow-clear allow-search />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="det_bus_id" label="涉及">
                  <BusSelectAdmin
                    v-model="searchParam.det_bus_id"
                    placeholder="产品涉及其他场馆"
                    allow-clear
                    allow-search />
                </a-form-item>
              </a-col>
              <!-- <a-col :span="8">
                <a-form-item field="belong_bus_id" label="场馆">
                  <admin-region v-model="searchParam.bus_or_region_id" :multiple="false" placeholder="请选择" />
                </a-form-item>
              </a-col> -->
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 76px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space></a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button :loading="isLoading" @click="handleClickExport(handleExport as Callback<ExportData>)">
                导出
              </a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>

      <a-table v-bind="tableProps" ref="tableRef" v-on="tableEvent">
        <template #columns>
          <!-- <a-table-column v-if="IS_BRAND_SITE" title="场馆" data-index="bus_name" /> -->
          <a-table-column title="时间" data-index="deal_time" />
          <a-table-column title="会员" data-index="username">
            <template #cell="{ record }">
              <a-link
                :href="goSubDetail(record.user_id, record.bus_id, false)"
                @click.prevent="goSubDetail(record.user_id, record.bus_id)">
                {{ record.username }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="购买场馆" data-index="bus_name" />
          <a-table-column title="产品" data-index="card_name" />
          <a-table-column title="合同编号" data-index="order_sn" />
          <a-table-column title="产品涉及其他场馆" data-index="debt_bus_name" />
          <a-table-column title="涉及金额" data-index="income_amount" />
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { getReconciliationLog } from '@/api/statistics';
  // import AdminRegion from '@/components/form/adminRegion.vue';
  import useTableProps from '@/hooks/table-props';
  import { useBusInfoStore } from '@/store';
  import { goSubDetail } from '@/utils/router-go';
  import { Callback, ExportData } from '@/types/global';

  import ExportExcel from '@/components/exportExcel.vue';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';

  defineOptions({
    name: 'Reconciliation',
  });

  const { IS_BRAND_SITE } = window;
  const busInfo = useBusInfoStore();
  const { isLoading, tableProps, tableEvent, searchParam, handleSearch, setSearchParam, loadTableList } =
    useTableProps(getReconciliationLog);

  const today = ref(new Date());
  const begin = ref(new Date(new Date().setDate(today.value.getDate() - 30))); // 最近30天
  // 设置除分页外的其它属性值
  setSearchParam({
    username: '',
    // bus_id: IS_BRAND_SITE ? '' : busInfo.bus_id,
    bus_id: '',
    det_bus_id: '',
    begin_date: dayjs(begin.value).format('YYYY-MM-DD'),
    end_date: dayjs(today.value).format('YYYY-MM-DD'),
  });

  const rangeValue = computed({
    get() {
      if (searchParam.end_date) {
        return [searchParam.begin_date, searchParam.end_date];
      }
      return [];
    },
    set(val) {
      setSearchParam({
        begin_date: val[0],
        end_date: val[1],
      });
    },
  });

  const route = useRoute();
  const tableRef = ref(null);
  const handleClickExport = (cb: Callback<ExportData>) => {
    loadTableList(true).then((list) => {
      cb({
        filename: (route.matched[route.matched.length - 1].meta.locale as string | undefined) || '跨店销售对账表',
        columns: [
          {
            title: '时间',
            dataIndex: 'deal_time',
          },
          {
            title: '会员',
            dataIndex: 'username',
          },
          {
            title: '操作场馆',
            dataIndex: 'bus_name',
          },
          {
            title: '产品',
            dataIndex: 'card_name',
          },
          {
            title: '合同编号',
            dataIndex: 'order_sn',
          },
          {
            title: '产品涉及其他场馆',
            dataIndex: 'debt_bus_name',
          },
          {
            title: '涉及金额',
            dataIndex: 'income_amount',
          },
        ],
        data: list,
      });
    });
  };
  onActivated(() => {
    loadTableList();
  });
</script>
