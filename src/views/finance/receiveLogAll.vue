<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="region_bus" label="场馆">
                  <AdminRegion
                    v-model="formModel.region_bus"
                    :multiple="false"
                    url="/Web/OpenClass/get_openclass_region_bus"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="rangeValue" label="时间">
                  <a-range-picker v-model="formModel.rangeValue" :allow-clear="false" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" :loading="isLoading" @click="search">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <!-- <a-button @click="reset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button> -->
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />

      <template v-if="totalStatList.length">
        <a-scrollbar type="track" style="height: 98px; overflow-x: auto">
          <a-space :size="10">
            <a-statistic
              v-for="(item, index) in totalStatList"
              :key="index"
              class="finance-statistics"
              :class="[item.class]"
              :title="item.name"
              :value="Number(item.amount)"
              :start="false"
              :precision="2"
              :animation-duration="800"
              show-group-separator
              animation
            >
              <template v-if="item.icon" #prefix>
                <img :src="item.icon" />
              </template>
            </a-statistic>
          </a-space>
        </a-scrollbar>
        <a-divider style="margin-top: 0" />
      </template>

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space> </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button :loading="isLoading" @click="handleClickExport(handleExport as Callback<ExportData>)">
                导出
              </a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>

      <a-table
        row-key="id"
        :loading="isLoading"
        :pagination="tablePagination"
        :columns="(cloneColumns as TableColumnData[])"
        :data="renderData"
        :bordered="false"
        :size="size"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #operations="{ record }">
          <a-link @click="handleToDetail(record)"> 详情 </a-link>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { computed, ref, reactive, watch, nextTick } from 'vue';
  import cloneDeep from 'lodash/cloneDeep';
  import Sortable from 'sortablejs';

  import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface';
  import type { PaginationProps } from '@arco-design/web-vue/es/pagination/interface';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import AdminRegion from '@/components/form/adminRegion.vue';
  import ExportExcel from '@/components/exportExcel.vue';

  // import useLoading from '@/hooks/loading';
  import { getReceiveLogAll } from '@/api/statistics';
  import { getAssetsImg } from '@/utils';
  import { AnyObject, Callback, ExportData } from '@/types/global';

  defineOptions({
    name: 'ReceiveLogAll',
  });

  type SizeProps = 'mini' | 'small' | 'medium' | 'large';
  type Column = TableColumnData & { checked?: true };

  const route = useRoute();

  const generateFormModel = () => {
    const today = new Date();
    const date = new Date().setDate(today.getDate() - 30);

    return {
      begin_date: '',
      end_date: '',
      rangeValue: [date, today],
      region_bus: '', // (场馆ID||区域ID)_(1区域||2场馆)_(1.一级，2.二级，3三级，4.四级)
    };
  };

  const renderData = ref<AnyObject[]>([]);
  const totalStatList = ref([
    {
      key: 'all_income', // 总收入
      name: '收入',
      amount: '',
      class: 'finance-income',
      icon: getAssetsImg('finance/income.png'),
    },
    {
      key: 'all_disburse',
      name: '支出',
      amount: '',
      class: 'finance-expenditure',
      icon: getAssetsImg('finance/expenditure.png'),
    },
    {
      key: 'all_total', // 合计
      name: '合计',
      amount: '',
      class: 'finance-amountTo',
      icon: getAssetsImg('finance/amountTo.png'),
    },
  ]);
  const formModel = ref(generateFormModel());
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);

  const size = ref<SizeProps>('medium');

  const pagination: PaginationProps = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const tablePagination = computed(() => ({
    ...pagination,
    showPageSize: true,
    showTotal: true,
  }));

  const columns = computed<TableColumnData[]>(() => [
    {
      title: '场馆',
      dataIndex: 'bus_name',
    },
    {
      title: '线上支付',
      dataIndex: 'online',
      align: 'right',
    },
    {
      title: '线下支付',
      dataIndex: 'offline',
      align: 'right',
    },
    {
      title: '收入',
      dataIndex: 'income',
      align: 'right',
    },
    {
      title: '支出',
      dataIndex: 'disburse',
      align: 'right',
    },
    {
      title: '合计',
      dataIndex: 'total',
      align: 'right',
    },
    {
      title: '操作',
      dataIndex: 'operations',
      slotName: 'operations',
      align: 'right',
    },
  ]);

  const { isLoading, execute } = getReceiveLogAll();

  const fetchData = async (params: any, isExport = false) => {
    if (params.rangeValue.length) {
      params.begin_date = dayjs(params.rangeValue[0]).format('YYYY-MM-DD');
      params.end_date = dayjs(params.rangeValue[1]).format('YYYY-MM-DD');
      delete params.rangeValue;
    }
    const { data } = await execute({
      data: params,
    });
    const list: AnyObject[] = data.value.list || [];

    if (isExport) {
      return Promise.resolve(list);
    }

    pagination.total = +data.value.count;
    renderData.value = list;
    totalStatList.value.forEach((v) => {
      v.amount = data.value.total_stat[v.key] || '0';
    });

    return Promise.resolve(list);
  };

  const handleClickExport = (cb: Callback<ExportData>) => {
    fetchData(
      {
        ...formModel.value,
        current: 1,
        pageSize: pagination.total,
      },
      true
    ).then((list) => {
      cb({
        filename: (route.matched[route.matched.length - 1].meta.locale as string | undefined) || '收银流水汇总',
        columns: columns.value.filter((v) => v.dataIndex !== 'operations'),
        data: list,
      });
    });
  };

  const search = () => {
    pagination.current = 1;
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...formModel.value,
    });
  };

  const onPageChange = (current: number) => {
    pagination.current = current;
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const onPageSizeChange = (pageSize: number) => {
    pagination.current = 1;
    pagination.pageSize = pageSize;
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const reset = () => {
    formModel.value = generateFormModel();
  };

  const router = useRouter();
  const handleToDetail = (record) => {
    router.push({
      name: 'ReceiveLog',
      params: {
        busId: record.bus_id,
        beginDate: dayjs(formModel.value.rangeValue[0]).format('YYYY-MM-DD'),
        endDate: dayjs(formModel.value.rangeValue[1]).format('YYYY-MM-DD'),
      },
    });
  };

  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );

  onActivated(() => {
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  });
</script>

<style scoped lang="less">
  .base-box {
    padding: 0 20px 20px 20px;
  }
</style>
