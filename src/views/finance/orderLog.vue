<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="bus_id" label="场馆">
                  <BusSelectAdmin v-model="formModel.bus_id" placeholder="选择场馆" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="search" label="查询">
                  <a-input v-model="formModel.search" allow-clear placeholder="姓名/电话/合同单号" />
                </a-form-item>
              </a-col>
              <CardSelect
                :model-value="formModel.card_id"
                :bus-id="formModel.bus_id"
                :max-tag-count="2"
                placeholder="卡/课类型"
                return-card-info
                multiple
                show-package
                @on-change="cardChange"></CardSelect>
              <a-col :span="8">
                <a-form-item field="pay_type" label="支付方式">
                  <PayTypeSelect
                    v-model="formModel.pay_type"
                    show-card-pay
                    :bus-id="formModel.bus_id"
                    placeholder="支付方式" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="from_type" label="来源">
                  <a-select v-model="formModel.from_type" allow-clear placeholder="线上/线下">
                    <a-option value="1">线下</a-option>
                    <a-option value="2">线上</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="marketers_id" label="业绩归属">
                  <SalesSelect
                    v-model="formModel.marketers_id"
                    :belong-bus-id="formModel.bus_id"
                    label-in-value
                    is-coach
                    placeholder="业绩归属"
                    @on-change="saleChange"></SalesSelect>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="consumption_type" label="消费类型">
                  <a-select v-model="formModel.consumption_type" allow-clear placeholder="消费类型">
                    <!-- <a-option value="">全部</a-option> -->
                    <a-option value="购卡">购卡</a-option>
                    <a-option value="升卡">升卡</a-option>
                    <a-option value="续卡">续卡</a-option>
                    <a-option value="购私教">购私教</a-option>
                    <a-option value="续私教">续私教</a-option>
                    <a-option value="购泳教">购泳教</a-option>
                    <a-option value="续泳教">续泳教</a-option>
                    <a-option value="转卡">转卡</a-option>
                    <a-option value="补卡">补卡</a-option>
                    <a-option value="请假">请假</a-option>
                    <a-option value="销卡">销卡</a-option>
                    <a-option value="拆分">拆分</a-option>
                    <a-option value="租柜">租柜</a-option>
                    <a-option value="跨店购卡">跨店购卡</a-option>
                    <a-option value="在线购课">在线购课</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="rangeValue" label="时间">
                  <a-range-picker
                    v-model="formModel.rangeValue"
                    :allow-clear="false"
                    format="YYYY-MM-DD"
                    style="width: 100%"
                    shortcuts-position="left"
                    :shortcuts="shortcuts"
                    :disabled-date="getDisabledDate" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="approve_status" label="审批状态">
                  <a-select v-model="formModel.approve_status" placeholder="审批状态" allow-clear>
                    <!-- <a-option value="">全部</a-option> -->
                    <a-option value="1">待审</a-option>
                    <a-option value="2">不通过</a-option>
                    <a-option value="3">通过</a-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item field="contract_type" label="合同类型">
                  <a-select v-model="formModel.contract_type" placeholder="合同类型" allow-clear>
                    <!-- <a-option value="">全部</a-option> -->
                    <a-option value="1">会籍合同</a-option>
                    <a-option value="2">私教合同</a-option>
                    <a-option value="4">泳教合同</a-option>
                    <a-option value="5">套餐包合同</a-option>
                    <a-option value="3">其他</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <!-- 合同订单 新增传参与返回参数 order_sign_status 0所有 1未签 2线下签署 3会员端 4免 -->
                <a-form-item field="order_sign_status" label="合同签署">
                  <a-select v-model="formModel.order_sign_status" placeholder="合同签署" allow-clear>
                    <a-option value="1">未签</a-option>
                    <a-option value="2">线下签署</a-option>
                    <a-option value="3">会员端签署</a-option>
                    <a-option value="4">免签</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>

        <a-divider style="height: 164px" direction="vertical" />

        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" :loading="isLoading" @click="search">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />

      <template v-if="totalStatList.length">
        <a-scrollbar type="track" style="height: 98px; overflow-x: auto">
          <a-space :size="10">
            <a-statistic
              v-for="(item, index) in totalStatList"
              :key="index"
              class="finance-statistics"
              :title="item.name"
              :value="Number(item.amount)"
              :start="false"
              :precision="2"
              :animation-duration="800"
              show-group-separator
              animation></a-statistic>
          </a-space>
        </a-scrollbar>
        <a-divider style="margin-top: 0" />
      </template>

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space>
            <a-button v-if="!IS_BRAND_SITE" type="primary" :loading="orderSignLoading" @click="handleSign">
              标记签署
            </a-button>
            <a-button
              v-if="!IS_BRAND_SITE"
              type="secondary"
              :loading="cancelOrderSignLoading"
              @click="handleCancelSign">
              取消签署
            </a-button>
          </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportButton
            :data="exportPostParams"
            :url="IS_BRAND_SITE ? '/Web/Statistics/cardOrderList' : '/merchant/statistics/cardOrderList'" />
        </a-col>
      </a-row>

      <a-table
        v-model:selectedKeys="selectedIds"
        row-key="card_order_id"
        :loading="isLoading"
        :pagination="tablePagination"
        :data="renderData"
        :bordered="false"
        :size="size"
        :row-selection="rowSelection"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange">
        <template #columns>
          <a-table-column title="成交时间" data-index="deal_time" :width="160" />
          <a-table-column title="购买会员" data-index="username" :width="140">
            <template #cell="{ record }">
              <a-link
                :href="goSubDetail(record.user_id, record.bus_id, false)"
                @click.prevent="handleToUserDetail(record)">
                {{ record.username }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="合同类型" data-index="contract_type" :width="120" />
          <a-table-column title="合同单号" data-index="custom_order_sn" :width="130">
            <template #cell="{ record }">
              <a-link @click="clickRecordNum(record)">
                {{ record.custom_order_sn }}
              </a-link>
            </template>
          </a-table-column>
          <a-table-column title="会员卡" data-index="cardname" :width="120" />
          <a-table-column title="金额" data-index="amount" :width="120" align="right">
            <template #cell="{ record }">
              <a-link @click="handleShowDetail(0, record.card_order_id)">
                {{ record.amount }}
              </a-link>
              {{ record.from_type == '线上' ? ` [${record.from_type}]` : '' }}
            </template>
          </a-table-column>
          <a-table-column title="消费类型" :width="100" data-index="Consumption_type" />
          <a-table-column title="支付方式" data-index="pay_type" :width="120">
            <template #cell="{ record }">
              <a-link v-if="record.pay_type.split(',').length > 1" @click="handleShowDetail(1, record.card_order_id)">
                {{ record.pay_type }}
              </a-link>
              <span v-else style="padding: 1px 4px">
                {{ record.pay_type }}
              </span>
            </template>
          </a-table-column>
          <a-table-column title="审批状态" data-index="approve_status" :width="100">
            <template #cell="{ record }">
              <div v-if="record.approve_status == 1">待审</div>
              <div v-else-if="record.approve_status == 2" style="color: #d9534f">不通过</div>
              <div v-else style="color: #19be6b">通过</div>
            </template>
          </a-table-column>
          <a-table-column title="合同签署" data-index="order_sign_status" :width="100">
            <template #cell="{ record }">
              <div>{{ orderSignTexts[Number(record.order_sign_status) || 0] }}</div>
            </template>
          </a-table-column>
          <a-table-column title="业绩归属" data-index="sale_name" :width="100">
            <template #cell="{ record }">
              <a-link
                v-if="record.sale_name.split(',').length > 1"
                :title="record.sale_name"
                style="width: 100px; overflow: hidden; text-overflow: ellipsis; color: #07abf2"
                @click="handleShowDetail(2, record.card_order_id)">
                {{ record.sale_name }}
              </a-link>
              <span v-else style="padding: 1px 4px">
                {{ record.sale_name }}
              </span>
            </template>
          </a-table-column>
          <a-table-column title="备注" data-index="description" :width="200" ellipsis tooltip></a-table-column>
          <a-table-column title="操作" :width="140">
            <template #cell="{ record }">
              <a-space v-if="record.pdf_url">
                <a-link :href="record.pdf_url" target="_blank">查看电子合同</a-link>
              </a-space>
              <a-space v-else>
                <span style="margin-left: 10px">-</span>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>

    <TableModal
      v-model="showModal"
      :title="modalTitle"
      :columns="modalColumns"
      :data="modalTableData"
      :loading="isModalLoading"
      :pagination="false"></TableModal>

    <a-modal v-model:visible="showRecord" :title="modalTitle" :width="720">
      <div style="padding: 10px; font-size: 14px; line-height: 2">
        {{ recordDetail.new_description }}
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import cloneDeep from 'lodash/cloneDeep';
  import { computed, ref, reactive, watch } from 'vue';
  import { Modal, Message } from '@arco-design/web-vue';

  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import type { PaginationProps } from '@arco-design/web-vue/es/pagination/interface';
  import type { ShortcutType } from '@arco-design/web-vue/es/date-picker/interface';
  import PayTypeSelect from '@/components/form/payTypeSelect.vue';
  import BusSelectAdmin from '@/components/bus-select/admin.vue';
  import CardSelect from '@/components/card/cardListForOrderList.vue';
  import SalesSelect from '@/components/membership/salesSelect.vue';
  // import ExportExcel from '@/components/exportExcel.vue';
  import ExportButton from '@/components/form/export-button.vue';
  import TableModal from '@/components/table-modal.vue';

  import { useAdminInfoStore, useBusInfoStore, usePayStore } from '@/store';
  import { cardOrderList, getOrderLog, getOrderLogInfo, orderSign, cancelOrderSign } from '@/api/statistics';
  import { goSubDetail } from '@/utils/router-go';

  import { AnyObject, Callback, ExportData } from '@/types/global';

  defineOptions({
    name: 'OrderLog',
  });

  type SizeProps = 'mini' | 'small' | 'medium' | 'large';
  type Column = TableColumnData & { checked?: true };

  const route = useRoute();

  const AMOUNT_TYPE = {
    front_money: '定金',
    coupon_amount: '折扣',
    income_amount: '收款',
    debt_card_amount: '储值卡',
  };
  const MODAL_TITLE = ['金额明细', '支付方式', '业绩归属'];
  const { IS_BRAND_SITE } = window;
  const rowSelection = ref(
    IS_BRAND_SITE
      ? null
      : {
          type: 'checkbox',
          showCheckedAll: true,
          onlyCurrent: false,
        }
  );
  const selectedIds = ref([]);
  const COLUMNS = {
    0: [
      {
        title: '类型',
        dataIndex: 'name',
      },
      {
        title: '涉及金额',
        dataIndex: 'value',
      },
    ],
    1: [
      {
        title: '支付方式',
        dataIndex: 'payType',
      },
      {
        title: '支付金额',
        dataIndex: 'amount',
      },
    ],
    2: [
      {
        title: '姓名',
        dataIndex: 'marketers_name',
      },
      {
        title: '归属/协助',
        dataIndex: 'isMain',
      },
      {
        title: '贡献占比',
        dataIndex: 'percent',
      },
      {
        title: '业绩金额',
        dataIndex: 'amount',
      },
    ],
    3: [
      {
        title: '子合同编号',
        dataIndex: 'custom_order_sn',
      },
      {
        title: '名称',
        dataIndex: 'name',
      },
      {
        title: '总共',
        dataIndex: 'overplus',
      },
      {
        title: '价值',
        dataIndex: 'amount',
      },
      {
        title: '到期时间',
        dataIndex: 'end_time',
      },
    ],
  } as any;

  const generateFormModel = () => {
    const today = new Date();
    let bus_id = '';
    if (route.query.busId) {
      bus_id = route.query.busId as string;
    }
    let marketers_id = '';
    if (route.query.id) {
      marketers_id = route.query.id as string;
    }
    let marketers_name = '';
    if (route.query.name) {
      marketers_name = route.query.name as string;
    }
    let rangeValue = [today, today];
    if (route.query.s_date && route.query.e_date) {
      rangeValue = [new Date(route.query.s_date as string), new Date(route.query.e_date as string)];
    }
    return {
      bus_id,
      search: '', // 业务单号/姓名/电话
      pay_type: '',
      from_type: '', // 1 线下 2 线上
      order_sign_status: '',
      consumption_type: '', // 消费类型
      card_id: [] as any[],
      card_name: [] as any[],
      marketers_id,
      marketers_name,
      approve_status: '',
      contract_type: '',
      s_date: '',
      e_date: '',
      rangeValue,
    };
  };

  const renderData = ref<AnyObject[]>([]);
  const totalStatList = ref<{ name: string; amount: string }[]>([
    {
      // key: 'amount',
      name: '总计金额',
      amount: '',
    },
  ]);
  const formModel = ref(generateFormModel());
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);

  const size = ref<SizeProps>('medium');

  const pagination: PaginationProps = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const tablePagination = computed(() => ({
    ...pagination,
    showPageSize: true,
    showTotal: true,
  }));

  const shortcuts = ref<ShortcutType[]>([
    {
      label: '一周',
      value: () => [dayjs().subtract(1, 'week'), dayjs()],
    },
    {
      label: '一个月',
      value: () => [dayjs().subtract(30, 'day'), dayjs()],
    },
    {
      label: '三个月',
      value: () => [dayjs().subtract(90, 'day'), dayjs()],
    },
  ]);
  const adminInfo = useAdminInfoStore();
  const getDisabledDate = (current: Date) => {
    const days = adminInfo.financeCheckDays;
    return (
      (days ? current.getTime() - Date.now() < days * -24 * 60 * 60 * 1000 : false) || current.valueOf() > Date.now()
    );
  };

  const columns = computed<TableColumnData[]>(() => [
    {
      title: '成交时间',
      dataIndex: 'deal_time',
    },
    {
      title: '购买会员',
      dataIndex: 'username',
    },
    {
      title: '合同类型',
      dataIndex: 'contract_type',
    },
    {
      title: '合同单号',
      dataIndex: 'custom_order_sn',
    },
    {
      title: '会员卡',
      dataIndex: 'cardname',
    },
    {
      title: '金额',
      dataIndex: 'amount_text',
    },
    {
      title: '消费类型',
      dataIndex: 'Consumption_type',
    },
    {
      title: '支付方式',
      dataIndex: 'pay_type',
    },
    {
      title: '审批状态',
      dataIndex: 'approve_status_text',
    },
    {
      title: '会员签字',
      dataIndex: 'order_sign_status_text',
    },
    {
      title: '业绩归属',
      dataIndex: 'sale_name',
    },
    {
      title: '备注',
      dataIndex: 'description',
    },
  ]);

  const { isLoading, execute } = IS_BRAND_SITE ? getOrderLog() : cardOrderList();

  const fetchData = async (params: any, isExport = false) => {
    if (params.rangeValue.length) {
      params.s_date = dayjs(params.rangeValue[0]).format('YYYY-MM-DD');
      params.e_date = dayjs(params.rangeValue[1]).format('YYYY-MM-DD');
      delete params.rangeValue;
    }
    params.card_id = params.card_id.join(',');
    params.card_name = params.card_name.join(',');

    const { data } = await execute({
      data: params,
    });

    const list: AnyObject[] = data.value.OrderList || [];
    // if (!list.length) {
    //   Message.error('未查询到数据');
    // }

    if (isExport) {
      return Promise.resolve(list);
    }

    pagination.total = +data.value.count;
    renderData.value = list;
    totalStatList.value[0].amount = data.value.amount;

    return Promise.resolve(list);
  };

  const exportPostParams = computed(() => {
    const params: any = {
      ...formModel.value,
      current: 1,
      pageSize: pagination.total,
    };
    if (params.rangeValue.length) {
      params.s_date = dayjs(params.rangeValue[0]).format('YYYY-MM-DD');
      params.e_date = dayjs(params.rangeValue[1]).format('YYYY-MM-DD');
      delete params.rangeValue;
    }
    return params;
  });
  const orderSignTexts = {
    0: '-',
    1: '未签',
    2: '线下签署',
    3: '会员端签署',
    4: '免签',
  };
  const search = () => {
    pagination.current = 1;
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...formModel.value,
    });
  };

  const { isLoading: orderSignLoading, execute: orderSignInfo } = orderSign();
  const { isLoading: cancelOrderSignLoading, execute: cancelOrderSignInfo } = cancelOrderSign();
  function handleSign() {
    if (!selectedIds.value.length) {
      Message.error('请先选择');
      return;
    }
    Modal.confirm({
      title: '提示',
      content: '确定将合同标记为线下签署？（仅对未签合同生效）',
      onOk: () => {
        orderSignInfo({
          data: {
            order_ids: selectedIds.value,
          },
        }).then(() => {
          Message.success('设置成功');
          selectedIds.value = [];
          fetchData({
            ...formModel.value,
            current: pagination.current,
            pageSize: pagination.pageSize,
          });
        });
      },
    });
  }
  function handleCancelSign() {
    if (!selectedIds.value.length) {
      Message.error('请先选择');
      return;
    }
    Modal.confirm({
      title: '提示',
      content: '确定取消合同的签署标记么？（仅对线下签署合同生效）',
      onOk: () => {
        cancelOrderSignInfo({
          data: {
            order_ids: selectedIds.value,
          },
        }).then(() => {
          Message.success('设置成功');
          selectedIds.value = [];
          fetchData({
            ...formModel.value,
            current: pagination.current,
            pageSize: pagination.pageSize,
          });
        });
      },
    });
  }

  const onPageChange = (current: number) => {
    pagination.current = current;
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const onPageSizeChange = (pageSize: number) => {
    pagination.current = 1;
    pagination.pageSize = pageSize;
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const cardChange = (arr: any[]) => {
    const cardId: string[] = [];
    const cardNames: string[] = [];
    arr.forEach((item: Record<string, any>) => {
      cardId.push(item.card_id);
      cardNames.push(item.card_name);
    });
    formModel.value.card_id = cardId;
    formModel.value.card_name = cardNames;
  };

  const saleChange = ({ value, label }: { value: string; label: string }) => {
    formModel.value.marketers_id = value;
    formModel.value.marketers_name = label;
  };

  const modalTitle = ref('');
  const modalColumns = ref([]);
  const modalTableData = ref<any[]>([]);
  const recordDetail: Record<string, any> = ref({});
  const showModal = ref(false);
  const showRecord = ref(false);

  const clickRecordNum = (item: Record<string, any>) => {
    modalTitle.value = item.custom_order_sn;

    if (+item.custom_type === 1) {
      modalColumns.value = COLUMNS['3'];
      modalTableData.value = item.sub_detail;
      showModal.value = true;
    } else {
      recordDetail.value = item;
      showRecord.value = true;
    }
  };

  const payInfo = usePayStore();
  const { isLoading: isModalLoading, execute: executeInfo } = getOrderLogInfo();

  async function getModalData(type: number, cardOrderId: string) {
    const { data } = await executeInfo({
      data: { type, card_order_id: cardOrderId },
    });

    if (type === 0) {
      modalTableData.value = Object.keys(data.value)
        .sort()
        .map((key) => {
          return {
            name: AMOUNT_TYPE[key],
            value: Number(data.value[key]),
          };
        })
        .filter((item) => +item.value !== 0);
    } else {
      modalTableData.value = data.value.map((item: Record<string, any>) => {
        if (+type === 1) {
          const payItem = payInfo.payTypes.find((v) => v.pay_type_id === +item.pay_type);
          const payType = payItem?.pay_type_name;
          return {
            ...item,
            ...{
              payType,
              amount: `￥${item.amount}`,
            },
          };
        }
        return {
          ...item,
          ...{
            isMain: item.is_main === '1' ? '主归属' : '协助',
            percent: `${item.proportion}%`,
            amount: `￥${item.amount}`,
          },
        };
      });
    }
  }

  const handleShowDetail = (index: number, id: string) => {
    modalTitle.value = MODAL_TITLE[index];
    modalColumns.value = COLUMNS[index] || [];
    getModalData(index, id);
    showModal.value = true;
  };
  // 跳转会员详情
  const handleToUserDetail = (record: Record<string, any>) => {
    goSubDetail(record.user_id, record.bus_id);
  };

  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );

  const busInfo = useBusInfoStore();
  const currentState = window.history.state;
  if (!formModel.value.bus_id) {
    formModel.value.bus_id = currentState.busId || busInfo.bus_id || '';
  }
  if (currentState.beginDate && currentState.endDate) {
    formModel.value.rangeValue = [currentState.beginDate, currentState.endDate];
  }
  onMounted(() => {
    console.log('onMounted', formModel.value);
  });
  console.log('formModel.value', formModel.value);
  onActivated(() => {
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  });

  onBeforeUpdate(() => {
    console.log('onBeforeUpdate');
  });

  onDeactivated(() => {
    // 在从 DOM 上移除、进入缓存
    // 以及组件卸载时调用
    console.log('onDeactivated');
  });
</script>

<style scoped lang="less">
  .base-box {
    padding: 0 20px 20px 20px;
  }
</style>
