<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-tabs lazy-load>
        <a-tab-pane key="0" title="充值记录">
          <pay-record />
        </a-tab-pane>
        <a-tab-pane key="1" title="短信消耗">
          <msg-record />
        </a-tab-pane>
        <a-tab-pane key="2" title="合同券消耗">
          <electronic-contract />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import PayRecord from './components/pay-record.vue';
  import MsgRecord from './components/msg-record.vue';
  import ElectronicContract from './components/electronic-contract.vue';

  defineOptions({
    name: 'RechargeRecord',
  });
</script>
