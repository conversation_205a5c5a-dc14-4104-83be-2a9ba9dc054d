<template>
  <div class="base-box">
    <Breadcrumb />
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="region_bus" label="场馆">
                  <AdminRegion
                    v-model="formModel.region_bus"
                    :multiple="false"
                    url="/Web/OpenClass/get_openclass_region_bus"
                    placeholder="请选择"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="rangeValue" label="时间">
                  <a-range-picker v-model="formModel.rangeValue" :allow-clear="false" format="YYYY-MM-DD" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 32px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" :loading="isLoading" @click="search">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />

      <template v-if="totalStatList.length">
        <a-scrollbar type="track" style="height: 98px; overflow-x: auto">
          <a-space :size="10">
            <a-statistic
              v-for="(item, index) in totalStatList"
              :key="index"
              class="finance-statistics"
              :title="item.name"
              :value="Number(item.amount)"
              :start="false"
              :precision="2"
              :animation-duration="800"
              show-group-separator
              animation
            ></a-statistic>
          </a-space>
        </a-scrollbar>
        <a-divider style="margin-top: 0" />
      </template>

      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-space> </a-space>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: flex-end">
          <ExportExcel>
            <template #default="{ handleExport }">
              <a-button :loading="isLoading" @click="handleClickExport(handleExport as Callback<ExportData>)">
                导出
              </a-button>
            </template>
          </ExportExcel>
        </a-col>
      </a-row>

      <a-table
        row-key="id"
        :loading="isLoading"
        :pagination="tablePagination"
        :columns="(cloneColumns as TableColumnData[])"
        :data="renderData"
        :bordered="false"
        :scroll="{ x: 3500 }"
        :size="size"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
      >
        <template #operations="{ record }">
          <a-link @click="handleToDetail(record)"> 详情 </a-link>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import dayjs from 'dayjs';
  import cloneDeep from 'lodash/cloneDeep';
  import { computed, ref, reactive, watch } from 'vue';

  import type { PaginationProps } from '@arco-design/web-vue/es/pagination/interface';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import AdminRegion from '@/components/form/adminRegion.vue';
  import ExportExcel from '@/components/exportExcel.vue';

  // import useLoading from '@/hooks/loading';
  import { getBusinessLogAll } from '@/api/statistics';

  import { AnyObject, Callback, ExportData } from '@/types/global';

  defineOptions({
    name: 'BusinessLogAll',
  });

  type SizeProps = 'mini' | 'small' | 'medium' | 'large';
  type Column = TableColumnData & { checked?: true };

  const route = useRoute();

  const generateFormModel = () => {
    const today = new Date();
    // const days = new Date().getDate() - 1;
    // const beginDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    return {
      begin_date: '',
      end_date: '',
      rangeValue: [dayjs().subtract(30, 'day').$d, today],
      region_bus: '', // (场馆ID||区域ID)_(1区域||2场馆)_(1.一级，2.二级，3三级，4.四级)
    };
  };

  // const { loading, setLoading } = useLoading(true);
  const renderData = ref<AnyObject[]>([]);
  const totalStatList = ref([
    {
      key: 'all_mount', // 合计实收
      name: '合计实收',
      amount: '',
    },
  ]);
  const formModel = ref(generateFormModel());
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);

  const size = ref<SizeProps>('medium');

  const pagination: PaginationProps = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const tablePagination = computed(() => ({
    ...pagination,
    showPageSize: true,
    showTotal: true,
  }));
  const operateTypeList = ref([
    { value: 0, label: '购卡' },
    { value: 1, label: '续卡' },
    { value: 21, label: '购私教' },
    { value: 22, label: '续私教' },
    { value: 23, label: '购泳教' },
    { value: 24, label: '续泳教' },
    { value: 2, label: '升卡' },
    { value: 3, label: '转入卡' },
    { value: 5, label: '请假' },
    { value: 6, label: '销卡' },
    { value: 7, label: '租柜' },
    { value: 8, label: '商品售卖' },
    { value: 27, label: '商品退款' },
    { value: 9, label: '定金' },
    { value: 10, label: '退定金' },
    { value: 11, label: '押金' },
    { value: 12, label: '退押金' },
    { value: 13, label: '在线购课' },
    { value: 15, label: '补卡' },
    { value: 16, label: '付费活动' },
    { value: 17, label: '拆分' },
    { value: 18, label: '跨店购卡' },
    { value: 19, label: '场地预订' },
    { value: 20, label: '场地退订' },
    { value: 29, label: '票务' },
  ]);
  const columns = computed<TableColumnData[]>(() => [
    {
      dataIndex: 'name',
      title: '场馆',
      fixed: 'left',
      width: 200,
    },
    { dataIndex: 'income_amount', title: '总计', align: 'right' },
    { dataIndex: '0', title: '购卡', align: 'right' },
    { dataIndex: '1', title: '续卡', align: 'right' },
    { dataIndex: '21', title: '购私教', align: 'right' },
    { dataIndex: '22', title: '续私教', align: 'right' },
    { dataIndex: '23', title: '购泳教', align: 'right' },
    { dataIndex: '24', title: '续泳教', align: 'right' },
    { dataIndex: '2', title: '升卡', align: 'right' },
    { dataIndex: '3', title: '转卡（入）', align: 'right' },
    // { dataIndex: '4', title: '转卡（出）', align: 'right' },
    { dataIndex: '5', title: '请假', align: 'right' },
    { dataIndex: '6', title: '销卡', align: 'right' },
    { dataIndex: '7', title: '租柜', align: 'right' },
    { dataIndex: '8', title: '商品售卖', align: 'right' },
    { dataIndex: '27', title: '商品退款', align: 'right' },
    { dataIndex: '9', title: '定金', align: 'right' },
    { dataIndex: '10', title: '退定金', align: 'right' },
    { dataIndex: '11', title: '押金', align: 'right' },
    { dataIndex: '12', title: '退押金', align: 'right' },
    { dataIndex: '13', title: '在线购课', align: 'right' },
    // { dataIndex: '14', title: '赠体验卡', align: 'right' },
    { dataIndex: '15', title: '补卡', align: 'right' },
    { dataIndex: '16', title: '付费活动', align: 'right' },
    { dataIndex: '17', title: '卡拆分', align: 'right' },
    { dataIndex: '18', title: '跨店购卡', align: 'right' },
    { dataIndex: '19', title: '场地预订', align: 'right' },
    { dataIndex: '20', title: '场地退订', align: 'right' },
    // { dataIndex: '25', title: '编辑卡', align: 'right' },
    // { dataIndex: '26', title: '销账', align: 'right' },
    // { dataIndex: '28', title: '套餐包', align: 'right' },
    { dataIndex: '29', title: '票务', align: 'right' },
    {
      title: '操作',
      dataIndex: 'operations',
      slotName: 'operations',
      width: 100,
      fixed: 'right',
      align: 'right',
    },
  ]);

  const { isLoading, execute } = getBusinessLogAll();

  const fetchData = async (params: any, isExport = false) => {
    if (params.rangeValue.length) {
      params.begin_date = dayjs(params.rangeValue[0]).format('YYYY-MM-DD');
      params.end_date = dayjs(params.rangeValue[1]).format('YYYY-MM-DD');
      delete params.rangeValue;
    }
    const { data } = await execute({
      data: params,
    });
    const list: AnyObject[] = data.value.list || [];

    if (isExport) {
      return Promise.resolve(list);
    }

    pagination.total = +data.value.count;
    renderData.value = data.value.list;
    totalStatList.value.forEach((v) => {
      // v.amount = data.value.total_stat[v.key] || '0';
      v.amount = data.value.all_mount || '';
    });

    return Promise.resolve(list);
  };

  onActivated(() => {
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  });

  const handleClickExport = (cb: Callback<ExportData>) => {
    fetchData(
      {
        ...formModel.value,
        current: 1,
        pageSize: pagination.total,
      },
      true
    ).then((list) => {
      cb({
        filename: (route.matched[route.matched.length - 1].meta.locale as string | undefined) || '业务流水汇总',
        columns: columns.value.filter((v) => v.dataIndex !== 'operations'),
        data: list,
      });
    });
  };

  const search = () => {
    pagination.current = 1;
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...formModel.value,
    });
  };

  const onPageChange = (current: number) => {
    pagination.current = current;
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const onPageSizeChange = (pageSize: number) => {
    pagination.current = 1;
    pagination.pageSize = pageSize;
    fetchData({
      ...formModel.value,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const router = useRouter();
  const handleToDetail = (record: any) => {
    router.push({
      // 业务流水
      name: 'Inventory',
      query: {
        beginTime: dayjs(formModel.value.rangeValue[0]).format('YYYY-MM-DD'),
        endTime: dayjs(formModel.value.rangeValue[1]).format('YYYY-MM-DD'),
        busId: record.id,
      },
    });
  };

  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );
</script>

<style scoped lang="less">
  .base-box {
    padding: 0 20px 20px 20px;
  }
</style>
