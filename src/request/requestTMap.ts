const qqKey = 'N5GBZ-D736C-WFA25-AGZXU-UKCME-6WFA5';
// const libs = ['visualization', 'tools', 'geometry', 'model', 'view', 'service'];
const libs = ['service', 'tools'];

const loadScript = (resolve: any) => {
  if ((window as any).TMap) {
    console.log('TMap 已经加载了哈~');
    resolve((window as any).TMap);
    return;
  }

  (window as any).tmapCallback = function tmapCallback() {
    console.log('library 加载成功了哈~');
    if ((window as any)?.TMap) {
      console.log('TMap 已经加载了哈~');
    }
    resolve((window as any).TMap);
  };

  // 在乾坤子应用中使用
  // if (window.__POWERED_BY_QIANKUN__ && top) {
  //   top.tmapCallback = function tmapCallback() {
  //     console.log('乾坤 加载成功了哈~');
  //     if (TMap) {
  //       console.log('TMap 已经加载了哈~');
  //     }
  //     resolve(TMap);
  //   };
  // }

  const script = document.createElement('script');
  // script.ignore = true;
  script.type = 'text/javascript';
  script.src = `https://map.qq.com/api/gljs?v=1.exp&key=${qqKey}&callback=tmapCallback&libraries=${libs.join(',')}`;
  document.body.appendChild(script);
};

let timer: any = null;
const timeout = 30000;

const loadTMap = () => {
  if ((window as any)?.TMap) {
    console.log('TMap 已经加载了哈~');
    return;
  }
  Promise.race([
    new Promise((resolve, reject) => {
      timer = setTimeout(() => {
        reject(new Error('地图加载超时，可刷新重新加载！'));
      }, timeout);
    }),
    new Promise((resolve) => {
      loadScript(resolve);
    }),
  ]).finally(() => {
    clearTimeout(timer);
  });
};

export default loadTMap;
