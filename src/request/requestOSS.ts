// @ts-ignore
import OSS from 'ali-oss';
import axios from 'axios';
import request from './index';

let client: any = null;

const init = async () => {
  if (!client) {
    // const settings = await service.get('/Web/business/get_oss_token').then((res) => res.data);
    const settingsRes: any = await request({
      url: '/Web/business/get_oss_token',
      options: {
        method: 'GET',
      },
    });
    const settings = settingsRes.response.value;
    client = new OSS({
      // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
      region: 'oss-cn-shenzhen',
      // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
      accessKeyId: settings.AccessKeyId,
      accessKeySecret: settings.AccessKeySecret,
      // 从STS服务获取的安全令牌（SecurityToken）。
      stsToken: settings.SecurityToken,
      // 填写Bucket名称。
      bucket: 'rb-platform',
      secure: true,
    });
  }
};

let env = '';
const domain = window.location.host.split('.')[0];
if (['fe', 'vip-test'].includes(domain)) {
  env = '/test/json/';
} else if (['vip-sim'].includes(domain)) {
  env = '/sim/json/';
} else {
  env = '/online/json/';
}

export const pushFile = async (directory: string, filename: string, fileJSON: any): Promise<string | boolean> => {
  await init();
  try {
    // object表示上传到OSS的文件名称。
    // file表示浏览器中需要上传的文件，支持HTML5 file和Blob类型。
    const bf = new OSS.Buffer(JSON.stringify(fileJSON));
    if (directory.length > 0 && directory[directory.length - 1] !== '/') {
      directory = `${directory}/`;
    }
    const cb = await client.put(`${env}${directory}${filename}`, bf);
    return cb.url;
  } catch (e) {
    console.error('error: ', e);
    return false;
  }
};

export async function fetchFile(url: string) {
  return axios.get(url).then((res) => res.data);
}
