import axios from 'axios';
import Qs from 'qs';
import type { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { Message } from '@arco-design/web-vue';
import { useAxios } from '@vueuse/integrations/useAxios';
import { getBaseUrl } from '@/config/url';
import { uuid } from '@/utils';

/**
 * showError 是否显示错误提示 false不显示
 * pageKey 分页参数 默认 page: 'page_no', size: 'page_size'
 */
export interface RequestConfig extends AxiosRequestConfig {
  showError?: boolean;
  pageKey?: {
    page: string;
    size: string;
  };
}

export interface HttpResponse<T = any> {
  errormsg: string;
  errorcode?: number;
  trace_id?: string;
  data: T;
  [key: string]: unknown;
}
const axiosInstance = axios.create({
  baseURL: getBaseUrl(),
  withCredentials: true,
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  transformRequest: [
    (data, headers) => {
      // `transformRequest` allows changes to the request data before it is sent to the server
      // This is only applicable for request methods 'PUT', 'POST', and 'PATCH'
      return headers['Content-Type'] === 'application/json' ? JSON.stringify(data) : Qs.stringify(data);
    },
  ],
  timeout: 30 * 1000, // 请求超时时间
});

axiosInstance.interceptors.request.use(
  (config: RequestConfig) => {
    config.headers.requestId = uuid();
    // 分页数据多于50条时,超时设置为50秒
    const configData = config.method === 'get' ? config.params : config.data;
    // 分页接口，前端这边统一使用current、pageSize，请求时转换为后端接收的page_no、page_size
    if (configData?.pageSize && configData?.current) {
      configData[config.pageKey ? config.pageKey.size : 'page_size'] = configData.pageSize;
      configData[config.pageKey ? config.pageKey.page : 'page_no'] = configData.current;
      delete configData.current;
      delete configData.pageSize;
    }

    if (configData && +configData.page_size > 50) {
      config.timeout = 50 * 1000;
    }
    return config;
  },
  (error) => {
    // do something
    return Promise.reject(error);
  }
);

// add response interceptors
axiosInstance.interceptors.response.use(
  (response: AxiosResponse<HttpResponse>) => {
    const res = response.data;
    if (typeof res.errorcode !== 'undefined' && res.errorcode !== 0) {
      if (response.config?.showError !== false) {
        Message.error({
          id: 'errorInResponse',
          content: res.errormsg || '网络错误',
          duration: 3 * 1000,
        });
      }
      return Promise.reject(res);
    }
    return res;
  },
  (error: AxiosError) => {
    // 当主动取消请求时
    if (axios.isCancel(error)) {
      Message.error({
        id: 'errorInResponse',
        content: '请求过于频繁！',
        duration: 3 * 1000,
      });
    } else {
      Message.error({
        id: 'errorInResponse',
        content: error.message || '请求错误',
        duration: 3 * 1000,
      });
    }
    return Promise.reject(error);
  }
);
/**
 * @param {string} url - 发送请求的URL。
 * @param {string} options - AxiosRequestConfig选项，默认为{ method: 'GET' }。
 * @param {boolean} immediate - 确定请求是立即执行还是等待调用返回的execute()再执行。
 */
export default function request<T = any>({
  url,
  options = { method: 'GET' },
  immediate = true,
  ...otherUseAxiosOptions
}: {
  url: string;
  options: RequestConfig;
  immediate?: boolean;
  [key: string]: any;
}) {
  const useAxiosResult = useAxios<T>(url, options, axiosInstance, {
    immediate,
    ...otherUseAxiosOptions,
  });
  return {
    ...useAxiosResult,
    requestOptions: options,
  };
}
