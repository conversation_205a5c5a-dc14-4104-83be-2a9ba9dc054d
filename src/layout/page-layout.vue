<template>
  <router-view v-slot="{ Component, route }">
    <transition name="fade" mode="out-in" appear>
      <keep-alive :include="Array.from(keepAliveComponents)">
        <component :is="Component" :key="route.fullPath" />
      </keep-alive>
    </transition>
  </router-view>
</template>

<script lang="ts" setup>
  import type { RouteLocationNormalized } from 'vue-router';
  import { listenerRouteChange, removeRouteListener } from '@/utils/route-listener';

  const router = useRouter();
  const keepAliveComponents = ref(new Set<string>());
  // 同时拿出所有需要缓存的组件 这样当两个路由都指向同一个组件的时候，可以名中另一个路由名称
  function initKeepAliveComponents() {
    router
      .getRoutes()
      .filter((route) => route.meta.keepAlive)
      .forEach((route: RouteLocationNormalized) => {
        keepAliveComponents.value.add(route.name as string);
      });
  }
  // 防止动态路由和懒加载及路由元数据的动态更改
  function updateKeepAliveComponents(currentRoute: RouteLocationNormalized) {
    if (currentRoute.meta.keepAlive) {
      keepAliveComponents.value.add(currentRoute.name as string);
    } else {
      keepAliveComponents.value.delete(currentRoute.name as string);
    }
  }
  initKeepAliveComponents();
  listenerRouteChange((route: RouteLocationNormalized) => {
    updateKeepAliveComponents(route);
  }, true);
  onUnmounted(() => {
    removeRouteListener();
  });
</script>

<style scoped lang="less"></style>
