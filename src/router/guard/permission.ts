import type { Router, RouteRecordNormalized } from 'vue-router';
import NProgress from 'nprogress'; // progress bar
import { WHITE_LIST, NOT_FOUND } from '../constants';

export default function setupPermissionGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    // await appStore.fetchServerMenuConfig();
    const serverMenuConfig = [...WHITE_LIST];

    let exist = false;
    while (serverMenuConfig.length && !exist) {
      const element = serverMenuConfig.shift();
      if (element?.name === to.name) exist = true;

      if (element?.children) {
        serverMenuConfig.push(...(element.children as unknown as RouteRecordNormalized[]));
      }
    }
    if (exist) {
      next();
    } else next(NOT_FOUND);
    NProgress.done();
  });
}
