import type { Router } from 'vue-router';
import NProgress from 'nprogress';
import { setRouteEmitter } from '@/utils/route-listener';
import { useBusInfoStore } from '@/store';
import { isMicroApp } from '@/qiankun/config';

// import setupPermissionGuard from './permission';

function setDocumentTitle(to) {
  const busInfo = useBusInfoStore();
  document.title = `${window.IS_BRAND_SITE ? busInfo.mer_name : busInfo.bus_name || '勤鸟运动'}${
    to.meta.locale ? `_${to.meta.locale}` : ''
  }`;
}
let hasSetUpPageGuard = false;
function setupPageGuard(router: Router) {
  if (hasSetUpPageGuard) {
    return;
  }
  hasSetUpPageGuard = true;
  router.beforeEach(async (to, from, next) => {
    // emit route change
    setRouteEmitter(to);
    NProgress.start();

    // fix 主应用到子应用，然后子应用中有页面跳转之后就没办法再跳转到其他页面 报错Failed to execute 'replace' on 'Location': 'xxx undefined' is not a valid URL.
    // 解决办法：手动修改history的state
    if (!window.history.state.current) window.history.state.current = to.fullPath;
    if (!window.history.state.back) window.history.state.back = from.fullPath;

    if (!isMicroApp(to.path)) {
      setDocumentTitle(to);
    }
    next();
    NProgress.done();
  });
}

export default function createRouteGuard(router: Router) {
  setupPageGuard(router);
  // TODO 商家端和门店端权限控制
  // setupPermissionGuard(router);
}
