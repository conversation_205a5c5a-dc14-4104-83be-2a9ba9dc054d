import { defineComponent } from 'vue';
import type { RouteMeta, NavigationGuard } from 'vue-router';

type Meta = RouteMeta & {
  locale?: string;
  hideHeader?: boolean; // 是否隐藏头部
  hideSidebar?: boolean; // 是否隐藏侧边栏
  needLink?: boolean; // 面包屑导航可点击 只有恒等于false时，面包屑导航不可点击
  v2path?: string; // 用于当前页面在门店端需要面包屑跳到门店端路由时，指定门店端的路由
  parentName?: string; // 用于面包屑导航 当需要指定上级时 会通过此字段递归向上查找对应名称的路由 直到parentName为空
};

export type Component<T = any> =
  | ReturnType<typeof defineComponent>
  | (() => Promise<typeof import('*.vue')>)
  | (() => Promise<T>);

export interface AppRouteRecordRaw {
  path: string;
  name?: string | symbol;
  meta?: Meta;
  redirect?: string;
  component: Component | string;
  children?: AppRouteRecordRaw[];
  alias?: string | string[];
  props?: Record<string, any> | boolean;
  beforeEnter?: NavigationGuard | NavigationGuard[];
  fullPath?: string;
}
