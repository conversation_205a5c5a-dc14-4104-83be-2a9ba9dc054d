import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const COUPON: AppRouteRecordRaw = {
  path: '/coupon',
  name: 'coupon',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '折扣券',
  },
  children: [
    {
      path: 'list',
      name: 'CouponList',
      component: () => import('@/views/coupon/list.vue'),
      meta: {
        locale: '折扣券列表',
        keepAlive: true,
      },
    },
    {
      path: 'add',
      name: 'CouponAdd',
      component: () => import('@/views/coupon/add.vue'),
      meta: {
        locale: '折扣券添加',
      },
    },
    {
      path: 'out-records/:id?',
      name: 'OutRecords',
      component: () => import('@/views/coupon/out-records.vue'),
      meta: {
        locale: '发放记录',
      },
    },
    {
      path: 'receive-records/:id?',
      name: 'ReceiveRecords',
      component: () => import('@/views/coupon/receive-records.vue'),
      meta: {
        locale: '领取记录',
      },
    },
  ],
};

export default COUPON;
