import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ONE_TIME_PAY: AppRouteRecordRaw = {
  path: '/one-time-pay',
  name: 'OneTimePay',
  meta: {
    locale: '管理',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'list/:cardId?',
      name: 'OneTimePayList',
      props: true,
      component: () => import('@/views/one-time-pay/list.vue'),
      meta: {
        keepAlive: false,
        locale: '单节付费课管理',
      },
    },
    {
      path: 'save/:id?',
      name: 'OneTimePaySave',
      props: true,
      component: () => import('@/views/one-time-pay/save.vue'),
      meta: {
        keepAlive: false,
        locale: '单节付费课方案',
      },
    },
    {
      path: 'cancel/:id/:direction?',
      name: 'OneTimePayCancel',
      props: true,
      component: () => import('@/views/one-time-pay/cancel.vue'),
      meta: {
        keepAlive: false,
        locale: '单节付费课取消',
      },
    },
  ],
};

export default ONE_TIME_PAY;
