import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ALIPAY: AppRouteRecordRaw = {
  path: '/marketing',
  name: 'Marketing',
  meta: {
    locale: '运营',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'group-online',
      name: 'GroupOnline',
      component: () => import('@/views/marketing/group-online.vue'),
      meta: {
        keepAlive: true,
        locale: '线上团购配置',
      },
    },
    {
      path: 'apply-info',
      name: 'ApplyInfo',
      component: () => import('@/views/marketing/apply-info.vue'),
      meta: {
        locale: '报名信息',
      },
    },
  ],
};

export default ALIPAY;
