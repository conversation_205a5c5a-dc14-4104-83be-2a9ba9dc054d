import DEFAULT_LAYOUT_MOBILE from '@/views/mobile-page/default-layout-mobile.vue';
import { AppRouteRecordRaw } from '../types';

const MOBILE: AppRouteRecordRaw = {
  path: '/mobile',
  name: '<PERSON><PERSON><PERSON><PERSON>',
  meta: {
    locale: '移动端',
  },
  component: DEFAULT_LAYOUT_MOBILE,
  children: [
    {
      path: 'verify',
      name: 'verifyMobile',
      component: () => import('@/views/mobile-page/verify/index.vue'),
      meta: {
        keepAlive: true,
        locale: '核销首页',
      },
    },
    {
      path: 'verify-action',
      name: 'verifyAction',
      component: () => import('@/views/mobile-page/verify/action.vue'),
      meta: {
        keepAlive: true,
        locale: '核销',
      },
    },
    {
      path: 'verify-record',
      name: 'verifyRecord',
      component: () => import('@/views/mobile-page/verify/record.vue'),
      meta: {
        keepAlive: true,
        locale: '核销记录',
      },
    },
  ],
};

export default MOBILE;
