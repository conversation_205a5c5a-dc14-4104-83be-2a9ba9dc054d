import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const FINANCE: AppRouteRecordRaw = {
  path: '/finance',
  name: 'finance',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '财务',
  },
  children: [
    {
      path: 'flow-log-all',
      name: 'FlowLogAll',
      component: () => import('@/views/finance/flowLogAll.vue'),
      meta: {
        locale: '流水汇总',
      },
    },
    {
      path: 'receive-log-all',
      name: 'ReceiveLogAll',
      component: () => import('@/views/finance/receiveLogAll.vue'),
      meta: {
        keepAlive: true,
        locale: '收银流水汇总',
      },
    },
    {
      path: 'receive-log/:busId?/:beginDate?/:endDate?',
      name: 'ReceiveLog',
      component: () => import('@/views/finance/receiveLog.vue'),
      meta: {
        keepAlive: true,
        locale: '收银流水',
      },
    },
    {
      path: 'business-log-all',
      name: 'BusinessLogAll',
      component: () => import('@/views/finance/businessLogAll.vue'),
      meta: {
        keepAlive: true,
        locale: '业务流水汇总',
      },
    },
    {
      path: 'inventory',
      name: 'Inventory',
      component: () => import('@/views/finance/inventory.vue'),
      meta: {
        keepAlive: true,
        locale: '业务流水',
      },
    },
    {
      path: 'order-log-all',
      name: 'OrderLogAll',
      component: () => import('@/views/finance/orderLogAll.vue'),
      meta: {
        keepAlive: true,
        locale: '合同订单汇总',
      },
    },
    {
      path: 'order-log',
      name: 'OrderLog',
      component: () => import('@/views/finance/orderLog.vue'),
      meta: {
        keepAlive: true,
        locale: '合同订单',
      },
    },
    {
      path: 'reconciliation',
      name: 'Reconciliation',
      component: () => import('@/views/finance/reconciliation.vue'),
      meta: {
        keepAlive: true,
        locale: '跨店销售对账表',
      },
    },
    {
      path: 'recharge-record',
      name: 'RechargeRecord',
      component: () => import('@/views/finance/recharge-record.vue'),
      meta: {
        keepAlive: true,
        locale: '充值记录列表',
      },
    },
  ],
};

export default FINANCE;
