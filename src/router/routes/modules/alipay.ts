import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ALIPAY: AppRouteRecordRaw = {
  path: '/alipay',
  name: '<PERSON><PERSON><PERSON>',
  meta: {
    locale: '支付宝月付',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'sales-commission',
      name: 'salesCommission',
      component: () => import('@/views/alipay/sales-commission.vue'),
      meta: {
        keepAlive: true,
        locale: '提成汇总',
      },
    },
    {
      path: 'sales-commission-rule',
      name: 'salesCommissionRule',
      component: () => import('@/views/alipay/sales-commission-rule.vue'),
      meta: {
        keepAlive: true,
        locale: '方案列表',
      },
    },
    {
      path: 'sales-commission-rule-save',
      name: 'salesCommissionRuleSave',
      component: () => import('@/views/alipay/sales-commission-rule-save.vue'),
      meta: {
        keepAlive: false,
        locale: '提成规则设置',
      },
    },
    // {
    //   path: 'finance-receipt',
    //   name: 'financeReceipt',
    //   component: () => import('@/views/alipay/finance-receipt.vue'),
    //   meta: {
    //     keepAlive: true,
    //     locale: '财务对账单',
    //   },
    // },
    {
      path: 'member-settings',
      name: 'MemberSettings',
      component: () => import('@/views/alipay/member-settings.vue'),
      meta: {
        keepAlive: true,
        locale: '支付宝配置',
      },
    },
  ],
};

export default ALIPAY;
