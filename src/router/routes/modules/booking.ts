import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const BOOKING: AppRouteRecordRaw = {
  path: '/booking',
  name: 'Booking',
  meta: {
    locale: '订场',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'long-term-rental',
      name: 'longTermRental',
      component: () => import('@/views/booking/long-term-rental.vue'),
      meta: {
        keepAlive: false,
        locale: '长租',
      },
    },
    {
      path: 'long-term-rental-cancel/:id',
      name: 'LongTermRentalCancel',
      props: true,
      component: () => import('@/views/booking/long-term-rental-cancel.vue'),
      meta: {
        keepAlive: false,
        locale: '长租批量取消',
      },
    },
    {
      path: 'stadium-save/:id?',
      name: 'StadiumSave',
      props: true,
      component: () => import('@/views/booking/stadium-save.vue'),
      meta: {
        keepAlive: false,
        locale: '场地',
      },
    },
    {
      path: 'strategy-list',
      name: 'StrategyList',
      component: () => import('@/views/booking/strategy-list.vue'),
      meta: {
        keepAlive: false,
        locale: '排场&定价方案记录',
      },
    },
    {
      path: 'strategy-save/:id?',
      name: 'StrategySave',
      props: true,
      component: () => import('@/views/booking/strategy-save.vue'),
      meta: {
        keepAlive: false,
        locale: '排场&定价',
      },
    },
  ],
};

export default BOOKING;
