import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const DASHBOARD: AppRouteRecordRaw = {
  path: '/index',
  name: 'index',
  meta: {
    locale: '首页',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: '/overview',
      name: 'Overview',
      component: () => import('@/views/index/overview.vue'),
      meta: {
        locale: '概况',
      },
    },
    {
      path: '/electronic',
      name: 'electronic',
      component: () => import('@/views/system/electronic.vue'),
      meta: {
        locale: '开通电子合同',
      },
    },
  ],
};

export default DASHBOARD;
