import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const SETTINGS: AppRouteRecordRaw = {
  path: '/settings',
  name: 'settings',
  meta: {
    locale: '设置',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'system-config',
      name: 'SystemConfig',
      component: () => import('@/views/settings/system-config.vue'),
      meta: {
        locale: '系统设置',
      },
    },
  ],
};

export default SETTINGS;
