import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ALIPAYMERCHANT: AppRouteRecordRaw = {
  path: '/alipay-merchant',
  name: 'AlipayMerchant',
  meta: {
    locale: '支付宝安心付',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'sales-commission',
      name: 'merchantSalesCommission',
      component: () => import('@/views/alipay-merchant/sales-commission.vue'),
      meta: {
        keepAlive: true,
        locale: '提成汇总',
      },
    },
    {
      path: 'sales-commission-rule',
      name: 'merchantSalesCommissionRule',
      component: () => import('@/views/alipay-merchant/sales-commission-rule.vue'),
      meta: {
        keepAlive: true,
        locale: '方案列表',
      },
    },
    {
      path: 'sales-commission-rule-save',
      name: 'merchantSalesCommissionRuleSave',
      component: () => import('@/views/alipay-merchant/sales-commission-rule-save.vue'),
      meta: {
        keepAlive: false,
        locale: '提成规则设置',
      },
    },
    // {
    //   path: 'finance-receipt',
    //   name: 'financeReceipt',
    //   component: () => import('@/views/alipay-merchant/finance-receipt.vue'),
    //   meta: {
    //     keepAlive: true,
    //     locale: '财务对账单',
    //   },
    // },
    {
      path: 'member-settings',
      name: 'merchantMemberSettings',
      component: () => import('@/views/alipay-merchant/member-settings.vue'),
      meta: {
        keepAlive: true,
        locale: '支付宝配置',
      },
    },
  ],
};

export default ALIPAYMERCHANT;
