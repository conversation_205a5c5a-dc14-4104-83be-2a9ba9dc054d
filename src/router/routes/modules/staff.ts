import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const STAFF: AppRouteRecordRaw = {
  path: '/staff',
  name: 'Staff',
  meta: {
    locale: '人员管理',
  },
  component: DEFAULT_LAYOUT,
  children: [
    {
      path: 'coach-save/:id?',
      props: true,
      name: 'coachSave',
      component: () => import('@/views/staff/coach-save.vue'),
      meta: {
        keepAlive: false,
        locale: '教练',
      },
    },
    {
      path: 'coach-list',
      name: 'coachList',
      component: () => import('@/views/staff/coach-list.vue'),
      meta: {
        keepAlive: true,
        locale: '教练列表',
      },
    },
  ],
};

export default STAFF;
