import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const Admin: AppRouteRecordRaw = {
  path: '/admin',
  name: 'Admin',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '管理',
  },
  children: [
    {
      path: 'list',
      name: 'AdminList',
      component: () => import('@/views/admin/list.vue'),
      meta: {
        keepAlive: true,
        locale: '账号管理',
      },
    },
    {
      path: 'add',
      name: 'AdminAdd',
      component: () => import('@/views/admin/add.vue'),
      meta: {
        locale: '账号添加',
      },
    },
    {
      path: 'account-reachage',
      name: 'AccountReachage',
      component: () => import('@/views/account/reachage.vue'),
      meta: {
        hideSidebar: true,
        locale: '续费',
      },
    },
    {
      path: 'black-list',
      name: 'BlackList',
      component: () => import('@/views/black-list/list.vue'),
      meta: {
        keepAlive: true,
        locale: '会员黑名单',
      },
    },
    {
      path: 'black-list-add',
      name: 'BlackListAdd',
      component: () => import('@/views/black-list/add.vue'),
      meta: {
        locale: '添加会员黑名单',
      },
    },
    {
      path: 'black-list-edit',
      name: 'BlackListEdit',
      component: () => import('@/views/black-list/add.vue'),
      meta: {
        locale: '编辑会员黑名单',
      },
    },
    {
      path: 'update-admin',
      name: 'UpdateAdmin',
      component: () => import('@/views/account/updateAdmin.vue'),
      meta: {
        locale: '修改密码',
      },
    },
    {
      path: 'card-list',
      name: 'CardList',
      component: () => import('@/views/card/list.vue'),
      meta: {
        keepAlive: true,
        locale: '会员卡设置',
        parentName: 'Admin',
      },
    },
    {
      path: 'card-add/:id/:msPtSwimType',
      name: 'CardAdd',
      component: () => import('@/views/card/add.vue'),
      meta: {
        locale: '会员卡编辑',
        parentName: 'CardList',
      },
    },
    {
      path: 'card-group',
      name: 'CardGroup',
      component: () => import('@/views/card/card-group.vue'),
      meta: {
        locale: '课程组',
      },
    },
    {
      path: 'sale-list',
      name: 'SaleList',
      component: () => import('@/views/third-party/sale-list.vue'),
      meta: {
        locale: '线上团购配置',
      },
    },
    {
      path: 'sale-add/:id?',
      name: 'SaleAdd',
      component: () => import('@/views/third-party/sale-add.vue'),
      meta: {
        locale: '美评抖音售卖',
      },
    },
    {
      path: 'verify-list',
      name: 'VerifyList',
      component: () => import('@/views/third-party/verify-list.vue'),
      meta: {
        locale: '团购核销',
        keepAlive: true,
      },
    },
  ],
};

export default Admin;
