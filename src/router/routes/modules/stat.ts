import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

// 统计报表
const STAT: AppRouteRecordRaw = {
  path: '/stat',
  name: 'Stat',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '报表',
  },
  children: [
    {
      path: 'table-nav',
      name: 'TableNav',
      component: () => import('@/views/statistics/tableNav.vue'),
      meta: {
        locale: '统计报表',
        keepAlive: true,
        needLink: true,
        v2path: '/v2/stat/menus',
        parentName: 'Stat',
      },
    },
    {
      path: 'table-nav/passenger-flow-log-all',
      name: 'PassengerFlowLogAll',
      component: () => import('@/views/statistics/passengerFlowLogAll.vue'),
      meta: {
        locale: '客流汇总',
        parentName: 'TableNav',
      },
    },
    {
      path: 'table-nav/open_class-expend-log-all',
      name: 'OpenClassExpendLogAll',
      component: () => import('@/views/statistics/openClassExpendLogAll.vue'),
      meta: {
        locale: '团课课时汇总',
        parentName: 'TableNav',
      },
    },
    {
      path: 'table-nav/ticket-log-all',
      name: 'TicketLogAll',
      component: () => import('@/views/statistics/ticketLogAll.vue'),
      meta: {
        keepAlive: true,
        parentName: 'TableNav',
        locale: '散场票汇总',
      },
    },
    {
      path: 'table-nav/space-income-log-all',
      name: 'SpaceIncomeLogAll',
      component: () => import('@/views/statistics/spaceIncomeLogAll.vue'),
      meta: {
        keepAlive: true,
        parentName: 'TableNav',
        locale: '场地收益汇总',
      },
    },
    {
      path: 'table-nav/sign-list',
      name: 'signList',
      component: () => import('@/views/statistics/signList.vue'),
      meta: {
        keepAlive: true,
        parentName: 'TableNav',
        locale: '训练次数统计',
      },
    },
    {
      path: 'table-nav/new-member-brand',
      name: 'NewMemberBrand',
      component: () => import('@/views/statistics/newMemberBrand.vue'),
      meta: {
        locale: '新入会会员月报',
        parentName: 'TableNav',
      },
    },
    {
      path: 'table-nav/new-member',
      name: 'NewMember',
      component: () => import('@/views/statistics/newMember.vue'),
      meta: {
        locale: '新入会会员月报',
        parentName: 'TableNav',
      },
    },
    {
      path: 'table-nav/channel-report-brand',
      name: 'ChannelReportBrand',
      component: () => import('@/views/statistics/channelReportBrand.vue'),
      meta: {
        locale: '会员转化分析',
        parentName: 'TableNav',
      },
    },
    {
      path: 'table-nav/channel-report',
      name: 'ChannelReport',
      component: () => import('@/views/statistics/channelReport.vue'),
      meta: {
        locale: '会员转化分析',
        parentName: 'TableNav',
      },
    },
    {
      path: 'table-nav/card-report-brand',
      name: 'CardReportBrand',
      component: () => import('@/views/statistics/cardReportBrand.vue'),
      meta: {
        locale: '卡课分析',
        parentName: 'TableNav',
      },
    },
    {
      path: 'table-nav/card-report',
      name: 'CardReport',
      component: () => import('@/views/statistics/cardReport.vue'),
      meta: {
        locale: '卡课分析',
        parentName: 'TableNav',
      },
    },
  ],
};

export default STAT;
