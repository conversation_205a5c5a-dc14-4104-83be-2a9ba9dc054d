import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const CLASS: AppRouteRecordRaw = {
  path: '/class',
  name: 'class',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '课程',
  },
  children: [
    {
      path: 'open-class',
      name: 'OpenClass',
      component: () => import('@/views/class/open-class.vue'),
      meta: {
        locale: '课种管理',
      },
    },
    {
      path: 'open-add/:id?',
      name: 'OpenAdd',
      component: () => import('@/views/class/open-add.vue'),
      meta: {
        locale: '课种编辑',
      },
    },
    {
      path: 'open-schedule',
      name: 'OpenSchedule',
      component: () => import('@/views/class/open-schedule.vue'),
      meta: {
        locale: '团课排课',
      },
    },
    {
      path: 'charge-plane',
      name: 'ChargePlane',
      component: () => import('@/views/class/charge-plane.vue'),
      meta: {
        locale: '团课收费方案',
      },
    },
    {
      path: 'charge-plane-detail/:id?',
      name: 'ChargePlaneDetail',
      component: () => import('@/views/class/charge-plane-detail.vue'),
      meta: {
        locale: '方案详情',
      },
    },
    {
      path: 'open-reser/:date?/:coachId?/:classId?',
      name: 'OpenReser',
      component: () => import('@/views/class/open-reser.vue'),
      meta: {
        locale: '团课预约',
        keepAlive: true,
      },
    },
    {
      path: 'open-reser-detail/:id',
      name: 'OpenReserDetail',
      component: () => import('@/views/class/open-reser-detail.vue'),
      meta: {
        locale: '预约详情',
        keepAlive: true,
      },
    },
    {
      path: 'open-coach-sign',
      name: 'OpenCoachSign',
      component: () => import('@/views/class/open-coach-sign.vue'),
      meta: {
        locale: '团课教练签到',
      },
    },
    {
      path: 'open-reser-subscription/:scheduleId/:busId',
      props: true,
      name: 'OpenReserSubscription',
      component: () => import('@/views/class/open-reser-subscription.vue'),
      meta: {
        locale: '订阅详情',
      },
    },
  ],
};

export default CLASS;
