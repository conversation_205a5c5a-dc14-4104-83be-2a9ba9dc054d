export interface BusInfoState {
  admin_id: string; // 管理员id
  admin_name: string;
  admin_type: string; // 账号是否是商家管理员
  bus_id: string; // 当前场馆ID
  m_id: string; // 当前商家ID
  mer_name: string;
  bus_list: Record<string, any>[]; // 当前账号下所有有权限的场馆（包括过期和试用场馆）
  m_list: Record<string, any>[]; // 当前账号下所有有权限的商家信息和场馆信息
  bus_name: string; // 当前场馆名称
  is_qn_j: number; // 当前账号是否开通了新版会员端
  is_show_custom_logo: boolean; // 是否在侧边栏展示自定义logo
  is_support_pay: string; // 是否支持在线支付
  thumb: string;
  adminBusList: Record<string, any>[]; // 账号权限下当前商家的场馆列表 当为admin账号的时候显示的是当前商家下所有场馆
  merchantsBusList: Record<string, any>[]; // 当前商家下所有场馆列表
  [key: string]: any;
}
