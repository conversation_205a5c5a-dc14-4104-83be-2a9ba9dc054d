import { getAdminBusList } from '@/api/member-list';
import { getBusinessBusList } from '@/api/business';
import { getMerchantsBusList } from '@/api/card';
import { BusInfoState } from './types';

const useBusInfoStore = defineStore('busInfo', {
  state: (): BusInfoState => ({
    admin_id: '',
    admin_name: '',
    admin_type: '',
    bus_id: '',
    m_id: '',
    mer_name: '',
    bus_list: [],
    m_list: [],
    adminBusList: [],
    merchantsBusList: [],
    bus_name: '',
    is_qn_j: 1,
    is_show_custom_logo: false,
    is_support_pay: '',
    thumb: '',
  }),

  getters: {
    busInfo(state: BusInfoState): BusInfoState {
      return { ...state };
    },
  },

  actions: {
    setInfo(partial: Partial<BusInfoState>) {
      this.$patch(partial);
    },

    // Reset information
    resetInfo() {
      this.$reset();
    },

    async setInfoByServe() {
      // 该接口不会返回adminBusList和merchantsBusList
      const { data } = await getBusinessBusList();
      this.setInfo(data.value);
    },
    async setAdminBusList() {
      const { data } = await getAdminBusList();
      this.setInfo({
        adminBusList: data.value,
      });
    },
    async setMerchantsBusList() {
      const { data } = await getMerchantsBusList();
      this.setInfo({
        merchantsBusList: data.value?.list,
      });
    },
  },
});

export default useBusInfoStore;
