import { defineStore } from 'pinia';
import { Message } from '@arco-design/web-vue';
import _ from 'lodash';
import dayjs from 'dayjs';
import { dayMap } from '@/views/booking/types';

const payStore = defineStore('booking', {
  state: () => ({
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
    saturday: [],
    sunday: [],
    cardTree: <any>[],
  }),

  actions: {
    setList(type: string, oldList: []) {
      const list = _.cloneDeep(oldList);
      switch (type) {
        case 'monday':
          this.monday = list;
          break;
        case 'tuesday':
          this.tuesday = list;
          break;
        case 'wednesday':
          this.wednesday = list;
          break;
        case 'thursday':
          this.thursday = list;
          break;
        case 'friday':
          this.friday = list;
          break;
        case 'saturday':
          this.saturday = list;
          break;
        case 'sunday':
          this.sunday = list;
          break;
        default:
          break;
      }
    },
    setCardTree(tree: []) {
      this.cardTree = tree;
    },
    checkAll(maxHour: number, isHalf: boolean): boolean {
      // check is set price
      const isNull = (mt: any): boolean => {
        return mt?.common === null || mt?.holiday === null || mt?.common === undefined || mt?.holiday === undefined;
      };
      const isSetPrice = (price: any) => {
        const isWholeNull = isNull(price?.all);
        let isHalfNull = false;
        if (isHalf) {
          isHalfNull = isNull(price?.half);
        }
        return !isWholeNull && !isHalfNull;
      };

      // check is none
      if (
        this.monday.length === 0 &&
        this.tuesday.length === 0 &&
        this.wednesday.length === 0 &&
        this.thursday.length === 0 &&
        this.friday.length === 0 &&
        this.saturday.length === 0 &&
        this.sunday.length === 0
      ) {
        Message.warning('场次安排不能为空');
        return false;
      }

      // check price and limit
      let priceError = false;
      let priceErrorIdx = '';
      let priceErrorMemberSequence: number | string = '';
      let limitError = false;
      let limitErrorIdx = '';
      const limitTimestamp = maxHour * 60 * 60 * 1000;
      try {
        const date = dayjs().format('YYYY-MM-DD');
        [
          ...this.monday,
          ...this.tuesday,
          ...this.wednesday,
          ...this.thursday,
          ...this.friday,
          ...this.saturday,
          ...this.sunday,
        ].forEach((item: any) => {
          // check no member price
          if (!isSetPrice(item?.no_member_price)) {
            priceErrorIdx = item.idx;
            priceError = true;
            throw new Error('场次未设置价格');
          }

          // check member price
          if (Array.isArray(item?.member_price)) {
            item.member_price.forEach((member: any, index: number) => {
              if (!isSetPrice(member)) {
                priceErrorMemberSequence = index + 1;
                priceErrorIdx = item.idx;
                priceError = true;
                throw new Error('场次未设置价格');
              }
            });
          }

          // check limit
          const { start_time, end_time } = item;
          const startTimestamp = dayjs(`${date} ${start_time}`).valueOf();
          let endTimestamp = dayjs(`${date} ${end_time}`).valueOf();

          if (end_time === '00:00') {
            endTimestamp = dayjs(`${date} 24:00:00`).valueOf();
          }

          if (endTimestamp - startTimestamp > limitTimestamp) {
            limitErrorIdx = item.idx;
            limitError = true;
            throw new Error('单次订场时长过长');
          }
        });
      } catch (error) {
        console.log(error);
      }

      if (priceError) {
        const key = priceErrorIdx.split('_')[1];
        if (priceErrorMemberSequence === '') {
          Message.warning(`${dayMap[key]}场次"非会员价"未设置`);
        } else {
          Message.warning(`${dayMap[key]}场次"会员价${priceErrorMemberSequence}"未设置`);
        }
        return false;
      }

      if (limitError) {
        const key = limitErrorIdx.split('_')[1];
        Message.warning(`${dayMap[key]}单次订场时长不能大于${maxHour}个小时`);
        return false;
      }

      return true;
    },
  },
});

export default payStore;
