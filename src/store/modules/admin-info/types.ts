export interface AdminInfoState {
  // 账号名称
  account: string;
  // 账号中文名称
  account_realname?: string;
  is_admin: boolean;
  logo: string;
  bus_no_money?: string;
  // 商品权限
  commodity_inventory?: {
    commodity_settlement: boolean;
    add_commodity: boolean;
    add: boolean;
    in: boolean;
    export_excel: boolean;
  };
  financeCheckDays: number; // 对应角色在财务板块查询时，日期范围要根据角色权限中的设定来限制
  // 合同
  contract_expiry_date?: string;
  contract_number?: string;
  // 电子签
  esign_status?: number;
  esign_status_bo?: number;
  esign_status_pc?: string;
  esign_vip_status?: string;
  // 过期时间
  expire_date: string;
  renew_version: ''; // 到期版本
  hand_rfid?: string;
  // 是否体验版
  is_trial_version?: boolean;
  // 角色权限
  role_edit?: boolean;
  nfc_switch?: string;
  [key: string]: unknown;
}
