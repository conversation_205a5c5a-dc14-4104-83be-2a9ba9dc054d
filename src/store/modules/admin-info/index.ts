import { getAccountDetails, getFinanceCheckDays } from '@/api/admin';
import { AdminInfoState } from './types';

const useAdminInfoStore = defineStore('adminInfo', {
  state: (): AdminInfoState => ({
    // 账号名称
    account: '',
    // 账号中文名称
    account_realname: '',
    is_admin: false,
    role_edit: false,
    logo: '',
    phone: '',
    // 商品权限
    commodity_inventory: {
      commodity_settlement: false,
      add_commodity: false,
      add: false,
      in: false,
      export_excel: false,
    },
    financeCheckDays: 0, // 对应角色在财务板块查询时，日期范围要根据角色权限中的设定来限制
    version: '', // 类型
    expire_date: '', // 到期时间
    bus_no_money: '',
    renew_version: '', // 续费版本
    sms_number: 0, // 短信条数
    esign_status: 0,
    esign_vip_status: '', // 电子合同-使用中
    contract_number: '0', // 电子合同-余额
  }),

  getters: {
    getInfoState(state: AdminInfoState): AdminInfoState {
      return { ...state };
    },
  },

  actions: {
    setInfo(partial: Partial<AdminInfoState>) {
      this.$patch(partial);
    },

    // Reset information
    resetInfo() {
      this.$reset();
    },

    async setInfoByServe() {
      const { data } = await getAccountDetails();
      this.setInfo(data.value);
    },

    // 对应角色在财务板块查询时，日期范围要根据角色权限中的设定来限制
    async getFinanceDays() {
      const { data } = await getFinanceCheckDays();
      this.financeCheckDays = data.value.check_days || 0;
    },
  },
});

export default useAdminInfoStore;
