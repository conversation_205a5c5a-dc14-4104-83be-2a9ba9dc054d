import { defineStore } from 'pinia';
import { websocketState } from './types';

const websocketStore = defineStore('websocket', {
  state: (): websocketState => ({
    websocket: null,
    noticeType: 1, // 0 直接弹窗提示新签到 1 右侧全局通知浮窗  2 右侧红色角标数字（会员签到页面）
    noticeArray: [],
    msgCount: 0,
    modalShow: false,
  }),

  actions: {
    SET_WEBSOCKET(data: websocketState['websocket']) {
      this.websocket = data;
    },
    SET_SOCKET_NOTICE_ARRAY(data: websocketState['noticeArray']) {
      this.noticeArray = data;
    },
    SET_SOCKET_NOTICE_TYPE(data: websocketState['noticeType']) {
      this.noticeType = data;
    },
    SET_SOCKET_MODAL_SHOW(data: websocketState['modalShow']) {
      this.modalShow = data;
    },
    SET_SOCKET_MSG_COUNT(data: websocketState['msgCount']) {
      this.msgCount = data;
    },
  },
});

export default websocketStore;
