import {
  getMerchantCardAuth,
  getMerchantUniversalCardAuth,
  getMerchantExperienceCardAuth,
  getMerchantSwimCard,
  getMerchantMultiPtCardAuth,
  checMerchantAuthPackage,
  checkMerchantUpdateUserNo,
} from '@/api/card-class';
import {
  getCardAuth,
  getUniversalCardAuth,
  getExperienceCardAuth,
  getSwimCard,
  getMultiPtCardAuth,
  checkUpdateUserNo,
} from '@/api/card';
import { checkAuthPackage } from '@/api/package';
import { getCardRuleAuth } from '@/api/card-sale-rule';
import { CardState } from './types';

const useCardStore = defineStore('card', {
  state: (): CardState => ({
    cardSettingAuth: {
      expCard: false,
      multiCard: false,
      multiPtCard: false,
      singleCard: false,
      packageCard: false,
      swimCard: false,
      hasUserNo: false,
    },
    cardRuleAuth: false,
  }),

  getters: {
    getInfoState(state: CardState): CardState {
      return { ...state };
    },
  },

  actions: {
    setInfo(partial: Partial<CardState>) {
      this.$patch(partial);
    },

    // Reset information
    resetInfo() {
      this.$reset();
    },

    setCardAuth() {
      const isBradSite = window.IS_BRAND_SITE;
      const apiList = [
        isBradSite ? getMerchantCardAuth : getCardAuth,
        isBradSite ? getMerchantUniversalCardAuth : getUniversalCardAuth,
        isBradSite ? getMerchantExperienceCardAuth : getExperienceCardAuth,
        isBradSite ? getMerchantSwimCard : getSwimCard,
        isBradSite ? getMerchantMultiPtCardAuth : getMultiPtCardAuth,
        isBradSite ? checMerchantAuthPackage : checkAuthPackage,
        isBradSite ? checkMerchantUpdateUserNo : checkUpdateUserNo,
      ];
      const cardSettingAuthKeys = [
        'singleCard',
        'multiCard',
        'expCard',
        'swimCard',
        'multiPtCard',
        'packageCard',
        'hasUserNo',
      ];

      apiList.forEach((postApi, index) => {
        postApi().then((res) => {
          this.cardSettingAuth[cardSettingAuthKeys[index]] = res.response.value.errorcode === 0;
        });
      });
    },
    setCardRuleAuth() {
      getCardRuleAuth().then((res) => {
        this.setInfo({
          cardRuleAuth: res.response.value.errorcode === 0,
        });
      });
    },
  },
});

export default useCardStore;
