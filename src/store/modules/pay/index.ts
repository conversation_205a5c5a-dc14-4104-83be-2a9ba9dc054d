import { defineStore } from 'pinia';
import { getBusPayType } from '@/api/payment-manage';
import { PayState } from './types';

const { execute: getPayType } = getBusPayType();

const payStore = defineStore('pay', {
  state: (): PayState => ({
    payTypes: [],
    // 是否支持储值卡混合支付
    isMoneyCardMix: false,
  }),

  getters: {
    getPayNameById: (state) => (id: string | number, notIncludeUnusable: boolean) => {
      let allPayTypes = state.payTypes;
      if (notIncludeUnusable) {
        allPayTypes = allPayTypes.filter((item: Record<string, any>) => item.usable === 1);
      }
      for (let index = 0; index < allPayTypes.length; index += 1) {
        const item: Record<string, any> = allPayTypes[index];
        if (Number(id) === Number(item.pay_type_id)) {
          return item.pay_type_name;
        }
      }
      return '暂无';
    },
  },

  actions: {
    SET_PAY_TYPES(payTypes: any) {
      this.payTypes = payTypes.list || [];
      this.isMoneyCardMix = payTypes.stored_value_blend === 1;
    },
    async setPayTypesInit() {
      const { data } = await getPayType();
      this.SET_PAY_TYPES(data.value);
    },
  },
});

export default payStore;
