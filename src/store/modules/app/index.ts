import { defineStore } from 'pinia';
import { Message } from '@arco-design/web-vue';
import defaultSettings from '@/config/settings.json';
import { getMenus, getMerchantMenus } from '@/api/node-group';
import { AppState } from './types';

const useAppStore = defineStore('app', {
  state: (): AppState => ({ ...defaultSettings }),

  getters: {
    appCurrentSetting(state: AppState): AppState {
      return { ...state };
    },
    appDevice(state: AppState) {
      return state.device;
    },
    getServerMenu(state: AppState): RouteRecordNormalized[] {
      return state.serverMenu as unknown as RouteRecordNormalized[];
    },
    getMenuTree(state: AppState): RouteRecordNormalized[] {
      return state.menuTree as unknown as RouteRecordNormalized[];
    },
  },

  actions: {
    // Update app settings
    updateSettings(partial: Partial<AppState>) {
      this.$patch(partial);
    },
    async setServeMenu() {
      const router = useRouter();
      try {
        const { data } = window.IS_BRAND_SITE ? await getMerchantMenus() : await getMenus();
        this.serverMenu = data.value;
        this.setMenuTree(this.serverMenu);
        return data.value;
      } catch (error) {
        console.log('setServeMenu error', error);
        router.push({
          path: '/login',
          params: { from: window.location.pathname },
        });
        return error;
      }
    },

    setMenuTree(data) {
      const iconMap = {
        概况: 'icon-gaikuang',
        前台: 'icon-qiantai',
        会员: 'icon-huiyuan',
        课程: 'icon-kecheng',
        人员: 'icon-renyuan1',
        营销: 'icon-yingxiao',
        管理: 'icon-guanli',
        报表: 'icon-baobiao',
        财务: 'icon-caiwu',
        设置: 'icon-shezhi',
        运营: 'icon-yingxiao',
        门店设置: 'icon-mendianguanli',
        硬件: 'icon-yingjian',
        支付宝月付: 'icon-zhifubao',
        微信月付: 'icon-weixin',
        支付宝安心付: 'icon-zhifubao',
      };
      function travel(_routes, layer: number) {
        if (!_routes || _routes.length === 0) return null;
        const collector: any = _routes.map((element) => {
          element.children = element.node;
          element.meta = element.meta || {};
          if (layer === 0) {
            element.icon = element.icon || iconMap[element.name] || '';
          }
          const url = element.web_url || element.url || '';
          const isLineStart = /^\/.*/.test(url);
          element.path = element.version === 3 ? `/v2/${isLineStart ? url.slice(1) : url}` : url;
          if (!element.children) {
            element.children = [];
            return element;
          }
          const subItem = travel(element.children, layer + 1);
          if (subItem?.length) {
            element.children = subItem;
            return element;
          }
          // the else logic
          if (layer > 1) {
            element.children = subItem;
            return element;
          }
          return null;
        });
        return collector.filter(Boolean);
      }
      this.menuTree = travel(data, 0);
    },

    // Change theme color
    toggleTheme(dark: boolean) {
      if (dark) {
        this.theme = 'dark';
        document.body.setAttribute('arco-theme', 'dark');
      } else {
        this.theme = 'light';
        document.body.removeAttribute('arco-theme');
      }
    },
    toggleDevice(device: string) {
      this.device = device;
    },
    toggleMenu(value: boolean) {
      this.hideMenu = value;
    },
  },
});

export default useAppStore;
