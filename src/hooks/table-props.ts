import { Pagination } from '@/types/global';

/*
 * @param {Object} requestApi 请求api
 * @param {Function} initTableDataFunction 当需要在搜索、分页、导出等对表格数据统一处理时可以传入
 * @param {Function} resCallback 请求完成后的统一回调
 */
export default function useTableProps(
  requestApi: any,
  initTableDataFunction?: ((list: Record<string, any>[]) => Record<string, any>[]) | undefined | '',
  resCallback?: (res: any) => void
) {
  const basePagination: Pagination = {
    current: 1,
    pageSize: 10,
    total: 0,
  };
  const { isLoading, execute, requestOptions } = requestApi();

  interface TablePropsData {
    'row-key'?: string;
    'bordered'?: boolean;
    'loading'?: boolean;
    'data'?: Record<string, any>[];
    'pagination'?: Pagination;
    [key: string]: any;
  }
  // table全局默认props
  const tableProps = ref<TablePropsData>({
    'row-key': 'id',
    'bordered': false,
    'loading': isLoading,
    'data': [],
    'pagination': {
      ...basePagination,
      showPageSize: true,
      showTotal: true,
    },
  });

  // 请求参数
  const searchParam = reactive<Record<string, any>>({});

  // 设置请求参数
  const setSearchParam = (params: Record<string, any>) => {
    Object.keys(params).forEach((key) => {
      searchParam[key] = params[key];
    });
  };
  // 后端返回值的默认路径
  const dataPath = ref({
    list: 'list',
    count: 'count',
  });

  /*
   * 加载表格数据
   * @param {boolean} isExport 是否是导出
   
   */
  const loadTableList = async (isExport?: boolean) => {
    const { list: listKey, count } = dataPath.value;
    const { current, pageSize } = tableProps.value.pagination;
    const params = {
      ...searchParam,
      current: isExport ? 1 : current,
      pageSize: isExport ? tableProps.value.pagination.total : pageSize,
    };
    const dataIndex = requestOptions?.method.toLowerCase() === 'get' ? 'params' : 'data';
    try {
      const res = await execute({ [dataIndex]: params });
      const { value = {} } = res.data;
      const list = value[listKey] || [];

      if (isExport) {
        return Promise.resolve(initTableDataFunction ? initTableDataFunction(list) : list);
      }

      tableProps.value.data = initTableDataFunction ? initTableDataFunction(list) : list;
      tableProps.value.pagination.total = +value[count] || tableProps.value.data.length || 0;
      resCallback?.(res);
      return res;
    } catch (error) {
      resCallback?.(error);
      return error;
    }
  };

  // 表格事件
  const tableEvent = ref({
    // 分页改变
    pageChange: (current: number) => {
      tableProps.value.pagination.current = current;
      loadTableList();
    },
    // 修改每页显示条数
    pageSizeChange: (pageSize: number) => {
      tableProps.value.pagination.current = 1;
      tableProps.value.pagination.pageSize = pageSize;
      loadTableList();
    },
  });
  const handleSearch = () => {
    tableProps.value.pagination.current = 1;
    return loadTableList();
  };

  return {
    basePagination,
    dataPath,
    isLoading,
    execute,
    tableProps,
    tableEvent,
    searchParam,
    initTableDataFunction,
    setSearchParam,
    handleSearch,
    loadTableList,
  };
}
