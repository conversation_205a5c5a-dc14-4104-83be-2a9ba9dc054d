import { useWebSocket } from '@vueuse/core';
import { getSocketUrl } from '@/config/url';
import { websocketStore, useBusInfoStore } from '@/store';

const busInfo = useBusInfoStore();
const socketState = websocketStore();

export default function useMsgSocket(wsUrl?: Ref<string>) {
  const socketCloseTimer = ref();
  const heartBeatTimer = ref();

  const url = computed(() => wsUrl?.value || getSocketUrl());

  // socketState.SET_WEBSOCKET(new WebSocket(url));
  const { status, data, send, open, close } = useWebSocket(url.value, {
    // Auto-connect (enabled by default).
    // This will call automatically for you and you don't need to call it by yourself.open()
    // If url is provided as a ref, this also controls whether a connection is re-established when its value is changed (or whether you need to call open() again for the change to take effect).
    immediate: false,
    // Auto-close-connection (enabled by default).
    // This will call automatically when the event is triggered or the associated effect scope is stopped.close()beforeunload
    autoClose: true,
    // It's common practice to send a small message (heartbeat) for every given time passed to keep the connection active. In this function we provide a convenient helper to do it:
    heartbeat: {
      message: '{"action":"check_heart","msg":"@heart|end|"}',
      interval: 60 * 1000, // 心跳验证 60秒一次
      pongTimeout: 1000,
    },
    // Reconnect on errors automatically (disabled by default).
    autoReconnect: {
      retries: -1,
      delay: 30 * 1000,
      onFailed() {
        console.log('onFailed');
      },
    },
  });
  socketState.SET_WEBSOCKET(data.value);

  function action() {
    if (socketState.websocket && status.value === 'OPEN') {
      // send(agentData)
    } else if (socketState.websocket && status.value === 'CONNECTING') {
      // 若是正在开启状态
    } else {
      // 若未开启
      // eslint-disable-next-line no-use-before-define
      init();
    }
  }

  function handleSend(agentData: string | ArrayBuffer | Blob, useBuffer?: boolean | undefined) {
    if (socketState.websocket && status.value === 'OPEN') {
      send(agentData, useBuffer);
    }
  }

  function onOpen() {
    // 参数
    send(`{"action":"init","admin_id":${busInfo.adminId}}`);
    // if (socketCloseTimer) {
    //   clearInterval(socketCloseTimer.value);
    //   socketCloseTimer.value = null;
    // }
  }

  // websocket心跳验证
  function heartbeat() {
    console.log('heartbeat');
    handleSend('{"action":"check_heart","msg":"@heart|end|"}');
  }

  function closeWebSocket() {
    // if (socketState.websocket) {
    //   socketState.websocket.close();
    // }
    close();
  }

  function onClose(e: CloseEvent) {
    console.log('OnClose', heartBeatTimer);

    // if (heartBeatTimer.value) {
    //   clearInterval(heartBeatTimer.value);
    //   heartBeatTimer.value = null;
    // }
    if (!e.wasClean && !socketCloseTimer.value) {
      console.log('onClose-2', e);

      // socketCloseTimer.value = setInterval(action, 1000 * 30);
    }
  }

  function onError() {
    console.log('onError');
  }

  function init() {
    // function init(wsUrl?: string) {
    // if (socketState.websocket) {
    //   socketState.websocket.onopen = onOpen;
    //   // socketState.websocket.onmessage = websocketonmessage
    //   socketState.websocket.onerror = onError;
    //   socketState.websocket.onclose = onClose;
    // }
  }

  watch(
    () => busInfo.admin_id,
    (val, oldVal) => {
      if (val && oldVal !== val) {
        // action();
      }
    }
  );

  return {
    status,
    data,
    send,
    open,
    close,
    // socketCloseTimer,
    // heartBeatTimer,
    // action,
    // onOpen,
    // heartbeat,
    // closeWebSocket,
    // onClose,
    // onError,
    // init,
  };
}
