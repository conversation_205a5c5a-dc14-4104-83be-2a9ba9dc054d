// eslint-disable-next-line filename/match
import wx from 'weixin-js-sdk';
import { wxinit } from '@/api/wap';

export interface WxConfig {
  appId: string; // 必填，公众号的唯一标识
  timestamp: number; // 必填，生成签名的时间戳
  nonceStr: string; // 必填，生成签名的随机串
  signature: string; // 必填，签名
  jsApiList: []; // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
}
export function useWxSDK() {
  /**
   * 初始化设置
   */
  function initConfig() {
    return new Promise((resolve) => {
      wxinit.then((res) => {
        const configInfo: WxConfig = res.data.value;
        wx.config({
          debug: false,
          appId: configInfo.appId,
          timestamp: configInfo.timestamp,
          nonceStr: configInfo.nonceStr,
          signature: configInfo.signature,
          jsApiList: configInfo.jsApiList ?? [
            'scanQRCode',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'chooseWXPay',
          ],
        });
        wx.ready(() => {
          resolve(true);
        });
      });
    });
  }

  /** 是否是ios微信 */
  function isiOSWechat() {
    return (window as any).__wxjs_is_wkwebview;
  }

  return {
    initConfig,
    isiOSWechat,
  };
}
