<template>
  <a-card :bordered="false" class="user-card">
    <a-avatar :size="120" shape="square" :image-url="userInfo.avatar" />
    <a-descriptions style="width: 100%" :data="renderData" :column="2" :align="{ label: 'right' }" layout="horizontal">
    </a-descriptions>
  </a-card>
</template>

<script lang="ts" setup>
  /* 会员信息展示组件  一般和会员搜索下拉组件一起使用 */
  const props = defineProps<{
    userInfo: Record<string, any>;
  }>();
  const { username, age, sex, sale_name: saleName, birthday, phone, remark } = props.userInfo;
  const gender = sex === '2' || sex === '女' ? '女' : '男';
  const renderData = computed(() => [
    {
      label: '姓名',
      value: username,
    },
    {
      label: '年龄',
      value: age,
    },
    {
      label: '性别',
      value: gender,
    },
    {
      label: '跟进会籍',
      value: saleName,
    },
    {
      label: '手机号',
      value: phone,
    },
    {
      label: '生日',
      value: birthday,
    },
    {
      label: '备注',
      value: remark,
    },
  ]);
</script>

<style scoped lang="less">
  .user-card {
    width: 100%;
    padding: 0;
    background-color: rgb(var(--gray-1));
    :deep(.arco-card-body) {
      display: flex;
      align-items: center;
    }
    :deep(.arco-avatar) {
      margin-right: 16px;
    }
  }
</style>
