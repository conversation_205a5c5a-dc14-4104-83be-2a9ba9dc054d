<template>
  <a-select
    v-bind="$attrs"
    v-model="selectedValue"
    :loading="isLoading"
    :filter-option="false"
    :placeholder="placeholder || '姓名/电话/实体卡号'"
    allow-search
    allow-clear
    @change="handleUserChange"
    @search="getSearchUserList">
    <a-option v-for="user in userList" :key="user.user_id" :value="user.user_id">
      {{ user.username }} {{ user.username ? '(' : '' }}{{ user.phone }}{{ user.username ? ')' : '' }}
    </a-option>
    <template #empty>
      <a-empty :description="emptyDes" />
    </template>
  </a-select>
</template>

<script lang="ts" setup>
  /* 会员搜索组件 */
  import debounce from 'lodash/debounce';
  import { isChinese } from '@/utils';
  import request from '@/request/index';
  import { userAttribute } from '@/api/sign';

  const props = withDefaults(
    defineProps<{
      placeholder?: string;
      modelValue?: string;
      busId?: string;
      url?: string;
      hasSelfPhone?: boolean;
      from?: number; // 1？(后端缺省值)，2普通签到，3私教签到 4黑名单添加 5 订场长租 6 团课预约
      isGetMoreOnChange?: boolean;
    }>(),
    {
      isGetMoreOnChange: false,
      from: 1,
    }
  );

  // 远程会员搜索下拉
  function searchUserInfo() {
    return request({
      url: props.url || 'Web/Sign/search_user',
      options: {
        method: 'post',
      },
      abortPrevious: false,
      immediate: false,
    });
  }

  const emptyDes = ref('暂无数据');
  const emits = defineEmits(['update:modelValue', 'change']);
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const { isLoading, execute: getUser } = searchUserInfo();
  const userList = ref<Record<string, any>[]>([]);
  function addSelfPhone(phone: string) {
    if (props.hasSelfPhone && userList.value?.length === 0 && /^1\d{10}$/.test(phone)) {
      userList.value.push({
        user_id: 'self_id',
        username: '',
        phone,
      });
    }
  }
  const getSearchUserList = debounce(async (search: string) => {
    if (!isChinese(search)) {
      emptyDes.value = '至少输入3位数字';
      userList.value = [];
      return;
    }

    emptyDes.value = '暂无数据';
    if (search) {
      const { data } = await getUser({
        data: {
          search: search.trim(),
          bus_id: props.busId || '',
        },
      });
      userList.value = data.value?.list || [];
      addSelfPhone(search);
    } else {
      userList.value = [];
    }
  }, 400);
  const handleUserChange = async (value: any) => {
    const userInfo = userList.value.find((item) => item.user_id === value);
    let otherInfo = null;
    if (props.isGetMoreOnChange && !props.url && userInfo) {
      const { data } = await userAttribute({
        user_id: userInfo.user_id,
        from: props.from || '',
      });
      otherInfo = data;
    }
    emits('change', {
      ...userInfo,
      ...otherInfo,
    });
  };
</script>
