<template>
  <a-select
    v-bind="$attrs"
    v-model="selectedValue"
    v-model:popup-visible="popupVisible"
    :input-value="query"
    :loading="isLoading"
    :filter-option="false"
    :placeholder="placeholder || '姓名/电话/实体卡号'"
    allow-search
    allow-clear
    @change="handleUserChange"
    @search="getSearchUserList"
    @input-value-change="handleInputValueChange">
    <a-option v-if="showPrefix && isChinese(query)" class="pre-option" value="memberInfo" :label="query">
      点击搜【会员信息】包含 {{ query }}
    </a-option>
    <a-option v-if="showPrefix && isChinese(query)" class="pre-option" value="cardInfo" :label="query">
      点击搜【卡号信息】包含 {{ query }}
    </a-option>
    <a-option v-for="user in userList" :key="user.user_id" :value="user.user_id">
      {{ user.username ? `${user.username} (${user.phone})` : user.phone }}
    </a-option>
    <template #empty>
      <a-empty :description="emptyDes" />
    </template>
  </a-select>
</template>

<script lang="ts" setup>
  /* 会员搜索组件 */
  import debounce from 'lodash/debounce';
  import { isChinese } from '@/utils';
  import request from '@/request/index';
  import { userAttribute } from '@/api/sign';

  const props = withDefaults(
    defineProps<{
      placeholder?: string;
      modelValue?: string;
      busId?: string;
      url?: string;
      from?: number; // 1？(后端缺省值)，2普通签到，3私教签到 4黑名单添加 5 订场长租 6 团课预约
      hasSelfPhone?: boolean;
      isGetMoreOnChange?: boolean;
    }>(),
    {
      isGetMoreOnChange: false,
      from: 1,
    }
  );

  // 远程会员搜索下拉
  function searchUserInfo() {
    return request({
      url: props.url || 'Web/Sign/search_user',
      options: {
        method: 'post',
      },
      abortPrevious: false,
      immediate: false,
    });
  }

  const popupVisible = ref(false);
  const showPrefix = ref(true);
  const query = ref('');
  const emptyDes = ref('暂无数据');
  const accurateType = ref(1); // 1走宽表查询 2按用户信息查mysql 3按卡号查实时库 ，不传则不走优化逻辑
  const emits = defineEmits(['update:modelValue', 'change']);
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const { isLoading, execute: getUser } = searchUserInfo();
  const userList = ref<Record<string, any>[]>([]);
  function addSelfPhone(phone: string) {
    if (props.hasSelfPhone && userList.value?.length === 0 && /^1\d{10}$/.test(phone)) {
      userList.value.push({
        user_id: 'self_id',
        username: '',
        phone,
      });
    }
  }

  async function emitChange(info) {
    let otherInfo = null;
    if (props.isGetMoreOnChange && info) {
      const { data } = await userAttribute({
        user_id: info.user_id,
        from: props.from || '',
      });
      otherInfo = data;
    }
    emits('change', {
      ...info,
      ...otherInfo,
    });
  }
  async function searchUser(search: string) {
    if (!isChinese(search)) {
      emptyDes.value = '至少输入3位数字';
      userList.value = [];
      return;
    }
    userList.value = [];
    emptyDes.value = '未找到结果，请重试其他关键词';
    if (search) {
      const { data } = await getUser({
        data: {
          search: search.trim(),
          accurate_type: accurateType.value,
          bus_id: props.busId || '',
          from: props.from || '',
        },
      });
      userList.value = data.value?.list || [];
      addSelfPhone(search);
      if (
        userList.value.length === 1 &&
        ((accurateType.value === 1 && userList.value[0].pg_accurate_uniq === 1) || accurateType.value !== 1)
      ) {
        selectedValue.value = userList.value[0].user_id;
        popupVisible.value = false;
        emitChange(userList.value[0]);
      } else {
        popupVisible.value = true;
      }
    }
  }
  const getSearchUserList = debounce((search: string) => {
    if (search === 'memberInfo' || search === 'cardInfo') {
      return;
    }
    accurateType.value = 1;
    searchUser(search);
  }, 400);

  function handleInputValueChange(value: string) {
    showPrefix.value = true;
    query.value = value;
  }
  const handleUserChange = (value: any) => {
    if (value === 'memberInfo' || value === 'cardInfo') {
      accurateType.value = value === 'memberInfo' ? 2 : 3;
      showPrefix.value = false;
      searchUser(query.value);
      selectedValue.value = '';
    } else {
      const userInfo = userList.value.find((item) => item.user_id === value);
      emitChange(userInfo);
    }
  };
</script>

<style lang="less" scoped>
  .pre-option {
    color: #2d8cf0;
  }
</style>
