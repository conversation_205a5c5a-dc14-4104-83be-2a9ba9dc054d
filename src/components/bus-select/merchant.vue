<template>
  <a-select v-bind="$attrs" v-model="selectedValue" allow-search>
    <a-option v-for="item in busInfo.merchantsBusList" :key="item.bus_id" :value="item.bus_id">
      {{ item.bus_name }}
    </a-option>
  </a-select>
</template>

<script lang="ts" setup>
  /* 当前商家下所有场馆 */
  import { useBusInfoStore } from '@/store';

  const props = defineProps<{
    modelValue?: string | string[];
  }>();

  const emits = defineEmits(['update:modelValue']);
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });
  const busInfo = useBusInfoStore();
  if (!busInfo.merchantsBusList.length) {
    busInfo.setMerchantsBusList();
  }
</script>
