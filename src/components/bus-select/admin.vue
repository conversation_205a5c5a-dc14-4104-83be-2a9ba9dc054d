<template>
  <a-select v-bind="$attrs" v-model="selectedValue" allow-search>
    <a-option v-for="item in busInfo.adminBusList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
  </a-select>
</template>

<script lang="ts" setup>
  /* 账号权限下当前商家的场馆列表 当为admin账号的时候显示的是当前商家下所有场馆 */
  import { useBusInfoStore } from '@/store';

  const props = defineProps<{
    modelValue?: string;
  }>();

  const emits = defineEmits(['update:modelValue']);
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const busInfo = useBusInfoStore();
  if (!busInfo.adminBusList.length) {
    busInfo.setAdminBusList();
  }
</script>
