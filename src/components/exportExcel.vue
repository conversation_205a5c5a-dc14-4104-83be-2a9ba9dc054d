<template>
  <div class="arco-btn-link">
    <slot :handle-export="handleExport">
      <!-- <a-button v-bind="$attrs" @click="getData">
        <span>{{ text }}</span>
      </a-button> -->
    </slot>
    <a :id="idName" style="color: inherit; width: 0; height: 0; opacity: 0" @click.stop="generate"></a>
  </div>
</template>

<script setup lang="ts">
  /* eslint-disable */
  /* prettier-ignore */
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import { AnyObject, ExportData } from '@/types/global';

  const state = reactive({
    animate: true,
    animation: '',
    columns: [] as TableColumnData[],
    data: [] as AnyObject[],
    filename: '',
  });

  const props = defineProps({
    stringExport: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'xls',
    },
    meta: {
      type: Array,
      default() {
        return [];
      },
    },
    text: {
      type: String,
      default: '导出',
    },
  });

  const now = new Date().getTime();
  const idName = ref(`export_${now}`);

  function jsonToXLS(data: any[], columns: TableColumnData[]) {
    const xlsTemp =
      '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta name=ProgId content=Excel.Sheet> <meta name=Generator content="Microsoft Excel 11"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>${table}</table></body></html>';
    let xlsData = '';
    const keys = [];
    xlsData += '<thead>';
    columns.forEach((item) => {
      keys.push(item.dataIndex);
      xlsData += `<th>${item.title}</th>`;
    });

    xlsData += '</thead>';
    xlsData += '<tbody>';

    for (let i = 0; i < data.length; i += 1) {
      const item = data[i];

      // 添加 rowspan
      for (let j = 0; j < keys.length; j += 1) {
        const key = keys[j];
        if (Array.isArray(item[key])) {
          item.rowspan = item[key].length;
          break;
        }
      }

      xlsData += '<tr>';
      if (item.rowspan) {
        let rowspan = +item.rowspan;
        const initRowspan = rowspan;
        const stringKeys = [];

        while (rowspan > 0) {
          for (let j = 0; j < keys.length; j += 1) {
            const key = keys[j];

            if (Array.isArray(item[key])) {
              xlsData += `<td style="mso-number-format:'@';">${
                item[key][initRowspan - rowspan] === undefined ? '' : item[key][initRowspan - rowspan]
              }</td>`;
            } else if (!stringKeys.includes(key)) {
              xlsData += `<td style="mso-number-format:'@';" rowspan="${initRowspan}">${
                item[key] === undefined ? '' : item[key]
              }</td>`;
              stringKeys.push(key);
            }
          }

          if (rowspan > 1) {
            xlsData += '</tr><tr>';
          }

          rowspan -= 1;
        }
      } else {
        for (let j = 0; j < keys.length; j += 1) {
          const key = keys[j];

          xlsData += `<td style="mso-number-format:'@';">${item[key] === undefined ? '' : item[key]}</td>`;
        }
      }

      xlsData += '</tr>';
    }

    xlsData += '</tbody>';

    return xlsTemp.replace('${table}', xlsData);
  }

  function jsonToCSV(data, header) {
    let csvData = '';

    if (header) {
      for (let j = 0; j < header.length; j += 1) {
        const key = header[j];
        csvData += `${header[key]},`;
      }

      csvData = csvData.slice(0, csvData.length - 1);
      csvData += '\r\n';
    }

    data.forEach((item) => {
      Object.keys(item).forEach((k) => {
        csvData += `${item[k]},`;
      });
      csvData = csvData.slice(0, csvData.length - 1);
      csvData += '\r\n';
    });

    return csvData;
  }
  function jsonToXLSByString(jsonString) {
    // jsonString格式形如 <thead><tr><th rowspan="2">成交方式</th><th colspan="3">总计</th><th colspan="3">新开卡</th></tr><tr><th>订单</th><th>课时数</th><th>电话3</th><th>订单</th><th>课时数</th><th>电话3</th></tr></thead><tbody><tr><td></td><td>555 77 854</td><td>555 77 855</td><td>555 77 854</td><td>555 77 855</td> <td>555 77 854</td><td>555 77 855</td></tr></tbody>
    return `<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta name=ProgId content=Excel.Sheet> <meta name=Generator content="Microsoft Excel 11"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>${jsonString}</table></body></html>`;
  }

  function base64(s) {
    return window.btoa(window.unescape(encodeURIComponent(s)));
  }
  function base64ToBlob(base64Data) {
    const arr = base64Data.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8ClampedArray(n);

    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
  }

  function download(base64data, fileName) {
    if (window.navigator.msSaveBlob) {
      const blob = base64ToBlob(base64data);
      window.navigator.msSaveBlob(blob, fileName);
    } else {
      const a = document.getElementById(idName.value) as HTMLAnchorElement;
      if (window.URL.createObjectURL) {
        const blob = base64ToBlob(base64data);
        const blobUrl = window.URL.createObjectURL(blob);

        a.href = blobUrl;
        a.download = fileName;
      } else if (a.download === '') {
        a.href = base64data;
        a.download = fileName;
      }
      a.click();
    }
  } // end download

  function exportXLS(data, fileName, header) {
    const XLSData = `data:application/vnd.ms-excel;base64,${base64(jsonToXLS(data, header))}`;
    download(XLSData, fileName);
  }
  function exportXLSByTableString(data, fileName) {
    const XLSData = `data:application/vnd.ms-excel;base64,${base64(jsonToXLSByString(data))}`;
    download(XLSData, fileName);
  }
  function exportCSV(data, fileName, keys) {
    const CSVData = `data:application/csv;base64,${base64(jsonToXLSByString(data, keys))}`;
    download(CSVData, fileName);
  }

  function generate() {
    if (props.stringExport) {
      return exportXLSByTableString(state.data, `${state.filename}.xls`);
    }
    if (props.type === 'csv') {
      return exportCSV(state.data, `${state.filename}.csv`, state.columns);
    }
    return exportXLS(state.data, `${state.filename}.xls`, state.columns);
  }

  function generateExcel() {
    exportXLS(state.data, state.filename, state.columns);
  }

  function handleExport({ columns, data, filename }: ExportData) {
    state.columns = columns;
    state.data = data;
    state.filename = filename || '表格导出';
    if (!state.data || (!props.stringExport && !Array.isArray(state.data))) {
      console.error('export.vue: 没有导出数据');
    } else {
      const a = document.getElementById(idName.value) as HTMLAnchorElement;
      a.click();
    }
  }
  defineExpose({
    handleExport,
  })
</script>

<style scoped lang="less">
#mainApp {
  // 导出按钮
  .arco-btn-link {
    &.arco-btn, :deep(.arco-btn) {
      color: @theme-text-color-while;
      background-color: @theme-link;
      &:hover {
        opacity: 0.85;
      }
    }
  }
}
</style>
