<template>
  <div class="cropper">
    <div v-if="(imgSrc || cropImg) && !multiLoaded" class="image-wrapper">
      <img v-if="cropImg" :src="cropImg" class="upload-image" alt="形象照" />
      <vue-cropper
        v-show="imgSrc && !cropImg"
        ref="croperRef"
        preview="#preview"
        :view-mode="2"
        :auto-crop="true"
        :info="false"
        :fixed="true"
        :output-type="outputType"
        :fixed-number="fixedNumber"
        :auto-crop-width="400"
        :auto-crop-height="240"
        :img="imgSrc"
        :high="true"
        alt="原图片"
        :img-style="{ width: '400px', height: '300px' }"></vue-cropper>
    </div>
    <div :class="[center ? 'centerButton' : 'button']">
      <label v-if="!imgSrc" class="upload-btn">
        选择本地图片
        <form id="myForm">
          <input
            id="croperRef"
            ref="uploadInput"
            type="file"
            name="image"
            style="font-size: 1.2em; padding: 10px 0; display: none"
            icon="ios-upload-outline"
            accept="image/png, image/jpeg, image/gif, image/jpg"
            @change="setImage" />
        </form>
      </label>
      <a-button v-if="imgSrc && !cropImg" :loading="isLoading" type="primary" @click="handleConfirmUpload">
        保存图片
      </a-button>
      <a-button v-if="imgSrc && !cropImg" style="margin-left: 30px" @click="cancelUpload">取消</a-button>
      <div v-if="modelPicAddr && modelPicAddr.length > 0" class="specbutton" @click="showmodelPic = true">
        <label v-if="!imgSrc" class="upload-btn">选择模板图片</label>
      </div>
    </div>
    <!-- <choosePic v-model="showmodelPic" :model-pic-addr="modelPicAddr" @selectPic="selectPic" /> -->
  </div>
</template>

<script setup lang="ts">
  import { Message, Modal } from '@arco-design/web-vue';
  import 'vue-cropper/dist/index.css';
  import { VueCropper } from 'vue-cropper';
  import { uploadImage } from '@/api/public';

  //   readme: 取消编辑右方的预览，父组件中传入nopreview, 默认是false
  // 取消保存图片后的图片展示，父组件中传入multiple，默认为false
  // 需要加入模版图片选项，父组件中传入模板图片的地址数组modelPicAddr
  // 编辑图片时需要隐藏父组件中的图片显示，父组件中添加监听emit事件v-on:noshowpic
  const props = defineProps({
    modelValue: {
      type: String,
      default: '',
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    compress: {
      type: Boolean,
      default: false,
    },
    userId: {
      type: [String, Number],
    },
    outputWidth: { type: Number, default: 0 }, // 输出图片宽度, 默认 0 不限制
    outputHeight: { type: Number, default: 0 }, // 输出图片高度, 如果不传默认为: 输出宽度 * 比例
    outputType: { type: String, default: 'png' }, // 输出图片格式, 默认为 png, 可选: jpeg, webp 等, 注: 上传后服务器返回的都是 png 格式, 但是 jpeg 会比 png 小很多
    options: {
      type: Object,
      default: () => {
        return {};
      },
    },
    modelPicAddr: {
      type: Array,
    },
    maxSize: {
      type: Number,
      default: 5,
    },
    uploadTips: {
      // 上传确认提示内容，控制上传前是否需要确认
      type: String,
      default: '',
    },
    center: {
      type: Boolean,
      default: false,
    },
  });
  // imgsrc为生成的图片数据，cropimg为上传后生成的图片地址
  // multiloaded为图片展示，showmodelpic为选择模版图片
  const imgSrc = ref('');
  const cropImg = ref('');
  const croperRef = ref<any>(null);
  const multiLoaded = ref(true);
  const showmodelPic = ref(false);
  const emit = defineEmits(['update:modelValue', 'onSuccess', 'noshowpic']);
  // 截取比例: 长 / 宽, 默认 1,1
  const fixedNumber = computed(() => {
    return (props.options && props.options.fixedNumber) || [1, 1];
  });

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        cropImg.value = val;
      }
    }
  );
  function cancelUpload() {
    cropImg.value = '';
    imgSrc.value = '';
    multiLoaded.value = true;
    emit('noshowpic', false);
  }
  function setImage(e) {
    cropImg.value = '';
    imgSrc.value = '';
    const file = e.target.files[0];
    if (props.maxSize && file.size > props.maxSize * 1024 * 1024) {
      Message.error('图片过大');
      return;
    }
    if (file && !file.type.includes('image/')) {
      Message.error('请选择图片文件');
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      let data;
      console.log(event.target.result);
      if (typeof event.target.result === 'object') {
        // 把Array Buffer转化为blob 如果是base64不需要
        data = window.URL.createObjectURL(new Blob([event.target.result]));
      } else {
        data = event.target.result;
      }
      multiLoaded.value = false;
      cropImg.value = '';
      imgSrc.value = data;
      const imgform = document.getElementById('myForm');
      if (imgform) {
        imgform.reset();
      }
      if (imgSrc.value) {
        emit('noshowpic', true);
      }
    };
    // 转化为base64
    reader.readAsDataURL(file);
  }
  const { isLoading, execute: useUploadImage } = uploadImage();
  async function doUpload() {
    const cropImgData = await new Promise((resolve) => {
      croperRef.value.getCropData((data: string) => {
        resolve(data);
      });
    });
    // const cropImgData = imgSrc.value;
    const postData = {
      image_data: cropImgData,
      _type: 'platform',
      userid: props.userId || '',
    };
    useUploadImage({ data: postData }).then((res) => {
      const resData = res.response.value;
      if (!resData) {
        return;
      }
      const { status } = resData;
      if (status === 1) {
        cropImg.value = cropImgData;
        if (props.multiple) {
          multiLoaded.value = true;
          imgSrc.value = '';
        }
        const path = `${resData.path}${props.compress ? '@70q_1pr' : ''}`;
        emit('update:modelValue', path);
        emit('onSuccess', { type: resData.type });
        emit('noshowpic', false);
      }
    });
  }
  // 是否确认上传
  function handleConfirmUpload() {
    if (props.uploadTips) {
      Modal.confirm({
        title: '是否确认上传',
        content: props.uploadTips,
        onOk: () => {
          doUpload();
        },
      });
    } else {
      doUpload();
    }
  }
</script>

<style lang="less" scoped>
  @btn-color: #ff2351ff;
  .cropper {
    #preview {
      width: 300px;
      height: 300px;
      overflow: hidden;
      position: absolute;
      right: -320px;
      border: 1px solid #dcdcdc;
      top: 0;
      background-color: #ccc;
    }
    .button {
      display: flex;
      align-items: center;
      .upload-btn {
        border: 1px solid @btn-color;
        border-radius: 4px;
        height: 32px;
        line-height: 32px;
        padding: 0 10px;
        margin-right: 30px;
        font-size: 14px;
        display: inline-block;
        cursor: pointer;
        color: @btn-color;
        &:hover {
          color: #fff;
          background-color: @btn-color;
        }
      }
    }
    .centerButton {
      display: flex;
      align-items: center;
      justify-content: center;
      .upload-btn {
        border: 1px solid @btn-color;
        border-radius: 4px;
        height: 32px;
        line-height: 32px;
        padding: 0 10px;
        margin-right: 30px;
        font-size: 14px;
        display: inline-block;
        cursor: pointer;
        color: @btn-color;
        &:hover {
          color: #fff;
          background-color: @btn-color;
        }
      }
    }
    .image-wrapper {
      display: flex;
      justify-content: center;
      width: 500px;
      height: 300px;
      border: 2px solid #dcdcdc;
      margin-bottom: 30px;
      position: relative;
      .upload-image {
        max-height: 100%;
        max-width: 100%;
      }
    }
  }
  .specbutton {
    display: flex;
    align-items: center;
    .upload-btn {
      margin-left: 80px;
      border: 1px solid @btn-color;
      border-radius: 4px;
      height: 32px;
      line-height: 32px;
      padding: 0 10px;
      font-size: 14px;
      display: inline-block;
      cursor: pointer;
      color: @btn-color;
      &:hover {
        color: #fff;
        background-color: @btn-color;
      }
    }
  }
</style>
