<template>
  <div class="checkbox-tag">
    <div class="tags">
      <div
        v-for="item in dataList"
        :key="item.id"
        class="tag"
        :class="{ checked: checkedIds.includes(item.id) && !deleting, deleting: deleting && item.can_deleted != 1 }"
        @click="clickTag(item.id, item.can_deleted)">
        <span style="padding-right: 5px">{{ item.name }}</span>
        <template v-if="!deleting">
          <icon-check-circle v-if="checkedIds.findIndex((val) => val == item.id) !== -1" style="color: #fff" />
          <icon-check-circle v-else style="color: #999" />
        </template>
        <icon-close-circle v-if="deleting && item.can_deleted != 1" color="#d9534f" />
      </div>
      <template v-if="!deleting && !disabled">
        <a-button class="button" type="text" @click="adding = true">添加</a-button>
        <a-button class="button" type="text" @click="deleting = true">删除</a-button>
      </template>
      <a-button v-if="deleting" class="button" type="text" @click="deleting = false">完成</a-button>
    </div>
    <div v-if="adding" ref="form" :model="formData" :rules="formRules">
      <a-input
        v-model="formData.name"
        placeholder="请输入"
        :maxlength="10"
        style="width: 200px"
        @on-enter="addTag"></a-input>
      <a-button type="text" class="button" @click="addTag">保存</a-button>
      <a-button type="text" class="button" @click="adding = false">取消</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { setBusTag } from '@/api/membership-tags';
  import { delCoachSpec, addCoachSpec } from '@/api/coach';

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    isMemberDetail: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    radio: {
      type: Boolean,
      default: false,
    },
    type: {
      type: [Number, String],
      default: '',
    },
    beforeTagAdd: Function,
    beforeTagDelete: Function,
  });

  const deleting = ref(false);
  const adding = ref(false);
  const formData = reactive({
    name: '',
  });
  const formRules = {
    name: {
      required: true,
      message: '请输入',
    },
  };

  const emit = defineEmits(['update:modelValue', 'update:data', 'tagAdded']);
  const checkedIds = computed({
    get: () => props.modelValue,
    set: (val) => {
      emit('update:modelValue', val);
    },
  });
  const dataList = computed({
    get: () => props.data,
    set: (val) => {
      emit('update:data', val);
    },
  });

  function deleteSuccess(id: string) {
    const deletedIndex = dataList.value.findIndex((item) => item.id === id);
    dataList.value.splice(deletedIndex, 1);
    const findIndex = checkedIds.value.findIndex((val) => val === id);
    if (findIndex !== -1) {
      checkedIds.value.splice(findIndex, 1);
    }
  }

  function addSuccess() {
    adding.value = false;
    formData.name = '';
    emit('tagAdded');
  }
  function setMemberTag(tags_name: string, tags_id?: string) {
    setBusTag({ tags_name, tags_id: tags_id || '' }).then(() => {
      if (tags_name) {
        addSuccess();
      } else if (tags_id) {
        deleteSuccess(tags_id);
      }
    });
  }

  function deleteTag(id: string) {
    if (props.isMemberDetail) {
      setMemberTag(formData.name);
    } else if (props.beforeTagDelete) {
      props.beforeTagDelete(id).then(() => deleteSuccess(id));
    } else {
      delCoachSpec({ ids: id }).then(() => {
        deleteSuccess(id);
      });
    }
  }

  function clickTag(id: string, canDeleted: number) {
    if (props.disabled) {
      return;
    }
    if (!deleting.value) {
      if (props.radio) {
        checkedIds.value = [id];
      } else {
        const findIndex = checkedIds.value.findIndex((val) => val === id);
        if (findIndex === -1) {
          checkedIds.value.push(id);
        } else {
          checkedIds.value.splice(findIndex, 1);
        }
      }
    } else if (canDeleted === 1) {
      Message.error('该标签不可进行删除操作');
    } else {
      deleteTag(id);
    }
  }

  function addTag() {
    if (props.isMemberDetail) {
      setMemberTag(formData.name);
    } else if (props.beforeTagAdd) {
      props.beforeTagAdd(formData.name, props.type).then(() => {
        addSuccess();
      });
    } else {
      addCoachSpec({ name: formData.name, type: props.type || '' }).then(() => {
        addSuccess();
      });
    }
  }
</script>

<style lang="less" scoped>
  .checkbox-tag {
    width: 100%;

    .tags {
      display: flex;
      flex-wrap: wrap;

      .tag {
        display: flex;
        align-items: center;
        justify-content: center;
        user-select: none;

        height: 30px;
        padding: 0 10px;
        margin-right: 10px;
        margin-bottom: 10px;

        cursor: pointer;
        background-color: #f1f1f1;
        color: #999;
      }

      .checked {
        background-color: #ff2351;
        color: #fff;
      }

      .deleting {
        background-color: #fff;
        color: #d9534f;
        border: 1px solid #d9534f;
      }
    }
  }

  .button {
    width: 50px;
    min-width: 0;
    height: 30px;
  }
</style>
