<template>
  <a-scrollbar v-if="modelValue.length" type="track" style="height: 98px; overflow-x: auto">
    <a-space>
      <div v-for="(item, index) in modelValue" :key="index" class="arco-statistic finance-statistics">
        <div class="arco-statistic-title">
          {{ item.name }}
          <a-tooltip v-if="item.tips" :content="item.tips">
            <icon-question-circle style="color: #ff6323" />
          </a-tooltip>
        </div>
        <div class="arco-statistic-content">
          <div class="arco-statistic-value">
            <span class="arco-statistic-value-integer">{{ item.value }}</span>
            <span v-if="item.unit" class="arco-statistic-suffix">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </a-space>
  </a-scrollbar>
  <a-divider v-if="modelValue.length" style="margin-top: 0" />
</template>

<script lang="ts" setup>
  defineProps<{
    modelValue: [];
  }>();
</script>
