<template>
  <a-select v-bind="$attrs" v-model="selectedValue" placeholder="请选择" allow-search>
    <a-option v-for="item in couponList" :key="item.coupon_id" :value="item.coupon_id">{{ item.coupon_name }}</a-option>
  </a-select>
</template>

<script lang="ts" setup>
  /* 折扣券下拉 */
  import { getBusCoupon } from '@/api/coupon';

  const props = defineProps<{
    modelValue?: string;
    showHistory?: boolean;
  }>();

  const couponList = ref([]);
  const emits = defineEmits(['update:modelValue', 'onChange']);
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
      const info = couponList.value.find((i) => i.coupon_id === value);
      emits('onChange', {
        value: info ? info.value : '',
        label: info ? info.label : '',
      });
    },
  });

  function loadCouponList() {
    getBusCoupon({ is_show: props.showHistory ? 1 : '' }).then((res) => {
      couponList.value = res.data.value || [];
    });
  }
  onMounted(() => {
    loadCouponList();
  });
</script>
