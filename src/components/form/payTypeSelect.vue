<template>
  <a-select
    v-model="payType"
    :placeholder="placeholder"
    :allow-search="allowSearch"
    :allow-clear="allowClear"
    :loading="isLoading"
    :disabled="disabled"
    @change="payTypeChange"
    @popup-visible-change="popupVisibleChange">
    <a-option v-for="item in filterPayTypeList" :key="item.id" :value="item.pay_type_id">
      {{ item.pay_type_name }}
    </a-option>
  </a-select>
</template>

<script lang="ts" setup>
  import { getBusPayType } from '@/api/payment-manage';

  const props = defineProps({
    modelValue: {
      type: [Number, String],
      default: '',
    },
    // 储值卡支付
    showCardPay: {
      type: Boolean,
      default: false,
    },
    // 收钱吧支付
    showSQB: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: '支付方式',
    },
    allowSearch: {
      type: Boolean,
      default: true,
    },
    allowClear: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    busId: {
      type: String,
      default: '',
    },
  });
  const emits = defineEmits(['update:modelValue', 'onChange']);

  const payTypeList = ref([]);
  const filterPayTypeList = computed(() => {
    return payTypeList.value.filter((item) => {
      const id = +item.pay_type_id;
      const show =
        item.usable === 1 &&
        ((id !== 8 && id !== 20 && id !== 21) ||
          (id === 8 && props.showCardPay) ||
          ((id === 20 || id === 21) && props.showSQB));
      return show;
    });
  });

  const payType = computed({
    // ...mapState('pay', ['payTypeList']),
    get() {
      let val = props.modelValue || '';
      // 支付方式赋值过滤  8 储值卡支付 20 收钱吧  21 杉德支付
      if (val !== undefined && (((val === 20 || val === 21) && !props.showSQB) || (val === 8 && !props.showCardPay))) {
        val = '';
        emits('update:modelValue', val);
      }
      return val;
    },
    set(val) {
      emits('update:modelValue', val);
    },
  });

  const { isLoading, execute } = getBusPayType();
  const needRefresh = ref(false);
  async function getPayTypes() {
    const { data } = await execute({
      data: { bus_id: props.busId },
    });
    payTypeList.value = data.value.list || [];
    needRefresh.value = false;
  }

  function payTypeChange(val) {
    emits('onChange', val);
  }

  function popupVisibleChange(visible: boolean) {
    if (visible && needRefresh.value) {
      getPayTypes();
    }
  }

  watch(
    () => props.busId,
    (newValue, oldValue) => {
      if (newValue !== oldValue) {
        payType.value = '';
        needRefresh.value = true;
      }
    }
  );

  getPayTypes();
</script>
