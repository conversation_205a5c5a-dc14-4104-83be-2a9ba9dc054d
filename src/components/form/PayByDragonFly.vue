<template>
  <a-modal v-model:visible="isShowModal" :mask-closable="false" title="收款" @cancel="handleCancelPay">
    <a-form v-if="status === '0'" ref="payForm" :model="postData" class="modal-form" auto-label-width>
      <a-form-item>
        <a-radio-group v-if="showPrePay" v-model="type">
          <a-radio :value="0">
            <span>从已收款中选择抵扣</span>
          </a-radio>
          <a-radio :value="1">
            <span>使用设备收款</span>
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item v-if="type === 0" label="抵扣款项">
        <a-checkbox-group v-if="payedList && payedList.length" v-model="payOrderIds">
          <a-checkbox v-for="payed in payedList" :key="payed.id" :value="payed.id">
            预收款抵扣
            <span class="text-red">{{ payed.amount }}</span>
          </a-checkbox>
        </a-checkbox-group>
        <div v-else>暂无已收款</div>
      </a-form-item>
      <div v-if="type === 1">
        <a-form-item
          label="收款金额"
          field="amount"
          :rules="{
            required: true,
            pattern: /^(0\.(0[1-9]|[1-9]\d?)|[1-9]\d*(\.\d{0,2})?)$/,
            message: '金额必须大于0且只能保留两位小数',
          }">
          <a-input v-model="postData.amount" />
        </a-form-item>
        <a-form-item label="收款设备" field="remark">
          <a-radio-group
            class="radiobg-tags"
            :value="postData.device_type + '---' + postData.device_id"
            type="button"
            @change="handleConfirm">
            <a-radio
              v-for="item in deviceList"
              :key="item.device_type + '_' + item.device_id"
              :value="item.device_type + '---' + item.device_id"
              :class="item.device_type === 1 ? 'dragonfly-bg' : ''"
              :disabled="item.device_type === 1 && isPayLoading">
              <a-spin v-if="item.device_type === 1 && isPayLoading"></a-spin>
              <span v-else>{{ item.device_name }}</span>
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </div>
    </a-form>
    <div v-else-if="!showScanBar">
      <div v-if="postData.device_type === 1">收款信息已发送到蜻蜓机</div>
      <a-spin v-if="status === '1'" :loading="status === '1'" tip="付款状态查询中"></a-spin>
      <div v-if="status === '2'">付款成功</div>
      <div v-if="status === '3'">付款失败</div>
      <div v-if="status === '4'">用户取消支付</div>
    </div>
    <div v-else>
      <ScanBarCode @on-enter="handleScanEnter" />
    </div>
    <template #footer>
      <div class="modal-buttons">
        <a-space v-if="status === '0' && type !== 1">
          <a-button @click="handleCancelPay">取消</a-button>
          <a-button type="primary" @click="handleConfirm">确定</a-button>
        </a-space>
        <a-button
          v-if="status == '1' && !showScanBar && postData.device_type === 1"
          type="text"
          :loading="isCancelLoading"
          @click="handleCancel">
          取消收款
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { cancelPay, pay, checkPayStat, getPayedList as getTOPayedList, getDevice } from '@/api/pay';
  import ScanBarCode from './ScanBarCode.vue';

  const props = defineProps({
    describe: {
      type: [String, Number],
    },
    amount: {
      type: [String, Number],
      default: 0,
    },
    isEqual: {
      type: Boolean,
      default: true,
    },
    modelValue: {
      type: Boolean,
    },
    userId: {
      type: [String, Number],
      default: '',
    },
    busId: {
      type: [String, Number],
      default: '',
    },
    phone: {
      type: [String, Number],
      default: '',
    },
    payType: {
      type: [String, Number],
    },
    serviceType: {
      // 1商品支付，2购续升卡等卡操作，3租柜、押金，4定金，5团课预约，其他不传
      type: Number,
    },
    list: {
      // 购卡等页面使用多支付方式  预售列表从外部传入以便去重已选预收项
      type: Array,
      default: () => [],
    },
  });

  const state = reactive({
    orderNo: '',
    orderId: '',
    payStatusTimer: '',
    timeOutCloseTimer: '',
    totalAmount: 0,
  });
  const type = ref(0);
  const status = ref('0');
  const deviceList = ref([]);
  const payedList = ref([]);
  const payOrderIds = ref([]);
  const showPrePay = ref(true);
  const showScanBar = ref(false);
  const postData = reactive({
    user_id: '',
    auth_code: '', // 扫码枪auth_code（必须存在device_id或auth_code）
    amount: '',
    describe: '',
    device_type: 0, // 扫码枪设备类型 默认为0
    device_id: '', // 蜻蜓机设备id（必须存在device_id或auth_code）
  });
  const payForm = ref();
  const emit = defineEmits(['on-success', 'on-cancel', 'update:modelValue', 'onDragonflyCancel', 'onDragonflyConfirm']);

  const isShowModal = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emit('update:modelValue', val);
    },
  });

  function payListInit(list) {
    payedList.value = list.filter((item) => item.show !== false && item.pay_type === props.payType);
    if (!payedList.value || !payedList.value.length) {
      type.value = 1;
      showPrePay.value = false;
    } else {
      showPrePay.value = true;
    }
  }

  function onCancel() {
    emit('onDragonflyCancel', {
      payType: props.payType,
    });
  }

  function handleCancelPay() {
    isShowModal.value = false;
    onCancel();
  }

  const { isLoading: isCancelLoading, execute: postToCancel } = cancelPay();
  function handleCancel() {
    postToCancel({ data: { order_no: state.orderNo } }).then(() => {
      handleCancelPay();
    });
  }

  function emitConfirm() {
    isShowModal.value = false;
    emit('onDragonflyConfirm', {
      type: type.value,
      pay_order_ids: payOrderIds.value,
      payType: props.payType,
      amount: state.totalAmount,
    });
  }

  function payByScan() {
    console.log('payByScan');
    status.value = '1';
    showScanBar.value = true;
  }

  function getPayStatus() {
    checkPayStat({ order_no: state.orderNo })
      .then((res) => {
        const { status: sta } = res.data.value;
        // status：1-正在支付，2-支付成功，3-支付失败，4-用户取消支付
        if (status && (sta === '2' || sta === '3' || sta === '4')) {
          status.value = sta;
          if (sta === '2') {
            Message.success('支付成功！');
            state.totalAmount = postData.amount;
            payOrderIds.value = [String(state.orderId)];
            emitConfirm();
          } else {
            onCancel();
          }
          setTimeout(() => {
            isShowModal.value = false;
          }, 3000);
        } else {
          state.payStatusTimer = setTimeout(() => {
            getPayStatus();
          }, 3000);
        }
      })
      .catch(() => {
        state.payStatusTimer = setTimeout(() => {
          getPayStatus();
        }, 3000);
      });
  }

  function timeOutClose() {
    if (state.timeOutCloseTimer) {
      return;
    }
    // 超时5分钟未支付取消订单
    state.timeOutCloseTimer = setTimeout(() => {
      if (isShowModal.value) {
        Message.error('支付超时，弹窗即将关闭！');
        handleCancel();
      }
    }, 5 * 60 * 1000);
  }

  const { isLoading: isPayLoading, execute: postToPay } = pay();
  function payByDragonFly() {
    showScanBar.value = false;
    postToPay({ data: { ...postData, pay_type: props.payType } })
      .then((res) => {
        status.value = '1';
        state.orderNo = res.data.value.order_no;
        state.orderId = res.data.value.id;
        timeOutClose();
        getPayStatus();
      })
      .catch(() => {
        status.value = '3';
      });
  }

  function handleScanEnter(data) {
    postData.auth_code = data;
    payByDragonFly();
  }

  async function handleConfirm(info) {
    const totalAmount = type.value === 1 ? postData.amount : state.totalAmount;
    if (Number(state.amount) < totalAmount) {
      Message.error('金额超过剩余应收！');
      return;
    }
    if (state.isEqual && Number(state.amount) !== totalAmount) {
      Message.error('支付金额不等于应收金额');
      return;
    }
    if (type.value === 0) {
      emitConfirm();
    } else {
      const errors = await payForm.value?.validate();
      if (!errors && info) {
        const [deviceType, deviceID] = info.split('---');
        postData.device_id = deviceID || '';
        postData.device_type = +deviceType;
        if (postData.device_type === 2) {
          payByScan();
        } else {
          payByDragonFly();
        }
      }
    }
  }

  function getPayedList() {
    if (!Number(postData.user_id) && !props.phone) {
      return;
    }
    getTOPayedList({
      bus_id: props.busId || '',
      user_id: postData.user_id,
      phone: props.phone,
    }).then((res) => {
      postData.user_id = res.data.value.user_id;
      payListInit(res.data.value.list);
    });
  }
  function getDeviceList() {
    if (!Number(postData.user_id) && !props.phone) {
      Message.error('请先选择会员');
      setTimeout(() => {
        handleCancelPay();
      }, 0);
      return;
    }
    getDevice({
      service_type: props.serviceType || '',
      pay_type_id: props.payType,
    }).then((res) => {
      deviceList.value = res.data.value;
      if (deviceList.value.length) {
        postData.device_id = deviceList.value[0].device_id;
      }
    });
  }

  watch(
    () => props.userId,
    (newValue, oldValue) => {
      if (newValue !== oldValue) {
        postData.user_id = newValue;
        if (newValue && oldValue !== newValue) {
          getPayedList();
          payOrderIds.value = [];
        }
      }
    }
  );

  watch(
    () => props.amount,
    (newValue, oldValue) => {
      if (newValue !== oldValue) {
        postData.amount = String(newValue);
      }
    },
    {
      immediate: true,
    }
  );

  watch(
    () => payOrderIds.value,
    () => {
      state.totalAmount = 0;
      payedList.value.forEach((item) => {
        if (payOrderIds.value.indexOf(item.id) !== -1) {
          state.totalAmount = (Number(state.totalAmount) + Number(item.amount)).toFixed(2);
        }
      });
    }
  );

  watch(
    () => props.payType,
    (newValue, oldValue) => {
      if (newValue !== oldValue) {
        postData.pay_type = newValue;
      }
    }
  );

  watch(
    () => props.describe,
    (val) => {
      postData.describe = val;
    },
    {
      immediate: true,
    }
  );

  const route = useRoute();
  onMounted(() => {
    postData.user_id = props.userId ? props.userId : route.params.userId ? route.params.userId : route.query.userId;
    if (props.list && props.list.length) {
      payListInit(props.list);
    } else {
      getPayedList();
    }
    getDeviceList();
  });

  onBeforeUnmount(() => {
    if (state.payStatusTimer) {
      clearTimeout(state.payStatusTimer);
    }
    if (state.timeOutCloseTimer) {
      clearTimeout(state.timeOutCloseTimer);
    }
  });
</script>

<style lang="less" scoped>
  .radiobg-tags {
    :deep(.arco-radio-button) {
      display: inline-flex;
      align-items: flex-end;
      justify-content: center;
      width: 100px;
      height: 60px;
      border-radius: 4px;
      text-align: center;
      margin: 0 10px 10px 0;
      white-space: normal;
      background: url(../../assets/img/saomaqiang.png) #8dbef1 no-repeat top center;
      background-size: 50px 40px;
      border-color: #8dbef1;
      color: #73263b;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      &.dragonfly-bg {
        background: url(../../assets/img/dianshiji.png) #ef9780 no-repeat top center;
        background-size: 50px 40px;
        border-color: #ef9780;
      }
    }
  }
  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }
</style>
