<template>
  <div>
    <div class="scan-tit">请将扫描枪/ 二维码读取器对准收款二维码</div>
    <img class="scan-img" :src="getAssetsImg('scan-bar.png')" alt="请将扫描枪/ 二维码读取器对准收款二维码" />
  </div>
</template>

<script lang="ts" setup>
  import { getAssetsImg } from '@/utils';

  const emit = defineEmits(['onEnter']);
  const hasEnterNum = ref('');
  const authCode = ref('');

  function handleKeyDown(e) {
    const event = e || window.event;
    const { key } = event;
    if (event.target.localName !== 'input' && /^[0-9]*$/.test(key)) {
      hasEnterNum.value += key;
    }
    if (key === 'Enter') {
      authCode.value = hasEnterNum.value;
      hasEnterNum.value = '';
      emit('onEnter', authCode.value);
    }
  }
  onBeforeUnmount(() => {
    document.removeEventListener('keydown', handleKeyDown);
  });
  document.addEventListener('keydown', handleKeyDown);
</script>

<style lang="less" scoped>
  .scan-tit {
    font-size: 16px;
    text-align: center;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .scan-img {
    display: block;
    margin: 0 auto;
  }
</style>
