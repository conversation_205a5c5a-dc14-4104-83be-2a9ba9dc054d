# DateRangePicker Component

A reusable Vue 3 date range picker component that combines an Arco Design range picker with quick time selection buttons.

## Features

- **Date Range Selection**: Uses Arco Design's `a-range-picker` for date selection
- **Quick Time Buttons**: Provides buttons for common time ranges (昨天/今天/本周/本月)
- **Flexible Range Validation**: Configurable maximum date range validation (no limit by default)
- **Vue 3 Composition API**: Built with modern Vue 3 patterns
- **TypeScript Support**: Fully typed with TypeScript
- **Event Emission**: Emits events for parent component integration

## Usage

### Basic Usage

```vue
<template>
  <a-form-item field="date_range" label="时间">
    <DateRangePicker
      v-model:model-value="searchForm.date_range"
      v-model:time-range="searchForm.time_range"
      :max-range-months="6"
      max-range-message="日期范围不能超过6个月，请重新选择"
      @change="handleDateRangeChange" />
  </a-form-item>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import DateRangePicker from '@/components/form/date-range-picker.vue';

const searchForm = reactive({
  date_range: [],
  time_range: '',
});

const handleDateRangeChange = (dateRange: string[], timeRange: string) => {
  console.log('Date range changed:', dateRange, timeRange);
  // Add your custom logic here
};
</script>
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `modelValue` | `string[]` | `[]` | The selected date range (v-model) |
| `timeRange` | `string` | `''` | The selected time range button |
| `placeholder` | `string[]` | `['开始时间', '结束时间']` | Placeholder text for date inputs |
| `pickerStyle` | `string \| Record<string, any>` | `'width: 300px; margin-right: 16px'` | Custom styles for the date picker |
| `maxRangeMonths` | `number \| undefined` | `undefined` | Maximum date range in months (undefined = no limit) |
| `maxRangeMessage` | `string` | `'日期范围超出限制，请重新选择'` | Custom message when range limit is exceeded |

### Events

| Event | Parameters | Description |
|-------|------------|-------------|
| `update:modelValue` | `value: string[]` | Emitted when date range changes |
| `update:timeRange` | `value: string` | Emitted when time range button is selected |
| `change` | `value: string[], timeRange: string` | Emitted when either date range or time range changes |

### Time Range Options

The component provides four quick selection buttons:

- **昨天** (`yesterday`): Selects yesterday's date
- **今天** (`today`): Selects today's date  
- **本周** (`week`): Selects current week (Monday to Sunday)
- **本月** (`month`): Selects current month

## Validation

The component supports **flexible date range validation**:

- **No limit by default**: When `maxRangeMonths` is undefined (default), no validation is applied
- **Custom range limit**: Set `maxRangeMonths` to enforce a maximum range (e.g., 6 for 6 months)
- **Custom messages**: Use `maxRangeMessage` to provide user-friendly error messages
- **Automatic validation**: When manually selecting dates, if the range exceeds the limit, a warning message is shown
- **Smart date picker**: The `disabledDate` function prevents selection of dates outside the allowed window

### Examples

```vue
<!-- No limit (default) -->
<DateRangePicker v-model:model-value="dateRange" />

<!-- 6-month limit -->
<DateRangePicker
  v-model:model-value="dateRange"
  :max-range-months="6"
  max-range-message="日期范围不能超过6个月，请重新选择" />

<!-- 1-year limit -->
<DateRangePicker
  v-model:model-value="dateRange"
  :max-range-months="12"
  max-range-message="日期范围不能超过1年，请重新选择" />
```

## Implementation Details

### Date Format
- All dates are formatted as `YYYY-MM-DD` using dayjs
- The component uses dayjs for all date calculations and formatting

### State Management
- When a time range button is clicked, it automatically sets the corresponding date range
- When dates are manually selected, the time range selection is cleared
- Invalid date ranges (>6 months) clear both date and time selections

### Styling
- Uses flexbox layout with `display: flex` and `align-items: center`
- Maintains consistent spacing between the date picker and button group
- Inherits Arco Design's styling for consistent UI

## Migration from Original Implementation

This component replaces the duplicated date picker logic in:
- `src/views/member/followup-detail.vue`
- `src/views/member/components/followup-coach-list.vue`

### Before (Duplicated Code)
```vue
<a-range-picker
  v-model="searchForm.date_range"
  style="width: 300px; margin-right: 16px"
  :placeholder="['开始时间', '结束时间']"
  :disabled-date="disabledDate"
  @change="handleDateRangeChange" />
<a-button-group>
  <a-button :type="searchForm.time_range === 'yesterday' ? 'primary' : 'outline'" @click="handleTimeRangeChange('yesterday')">昨天</a-button>
  <!-- ... more buttons ... -->
</a-button-group>
```

### After (Reusable Component)
```vue
<DateRangePicker
  v-model:model-value="searchForm.date_range"
  v-model:time-range="searchForm.time_range"
  @change="handleDateRangeChange" />
```

## Benefits

1. **Code Reusability**: Single component used across multiple views
2. **Maintainability**: Changes only need to be made in one place
3. **Consistency**: Ensures identical behavior across all usage locations
4. **Type Safety**: Full TypeScript support with proper prop and event typing
5. **Performance**: Reduced bundle size by eliminating duplicate code
6. **Flexibility**: Configurable range limits for different use cases
7. **User Experience**: Customizable error messages for better UX

## Changelog

### v2.0.0 - Flexible Range Limits
- **BREAKING CHANGE**: `maxRangeMonths` now defaults to `undefined` (no limit) instead of 6 months
- **NEW**: Added `maxRangeMonths` prop for configurable range limits
- **NEW**: Added `maxRangeMessage` prop for custom error messages
- **IMPROVED**: Better validation logic that only applies when limits are set
- **MIGRATION**: Existing usage needs to explicitly set `:max-range-months="6"` to maintain 6-month limit
