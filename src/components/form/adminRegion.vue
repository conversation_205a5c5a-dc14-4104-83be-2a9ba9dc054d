<template>
  <a-select
    v-model="selectedUnionIds"
    :multiple="multiple"
    :allow-search="allowSearch"
    :allow-clear="!multiple"
    :placeholder="placeholder"
    :disabled="disabled"
    @change="optionChange">
    <a-option v-if="hasStore" :value="singularPrimaryKey ? 'm' : 'm_m_m'">场馆收入</a-option>
    <a-option
      v-for="item in state.list"
      :key="item.unionId"
      :value="item.unionId"
      :disabled="disabledUids.indexOf(item.unionId) !== -1"
      :style="{ 'padding-left': +item.level * 15 + 'px' }">
      {{ item.name }}
    </a-option>
  </a-select>
</template>

<script setup lang="ts">
  import cloneDeep from 'lodash/cloneDeep';
  import { treeToList } from '@/utils';
  import request from '@/request/index';

  const props = defineProps({
    singularPrimaryKey: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: [Array, String],
      default: '',
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    allowSearch: {
      type: Boolean,
      default: true,
    },
    id: {
      type: [String, Number],
      default: '',
    },
    shouldDefault: {
      // 是否需要默认选中当前场馆 0不需要  1需要
      type: Number,
      default: 0,
    },
    url: {
      type: String,
      default: '/Merchant/admin/getMerchantsRegionBus',
    },
    busId: {
      type: String,
      default: '',
    },
    hasStore: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['update:modelValue', 'change']);

  const state = reactive({
    list: [] as any[],
  });
  const disabledUids = ref([]);

  const selectedUnionIds: any = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emits('update:modelValue', val);
      // 选中的节点数组对象信息
      const selectedList = state.list.filter((item) => {
        return val.indexOf(item.unionId) !== -1;
      });
      emits('change', props.multiple ? selectedList : selectedList[0]);
    },
  });

  function initResData(data: any[]) {
    let selectedUnionIdsCopy = cloneDeep(selectedUnionIds.value);
    // 树Id和场馆id可能重合  组合唯一id
    data.forEach((item) => {
      let unionId = '';
      if (props.singularPrimaryKey) {
        unionId = item.id;
      } else {
        unionId = `${item.id}_${item.type}_${item.level}`;
      }

      item.unionId = unionId;

      if (item.default) {
        if (props.multiple) {
          selectedUnionIdsCopy.push(unionId);
        } else {
          selectedUnionIdsCopy = unionId;
        }
      }
      item.default = 0;
    });
    state.list = data;
    selectedUnionIds.value = selectedUnionIdsCopy;
  }
  // 获取父元素
  function getParentItem(parentUid) {
    return state.list.find((i) => i.unionId === parentUid);
  }
  // 选中了祖先元素
  function isCheckedAncestor(item) {
    // 如果不支持多选，直接返回false
    if (!props.multiple) return false;
    const parentUid = `${item.pid}_1_${+item.level - 1}`;
    const parentItem = getParentItem(parentUid);
    if (!parentItem) {
      // 如果没有父元素，说明已经到达根节点
      return false;
    }
    // 检查当前父级是否被选中
    if (selectedUnionIds.value.includes(parentUid)) {
      return true;
    }
    // 递归检查上一级的祖先是否被选中
    return isCheckedAncestor(parentItem);
  }

  // 本身被禁用,或者父元素被选中的都是需要禁用的
  function setDisabledUnionIds() {
    disabledUids.value = [];
    state.list.forEach((item) => {
      if (item.is_disable || isCheckedAncestor(item)) {
        disabledUids.value.push(item.unionId);
        if (Array.isArray(selectedUnionIds.value)) {
          selectedUnionIds.value.forEach((uid, index) => {
            if (uid === item.unionId) {
              selectedUnionIds.value.splice(index, 1);
            }
          });
        }
      }
    });
  }

  function optionChange() {
    setTimeout(() => {
      setDisabledUnionIds();
    }, 0);
  }

  async function getRoleBusRegion() {
    const { execute } = request({
      url: props.url,
      options: { method: 'POST' },
      immediate: false,
    });
    const { data } = await execute({
      data: {
        admin_id: props.id,
        id: props.id,
        is_default: props.shouldDefault,
        bus_id: props.busId,
      },
    });

    initResData(treeToList(data.value, 'son'));
    setTimeout(() => {
      setDisabledUnionIds();
    }, 0);
  }

  getRoleBusRegion();

  watch(
    () => props.busId,
    (newValue, oldValue) => {
      if (newValue !== '' && newValue !== oldValue) {
        selectedUnionIds.value = '';
        getRoleBusRegion();
      }
    }
  );
</script>

<style scoped></style>
