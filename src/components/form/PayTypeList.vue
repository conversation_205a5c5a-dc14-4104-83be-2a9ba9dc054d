<template>
  <div class="pay-type-list">
    <div
      v-for="payType in showPayTypes"
      :key="payType.pay_type"
      class="pay-type-item"
      :class="{
        'selected': selectPayTypeIds.includes(payType.pay_type),
        'choose-disabled': isLockPayTypeItem(payType.pay_type),
      }"
      :isMoveFlag="false"
      @click="onPayTypeClick(payType, $event)"
      @mousedown="onMouseDown">
      <div class="pay-type">
        {{ payType.name }}
      </div>
      <div v-if="selectPayTypeIds.includes(payType.pay_type)" class="pay-con" @click.stop>
        <div v-if="payType.pay_type === 8" class="pay-type-info">
          <!-- <div class="con-title">用卡</div> -->
          <MoneyCardSelect
            v-model="selectedPayTypes[selectPayTypeIds.indexOf(payType.pay_type)].card_user_id"
            class="con-main"
            :disabled="isCardSelectDisabled(payType.pay_type)"
            :bus-id="busId"
            :user-id="userId"
            @user-no-card="onSelectCancel(selectPayTypeIds.indexOf(payType.pay_type), $event)" />
        </div>
        <div v-if="selectedPayTypes[selectPayTypeIds.indexOf(payType.pay_type)]" class="pay-type-info">
          <!-- <div class="con-title">金额</div> -->
          <a-input-number
            class="con-main"
            :min="0"
            :precision="2"
            :model-value="Number(selectedPayTypes[selectPayTypeIds.indexOf(payType.pay_type)].amount)"
            :disabled="isAmountDisabled(payType.pay_type)"
            :max="
              isMaxAmount
                ? Number(selectedPayTypes[selectPayTypeIds.indexOf(payType.pay_type)]?.max_amount || Infinity)
                : Infinity
            "
            @change="handleAmountChange(payType.pay_type, $event)" />
        </div>
      </div>
    </div>
    <div v-if="showDragonFly">
      <PayByDragonFly
        v-model="showDragonFly"
        :describe="computeSqbOption.describe"
        :list="sqbPayedList.value"
        :pay-type="computeSqbOption.payType"
        :amount="computeSqbOption.amount"
        :bus-id="busId"
        :user-id="userId"
        :is-equal="computeSqbOption.isEqual"
        :phone="sqbPhoneInfo.phone"
        :service-type="computeSqbOption.serviceType"
        @on-dragonfly-confirm="onDragonFlyConfirm"
        @on-dragonfly-cancel="onDragonflyCancel" />
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, inject, watchEffect } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { usePayStore } from '@/store';
  import MoneyCardSelect from '@/components/member/moneyCardSelect.vue';
  import { getPayedList } from '@/api/pay';
  import PayByDragonFly from './PayByDragonFly.vue';

  const props = defineProps({
    // 选中的支付方式列表
    modelValue: {
      type: Array,
      default: () => [],
    },
    amount: {
      type: Number,
      required: true,
      default: 0,
    },
    // 为true时所有选项不可编辑并且只会展示之前已经选中的支付方式，用于展示详情等场景
    disabled: {
      type: Boolean,
      default: false,
    },
    // 退款场景下是否需要展示收钱吧需要传这个字段 传了之后会根据收款时是否含有收钱吧来决定是否展示
    isRefundNeedSQB: {
      type: Boolean,
      default: false,
    },
    // 锁定储值卡卡种
    isLockCard: {
      type: Boolean,
      default: false,
    },
    /* 是否特定退款场景
     *1.如果之前收款方式含有储值卡此时会锁定储值卡支付不让取消，并且不能改变卡种，但金额可编辑
     *2.退款场景下如果当前支付项can_edit===0，会锁定支付不让取消，但金额可编辑. can_edit===null，会锁定支付不让取消，金额不可编辑
     */
    isRefund: {
      type: Boolean,
      default: false,
    },
    // 是否展示储值卡支付
    showCardPay: {
      type: Boolean,
      default: true,
    },
    // 工行支付除不需要展示收钱吧收款弹窗可直接编辑金额外，其余逻辑可大体按照收钱吧处理
    // 是否展示收钱吧支付（pay_type=20||21）工行支付 22 建行支付 23
    showSQB: {
      type: Boolean,
      default: true,
    },
    userId: {
      type: [String, Number],
      default: '',
    },
    busId: {
      type: [String, Number],
      default: '',
    },
    // 收钱吧相关选项
    sqbOption: {
      type: Object,
      default: () => ({
        isEqual: false, // 针对多支付方式的情况,并不知道收钱吧支付金额是否为剩余金额，此时不需要金额相等判断
        serviceType: 2, // 1为商品支付 2为购续升编辑订场等卡相关操作，3为租柜收押金，4为收定金，其它场景暂时为空
        describe: '购卡', // 支付的描述，主要用于在蜻蜓机上显示
      }),
    },
    // 选中收钱吧时是否需要拉起收钱吧弹窗重新收款或选择
    needShowDragonFly: {
      type: Boolean,
      default: true,
    },
    // 是否限制最大支付金额
    isMaxAmount: {
      type: Boolean,
      default: true,
    },
  });

  // payTypes：所有支付方式 isMoneyCardMix：是否支持储值卡混合支付
  const payInfo = usePayStore();
  const { payTypes, isMoneyCardMix } = storeToRefs(payInfo);
  // 经过过滤后可供选择的支付方式
  const showPayTypes = ref([]);
  const emit = defineEmits(['update:modelValue']);
  // 选中的支付方式
  const selectedPayTypes = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emit('update:modelValue', val);
    },
  });

  const selectPayTypeIds = computed(() => {
    return selectedPayTypes.value.map((item) => item.pay_type);
  });
  const currentIsMoneyCardMix = ref(isMoneyCardMix.value);
  // 若之前选中的支付方式为储值卡和其它支付方式,则需要当前也支持混合支付
  function isMoneyCardMixChange() {
    if (selectPayTypeIds.value.length > 1 && selectPayTypeIds.value.includes(8)) {
      currentIsMoneyCardMix.value = true;
    }
  }
  watchEffect(() => {
    isMoneyCardMixChange();
  });
  function findSelectPayTypeIndex(pay_type) {
    return selectedPayTypes.value.findIndex((item) => item.pay_type === pay_type);
  }
  // 退款场景下 只有收款时选择了储值卡才会显示储值卡,此时储值卡锁定，只可编辑金额
  function isRefundShowCar(pay_type) {
    if (pay_type !== 8) {
      return false;
    }
    return props.isRefund ? selectPayTypeIds.value.includes(8) : true;
  }
  // 退款场景下 只有收款时选择了收钱吧并且来源为小程序时才会显示收钱吧，只可编辑金额
  function isRefundShowSQB(pay_type) {
    const validPayTypes = new Set([20, 21, 22, 23]);
    if (!validPayTypes.has(pay_type)) {
      return false;
    }
    return props.isRefund
      ? props.isRefundNeedSQB && Array.from(validPayTypes).some((type) => selectPayTypeIds.value.includes(type))
      : true;
  }

  // 是否需要锁定当前支付方式 需要动态锁定的支付方式后端需要返回can_edit === 0
  function isLockPayTypeItem(pay_type) {
    const payType = selectedPayTypes.value[findSelectPayTypeIndex(pay_type)];
    const canEdit = payType?.can_edit;
    return (
      props.disabled ||
      canEdit === 0 ||
      canEdit === null ||
      (props.isRefund ? isRefundShowCar(pay_type) || isRefundShowSQB(pay_type) : false) ||
      (pay_type === 8 && props.isLockCard)
    );
  }

  function updateShowPayTypes() {
    const validPayTypeIds = new Set([20, 21, 22, 23, 8]);
    const refundPayTypeIds = new Set([20, 21, 22, 23]);

    const payTypesFilter = (item) => {
      const payTypeId = item.pay_type_id;
      const isUsable = item.usable !== undefined ? item.usable === 1 : true;
      if (!isUsable || (props.disabled && !selectPayTypeIds.value.includes(payTypeId))) {
        return false;
      }
      const isInvalidPayType = !validPayTypeIds.has(payTypeId);
      const isCardPayType = payTypeId === 8 && props.showCardPay && isRefundShowCar(payTypeId);
      // 退款场景下只有收款时选择了收钱吧并且来源为小程序时才会显示收钱吧
      const isShowSQB = refundPayTypeIds.has(payTypeId) && props.showSQB && isRefundShowSQB(payTypeId);
      return isInvalidPayType || isShowSQB || isCardPayType;
    };

    const list = payTypes.value.filter(payTypesFilter).map((item) => ({
      pay_type: item.pay_type_id,
      name: item.pay_type_name,
    }));
    showPayTypes.value = list;
  }
  watchEffect(async () => {
    if (!payTypes.value.length) {
      return;
    }
    updateShowPayTypes();
  });
  watchEffect(async () => {
    if (!showPayTypes.value.length) {
      return;
    }
    // 如果显示的支付方式发生变化 selectedPayTypes中含有之前显示现在不再显示的需要删除
    selectedPayTypes.value.forEach((item) => {
      const index = showPayTypes.value.findIndex((payType) => payType.pay_type === item.pay_type);
      if (index === -1) {
        selectedPayTypes.value.splice(findSelectPayTypeIndex(item.pay_type), 1);
      }
    });
  });

  // 是否禁用储值卡选择
  const isCardSelectDisabled = computed(() => {
    return (pay_type) => {
      const payType = selectedPayTypes.value[findSelectPayTypeIndex(pay_type)];
      const canEdit = payType?.can_edit;
      return props.disabled || canEdit === null || canEdit === 0 || props.isRefund || props.isLockCard;
    };
  });
  // 是否禁用金额输入框
  function getAmountDisabled(pay_type) {
    const payType = selectedPayTypes.value[findSelectPayTypeIndex(pay_type)];
    const payTypeId = payType?.pay_type;
    const canEdit = payType?.can_edit;
    return (
      props.disabled ||
      canEdit === null ||
      (canEdit === 0 && !props.isRefund) ||
      ((payTypeId === 20 || payTypeId === 21) && !props.isRefundNeedSQB)
    );
  }
  const isAmountDisabled = computed(() => {
    return (pay_type) => {
      return getAmountDisabled(pay_type);
    };
  });
  // 根据鼠标按下与鼠标抬起来的时间计算是否为移动，防止从金额输入框滑动到支付方式的误点击
  function onMouseDown(event) {
    const { currentTarget } = event;
    if (!currentTarget) {
      return;
    }
    currentTarget.isMoveFlag = false;
    const firstTime = new Date().getTime();
    document.onmouseup = () => {
      document.onmouseup = null;
      const lastTime = new Date().getTime();
      if (lastTime - firstTime > 300) {
        currentTarget.isMoveFlag = true;
      }
    };
  }

  // 取消选中某一项
  function onSelectCancel(index) {
    selectedPayTypes.value.splice(index, 1);
  }

  // 1.当有两项选中时，一项金额变化，另一项不是收钱吧时自动计算金额 2.当有某一项金额为undefined时自动填充
  function handleAmountChange(pay_type, event) {
    const index = findSelectPayTypeIndex(pay_type);
    const curPayType = selectedPayTypes.value[index];

    // 剩余可退金额XX元；剩余储值卡可退金额XX元
    if (curPayType?.max_amount !== undefined) {
      const price = curPayType?.max_amount || 0;
      let text = '';
      if (price >= 0 && event > price) {
        if (pay_type === 8) {
          text = `储值卡剩余可退金额${price}元`;
        } else if (pay_type === 20) {
          text = `收钱吧剩余可退金额${price}元`;
        } else {
          text = `剩余可退金额${price}元`;
        }
        Message.warning(text);
      }
    }

    curPayType.amount = event?.target ? event.target.value : event;
    const selectedPayTypesLen = selectedPayTypes.value.length;
    if (selectedPayTypesLen !== 2) {
      return;
    }
    const total = props.amount;
    if (selectedPayTypesLen === 2) {
      const otherPayTypeIndex = index === 0 ? 1 : 0;
      const otherPayType = selectedPayTypes.value[otherPayTypeIndex];
      if (curPayType.amount === 0 && !getAmountDisabled(curPayType.pay_type)) {
        nextTick(() => {
          const amount = Number(Math.max(total - otherPayType.amount, 0).toFixed(2));
          const maxAmount = Number(curPayType?.max_amount || Infinity);
          if (amount < maxAmount) {
            curPayType.amount = amount;
          } else {
            curPayType.amount = maxAmount;
          }
        });
      } else if (!getAmountDisabled(otherPayType.pay_type) && otherPayType.can_edit !== 0) {
        nextTick(() => {
          const amount = Number(Math.max(total - curPayType.amount, 0).toFixed(2));
          const maxAmount = Number(otherPayType?.max_amount || Infinity);
          if (amount < maxAmount) {
            otherPayType.amount = amount;
          } else {
            otherPayType.amount = maxAmount;
          }
        });
      }
    }
  }

  const computeSqbOption = ref({
    // amount和payType 通过当前组件计算获得 这里是初始值
    amount: 0, //  收钱吧支付金额，可用于填充弹窗中金额初始值和金额判断等
    payType: 20,
    isEqual: false,
    ...props.sqbOption,
  });

  watch(
    () => props.sqbOption,
    () => {
      computeSqbOption.value = {
        amount: 0,
        payType: 20,
        isEqual: false,
        ...props.sqbOption,
      };
    },
    { immediate: true }
  );

  const showDragonFly = ref(false);
  function onPayTypeClick(payType, event) {
    if (event.target?.isMoveFlag) {
      return;
    }
    const index = findSelectPayTypeIndex(payType.pay_type);
    const curPayType = payType.pay_type;
    if (isLockPayTypeItem(curPayType)) {
      return;
    }
    if (index >= 0) {
      onSelectCancel(index);
    } else {
      let amount = 0;
      // 不允许混合使用时 根据当前点击的支付方式处理其它选中
      if (!currentIsMoneyCardMix.value) {
        // 选中储值卡后，其他的支付方式全部取消选中
        if (curPayType === 8 && selectPayTypeIds.value.length > 0) {
          selectedPayTypes.value = [
            {
              ...payType,
              amount: props.amount,
            },
          ];
          return;
        }
        if (curPayType !== 8 && selectPayTypeIds.value.includes(8)) {
          // 当前选中为其它支付方式后，储值卡取消选中
          selectedPayTypes.value.splice(findSelectPayTypeIndex(8), 1);
        }
      }
      // 如果之前没有选择支付方式，自动填充金额
      if (selectedPayTypes.value.length === 0 && ![20, 21, 22, 23].includes(payType.pay_type)) {
        amount = props.amount;
      }
      // 收钱吧弹窗展示逻辑
      if (curPayType === 20 || curPayType === 21) {
        let otherAmount = 0;
        selectedPayTypes.value.forEach((item) => {
          if (item.pay_type !== curPayType) {
            otherAmount += +item.amount || 0;
          }
        });
        if (props.needShowDragonFly && !props.isRefund) {
          amount = Number(Math.max(props.amount - otherAmount, 0).toFixed(2));
          computeSqbOption.value.amount = amount;
          computeSqbOption.value.payType = curPayType;
          showDragonFly.value = true;
        }
      }
      // 选中
      selectedPayTypes.value.push({
        ...payType,
        amount,
      });
      handleAmountChange(payType.pay_type, amount);
    }
  }

  // 下面是收钱吧逻辑
  // 手机号信息，跨店转卡等特殊场景需要 用以通过手机号识别用户身份 注意祖先组件provide注入方需要使用 computed() 函数提供一个计算属性
  const sqbPhoneInfo = inject('sqbPhoneInfo', {
    phone: '',
    old_bus_id: '',
  });
  const sqbServInfo = inject('sqbServInfo', {
    serv_type: '', // 业务类型,现阶段只有编辑卡和合同相关操作的时候传了1  其它场景没传
    serv_id: '', // 业务id 编辑的时候从后台获取到的值
  });
  // 收钱吧在当前用户下已经收取了的款项ID
  const sqbPayedList = ref([]);

  // 获取收钱吧预收款信息
  function getPayedListInfo() {
    if (!(sqbPhoneInfo.value && sqbPhoneInfo.value.phone) && !props.userId) {
      return false;
    }
    return getPayedList({
      bus_id: props.busId || '',
      user_id: props.userId,
      serv_type: sqbServInfo.value?.serv_type,
      serv_id: sqbServInfo.value?.serv_id,
      ...sqbPhoneInfo.value,
    }).then((res) => {
      sqbPayedList.value = res.data.value?.list || [];
    });
  }
  // 确认收钱吧收款后更新收钱吧选项信息
  async function onDragonFlyConfirm(info) {
    await getPayedListInfo();
    const index = findSelectPayTypeIndex(info.payType);
    if (index >= 0) {
      selectedPayTypes.value.splice(index, 1, {
        ...selectedPayTypes.value[index],
        amount: info.amount,
        pay_order_ids: info.pay_order_ids,
      });
    }
  }
  function onDragonflyCancel(info) {
    const index = findSelectPayTypeIndex(info.payType);
    onSelectCancel(index);
  }

  onMounted(() => {
    getPayedListInfo();
  });
</script>

<style lang="less" scoped>
  .pay-type-list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  .pay-type-info {
    display: flex;
    &:first-child {
      margin-bottom: 5px;
    }

    .con-title {
      width: 30px;
      flex-shrink: 0;
    }
    .con-main {
      flex-grow: 1;
    }
  }

  .pay-type-item {
    position: relative;
    background: #f1f3f7;
    max-width: 320px;
    width: calc(33.33% - 8px);
    height: 80px;
    border-radius: 6px;
    padding: 0 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
    font-size: 12px;
    cursor: pointer;
    &:not(:nth-child(3n)) {
      margin-right: 8px;
    }
    &.choose-disabled {
      cursor: not-allowed;
    }

    &.selected {
      display: flex;
      background: #e6f3ff;
      border: 2px solid #52a5ec;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        border: 17px solid #52a5ec;
        border-bottom-color: transparent;
        border-right-color: transparent;
      }

      &::after {
        content: '';
        width: 5px;
        height: 12px;
        position: absolute;
        left: 7px;
        top: 4px;
        border: 2px solid #fff;
        border-top-color: transparent;
        border-left-color: transparent;
        transform: rotate(45deg);
      }
    }

    .pay-type {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 34px;
      height: 34px;
      background: #ffffff;
      border-radius: 50%;
      flex: none;
      line-height: 1.4;
      margin-right: 5px;
      text-align: center;
    }
    .pay-con {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
