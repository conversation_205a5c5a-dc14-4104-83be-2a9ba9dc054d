<template>
  <a-modal v-model:visible="showAdd" :mask-closable="false" :footer="false" :width="720" @before-close="cameraClose">
    <template #title>
      <p class="upload-modal-header-content">
        <span>添加图片</span>
        <span v-if="titleTips">{{ titleTips }}</span>
      </p>
    </template>
    <a-tabs v-model:activeKey="tabs">
      <a-tab-pane v-if="allowTemplate" key="系统模板" title="系统模板">
        <div class="pic-box">
          <ul>
            <li v-for="(item, i) in defaultList" :key="i" :style="{ width: width }" @click="changeImg(item)">
              <img :style="{ width: width, height: height }" :src="item" :class="imgUrl === item ? 'selected' : ''" />
            </li>
          </ul>
        </div>
      </a-tab-pane>
      <a-tab-pane key="自定义" title="自定义">
        <div v-if="showTips" class="tips">
          <p>上传图片建议尺寸: {{ curTips.widthHeight }}</p>
          <p>格式限制: {{ curTips.format }}</p>
          <p>推荐大小: 小于{{ curTips.maxSize }}</p>
        </div>
        <div class="self-wrap">
          <CroperImg v-if="showAdd" v-model="imgUrl" :options="options" :max-size="1" :center="true" />
        </div>
      </a-tab-pane>
      <a-tab-pane v-if="allowCamera" key="拍照上传" title="拍照上传">
        <a-space direction="vertical" style="width: 100%">
          <a-alert v-if="cameraStatus === 2" type="warning">没有发现摄像设备或用户拒绝了连接</a-alert>
          <a-alert v-if="cameraStatus === 3" type="warning">请根据浏览器提示点击允许使用摄像头</a-alert>
          <video v-show="cameraStatus === 1" id="camera" ref="cameraRef" autoplay></video>
          <canvas v-show="cameraStatus === 11" id="photo" :width="optionSize[0]" :height="optionSize[1]"></canvas>
          <div style="text-align: center; width: 100%">
            <a-button v-if="cameraStatus === 1" type="primary" :loading="isLoading" @click="handleTakePhoto">
              保存图片
            </a-button>
            <a-button v-else type="outline" @click="handleCamera">打开摄像头</a-button>
          </div>
        </a-space>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script setup lang="ts">
  import { uploadImage } from '@/api/public';
  import CroperImg from './cropper.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    options: {
      type: Object,
    },
    titleTips: {
      type: String,
      default: '',
    },
    showTips: {
      type: Boolean,
      default: false,
    },
    tips: {
      type: Object,
      default: () => {
        return {
          widthHeight: '',
          format: '',
          maxSize: '',
        };
      },
    },
    defaultList: {
      // 系统封面数组
      type: Array<string>,
      default: () => [
        'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-1.png',
        'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-2.png',
        'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-3.png',
        'https://imagecdn.rocketbird.cn/mainsite-fe/diy/card-cover-4.png',
      ],
    },
    allowTemplate: {
      type: Boolean,
      default: true,
    },
    allowCamera: {
      type: Boolean,
      default: true,
    },
  });
  // 大小尺寸
  const width = '276px';
  const height = '160px';
  const curTips = reactive({
    widthHeight: `${width}X${height}`,
    format: 'jpg、png、jpeg',
    maxSize: '1MB',
  });
  watch(
    () => props.tips,
    () => {
      Object.assign(curTips, props.tips);
    },
    { immediate: true }
  );
  // 默认封面
  const imgUrl = ref('');

  // 菜单切换
  const tabs = ref('');
  const emit = defineEmits(['update:modelValue', 'onChange']);
  const showAdd = computed({
    get: () => props.modelValue,
    set: (val) => {
      emit('update:modelValue', val);
    },
  });

  // 拍照上传
  const cameraStatus = ref(0);
  const cameraRef = ref();
  const optionSize = computed(() => {
    if (Array.isArray(props.options?.fixedNumber) && props.options.fixedNumber.length === 2) {
      return props.options.fixedNumber;
    }
    return [cameraRef.value.videoWidth, cameraRef.value.videoHeight];
  });
  const cameraSize = computed(() => {
    if (Array.isArray(props.options?.fixedNumber) && props.options.fixedNumber.length === 2) {
      let [displayWidth, displayHeight] = props.options.fixedNumber;
      const maxDimension = 500;
      const aspectRatio = displayWidth / displayHeight;
      // 判断是否需要缩放
      if (displayWidth > maxDimension || displayHeight > maxDimension) {
        if (width > height) {
          // 按宽度缩放
          displayWidth = maxDimension;
          displayHeight = Math.round(maxDimension / aspectRatio);
        } else {
          // 按高度缩放
          displayHeight = maxDimension;
          displayWidth = Math.round(maxDimension * aspectRatio);
        }
      }
      return [displayWidth, displayHeight];
    }
    return [cameraRef.value.videoWidth, cameraRef.value.videoHeight];
  });
  let cameraStream: any = null;
  const cameraClose = () => {
    cameraStatus.value = 0;
    if (cameraStream) {
      const tracks = cameraStream.getTracks();
      tracks.forEach((track: any) => track.stop());
    }
  };

  const handleCamera = () => {
    // 检查浏览器是否支持 getUserMedia
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices
        .getUserMedia({
          audio: false,
          video: {
            width: { min: 640, ideal: cameraSize.value[0], max: 1920 },
            height: { min: 480, ideal: cameraSize.value[1], max: 1080 },
          },
        })
        .then((stream) => {
          cameraStream = stream;
          const video = document.getElementById('camera') as HTMLVideoElement;
          video.srcObject = stream;
          cameraStatus.value = 1;
        })
        .catch((err) => {
          console.error('无法获取摄像头: ', err);
          cameraStatus.value = 2;
        });
    } else {
      console.error('getUserMedia 不被支持');
      cameraStatus.value = 3;
    }
  };
  const { isLoading, execute: useUploadImage } = uploadImage();
  const handleTakePhoto = () => {
    const canvas = document.getElementById('photo') as HTMLCanvasElement;
    const [canvasWidth, canvasHeight] = optionSize.value;
    const [cameraWidth, cameraHeight] = cameraSize.value;
    canvas.width = canvasWidth;
    canvas.height = canvasHeight;
    const ctx = canvas.getContext('2d');
    ctx?.drawImage(cameraRef.value, 0, 0, cameraWidth, cameraHeight, 0, 0, canvasWidth, canvasHeight);

    // 将图片转换为 base64 格式
    const dataUrl = canvas.toDataURL('image/jpeg');
    const postData = {
      image_data: dataUrl,
      _type: 'platform',
    };
    useUploadImage({ data: postData }).then((res) => {
      const resData: any = res.response.value;
      if (resData.status === 1) {
        imgUrl.value = resData.path;
      }
    });
  };

  function changeImg(path: string) {
    showAdd.value = false;
    // 关闭弹窗, 关闭摄像头
    cameraClose();
    emit('onChange', path);
  }
  watch(
    () => imgUrl.value,
    (val, oldVal) => {
      if (val && val !== oldVal) {
        changeImg(imgUrl.value);
      }
    }
  );

  watch(
    () => props.allowTemplate,
    (val) => {
      if (val) {
        tabs.value = '系统模板';
      } else {
        tabs.value = '自定义';
      }
    },
    { immediate: true }
  );
</script>

<style lang="less" scoped>
  .upload-modal-header-content {
    font-size: 16px;
    color: #333;

    span + span {
      font-size: 12px;
      color: rgba(#333, 0.8);
    }
  }

  .pic-box {
    margin: 30px auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pic-box ul {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
    margin-right: -15px;
  }

  .pic-box ul li {
    float: left;
    cursor: pointer;
    text-align: center;
    margin-bottom: 15px;
    margin-right: 15px;
  }

  .pic-box img {
    padding: 2px;
    margin-bottom: 5px;
    border: 1px solid #fff;
  }

  .pic-box img.selected {
    border-color: #0b78e3;
  }

  .self-wrap {
    margin-top: 40px;
  }

  .tips {
    margin-bottom: 16px;
    font-size: 12px;
    color: #999999;
  }
</style>
