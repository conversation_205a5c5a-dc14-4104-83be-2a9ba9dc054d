<template>
  <div class="quill-editor">
    <slot name="toolbar"></slot>
    <div ref="editorRef" :style="{ height: height + 'px' }" class="editor"></div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { getBaseUrl } from '@/config/url';
  import Quill from './QuillEditor';
  import defaultOptions from './defaultOptions';

  let quillEditorInstance = null;
  const props = withDefaults(
    defineProps<{
      modelValue: string;
      height?: number;
      disabled?: boolean;
      options?: Record<string, any>;
    }>(),
    {
      modelValue: '',
      height: 400,
      disabled: false,
      options: () => ({}),
    }
  );

  const emits = defineEmits(['update:modelValue', 'change', 'ready']);
  const state = reactive({
    curOptions: {},
    curContent: '',
    uploadUrl: `${getBaseUrl()}/Admin/Public/upload`,
    defaultList: [],
    imgUrl: '',
    visible: false,
    uploadList: [],
    addImgRange: [],
  });
  // 图片上传参数配置
  const uploadConfig = {
    action: `${getBaseUrl()}/Admin/Public/upload`, // 必填参数 图片上传地址
    methods: 'POST', // 必填参数 图片上传方式
    name: 'file', // 必填参数 文件的参数名
    data: {
      savePath: './Uploads/',
    }, // 可选参数 上传时附带的参数
    size: 10000, // 可选参数   图片大小，单位为Kb, 1M = 1024Kb
    accept: 'image/png, image/gif, image/jpeg, image/bmp, image/x-icon', // 可选 可上传的图片格式
  };

  // handler重写事件, 任何工具按钮的功能都可以重写，这里只重写图片上传事件
  function imageHandler() {
    let fileInput = document.querySelector('input.ql-image[type=file]');
    if (fileInput === null) {
      fileInput = document.createElement('input');
      fileInput.setAttribute('type', 'file');
      // 设置图片参数名
      if (uploadConfig.name) {
        fileInput.setAttribute('name', uploadConfig.name);
      }
      // 可设置上传图片的格式
      fileInput.setAttribute('accept', uploadConfig.accept);
      fileInput.classList.add('ql-image');
      // 监听选择文件
      fileInput.addEventListener('change', () => {
        // 如果图片限制大小
        if (uploadConfig.size && fileInput.files[0].size >= uploadConfig.size * 1024) {
          fileInput.value = '';
          Message.error('图片大小超过限制');
          return;
        }
        // 创建formData
        const formData = new FormData();
        formData.append(uploadConfig.name, fileInput.files[0]);
        if (uploadConfig.data) {
          Object.keys(uploadConfig.data).forEach((key) => {
            formData.append(key, uploadConfig.data[key]);
          });
        }
        // 图片上传
        const xhr = new XMLHttpRequest();
        xhr.open(uploadConfig.methods, uploadConfig.action, true);
        // 上传数据成功，会触发
        xhr.onload = () => {
          if (xhr.status === 200) {
            const res = JSON.parse(xhr.responseText);
            const curlSelection = quillEditorInstance.getSelection();
            quillEditorInstance.insertEmbed(
              curlSelection != null ? curlSelection.index : 0,
              'image',
              res.info,
              Quill.sources.USER
            );
          }
          fileInput.value = '';
        };
        // 开始上传数据
        xhr.upload.onloadstart = () => {
          fileInput.value = '';
          // console.log('开始上传')
        };
        // 当发生网络异常的时候会触发，如果上传数据的过程还未结束
        xhr.upload.onerror = () => {};
        // 上传数据完成（成功或者失败）时会触发
        xhr.upload.onloadend = () => {
          // console.log('上传结束')
        };
        xhr.send(formData);
      });
      document.getElementsByClassName('ql-editor')[0].appendChild(fileInput);
    }
    fileInput.click();
  }
  const editorRef = ref();

  function initialize() {
    state.curOptions = { ...defaultOptions, ...props.options };
    quillEditorInstance = new Quill(editorRef.value, state.curOptions);
    quillEditorInstance.getModule('toolbar').addHandler('image', imageHandler);
    quillEditorInstance.enable(false);

    if (props.modelValue) {
      quillEditorInstance.pasteHTML(props.modelValue);
    }
    // Disabled editor
    if (!props.disabled) {
      quillEditorInstance.enable(true);
    }
    quillEditorInstance.on('text-change', () => {
      let html = editorRef.value.children[0].innerHTML;
      const text = quillEditorInstance.getText();
      if (html === '<p><br></p>') {
        html = '';
      }
      state.curContent = html;
      emits('update:modelValue', html);
      emits('change', { html, text, quill: quillEditorInstance });
    });
    emits('ready', quillEditorInstance);
  }

  watch(
    () => props.modelValue,
    (newVal) => {
      if (quillEditorInstance) {
        if (newVal && newVal !== state.curContent) {
          state.curContent = newVal;
          quillEditorInstance.enable(false);
          quillEditorInstance.pasteHTML(newVal);
          quillEditorInstance.blur();
          quillEditorInstance.enable(true);
        } else if (!newVal) {
          quillEditorInstance.setText('');
        }
      }
    }
  );
  watch(
    () => props.disabled,
    (newVal) => {
      if (quillEditorInstance) {
        quillEditorInstance.enable(!newVal);
      }
    }
  );
  onMounted(() => {
    initialize();
  });
  onBeforeUnmount(() => {
    quillEditorInstance = null;
  });
</script>
