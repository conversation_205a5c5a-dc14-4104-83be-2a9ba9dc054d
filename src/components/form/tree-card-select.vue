<template>
  <a-tree-select
    v-model="selectedList"
    v-model:input-value="inputValue"
    class="tree-card-select"
    allow-search
    allow-clear
    tree-checkable
    label-in-value
    tree-checked-strategy="child"
    :max-tag-count="4"
    :filter-tree-node="filterTreeNode"
    :data="cardTreeDataArr"
    :tree-props="treeProps"
    placeholder="请选择"
    style="width: 100%"
    @search="handleSearch"
    @change="handleChange"
    @input-value-change="handleInputValueChange"
    @popup-visible-change="popupVisibleChange"></a-tree-select>
</template>

<script setup lang="ts">
  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array,
      default: () => [],
    },
    disabledIds: {
      type: Array,
      default: () => [],
    },
  });
  const treeProps = ref({
    virtualListProps: {
      threshold: 999,
    },
  });
  const emits = defineEmits(['update:modelValue', 'change']);
  const selectedList = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emits('update:modelValue', value);
    },
  });
  const inputValue = ref('');
  const cardTreeDataArr = ref([]);
  const disabledIdsSet = computed(() => new Set(props.disabledIds));
  function setDisabled(node: any) {
    if (Array.isArray(node)) {
      node.forEach((item) => {
        item.disabled = disabledIdsSet.value.has(item.key);
        if (Array.isArray(item.children)) {
          setDisabled(item.children);
        }
      });
    }
  }
  watch(
    () => props.data,
    (value) => {
      if (value.length) {
        setDisabled(value);
      }
      cardTreeDataArr.value = JSON.parse(JSON.stringify(value));
    },
    { immediate: true }
  );
  watch(
    () => props.disabledIds,
    () => {
      setDisabled(cardTreeDataArr.value);
    },
    { immediate: true }
  );

  function filterTreeNode(searchValue: string, nodeData: any) {
    return nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  }
  const searchWord = ref('');
  // 动态开启关闭虚拟列表，防止搜索虚拟列表后点击复选框无法选中
  function handleSearch(search: string) {
    searchWord.value = search;
    if (search) {
      treeProps.value.virtualListProps = null;
    } else {
      inputValue.value = '';
      treeProps.value.virtualListProps = {
        threshold: 500,
      };
    }
  }
  function handleChange() {
    treeProps.value.virtualListProps = {
      threshold: 500,
    };
  }
  const isVisible = ref(false);
  function handleInputValueChange(value: string) {
    if (isVisible.value && !value && searchWord.value) {
      inputValue.value = searchWord.value;
    }
  }
  function popupVisibleChange(visible: boolean) {
    isVisible.value = visible;
    if (!visible) {
      inputValue.value = '';
    }
  }
</script>

<style scoped></style>
