<template>
  <a-button class="arco-btn-link" :loading="isLoading" @click="exportPost">
    <template v-if="$slots.icon" #icon>
      <slot name="icon"></slot>
      <!-- <icon-download /> -->
    </template>
    导出
  </a-button>
</template>

<script setup lang="ts">
  /* 服务端导出组件，url为接口地址，data为请求参数（不用包含_export） */
  import { Message } from '@arco-design/web-vue';
  import request from '@/request';

  const props = defineProps({
    url: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
    },
  });
  function postToServe() {
    return request({
      url: props.url,
      options: {
        method: 'POST',
      },
      immediate: false,
    });
  }
  const { isLoading, execute } = postToServe();
  async function exportPost() {
    const { response }: { response: { value: any } } = await execute({
      data: { ...props.data, _export: 1 },
    });
    if (response.value?.errorcode === 0) {
      Message.success('导出任务运行中，请稍后到消息中心下载!');
    } else {
      Message.error(response.value?.errormsg);
    }
  }
</script>

<style scoped lang="less">
  #mainApp {
    // 导出按钮
    .arco-btn-link.arco-btn {
      color: @theme-text-color-while;
      background-color: @theme-link;
      &:hover {
        opacity: 0.85;
      }
    }
  }
</style>
