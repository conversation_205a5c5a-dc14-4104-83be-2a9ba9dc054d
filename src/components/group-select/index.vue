<template>
  <a-select v-bind="$attrs" v-model="selectedValue" allow-search>
    <a-option v-for="item in busGroupList" :key="item.level_id" :value="item.level_id">{{ item.level_name }}</a-option>
  </a-select>
</template>

<script lang="ts" setup>
  import { getLevelList } from '@/api/business';

  const props = defineProps<{
    modelValue?: string;
  }>();

  const emits = defineEmits(['update:modelValue']);
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const busGroupList = ref<any[]>([]);
  function getBusGroupList() {
    getLevelList().then((res) => {
      busGroupList.value = res.data.value.list;
    });
  }
  getBusGroupList();
</script>
