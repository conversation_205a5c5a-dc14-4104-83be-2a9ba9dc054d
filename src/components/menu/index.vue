<script lang="tsx">
  import { defineComponent, ref, h, compile, computed } from 'vue';

  import { useRoute, useRouter, RouteRecordRaw } from 'vue-router';
  import { useAppStore } from '@/store';
  import { listenerRouteChange } from '@/utils/route-listener';
  import { openWindow, regexUrl } from '@/utils';

  declare type RocketbirdRouteRecordRaw = RouteRecordRaw & { id?: string };

  export default defineComponent({
    emit: ['collapse'],
    setup() {
      const searchWords = ref('');
      const openKeys = ref<string[]>([]);
      const selectedKey = ref<string[]>([]);
      const appStore = useAppStore();
      const router = useRouter();
      const route = useRoute();

      const menuTree = computed(() => {
        let list = appStore.menuTree || [];
        if (searchWords.value) {
          list = list
            .map((item: Record<string, any>) => {
              const filteredMenuList = item.children
                ? item.children.filter((menu: Record<string, any>) => menu.name.includes(searchWords.value))
                : [];
              return { ...item, children: filteredMenuList };
            })
            .filter((item: Record<string, any>) => item.name.includes(searchWords.value) || item.children.length > 0);
        }
        return list;
      });

      const findMenuByPath = (target: string) => {
        const result: string[] = [];
        let isFind = false;
        const backtrack = (item: RocketbirdRouteRecordRaw, keys: string[]) => {
          if (item.path === target) {
            isFind = true;
            result.push(...keys);
            return;
          }
          if (item.children?.length) {
            item.children.forEach((el: RocketbirdRouteRecordRaw) => {
              backtrack(el, [...keys, el.id as string]);
            });
          }
        };
        menuTree.value?.forEach((el: RocketbirdRouteRecordRaw) => {
          if (isFind) return; // Performance optimization
          backtrack(el, [el.id as string]);
        });
        return result;
      };
      function setOpenByPath(path: string) {
        const menuOpenKeys = findMenuByPath(path);
        const keySet = new Set([...menuOpenKeys]);
        openKeys.value = [...keySet];
        selectedKey.value = [menuOpenKeys[menuOpenKeys.length - 1]];
      }
      watch(menuTree, (val) => {
        if (searchWords.value) {
          const keySet = [] as string[];
          val.forEach((item: any) => {
            if (item.children.length > 0) {
              keySet.push(...findMenuByPath(item.children[0].path));
            } else {
              keySet.push(...findMenuByPath(item.path));
            }
          });
          openKeys.value = [...keySet];
        } else {
          setOpenByPath(route.path);
        }
      });

      const collapsed = computed({
        get() {
          if (appStore.device === 'desktop') return appStore.menuCollapse;
          return false;
        },
        set(value: boolean) {
          appStore.updateSettings({ menuCollapse: value });
        },
      });

      const goto = (item: Record<string, any>) => {
        // 完整外链
        if (regexUrl.test(item.path)) {
          openWindow(item.path);
          selectedKey.value = [item.path as string];
          return;
        }
        if (route.path === item.path) {
          selectedKey.value = [item.path as string];
          return;
        }
        router.push({
          path: item.path,
        });
      };

      listenerRouteChange(async (newRoute) => {
        if (appStore.serverMenu.length === 0) {
          await appStore.setServeMenu();
        }
        const activeMenu = newRoute.path as string;
        if (activeMenu) {
          setOpenByPath(activeMenu);
        }
      }, true);
      const setCollapse = (val: boolean) => {
        if (appStore.device === 'desktop') appStore.updateSettings({ menuCollapse: val });
      };

      const renderSubMenu = () => {
        function travel(_route: Record<string, any>[], nodes = []) {
          if (_route) {
            _route.forEach((element) => {
              const icon = element.icon
                ? () => h(compile(`<my-icon icon="${element.icon}" style="font-size:18px" />`))
                : null;
              const node =
                element.children && element.children.length !== 0 ? (
                  <a-sub-menu
                    key={element.id}
                    v-slots={{
                      icon,
                      title: () => h(compile(element.name || '')),
                    }}>
                    {travel(element.children)}
                  </a-sub-menu>
                ) : (
                  <a
                    href={element.path}
                    onClick={(e) => {
                      e.preventDefault();
                      return false;
                    }}>
                    <a-menu-item key={element.id} v-slots={{ icon }} onClick={() => goto(element)}>
                      {element.name || ''}
                    </a-menu-item>
                  </a>
                );
              nodes.push(node as never);
            });
          }
          return nodes;
        }
        return travel(menuTree.value as any);
      };

      return () => (
        <a-menu
          mode="vertical"
          v-model:collapsed={collapsed.value}
          v-model:open-keys={openKeys.value}
          show-collapse-button={appStore.device !== 'mobile'}
          auto-open={false}
          accordion={true}
          selected-keys={selectedKey.value}
          auto-open-selected={true}
          level-indent={34}
          popup-max-height={false}
          style="height: 100%;width:100%;font-size:15px"
          onCollapse={setCollapse}>
          {!collapsed.value ? (
            <a-input-search
              v-model={searchWords.value}
              style="width: 100%; border-radius: 16px; margin-bottom: 8px;"
              placeholder="菜单名称"
              allow-clear
            />
          ) : (
            ''
          )}
          {renderSubMenu()}
        </a-menu>
      );
    },
  });
</script>

<style lang="less">
  .arco-trigger-menu-inner {
    a,
    a:link,
    a:visited,
    a:focus,
    a:hover,
    a:active {
      color: inherit;
      text-decoration: none;
      cursor: inherit;
    }
  }
</style>

<style lang="less" scoped>
  :deep(.arco-menu-inner) {
    overflow-y: scroll;
    .arco-menu-inline-header {
      display: flex;
      align-items: center;
    }
    .arco-icon {
      &:not(.arco-icon-down) {
        font-size: 18px;
      }
    }
  }

  :deep(.arco-menu-collapse-button) {
    bottom: -20px;
    color: var(--color-text-2);
  }
</style>
