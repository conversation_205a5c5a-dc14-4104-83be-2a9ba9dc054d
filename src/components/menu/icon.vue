<template>
  <icon-font :type="icon" :size="size" />
</template>

<script lang="ts" setup>
  import { Icon } from '@arco-design/web-vue';

  // icon引入
  // eslint-disable-next-line prettier/prettier
  const IconFont = Icon.addFromIconFontCn({ src: 'https://imagecdn.rocketbird.cn/mainsite-fe/brand/font/iconfont.js?t=1700555840010' });
  // const IconFont = Icon.addFromIconFontCn({ src: 'https://at.alicdn.com/t/c/font_4248787_7nqtqiobzjo.js' });

  const props = defineProps({
    icon: String,
    size: {
      type: Number,
      default: 16,
    },
  });
</script>
