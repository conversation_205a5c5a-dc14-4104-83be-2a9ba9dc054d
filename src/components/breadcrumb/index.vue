<template>
  <div class="container-breadcrumb">
    <a-space>
      <slot>
        <a-breadcrumb>
          <a-breadcrumb-item>
            <icon-list />
          </a-breadcrumb-item>
          <a-breadcrumb-item v-for="(item, index) in breadcrumbRoutes" :key="index">
            <a-link v-if="item.path && index !== breadcrumbRoutes.length - 1 && index !== 0" :href="item.path">
              {{ item.name }}
            </a-link>
            <template v-else>{{ item.name }}</template>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </slot>
    </a-space>
    <a-space>
      <slot name="center"></slot>
    </a-space>
    <a-space>
      <slot name="right"></slot>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
  import { PropType } from 'vue';

  type itemsType = Array<{ name: string | undefined; path: string }>;

  const { IS_BRAND_SITE } = window;
  const props = defineProps({
    items: {
      type: Array as PropType<itemsType>,
      default() {
        return [];
      },
    },
  });

  const route = useRoute();
  const router = useRouter();

  function findRouteByName(name: string) {
    const allRoutes = router.getRoutes();
    return allRoutes.find((v) => v.name === name) || null;
  }
  function getBreadcrumbs() {
    let items = [];
    const currentRoute = route;
    // 递归查找父级路由
    function findAncestors(item) {
      let parentRoute = null;
      if (item.meta && item.meta.parentName) {
        parentRoute = findRouteByName(item.meta.parentName);
        if (parentRoute) {
          items.unshift(parentRoute);
          if (parentRoute.meta.parentName === item.meta.parentName) return;
          findAncestors(parentRoute);
        }
      }
    }
    if (currentRoute.meta && currentRoute.meta.parentName) {
      findAncestors(currentRoute);
      items.push(currentRoute);
    } else {
      items = currentRoute.matched;
    }

    return items
      .filter((v) => v.meta?.locale)
      .map((v) => ({
        name: v.meta.locale,
        path: v.meta?.needLink !== false ? (!IS_BRAND_SITE && v.meta.v2path ? v.meta.v2path : v.path) : '',
      }));
  }
  const breadcrumbRoutes = computed(() => {
    return props.items.length ? props.items.map((v) => ({ locale: v, name: v })) : getBreadcrumbs();
  }) as ComputedRef<itemsType>;
</script>

<style scoped lang="less">
  .container-breadcrumb {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 57px;
    // margin: 16px 0;
    :deep(.arco-breadcrumb-item) {
      color: rgb(var(--gray-6));
      &:last-child {
        color: rgb(var(--gray-8));
      }
    }
  }
</style>
