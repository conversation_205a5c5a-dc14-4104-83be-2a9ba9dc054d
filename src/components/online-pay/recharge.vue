<template>
  <a-modal
    v-model:visible="isShowModal"
    :title="type === 1 ? '短信购买' : '电子合同充值'"
    width="660px"
    :footer="false"
    :mask-closable="false"
    @before-close="beforeCloseModel">
    <a-row justify="space-between" style="min-height: 134px">
      <template v-for="(item, index) in bags" :key="`bags_${item.id}`">
        <a-col
          :span="(24 - bags.length) / bags.length"
          :class="{ bags: true, active: active === index }"
          @click="checkGrade(index)">
          <div class="price">￥{{ item.amount }}</div>
          <div v-if="type === 1" class="detail">{{ item.number }}条短信包</div>
          <div v-else class="detail">{{ item.number }}份合同券</div>
        </a-col>
      </template>
    </a-row>
    <a-row justify="center">
      <a-col :span="24" class="code">
        <a-spin :loading="loading" tip="加载中..." style="min-width: 200px; min-height: 200px">
          <a-skeleton :animation="true" :loading="loading">
            <a-skeleton-shape style="width: 200px; height: 200px" />
          </a-skeleton>
          <img v-if="!loading" :src="qrCode" alt="微信支付二维码" />
        </a-spin>
        <div class="tips">
          <h2 class="money">{{ money }}元</h2>
          <p style="padding-bottom: 5px">请用微信扫描二维码支付</p>
          <icon-wechat size="18" :style="{ color: '#62b900' }" />
        </div>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, watch, computed } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { getSmsPackage, getQrCode, getSmsPayStatus } from '@/api/online-pay';

  const props = defineProps<{
    modelValue?: boolean;
    type: number;
  }>();

  const emits = defineEmits(['update:modelValue', 'paySuccess']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  // 定时器变量
  const timer = ref<any>(null);
  const { execute: executePayStatus } = getSmsPayStatus();
  /**
   * 检查二维码是否完成付款
   * @param order_sn 二维码对应的订单编码
   */
  const checkPayStatus = async (order_sn: string) => {
    if (timer.value) clearTimeout(timer.value);
    timer.value = null;
    try {
      const { response }: { response: { value: any } } = await executePayStatus({
        data: { order_sn },
      });
      if (response.value?.errorcode === 0) {
        Message.error(response.value?.errormsg);
        emits('paySuccess', { bool: true });
      }
    } catch (err: any) {
      if (err?.errorcode === 47031) {
        timer.value = setTimeout(() => {
          checkPayStatus(order_sn);
        }, 2000);
      }
    }
  };

  const { execute: executeQrCode } = getQrCode();
  const qrCode = ref<string>('');
  const money = ref<string>('0');
  const loading = ref<boolean>(true);
  /**
   * 获取二维码
   * @param id id
   * @param amount 金额
   */
  const getQr = async (id: string, amount: string) => {
    money.value = amount;
    try {
      const { data } = await executeQrCode({
        data: { sms_package_id: id, type: props.type },
      });
      qrCode.value = data.value.image_string;
      loading.value = false;
      checkPayStatus(data.value.order_sn);
    } catch (err: any) {
      console.log(err);
      if (timer.value) clearTimeout(timer.value);
      timer.value = null;
    }
  };

  const active = ref<number>(0);
  interface bagsType {
    id: string;
    type: string;
    amount: string;
    number: string;
  }
  const bags = ref<bagsType[]>([]);
  const { execute: executeSmsPackage } = getSmsPackage();
  /**
   * 获取对应的数据
   * @param type 1短信, 2电子合同
   */
  const getData = async (type: number) => {
    const { data } = await executeSmsPackage({
      data: { type },
    });
    bags.value = data.value;
    active.value = 0;
    getQr(data.value[0].id, data.value[0].amount);
  };

  watch(
    () => [props.modelValue],
    (newValue, oldValue) => {
      if (newValue !== oldValue && newValue[0]) {
        loading.value = true;
        getData(props.type);
      }
    }
  );

  // 关闭弹窗时候清除定时器
  const beforeCloseModel = () => {
    if (timer.value) {
      clearTimeout(timer.value);
      timer.value = null;
    }
  };

  /**
   * 切换充值项
   * @param num 选择数据的下标
   */
  function checkGrade(num: number) {
    if (loading.value) return;
    loading.value = true;
    active.value = num;
    getQr(bags.value[num].id, bags.value[num].amount);
  }
</script>

<style scoped lang="less">
  .bags {
    cursor: pointer;
    user-select: none;
    border: 1px solid #e5e5e5;
    .price,
    .detail {
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .price {
      height: 84px;
      font-size: 32px;
      color: #434343;
    }
    .detail {
      height: 48px;
      color: #313131;
      font-size: 16px;
      background-color: #eee;
    }
  }

  .active {
    border-color: #ff6e1e;
    .price {
      color: #ff6e1e;
    }
  }

  .code {
    display: flex;
    justify-content: center;
    margin-top: 50px;
    img {
      width: 200px;
      height: 200px;
    }
    .tips {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      padding-left: 30px;
      text-align: center;
      > h2 {
        font-size: 26px;
        font-weight: normal;
        line-height: 50px;
      }
    }
  }
</style>
@/api/online-pay
