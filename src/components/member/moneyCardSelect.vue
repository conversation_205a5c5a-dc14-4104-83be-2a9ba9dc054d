<template>
  <a-select
    v-if="list.length"
    v-model="selected"
    placeholder="请选择储值卡"
    :disabled="disabled"
    :trigger-props="{ autoFitPopupMinWidth: true }"
    allow-search>
    <a-option v-for="item in list" :key="item.card_user_id" :value="item.card_user_id">
      {{ item.card_name }} （余额: {{ item.amount }} 元）
    </a-option>
  </a-select>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';
  import { getUserDebitCard } from '@/api/commodity';

  const props = defineProps<{
    modelValue?: string;
    disabled?: boolean;
    userId?: string;
    busId?: string;
  }>();
  const emit = defineEmits(['update:modelValue', 'userNoCard']);
  const list = ref<Record<string, any>[]>([]);
  const selected = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value);
    },
  });
  const route = useRoute();
  function getList() {
    const user_id = props.userId || route.params.userId || route.query.userId;
    if (!user_id) {
      emit('userNoCard');
      Message.error('无可用会员ID');
    }
    if (user_id === 'self_id') {
      // 散客购票，不调用接口
      emit('userNoCard');
      Message.error('无可用会员ID');
    }
    getUserDebitCard({ user_id, type: 1, is_disabled: props.disabled ? 1 : 0, bus_id: props.busId || '' })
      .then((res) => {
        list.value = res.data.value.list || [];
        if (!list.value.length) {
          if (user_id === -1 && selected.value) {
            // 当批量购票时使用了储值卡。所以虚拟会员的购票记录会有储值卡数据需要显示，并且不提示错误
            return;
          }
          emit('userNoCard');
          Message.error('该用户无储值卡');
        }
      })
      .catch((error) => {
        list.value = [];
        emit('userNoCard');
        Message.error(error.message);
      });
  }
  onMounted(() => {
    getList();
  });
</script>

<style scoped></style>
