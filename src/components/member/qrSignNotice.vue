<template>
  <div>
    <!-- <a-modal
      v-model="showModal"
      title="自主签到提醒"
      width="800"
      class="sign_modal"
      :mask-closable="false"
      @on-cancel="commit"
    >
      <div class="voice-wrap">
        <span>语音提示</span>
        <i-switch v-model="isPlayVoice" size="small" @on-change="voiceStatusChange"></i-switch>
      </div>
      <div v-if="commitItem.user_id" from="2" style="min-height: 100px">
        <RemindMessage :user-id="commitItem.user_id"></RemindMessage>
        <UserInfo :data="noticeUserInfo"></UserInfo>
      </div>
      <div class="newsign-lang">
        <div class="newsign-lang-t"> 会员卡信息 </div>
        <div class="newsign-lang-b">
          <table
            class="table"
            style="border-collapse: collapse"
            borderColor="#eeeeee"
            cellSpacing="0"
            align="center"
            border="1"
          >
            <thead>
              <tr>
                <th>卡种</th>
                <th width="80">卡号</th>
                <th>天数</th>
                <th>有效期</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="data in cardData" :key="data.card_id">
                <td>{{ data.cardname }}</td>
                <td width="80">{{ data.card_sn }}</td>
                <td>
                  总计<span v-if="data.charge_type == 1 && data.card_type_id != 3"
                    ><span class="red">{{ data.all_num }}</span
                    >次</span
                  >
                  <span v-if="data.card_type_id == 3"
                    ><span class="red">{{ data.all_num }}</span
                    >元</span
                  >
                  <span v-if="data.charge_type == 2"
                    ><span class="red">{{ data.all_days }}</span
                    >天</span
                  >, 剩余
                  <span class="red">{{ data.overplus }}</span>
                </td>
                <td>{{ data.validity }}</td>
                <td :class="{ green: data.status != '正常' }">{{ data.status }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <brandAdd v-if="noticeUserInfo" v-model:number="commitItem.brand_number" @on-enter="commit"></brandAdd>
      <Form v-if="allowPrint">
        <FormItem label="打印小票">
          <i-switch v-model="is_print_ticket" size="small" true-value="1" false-value="0" />
        </FormItem>
      </Form>
      <div v-if="noticeUserInfo" slot="footer" class="modal-buttons" style="padding-bottom: 20px; padding-top: 10px">
        <Button style="margin: 0 30px" type="success" @click="commit">确认，下一条</Button>
        <Button @click="cancel">撤销签到</Button>
      </div>
      <div v-else>
        <slot name="footer"></slot>
      </div>
    </a-modal> -->
    <!-- 音频播放 -->
    <!-- <audio id="audio" controls="controls" style="display: none">
      <source src="static/media/welcome.mp3" type="audio/mpeg" />
    </audio> -->
  </div>
</template>

<script setup lang="ts">
  import { onBeforeUnmount, reactive, watch } from 'vue';
  import { Notification } from '@arco-design/web-vue';
  import useMsgSocket from '@/hooks/useMsgSocket';
  import { websocketStore, useBusInfoStore } from '@/store';
  import { getCardUserList, getUserMsg } from '@/api/member';
  import { getUnreadCount } from '@/api/msg';
  // import brandAdd from 'src/views/signin/components/brandAdd.vue';
  // import UserInfo from 'src/views/signin/components/userInfo.vue';
  // import RemindMessage from 'components/user/remindMessage.vue';
  // import { EventBus } from 'components/EventBus.js';

  const socketState = websocketStore();
  const busInfo = useBusInfoStore();

  // if (window.IS_BRAND_SITE) {
  const ws = useMsgSocket();
  ws.open();
  // }

  const state = reactive({
    maxTime: '0',
    index: '',
    msg: '有新签到消息提醒',
    noticeUserInfo: null,
    allowPrint: false,
    is_print_ticket: '1',
    cardData: [],
    isEntering: false,
    isFlash: false,
    oTitle: '',
    isPlayVoice: localStorage.getItem('isPlayVoice') !== 'false',
    commitItem: {
      user_id: '',
      symbol: '',
      sign_log_id: '',
      brand_number: [],
    },
  });

  const showModal = computed({
    get() {
      return socketState.modalShow;
    },
    set(value) {
      this.$store.commit('websocket/SET_SOCKET_MODAL_SHOW', value);
    },
  });

  // function websocketonmessage(e) {
  //   // {"user_id":"8289","create_time":1524730365,"sign_log_id":"14279","type":"sign","action":"push_info","symbol":"01|34"}
  //   // {"admin_id":"1","create_time":1524730191,"msg_count":null,"type":"","action":"push_info","symbol":"01|34"}
  //   const resdata = JSON.parse(e.data);
  //   if (
  //     resdata.action === 'push_info' &&
  //     resdata.type === 'sign' &&
  //     localStorage.getItem(`noticeStatus${busInfo.bus_id}`) !== 'false' &&
  //     resdata.bus_id == busInfo.bus_id
  //   ) {
  //     const noticeType = socketState.noticeType;
  //     this.$store.commit('websocket/SET_SOCKET_NOTICE_ARRAY', socketState.noticeArray.concat(resdata));
  //     this.handleFlash();
  //     if (localStorage.getItem('isPlayVoice') !== 'false') {
  //       this.playVoice();
  //     }
  //     if (noticeType === 0) {
  //       noticeModalShow(resdata);
  //     } else if (noticeType === 1) {
  //       Notification.remove('socketNotice');
  //       this.$Notice.config({
  //         top: 130,
  //       });
  //       Notification.info({
  //         title: '',
  //         id: 'socketNotice',
  //         content: (h, params) => {
  //           return (
  //             <div
  //               style="cursor: pointer;"
  //               on-click={(name) => {
  //                 noticeModalShow(resdata);
  //               }}
  //             >
  //               <h3>新签到提醒通知</h3>
  //               <p>有{socketState.noticeArray.length}条新签到通知待确认。</p>
  //             </div>
  //           );
  //         },
  //         onClose: () => {
  //           this.$store.commit('websocket/SET_SOCKET_NOTICE_ARRAY', []);
  //         },
  //         duration: 0,
  //       });
  //     }
  //   } else if (resdata.action == 'push_info' && resdata.type === 'message') {
  //     localStorage.setItem('msgCount', +resdata.msg_count);
  //     this.$store.commit('websocket/SET_SOCKET_MSG_COUNT', +resdata.msg_count);
  //     EventBus.$emit('newSocketMsgIn', resdata);
  //   } else if (resdata.action == 'clean_sign') {
  //     this.noticeOut(resdata.sign_log_id);
  //     // EventBus.$emit('outSocketNotice', resdata.sign_log_id)
  //   }
  // }

  function getCount() {
    return getUnreadCount({ ...state.postData }).then(({ data }) => {
      const count = Number(data.value?.count) || 0;
      localStorage.setItem('msgCount', count);
      socketState.SET_SOCKET_MSG_COUNT(count);
    });
  }

  // // 音频播放按钮
  function playVoice() {
    const audio = document.getElementById('audio');
    audio.play();
  }
  function noticeModalShow(resdata) {
    Notification.remove('socketNotice');
    socketState.SET_SOCKET_MODAL_SHOW(true);
  }
  function destroyEvent() {
    state.noticeUserInfo = null;
  }
  // 用户信息
  function getUser() {
    getUserMsg({
      user_id: this.commitItem.user_id,
      sign_log_id: this.commitItem.sign_log_id,
      sign_log_ids: this.commitItem.sign_log_ids,
    }).then(({ data }) => {
      state.noticeUserInfo = data.value;
      state.is_print_ticket = state.noticeUserInfo.is_print_ticket;

      const ids = state.noticeUserInfo.card_type_ids;
      if (Array.isArray(ids) && ids.length > 0) {
        ids.forEach((item) => {
          if (+item === 2 || +item === 3) {
            state.allowPrint = true;
          }
        });
      } else {
        state.allowPrint = false;
      }
    });
  }
  // 用户会员卡列表
  function getCardUser() {
    getCardUserList({ user_id: this.commitItem.user_id }).then(({ data }) => {
      this.cardData = data.value?.list;
    });
  }

  function voiceStatusChange(val) {
    localStorage.setItem('isPlayVoice', val);
  }

  // // 签到数据提交
  // function commit() {
  //   if (this.isEntering) {
  //     return false;
  //   }
  //   this.isEntering = true;
  //   let commitUrl = 'Web/Sign/sign_confirm_web';
  //   let postData = {
  //     sign_log_id: this.commitItem.sign_log_id,
  //     symbol: this.commitItem.symbol,
  //     is_print_ticket: this.is_print_ticket,
  //   };
  //   if (this.commitItem.brand_number.length > 0 && this.commitItem.brand_number[0] != '') {
  //     commitUrl = '/Web/Sign/add_brand_number';
  //     postData = this.commitItem;
  //   }

  //   let goAhead = false;
  //   let signinIds = '';
  //   if (this.is_print_ticket == 1 && this.allowPrint) {
  //     goAhead = true;
  //     signinIds = this.commitItem.sign_log_ids;
  //   }

  //   this.$service
  //     .post(commitUrl, postData)
  //     .then((res) => {
  //       if (res.data.errorcode == 0) {
  //         this.$Message.success(res.data.errormsg);
  //         this.next();

  //         if (goAhead) {
  //           if (Array.isArray(signinIds) && signinIds.length > 0) {
  //             // signinIds.forEach(signinId => {
  //             //   const routeUrl = this.$router.resolve({path: '/signinPrint', query: {signinId}});
  //             //   window.open(routeUrl.href, '_blank');
  //             // });
  //             signinIds = signinIds.join(',');
  //             const routeUrl = this.$router.resolve({ path: '/signinPrint', query: { signinIds } });
  //             window.open(routeUrl.href, '_blank');
  //           }
  //         }
  //       } else {
  //         this.$Message.error(res.data.errormsg);
  //       }
  //       this.isEntering = false;
  //     })
  //     .catch((err) => {
  //       this.isEntering = false;
  //     });
  // }
  // // 当其它客户端或页面已经确认或取消了某条签到时
  // function noticeOut(signLogId) {
  //   // 在队列中清除
  //   for (let index = 0; index < socketState.noticeArray.length; index++) {
  //     if (socketState.noticeArray[index].sign_log_id == signLogId) {
  //       socketState.noticeArray.splice(index, 1);
  //       socketState.SET_SOCKET_NOTICE_ARRAY(socketState.noticeArray)
  //       break;
  //     }
  //   }
  //   // 如果已经展示在页面弹窗上
  //   if (this.commitItem.sign_log_id == signLogId) {
  //     this.next();
  //   }
  // }
  // // 下一条
  // function next() {
  //   socketState.noticeArray.splice(0, 1);
  //   socketState.SET_SOCKET_NOTICE_ARRAY(socketState.noticeArray)
  //   this.noticeUserInfo = null;
  //   this.commitItem = {
  //     user_id: '',
  //     sign_log_id: '',
  //     symbol: '',
  //     brand_number: [],
  //   };
  //   if (socketState.noticeArray.length !== 0) {
  //     state.commitItem.user_id = socketState.noticeArray[0].user_id;
  //     state.commitItem.sign_log_id = socketState.noticeArray[0].sign_log_id;
  //     state.commitItem.sign_log_ids = socketState.noticeArray[0].sign_log_ids;
  //     state.commitItem.symbol = socketState.noticeArray[0].symbol;
  //   }
  // }
  // // 撤销签到
  // function cancel() {
  //   this.$service
  //     .post('/Web/Sign/cancel_user_sign', {
  //       sign_log_id: socketState.noticeArray[0].sign_log_id,
  //       symbol: socketState.noticeArray[0].symbol,
  //     })
  //     .then((res) => {
  //       if (res.data.errorcode == 0) {
  //         this.$Message.success('取消成功');
  //         this.next();
  //       } else {
  //         this.$Message.error(res.data.errormsg);
  //       }
  //     });
  // }

  function clear() {
    clearInterval(state.timer);
    state.timer = null;
    if (state.isFlash) {
      // 如果正在闪
      document.title = state.oTitle; // 将title复原
    }
    state.isFlash = false;
  }

  // title闪烁
  function flash(msg) {
    state.index = !state.index ? 1 : 0;
    document.title = `【${state.message[this.index]}】`;
  }
  function handleFlash(msg) {
    if (this.isFlash) {
      clear(); // 先停止
    } else {
      this.oTitle = document.title; // 保存原来的title
    }
    this.isFlash = true;
    if (typeof msg === 'undefined') {
      msg = this.msg;
    }
    this.message = [msg, this.getSpace(msg)];
    this.timer = setInterval(() => {
      flash(msg);
    }, 500);
  }

  function getSpace(msg) {
    let n = msg.length;
    let s = '';
    const num = msg.match(/\w/g);
    const n2 = num != null ? num.length : 0; // 半角字符的个数
    n -= n2; // 全角字符个数
    const t = parseInt(n2 / 2, 10);
    if (t > 0) {
      // 两个半角替换为一个全角
      n += t;
    }
    s = n2 % 2 ? ' ' : ''; // 如果半角字符个数为奇数
    while (n > 0) {
      // eslint-disable-next-line no-use-before-define
      s += '　'; // 全角空格
      n -= 1;
    }
    return s;
  }

  watch(
    () => showModal.value,
    (val) => {
      if (val) {
        state.commitItem.user_id = socketState.noticeArray[0].user_id;
        state.commitItem.sign_log_id = socketState.noticeArray[0].sign_log_id;
        state.commitItem.sign_log_ids = socketState.noticeArray[0].sign_log_ids;
        state.commitItem.symbol = socketState.noticeArray[0].symbol;
        getUser();
        getCardUser();
      } else {
        // EventBus.$emit('qrSignAllRead');
      }
      clear();
    }
  );

  watch(
    () => socketState.websocket,
    (val, oldVal) => {
      if (val && val !== oldVal) {
        // this.socketState.websocket.onmessage = websocketonmessage;
      }
    }
  );

  watch(
    () => socketState.noticeArray,
    (val) => {
      if (val && val.length === 0) {
        Notification.remove('socketNotice');
        showModal.value = false;
        clear();
      }
    }
  );

  watch(
    () => state.commitItem.sign_log_id,
    (val, oldVal) => {
      if (val && val !== oldVal && showModal.value) {
        getUser();
        getCardUser();
      }
    }
  );

  // created
  getCount();
  // // EventBus.$on('outSocketNotice', id => {
  // //   this.noticeOut(id)
  // // })
  // window.addEventListener('storage', (event) => {
  //   console.log(event);
  //   if (event.key == 'msgCount') {
  //     socketState.SET_SOCKET_MSG_COUNT(localStorage.getItem('msgCount'))
  //   }
  // });

  onBeforeUnmount(() => {
    destroyEvent();
  });
</script>

<style lang="less" scoped>
  .newsign-lang {
    overflow: hidden;
    clear: both;
    /*padding: 0 30px;*/
    background: #fff;
    border: 1px solid #eee;
    border-top: none;
    background: #fff;
    margin-bottom: 40px;
    /*min-height: 100px;*/
  }
  .newsign-lang-b {
    table {
      width: 100%;
      margin: 0 auto;
      font-size: 14px;
      color: #313131;
      td,
      th {
        height: 30px;
        font-weight: normal;
        text-align: center;
        word-break: break-all;
        .red {
          color: #e60012;
        }
        .green {
          color: #5fb75d;
        }
      }
    }
  }
  .newsign-lang-t {
    width: 100%;
    background: #fff;
    height: 40px;
    line-height: 40px;
    overflow: hidden;
    border: 1px solid #eee;
    text-align: center;
    border-left: none;
    border-right: none;
    font-size: 14px;
    font-weight: bold;
  }
  .voice-wrap {
    position: absolute;
    top: 15px;
    left: 135px;
    span {
      vertical-align: middle;
    }
  }
  .ivu-switch-checked {
    border-color: #5fb75d;
    background-color: #5fb75d;
  }
</style>
