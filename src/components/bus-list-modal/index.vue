<template>
  <a-modal v-model:visible="isShowModal" title="场馆" :footer="false" :width="720" @before-close="popupVisibleChange">
    <a-table row-key="index" :data="tableData" :pagination="pagination" @page-change="onPageChange">
      <template #columns>
        <a-table-column title="序号" data-index="avatar">
          <template #cell="{ rowIndex }">
            {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
          </template>
        </a-table-column>
        <a-table-column title="场馆名称" :data-index="nameKey" />
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  /* 用于展示场馆列表 用于表格中有多个场馆需要弹窗展示的情况 */
  import { computed } from 'vue';
  import { Pagination } from '@/types/global';

  const props = defineProps<{
    modelValue?: boolean;
    tableData: any[];
    nameKey: string;
  }>();

  const emits = defineEmits(['update:modelValue']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const pagination = reactive<Pagination>({
    current: 1,
    pageSize: 10,
  });
  const onPageChange = (current: number) => {
    pagination.current = current;
  };
  function popupVisibleChange() {
    pagination.current = 1;
  }
</script>
