<template>
  <a-modal v-model:visible="isShowModal" :title="title" :width="720" @before-close="popupVisibleChange">
    <a-table
      :data="tableData"
      :scroll="{ y: 400 }"
      :pagination="pagination"
      v-bind="$attrs"
      @page-change="onPageChange">
      <template #columns>
        <slot>
          <!-- <a-table-column title="序号" data-index="avatar">
            <template #cell="{ rowIndex }">
              {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
            </template>
          </a-table-column>
          <a-table-column title="场馆名称" :data-index="nameKey" /> -->
        </slot>
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { Pagination } from '@/types/global';

  const props = defineProps<{
    title?: string;
    modelValue?: boolean;
    tableData?: any[];
  }>();

  const emits = defineEmits(['update:modelValue']);
  const isShowModal = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits('update:modelValue', value);
    },
  });

  const pagination = reactive<Pagination>({
    current: 1,
    pageSize: 10,
  });
  const onPageChange = (current: number) => {
    pagination.current = current;
  };
  function popupVisibleChange() {
    pagination.current = 1;
  }
</script>
