<template>
  <a-select
    v-model="saleId"
    :placeholder="placeholder"
    allow-clear
    transfer
    allow-search
    :disabled="disabled"
    :multiple="multiple"
    :max-tag-count="maxTagCount"
    :loading="isLoading && !state.showSelect"
    @change="saleChanged"
    @popup-visible-change="popupVisibleChange">
    <slot></slot>
    <!-- :value-key="labelInValue ? 'marketers_id' : 'value'" -->
    <!-- :value="labelInValue ? item : item.marketers_id" -->
    <a-option v-for="item in state.salesList" :key="item.marketers_id" :value="item.marketers_id">
      <span>{{ item.sale_name }}</span>
      <span v-if="item.isCoach && showCoachType" style="float: right; color: #ccc">
        {{ item.isSwimCoach ? '泳教' : '私教' }}
      </span>
    </a-option>
  </a-select>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import { Message } from '@arco-design/web-vue';

  import { getCoachList, getPtCoachList, getMembershipList } from '@/api/member';

  const props = defineProps({
    modelValue: {
      type: [String, Number, Array],
    },
    placeholder: {
      type: String,
      default: '',
    },
    showCoachType: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    labelInValue: {
      type: Boolean,
      default: false,
    },
    isCoach: {
      type: Boolean,
      default: false,
    },
    isPtCoach: {
      type: Boolean,
      default: false,
    },
    isSwimCoach: {
      type: Boolean,
      default: false,
    },
    isMembership: {
      type: Boolean,
      default: true,
    },
    // 会员的归属场馆
    belongBusId: {
      type: [String, Number],
      default: '',
    },
    maxTagCount: {
      type: Number,
      default: Infinity,
    },
  });

  const emits = defineEmits(['update:modelValue', 'onChange', 'saleName']);

  // globalBelongBusId 归属场馆id
  const state = reactive<any>({
    salesList: [],
    coachList: [], // 教练列表
    ptCoachList: [], // 私教教练列表package/getPackageList
    swimCoachList: [], // 泳教教练列表
    membershipList: [], // 会籍列表
    showSelect: false,
    // curBelongBusId: '',
  });

  const saleId = computed({
    get() {
      return props.modelValue;
    },
    set() {
      // emits('update:modelValue', val);
    },
  });

  // 获取教练列表
  const { isLoading: isCoachLoading, execute: executeCoach } = getCoachList();
  async function getCoach(belongBusId: any) {
    const { data } = await executeCoach({
      params: { belong_bus_id: belongBusId },
    });

    const list = data.value || [];
    state.coachList = list;
    return Promise.resolve(list);
  }

  // 获取私教泳教教练列表
  const { isLoading: isPtCoachLoading, execute: executePtCoach } = getPtCoachList();
  async function getPtCoach(belongBusId: any) {
    const { data } = await executePtCoach({
      params: { belong_bus_id: belongBusId },
    });
    const list = data.value || [];
    const ptList = data.value.list.filter((item: any) => +item.is_swim !== 1);
    const swimList = data.value.list.filter((item: any) => +item.is_swim === 1);

    state.coachList = list;
    state.ptCoachList = ptList;
    state.swimCoachList = swimList;
    return Promise.resolve(list);
  }

  // 获取会籍列表
  const { isLoading: isMsLoading, execute: executeMembership } = getMembershipList();
  async function getMembership(belongBusId: any) {
    const { data } = await executeMembership({
      params: { belong_bus_id: belongBusId },
    });
    const list = data.value || [];
    state.membershipList = list;
    return Promise.resolve(list);
  }

  const isLoading = computed(() => isCoachLoading.value || isPtCoachLoading.value || isMsLoading.value);

  function coachSet(list: any) {
    const curCoachList = list.map((coach: any) => {
      return {
        marketers_id: props.isMembership ? `c${coach.coach_id}` : coach.coach_id,
        sale_name: coach.coach_name,
        isCoach: true,
        isSwimCoach: +coach.is_swim === 1,
      };
    });
    state.salesList = state.salesList.concat(curCoachList);
    nextTick(() => {
      state.showSelect = true;
    });
  }
  function shouldUpdateList(list: any) {
    return !(
      // props.belongBusId === this.globalBelongBusId &&
      (Array.isArray(list) && list.length !== 0)
    );
  }

  async function initData() {
    const busId = props.belongBusId;
    state.salesList = [];

    if (props.isMembership) {
      if (shouldUpdateList(state.membershipList)) {
        await getMembership(busId);
      }
      state.salesList = state.membershipList.concat(state.salesList);
      nextTick(() => {
        if (!props.isCoach && !props.isPtCoach && !props.isSwimCoach) {
          state.showSelect = true;
        }
      });
    }

    if (props.isCoach) {
      if (shouldUpdateList(state.coachList)) {
        await getCoach(busId);
      }
    }

    // 教练包括操课 泳教  私教
    if (props.isCoach && !props.isPtCoach && !props.isSwimCoach) {
      coachSet(state.coachList);
    } else if (props.isPtCoach || props.isSwimCoach) {
      if (
        (props.isPtCoach && shouldUpdateList(state.ptCoachList)) ||
        (props.isSwimCoach && shouldUpdateList(state.swimCoachList))
      ) {
        await getPtCoach(busId);
      }
      if (props.isPtCoach) {
        coachSet(state.ptCoachList);
      }
      if (props.isSwimCoach) {
        coachSet(state.swimCoachList);
      }
    }
    // this.$store.commit('SET_GLOBAL_BELONG_BUS_ID', busId);
  }

  const saleChanged = (data: any) => {
    console.log(data);
    if (Array.isArray(data)) {
      emits('update:modelValue', data);
    } else {
      const item = state.salesList.find((v: any) => v.marketers_id === data);
      if (item) {
        emits('update:modelValue', item.marketers_id);
        emits('saleName', item.sale_name);
        emits('onChange', props.labelInValue ? { value: item.marketers_id, label: item.sale_name } : '');
      } else {
        emits('update:modelValue', '');
        emits('saleName', '');
        emits('onChange', props.labelInValue ? { value: '', label: '' } : '');
      }
    }
  };

  const needRefresh = ref(false);
  function popupVisibleChange(visible: boolean) {
    if (visible && needRefresh.value) {
      initData();
    }
  }

  watch(
    () => [props.belongBusId, props.isPtCoach, props.isSwimCoach, props.isMembership, props.isCoach],
    (
      [newBusId, newPtCoach, newSwimCoach, newMembership, newCoach],
      [oldBusId, oldPtCoach, oldSwimCoach, oldMembership, oldCoach]
    ) => {
      if (
        newBusId !== oldBusId ||
        newPtCoach !== oldPtCoach ||
        newSwimCoach !== oldSwimCoach ||
        newMembership !== oldMembership ||
        newCoach !== oldCoach
      ) {
        saleChanged(props.multiple ? [] : '');
        needRefresh.value = true;
        state.coachList = [];
        state.ptCoachList = [];
        state.swimCoachList = [];
        state.membershipList = [];
        state.showSelect = false;
        // initData()
      }
    }
  );

  initData();
</script>

<style lang="less" scoped>
  :deep(.arco-select-option-content) {
    width: 100%;
  }
</style>
