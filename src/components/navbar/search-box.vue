<template>
  <div ref="searchForm" :model="form">
    <a-trigger v-model:popup-visible="isShowPop" auto-fit-position disabled>
      <a-trigger
        v-model:popup-visible="isShowErrorTips"
        :popup-offset="8"
        arrow-class="dark-error-arrow"
        position="bl"
        show-arrow
        disabled>
        <a-input-search
          v-if="IS_BRAND_SITE"
          v-model="form.search"
          :loading="isLoading"
          style="width: 320px; border-radius: 16px"
          placeholder="姓名/电话/身份证"
          allow-clear
          @clear="handleSearchInput('')"
          @input="handleSearchInput"
          @press-enter="handleSearch"
          @search="handleSearch" />
        <a-input-search
          v-else
          ref="searchRef"
          v-model="form.search"
          :loading="isLoading"
          style="width: 320px; border-radius: 16px"
          placeholder="姓名/电话/卡号/身份证"
          allow-clear
          @clear="handleSearchInput('')"
          @input="handleSearchInput"
          @press-enter="handleSearch"
          @search="handleSearch" />
        <template #content>
          <div class="tips">至少3位数字</div>
        </template>
      </a-trigger>

      <template #content>
        <div class="search-pop">
          <a-list
            class="list-demo-action-layout"
            :bordered="false"
            :data="userList"
            :max-height="540"
            :bottom-offset="40"
            @reach-bottom="handleReachBottom">
            <template #scroll-loading>
              <div v-if="bottom">没有更多了</div>
              <a-spin v-else-if="isLoading" />
              <a-empty v-else />
            </template>
            <template #item="{ item }">
              <a-list-item
                class="list-demo-item"
                action-layout="vertical"
                style="cursor: pointer"
                @click="goDetail(item)">
                <a-list-item-meta :title="item.title" :description="item.description">
                  <template #avatar>
                    <a-avatar :size="64" shape="square" :image-url="item.avatar"></a-avatar>
                  </template>
                  <template #title>
                    <div>
                      <span
                        v-for="(chart, index) in item.username"
                        :key="index"
                        :class="item.username_mark[index] === 1 ? 'red' : ''">
                        {{ chart }}
                      </span>
                    </div>
                  </template>
                  <template #description>
                    <div>
                      <span
                        v-for="(chart, index) in item.phone"
                        :key="index"
                        :class="item.phone_mark[index] === 1 ? 'red' : ''">
                        {{ chart }}
                      </span>
                    </div>
                    <div>
                      <span
                        v-for="(chart, index) in item.id_code"
                        :key="index"
                        :class="item.id_code_mark[index] === 1 ? 'red' : ''">
                        {{ chart }}
                      </span>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </template>
    </a-trigger>
  </div>
</template>

<script lang="ts" setup>
  import { getMerchantMemberList } from '@/api/member-list';
  import { goSubDetail, goSubMemberList } from '@/utils/router-go';
  import { Pagination } from '@/types/global';

  const { IS_BRAND_SITE } = window;
  const searchRef = ref<HTMLInputElement | null>(null);
  const form = reactive({
    search: '',
    is_top_search: 1,
  });
  const isShowPop = ref(false);
  function countChineseCharacters(str: string) {
    const chineseReg = /[\u4e00-\u9fa5]/g;
    const chineseMatches = str.match(chineseReg);
    return chineseMatches ? chineseMatches.length : 0;
  }
  function validSearch(val?: string) {
    const str = val !== undefined ? val : form.search;
    if (str.length >= 3 || countChineseCharacters(str) >= 1) {
      return true;
    }
    return false;
  }
  const isShowErrorTips = ref(false);
  function handleSearchInput(value: string) {
    if (value && !validSearch(value)) {
      isShowErrorTips.value = true;
    } else {
      isShowErrorTips.value = false;
    }
  }

  // 获取搜索结果
  const userList = ref<Record<string, any>[]>([]);
  const maxPage = ref(1);
  const bottom = ref(false);
  const basePagination: Pagination = {
    current: 1,
    pageSize: 10,
  };
  const pagination = reactive({
    ...basePagination,
  });
  const { isLoading, execute: fethList } = getMerchantMemberList();
  function goDetail(item: Record<string, any>) {
    goSubDetail(item.from_user_id, item.from_bus_id);
    isShowPop.value = false;
    form.search = '';
  }
  async function getMemberList(params?: Record<string, any>) {
    const { data } = await fethList({ data: params });
    const userListValue = data.value?.list || [];
    userList.value = params?.current === 1 ? userListValue : userList.value.concat(userListValue);
    maxPage.value = data.value?.max_page || 0;
    bottom.value = pagination.current >= maxPage.value && !!userList.value.length;
    isShowPop.value = true;
    if (data.value?.count === 1) {
      goDetail(userListValue[0]);
    }
  }
  function handleSearch() {
    if (validSearch()) {
      if (IS_BRAND_SITE) {
        getMemberList({
          ...basePagination,
          ...form,
        });
      } else {
        goSubMemberList({ curMenu: 'search', search: form.search });
        form.search = '';
        // searchRef.value?.blur();
      }
    }
  }
  function handleReachBottom() {
    if (isLoading.value || pagination.current >= maxPage.value) {
      return;
    }
    pagination.current += 1;
    getMemberList({
      ...pagination,
      ...form,
    });
  }
</script>

<style lang="less">
  .dark-error-arrow {
    background-color: var(--color-fill-2);
  }
</style>

<style scoped lang="less">
  .red {
    color: red;
  }

  .tips {
    background-color: var(--color-fill-2);
    height: 32px;
    line-height: 24px;
    font-size: 12px;
    padding: 4px 8px;
  }
  .search-pop {
    padding: 10px;
    width: 400px;
    max-height: 580px;
    overflow-y: scroll;
    background-color: var(--color-bg-popup);
    border-radius: 4px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
  }
  .list-demo-action-layout .image-area {
    width: 183px;
    height: 119px;
    border-radius: 2px;
    overflow: hidden;
  }
  .list-demo-action-layout .list-demo-item {
    padding: 20px 0;
    border-bottom: 1px solid var(--color-fill-3);
  }

  .list-demo-action-layout .image-area img {
    width: 100%;
  }

  .list-demo-action-layout .arco-list-item-action .arco-icon {
    margin: 0 4px;
  }
</style>
