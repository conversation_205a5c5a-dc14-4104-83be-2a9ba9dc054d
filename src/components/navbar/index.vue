<template>
  <div class="navbar">
    <div class="left-side">
      <div class="logo-wrap" :class="{ mobile: appStore.device === 'mobile' }">
        <a-space style="display: flex">
          <img width="40" alt="logo" :src="adminInfo.logo || getAssetsImg('logo.png')" />
          <span v-if="appStore.device !== 'mobile'" class="current-name">
            {{ busInfo.mer_name }}
          </span>
        </a-space>
      </div>

      <icon-menu-fold
        v-if="appStore.device === 'mobile'"
        style="margin-right: 20px; font-size: 22px; cursor: pointer"
        @click="toggleDrawerMenu" />
      <bus-switch />
    </div>
    <ul class="right-side">
      <li>
        <search-box />
      </li>
      <li>
        <a-tooltip content="联系我们">
          <a-popover title="" trigger="click">
            <div class="message-box-trigger">
              <a-button class="nav-btn" shape="circle">
                <my-icon icon="icon-kefu" />
              </a-button>
            </div>
            <template #content>
              <concat-us></concat-us>
            </template>
          </a-popover>
        </a-tooltip>
      </li>
      <li v-if="!IS_BRAND_SITE">
        <a-tooltip content="使用帮助">
          <div class="message-box-trigger" @click="goHelpCenter">
            <a-button class="nav-btn" shape="circle">
              <my-icon icon="icon-bangzhu" />
            </a-button>
          </div>
        </a-tooltip>
      </li>
      <li>
        <a-tooltip content="二维码导航">
          <a-popover title="" trigger="click">
            <div class="message-box-trigger">
              <a-button class="nav-btn" shape="circle">
                <my-icon icon="icon-lianxifangshi" />
              </a-button>
            </div>
            <template #content>
              <qrCodeCollection></qrCodeCollection>
            </template>
          </a-popover>
        </a-tooltip>
      </li>
      <li>
        <a-tooltip content="下载中心">
          <div class="message-box-trigger" @click="goDownCenter">
            <a-button class="nav-btn" shape="circle">
              <my-icon icon="icon-xiazai" />
            </a-button>
          </div>
        </a-tooltip>
      </li>
      <li>
        <a-tooltip content="消息通知">
          <div class="message-box-trigger" @click="goMsgCenter">
            <a-badge :count="websocketState.msgCount">
              <a-button class="nav-btn" shape="circle">
                <my-icon icon="icon-xiaoxitixing" />
              </a-button>
            </a-badge>
          </div>
        </a-tooltip>
      </li>
      <li>
        <a-tooltip :content="isFullscreen ? '点击退出全屏模式' : '点击切换全屏模式'">
          <a-button class="nav-btn" shape="circle" @click="toggleFullScreen">
            <template #icon>
              <icon-fullscreen-exit v-if="isFullscreen" />
              <icon-fullscreen v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <li>
        <a-tooltip content="用户中心">
          <a-popover title="" trigger="click" style="margin-left: -4px">
            <a-space direction="vertical" size="mini" align="center">
              <a-avatar :size="32" :style="{ marginRight: '8px', cursor: 'pointer' }">
                <img alt="avatar" :src="adminInfo.logo" />
              </a-avatar>
              {{ adminInfo.account }}
            </a-space>
            <template #content>
              <info-showcase @change-recharge="handleOpenRecharge" />
            </template>
          </a-popover>
        </a-tooltip>
      </li>
    </ul>
    <!-- 短信rechargeType=1/电子合同rechargeType=2充值弹窗 -->
    <recharge v-model="openRecharge" :type="rechargeType"></recharge>
  </div>
  <QrSignNotice />
</template>

<script lang="ts" setup>
  import { Notification, Button } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import { useFullscreen } from '@vueuse/core';
  import { useAdminInfoStore, useAppStore, useBusInfoStore, usePayStore, websocketStore } from '@/store';
  import { openWindow, getAssetsImg } from '@/utils';
  import QrSignNotice from '@/components/member/qrSignNotice.vue';
  import busSwitch from './bus-switch.vue';
  import ConcatUs from './concat-us.vue';
  import searchBox from './search-box.vue';
  import infoShowcase from './info-showcase.vue';
  import recharge from '../online-pay/recharge.vue';
  import qrCodeCollection from './qr-code-collection.vue';

  const { IS_BRAND_SITE } = window;
  const appStore = useAppStore();
  const router = useRouter();
  const route = useRoute();

  // 设置账号权限相关的账号信息和场馆信息
  const adminInfo = useAdminInfoStore();
  const busInfo = useBusInfoStore();
  const payInfo = usePayStore();
  const websocketState = websocketStore();
  adminInfo.setInfoByServe();
  busInfo.setInfoByServe().then(() => {
    document.title = `${IS_BRAND_SITE ? busInfo.mer_name : busInfo.bus_name || '勤鸟运动'}${
      route.meta.locale ? `_${route.meta.locale}` : ''
    }`;
  });
  payInfo.setPayTypesInit();
  adminInfo.getFinanceDays();

  function goMsgCenter() {
    openWindow('/v2/notice/msgCenter');
  }
  function goDownCenter() {
    router.push('/v2/stat/menus/exportLogTable');
  }

  function goHelpCenter() {
    router.push('/v2/signin/manual');
  }

  const { isFullscreen, toggle: toggleFullScreen } = useFullscreen();
  const toggleDrawerMenu = inject('toggleDrawerMenu') as () => void;

  // SMS弹窗
  const openRecharge = ref(false);
  // 充值弹窗类型(用于区分打开短信/电子合同充值弹窗)
  const rechargeType = ref(1);
  interface Type {
    bool: boolean;
    type: number;
  }
  const handleOpenRecharge = (result: Type) => {
    rechargeType.value = result.type;
    openRecharge.value = result.bool;
  };
  const modalFooter = (id: string) => {
    return adminInfo.renew_version
      ? h(
          Button,
          {
            type: 'text',
            onClick: () => {
              Notification.remove(id);
              router.push('/admin/account-reachage');
            },
          },
          '立即续费'
        )
      : null;
  };
  watch(
    () => adminInfo.expire_date,
    (val, oldVal) => {
      if (val !== oldVal) {
        const cha = dayjs(val).diff(dayjs(), 'day');
        if (cha <= 30) {
          const id = `${Date.now()}`;
          Notification.warning({
            id,
            title: '到期提醒',
            duration: 0,
            closable: true,
            content: `系统将于${val}到期，请及时续费以免影响使用`,
            footer: modalFooter(id),
          });
        }
      }
    },
    {
      immediate: true,
    }
  );
  watch(
    () => adminInfo.bus_no_money,
    (val, oldVal) => {
      if (val !== oldVal && Number(val) > 0) {
        const id = `${Date.now()}`;
        Notification.warning({
          id,
          title: '缴费提醒',
          closable: true,
          duration: 0,
          content: `当前欠费${val}元，请及时缴费以免影响使用`,
          footer: modalFooter(id),
        });
      }
    },
    {
      immediate: true,
    }
  );
</script>

<style scoped lang="less">
  .navbar {
    display: flex;
    justify-content: space-between;
    height: 100%;
    background-color: var(--color-bg-2);
    border-bottom: 1px solid var(--color-border);
  }

  .left-side {
    display: flex;
    align-items: center;
    padding-left: 20px;

    .logo-wrap {
      margin-right: 20px;
      width: 200px;
      &.mobile {
        width: unset;
      }

      .current-name {
        font-size: 14px;
        font-weight: bold;
        color: #000;
      }
    }
  }

  .center-side {
    flex: 1;
  }

  .right-side {
    display: flex;
    padding-right: 20px;
    list-style: none;

    :deep(.locale-select) {
      border-radius: 20px;
    }

    li {
      display: flex;
      align-items: center;
      padding: 0 10px;
    }

    a {
      color: var(--color-text-1);
      text-decoration: none;
    }

    .nav-btn {
      border-color: rgb(var(--gray-2));
      color: rgb(var(--gray-8));
      font-size: 16px;
      background-color: #e9ecee;
    }

    .trigger-btn,
    .ref-btn {
      position: absolute;
      bottom: 14px;
    }

    .trigger-btn {
      margin-left: 14px;
    }
  }
</style>

<style lang="less">
  .message-popover {
    .arco-popover-content {
      margin-top: 0;
    }
  }
</style>
