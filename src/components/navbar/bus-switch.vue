<template>
  <a-card class="small-card" size="small">
    <div
      :style="{
        display: 'flex',
        alignItems: 'center',
        cursor: 'pointer',
      }"
      @click="handleClick">
      <a-tag :color="IS_BRAND_SITE ? '#FFE9EE' : '#D3F2FF'">
        <span style="font-weight: bold" :style="{ color: IS_BRAND_SITE ? '#FD2451' : '#03B6FF' }">
          {{ IS_BRAND_SITE ? '商家' : '场馆' }}
        </span>
      </a-tag>
      <a-typography-text style="margin: 0 8px">
        {{ IS_BRAND_SITE ? '运营管理后台' : busInfo.bus_name }}
      </a-typography-text>
      <icon-down style="margin-left: auto" />
    </div>
  </a-card>
  <a-drawer :width="490" :visible="visible" placement="left" :footer="false" unmount-on-close @cancel="drawerCancel">
    <template #title>切换</template>
    <div ref="busSwitchContainer">
      <div style="display: flex">
        <a-input-search v-model="search" placeholder="商家/场馆" allow-clear />
        <!-- style="width: 344px" -->
        <!-- <a-button type="primary" style="width: 84px; margin-left: auto">
          <template #icon>
            <icon-search />
          </template>
          搜索
        </a-button> -->
      </div>
      <a-list
        class="m-list"
        :virtual-list-props="{
          height: mListHeight,
        }"
        :data="mList">
        <template #item="{ item, index }">
          <a-list-item :key="index">
            <a-row :gutter="16" align="center" justify="space-between" style="margin-bottom: 16px">
              <a-col flex="42px">
                <a-tag color="#FFE9EE">
                  <span style="font-weight: bold; color: #fd2451">商家</span>
                </a-tag>
              </a-col>
              <a-col flex="1">
                <div style="font-size: 18px; font-weight: bold">
                  {{ item.mer_name }}
                </div>
              </a-col>
              <a-col flex="100px">
                <a v-if="item.mer_version === 1" class="switch-btn" @click.stop="handleSwitchMerchant(item)">
                  进入商家后台
                </a>
              </a-col>
            </a-row>
            <a-card v-if="item.bus_list.length">
              <div v-for="bus in item.bus_list" :key="bus.id" class="card-item" @click.stop="handleSwitchBus(bus)">
                <div class="card-item-name">
                  {{ bus.name }}
                  <span v-if="bus.version_name === '试用版'" class="version-tag">试</span>
                  <span v-if="bus.version_expired" class="version-tag tag-red">过</span>
                </div>
                <div class="card-item-phone">
                  {{ bus.phone }}
                </div>
              </div>
            </a-card>
          </a-list-item>
        </template>
      </a-list>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
  import { Notification, Message } from '@arco-design/web-vue';
  import { ajaxSwitchBus } from '@/api/cutover';
  import { useBusInfoStore } from '@/store';
  import { getBrandHost, getBusHost } from '@/config/url';

  const { IS_BRAND_SITE } = window;
  const mListHeight = document.documentElement.clientHeight - (48 + 12 + 32 + 16 + 16);
  const search = ref('');
  const busInfo = useBusInfoStore();
  const mList = computed(() => {
    let list = busInfo.m_list || [];
    if (search.value) {
      list = list
        .map((item: Record<string, any>) => {
          const filteredBusList = item.bus_list.filter((bus: Record<string, any>) => bus.name.includes(search.value));
          return { ...item, bus_list: filteredBusList };
        })
        .filter((item: Record<string, any>) => item.mer_name.includes(search.value) || item.bus_list.length > 0);
    }
    return list;
  });
  const visible = ref(false);
  const handleClick = () => {
    visible.value = true;
  };
  const drawerCancel = () => {
    visible.value = false;
  };
  const handleSwitchBus = async (bus: Record<string, any>, changeMerchant = false) => {
    const { response }: any = await ajaxSwitchBus({ bus_id: bus.id, to_merchant: changeMerchant ? 1 : 0 });
    if (response.value?.status === 1) {
      // 初始化搜索标签
      sessionStorage.setItem('class_id', '0');
      sessionStorage.setItem('card_id', '0');
      // 初始化卡课管理tab
      sessionStorage.setItem('cardListActive', '0');
      // fix: 11791 切换场馆时重置，以获取通知
      sessionStorage.setItem('hasPopupNotice', 'false');
      if (!changeMerchant) {
        Notification.success({
          title: `从"${busInfo.bus_name}"飞奔向"${bus.name}"`,
          duration: 1500,
          content: '马上进行切换...',
          onClose() {
            window.location.href = changeMerchant ? getBrandHost() : getBusHost();
          },
        });
      } else {
        window.location.href = changeMerchant ? getBrandHost() : getBusHost();
      }
    } else {
      Message.error(response.value?.info);
    }
  };
  function handleSwitchMerchant(item: Record<string, any>) {
    const firstBus = busInfo.m_list.find((info) => item.id === info.id)?.bus_list[0];
    if (!firstBus) {
      Message.error('商家下无有效场馆');
      return;
    }
    handleSwitchBus(firstBus, true);
  }
</script>

<style scoped lang="less">
  .small-card :deep(.arco-card-body) {
    padding: 4px;
    min-width: 242px;
  }
  .m-list {
    margin-top: 16px;
    :deep(.arco-scrollbar-container) {
      border: 0 none;
    }
    :deep(.arco-list-item) {
      padding: 0 0 20px !important;
      border: 0 none;
    }
    :deep(.arco-card-body) {
      padding: 10px;
    }
  }
  .card-item {
    width: 100%;
    height: 72px;
    padding: 12px 20px;
    background: #f5f8fe;
    line-height: 1.5;
    margin-bottom: 10px;
    cursor: pointer;
    color: #000;

    &:last-child {
      margin-bottom: 0;
    }

    &-name {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 6px;
    }

    &-phone {
      font-size: 14px;
    }
  }

  .version-tag {
    vertical-align: middle;
    display: inline-block;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    color: #fff;
    text-align: center;
    line-height: 18px;
    font-size: 12px;
    background: #34d1c9;
    margin-right: 8px;
    font-family: 'sans-serif';
  }

  .tag-red {
    background: #ff2351;
  }

  .switch-btn {
    font-size: 14px;
    color: @theme-link;
    cursor: pointer;
  }
</style>
