<template>
  <a-space direction="vertical" fill style="width: 260px">
    <a-row justify="center">
      <a-col flex="74px">
        <img class="logo" :src="adminInfo.logo || 'https://imagecdn.rocketbird.cn/default/business_logo.jpg'" />
      </a-col>
    </a-row>
    <a-row justify="center">
      <a-col :span="24">
        <div class="username" style="text-align: center">{{ adminInfo.account }}</div>
      </a-col>
    </a-row>
    <div v-if="!IS_BRAND_SITE" class="from-content">
      <a-row justify="center" align="center">
        <a-col :span="18">
          <div class="from">
            <span class="label">类型：</span>
            <p class="value type">{{ adminInfo.version }}</p>
          </div>
        </a-col>
        <a-col :span="6">
          <a-button v-if="adminInfo.renew_version" type="text" @click="handleRenew">续费</a-button>
        </a-col>
      </a-row>
      <a-row justify="center">
        <a-col :span="24">
          <div class="from">
            <span class="label">到期：</span>
            <p class="value date">{{ adminInfo.expire_date }}</p>
          </div>
        </a-col>
      </a-row>
      <a-row justify="center" align="center">
        <a-col :span="18">
          <div class="from complex-content">
            <span class="label">短信：</span>
            <p class="value complex">
              剩余
              <span style="color: #ff2351">{{ adminInfo.sms_number }}</span>
              条
            </p>
          </div>
        </a-col>
        <a-col :span="6">
          <a-button type="text" @click="handleOpenRecharge(true, 1)">充值</a-button>
        </a-col>
      </a-row>
      <a-row justify="center" align="center">
        <a-col :span="18" class="complex-content">
          <span class="label">
            电子
            <br />
            合同：
          </span>
          <div class="complex">
            <p>
              {{ adminInfo.esign_status == 1 ? '已开通' : '未开通'
              }}{{ adminInfo.esign_status !== 1 ? '' : adminInfo.esign_vip_status == '1' ? '-使用中' : '-暂停使用' }}
            </p>
            <p v-if="adminInfo.esign_status == 1">
              余额
              <span style="color: #ff696a">{{ adminInfo.contract_number }}</span>
            </p>
          </div>
        </a-col>
        <a-col :span="6">
          <a-button v-if="adminInfo.esign_status == 1" type="text" @click="handleOpenRecharge(true, 2)">充值</a-button>
          <a-button v-else type="text" @click="handleRouterTo">开通</a-button>
        </a-col>
      </a-row>
    </div>
    <a-row justify="center">
      <a-col :span="24">
        <div class="bottom">
          <a-button type="text" @click="updateAdmin">修改密码</a-button>
          <a-button type="text" @click="handleLogout">退出登录</a-button>
        </div>
      </a-col>
    </a-row>
  </a-space>
</template>

<script lang="ts" setup>
  import { getBaseUrl } from '@/config/url';
  import { useAdminInfoStore } from '@/store';

  const router = useRouter();
  const { IS_BRAND_SITE } = window;

  // 设置账号权限相关的账号信息和场馆信息
  const adminInfo = useAdminInfoStore();
  const emit = defineEmits(['changeRecharge']);
  /**
   * 打开充值弹窗
   * @param bool 是否打开充值弹窗
   * @param type 打开弹窗的类型1短信,2电子合同
   */
  const handleOpenRecharge = (bool: boolean, type: number) => {
    emit('changeRecharge', { bool, type });
  };

  // 修改密码
  const updateAdmin = async () => {
    router.push({ name: 'UpdateAdmin' });
  };

  const url = `${getBaseUrl()}/Web/Public/logout`;
  // 退出登录
  const handleLogout = async () => {
    if (window.location.hostname === 'localhost') {
      router.push({ path: '/login', name: '登录', params: { from: window.location.hash } });
    } else {
      window.location.href = url;
    }
  };

  const handleRouterTo = () => {
    router.push({ path: '/electronic', name: 'electronic' });
  };

  // 版本续费
  const handleRenew = () => {
    router.push('/admin/account-reachage');
  };
</script>

<style scoped lang="less">
  .logo {
    width: 74px;
    height: 74px;
    border-radius: 50%;
    border: 2px solid #ececec;
  }
  .username {
    font-size: 14px;
    font-weight: bold;
  }
  .from-content {
    background-color: #f4f4f4;
    padding: 8px 14px;
    .from {
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .value {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 14px;
      }
      .type {
        height: 24px;
        padding: 0 14px;
        border-radius: 12px;
        background-color: #fff;
      }
      .date {
        font-weight: bold;
      }
      .special {
        justify-content: space-between;
      }
    }
    .complex-content {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .complex {
        flex: 1;
      }
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    .line {
      width: 1px;
      height: 13px;
      background-color: #d2d2d2;
    }
  }
</style>
