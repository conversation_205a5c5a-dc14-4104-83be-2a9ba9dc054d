<template>
  <a-tabs :default-active-key="1" lazy-load style="width: 320px">
    <a-tab-pane v-for="item in qrCodeList" :key="item.key" :title="item.name" style="padding-bottom: 16px">
      <a-space direction="vertical" fill>
        <div class="collection-content">{{ item.tips }}</div>
        <div class="collection-content">
          <template v-if="item.qrCode">
            <template v-for="(ele, qrCodeKey) in item.qrCode" :key="'qr_code_' + qrCodeKey">
              <a-spin :loading="androidLoading" tip="加载中..." class="qr-code-spin">
                <a-skeleton :animation="true" :loading="!ele.url && item.skeleton">
                  <a-skeleton-shape class="qr-code-img" />
                </a-skeleton>
                <img v-if="ele.url" class="qr-code-img" :src="ele.url" />
                <p class="qr-code-name">{{ ele.name }}</p>
              </a-spin>
            </template>
          </template>
          <template v-else>
            <a-spin :loading="loading" tip="加载中..." class="qr-code-spin">
              <a-skeleton :animation="true" :loading="!item.url && item.skeleton">
                <a-skeleton-shape class="qr-code-img" />
              </a-skeleton>
              <img v-if="item.url" class="qr-code-img" :src="item.url" />
              <p class="qr-code-name">{{ item.name }}</p>
            </a-spin>
          </template>
        </div>
      </a-space>
    </a-tab-pane>
  </a-tabs>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { getQrCodeMember, getIvepAndroid } from '@/api/qr-code';

  /**
   * skeleton: 是否需要骨架屏 true显示 false不显示
   */
  interface qrCodeType {
    url: string;
    name: string;
    skeleton?: boolean;
  }

  /**
   * skeleton: 是否需要骨架屏 true显示 false不显示
   */
  interface listType {
    key: string | number;
    tips?: string;
    name: string;
    url?: string;
    qrCode?: qrCodeType[];
    skeleton?: boolean;
  }
  const qrCodeList = ref<listType[]>([
    {
      key: 1,
      name: '会员端',
      tips: '微信扫一扫，即可使用手机端',
      url: '',
      skeleton: true,
    },
    { key: 2, name: '会籍端', url: 'https://imagecdn.rocketbird.cn/online/qrcode/membership_860.jpg' },
    { key: 3, name: '教练端', url: 'https://imagecdn.rocketbird.cn/online/qrcode/coach_860.jpg' },
    { key: 4, name: 'Boss端', url: 'https://imagecdn.rocketbird.cn/online/qrcode/boss_860.jpg' },
    {
      key: 5,
      name: 'IVEP',
      qrCode: [
        { name: 'andorid', url: '', skeleton: true },
        { name: 'ios', url: 'https://imagecdn.rocketbird.cn/online/qrcode/ivep_ios_350.png' },
      ],
    },
  ]);

  const { isLoading: loading, execute: executeGetQrCodeMember } = getQrCodeMember();
  const { isLoading: androidLoading, execute: executeGetIvepAndroid } = getIvepAndroid();

  const getMember = async () => {
    const { response }: { response: { value: any } } = await executeGetQrCodeMember({
      params: { client: window.IS_BRAND_SITE ? 1 : 2, width: 860 },
    });
    const url = window.URL.createObjectURL(response.value);
    qrCodeList.value[0].url = url;
    return Promise.resolve();
  };

  const getAndroid = async () => {
    // window.IS_BRAND_SITE ? '商家' : '门店'
    const { response }: { response: { value: any } } = await executeGetIvepAndroid({
      params: { client: window.IS_BRAND_SITE ? 1 : 2, width: 860 },
    });
    const adnroidUrl = window.URL.createObjectURL(response.value);
    qrCodeList.value[4].qrCode[0].url = adnroidUrl;
    return Promise.resolve();
  };

  const getData = async () => {
    getMember().then(() => getAndroid());
  };

  getData();
</script>

<style scoped lang="less">
  .collection-content {
    min-height: 22px;
    text-align: center;
  }

  .qr-code-img {
    width: 215px;
    height: 215px;
  }

  .qr-code-name {
    margin-top: 16px;
    font-weight: bold;
  }

  .qr-code-spin {
    width: 215px;
    height: 215px;
    min-width: 215px;
    min-height: 215px;
  }

  .qr-code-spin + .qr-code-spin {
    margin-top: 16px;
  }
</style>
