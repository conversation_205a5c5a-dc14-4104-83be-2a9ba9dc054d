<template>
  <a-col :span="8">
    <a-form-item field="card_id" label="卡课类型">
      <a-select
        v-model="state.value"
        v-bind="$attrs"
        :multiple="multiple"
        :loading="isLoading"
        allow-search
        :options="filterCardList"
        :virtual-list-props="{ height: 200 }"
        :threshold="200"
        @popup-visible-change="popupVisibleChange"
        @change="handleChange">
        <!-- <template v-if="filterCardList.length"></template> -->
        <!-- <a-option v-for="card in filterCardList" :key="card.card_id" :value="card.card_id">{{
          card.card_name
        }}</a-option>
        <template v-if="showPackage">
          <a-option v-for="pack in state.packageCardList" :key="pack.card_id" :value="pack.card_id">{{
            pack.card_name
          }}</a-option>
        </template> -->
      </a-select>
    </a-form-item>
  </a-col>
</template>

<script setup lang="ts">
  import { reactive, ref, computed, watch } from 'vue';
  import { getCardList, getPackageList } from '@/api/member';
  // import useLoading from '@/hooks/loading';
  // const { loading, setLoading } = useLoading(true);

  const props = defineProps({
    modelValue: {
      type: [Number, String, Array],
      default: '',
    },
    // 只显示在售的卡
    onlySale: {
      type: Boolean,
      default: false,
    },
    showPackage: {
      type: Boolean,
      default: false,
    },
    // 排除的卡类型, 如: 不显示私教卡和储值卡 ['4', '3']
    exceptType: {
      type: Array,
      default: () => [],
    },
    // 刷新卡列表
    refresh: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    // 将整个 card 返回
    returnCardInfo: {
      type: Boolean,
      default: false,
    },
    busId: {
      type: String,
      default: '',
    },
  });

  const emits = defineEmits(['update:modelValue', 'onChange']);

  const state = reactive({
    value: [] as any[] | string,
    packageCardList: [],
    addCardList: [],
  });

  const filterCardList = computed(() => {
    // if (!state.addCardList.length) {
    //   return [];
    // }

    let list = [...state.addCardList];
    if (props.onlySale) {
      list = list.filter((item) => item.sale_status === '1');
    }
    if (props.exceptType.length) {
      list = list.filter((item) => !props.exceptType.includes(item.card_type_id));
    }

    if (props.showPackage) {
      list = [...list, ...state.packageCardList];
    }
    return list;
  });

  const needRefresh = ref(false);
  const { isLoading: isCardLoading, execute: getCards } = getCardList();
  const { isLoading: isPackageLoading, execute: getPackage } = getPackageList();
  const isLoading = computed(() => isCardLoading.value || isPackageLoading.value);

  async function getAddCardList() {
    const { data } = await getCards({
      params: { bus_id: props.busId },
    });
    const list = data.value?.length ? data.value : [];
    state.addCardList = list.map((item: Record<string, any>) => {
      return {
        card_id: item.card_id,
        card_name: item.card_name,
        value: item.card_id,
        label: item.card_name,
      };
    });
    needRefresh.value = false;
  }
  async function getPackageCardList() {
    const { data } = await getPackage({
      params: { belong_bus_id: props.busId },
    });
    const list = data.value?.length ? data.value : [];

    state.packageCardList = list.map((item) => {
      return {
        card_id: item.id,
        card_name: item.name,
        value: item.id,
        label: item.name,
      };
    });
    needRefresh.value = false;
  }

  function initList() {
    getAddCardList();
    if (props.showPackage) {
      getPackageCardList();
    }
  }

  function popupVisibleChange(visible: boolean) {
    if (visible && needRefresh.value) {
      initList();
    }
  }

  function handleChange(val) {
    if (props.returnCardInfo) {
      if (Array.isArray(val)) {
        if (!val.length) {
          emits('onChange', []);
        } else {
          const card = [];
          filterCardList.value.forEach((item) => {
            if (val.indexOf(item.card_id) !== -1) {
              card.push(item);
            }
          });
          emits('onChange', card);
        }
      } else if (!val.length) {
        emits('onChange', '');
      } else {
        const card = filterCardList.value.find((item) => {
          return val === item.card_id;
        });
        emits('onChange', card);
      }
    } else {
      emits('onChange', val);
    }
  }

  watch(
    () => props.busId,
    (newValue, oldValue) => {
      if (newValue !== oldValue) {
        state.value = props.multiple ? [] : '';
        emits('onChange', state.value);
        needRefresh.value = true;
      }
    }
  );

  initList();
</script>

<style scoped lang="less"></style>
