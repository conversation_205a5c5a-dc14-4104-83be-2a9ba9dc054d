<template>
  <div class="room-wrap" :class="!isShowSet ? 'no-left' : ''">
    <div v-if="isShowSet" class="room-lef">
      <ul>
        <li v-for="(item, index) in rowArr" :key="index" class="room-lef-li">
          <label>第{{ index + 1 }}排</label>
          <a-input-number
            v-model="rowArr[index]"
            class="room-input"
            :max="99"
            :min="1"
            placeholder="请输入座位数"
            :step="1" />
          <icon-delete v-if="index === rowArr.length - 1" class="del-ico" @click="delRow(index)" />
        </li>
        <li>
          <span class="room-lef-last" @click="addRow">
            <icon-plus-circle class="add-ico" />
            再添加一排
          </span>
        </li>
      </ul>
    </div>
    <div ref="roomBox" class="room-box">
      <div class="room-box-head" :style="{ width: rowArrMax * 40 + 'px' }">
        <div v-if="!isShowSet" class="room-top-rig">
          <div class="not-sit"></div>
          可选
          <div class="is-sit">1</div>
          已售
        </div>
        <div v-else>排布图</div>
        <a-button disabled>讲台</a-button>
      </div>
      <div class="room-box-con" :style="{ height: rowArr.length * 40 + 'px', width: rowArrMax * 40 + 'px' }">
        <div v-for="(row, x) in rowArr.length" :key="row" class="seat-row" :style="{ width: rowArr[x] * 40 + 'px' }">
          <template v-if="isShowSet">
            <span v-for="(col, y) in rowArr[x]" :key="col" class="seat-num">
              {{ getSeatInfo(x, y, 'seat_number') }}
            </span>
          </template>
          <template v-else>
            <span
              v-for="(col, y) in rowArr[x]"
              :key="col"
              class="seat-num"
              :class="{
                'is-choose': chooseIds.indexOf(getSeatInfo(x, y, 'id')) !== -1,
                'is-used': getSeatInfo(x, y, 'is_used'),
              }"
              @click="choseNum(x, y)">
              <icon-check v-if="chooseIds.indexOf(getSeatInfo(x, y, 'id')) !== -1" />
              <template v-else>
                {{ getSeatInfo(x, y, 'seat_number') }}
              </template>
            </span>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
    isShowSet: {
      type: Boolean,
      default: false,
    },
    isCanChoose: {
      type: Boolean,
      default: true,
    },
    chooseNumber: {
      type: Number,
      default: 1,
    },
  });
  const emit = defineEmits(['update:modelValue', 'onChoose']);
  const rowArr = ref([]);
  const rowArrMax = ref(0);
  const chooseIds = ref([]);
  const seatsList = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emit('update:modelValue', val);
    },
  });

  watch(
    () => rowArr.value,
    (val) => {
      const seats = [];
      rowArrMax.value = val[0] || 0;
      let total = 0;
      val.forEach((num, index) => {
        if (num > rowArrMax.value) {
          rowArrMax.value = num;
        }
        if (props.isShowSet) {
          for (let i = 0; i < num; i += 1) {
            total += 1;
            seats.push({
              position_x: index + 1,
              position_y: i + 1,
              seat_number: total,
            });
          }
        }
      });
      if (props.isShowSet) {
        seatsList.value = seats;
      }
    }
  );
  function getSeatInfo(x, y, info) {
    return seatsList.value.find((item) => +item.position_x === +x + 1 && +item.position_y === +y + 1)[info];
  }
  function choseNum(x, y) {
    if (props.isCanChoose) {
      const curId = getSeatInfo(x, y, 'id');
      const isUsed = getSeatInfo(x, y, 'is_used');
      if (isUsed) {
        return;
      }
      if (chooseIds.value.indexOf(curId) !== -1) {
        chooseIds.value.splice(chooseIds.value.indexOf(curId), 1);
        emit('onChoose', chooseIds.value.join(','));
        return;
      }
      if (props.chooseNumber && chooseIds.value.length >= props.chooseNumber) {
        Message.error('座位数不可超过预约人数');
        return;
      }
      chooseIds.value.push(curId);
      emit('onChoose', chooseIds.value.join(','));
    }
  }
  function addRow() {
    rowArr.value.push(null);
  }
  function delRow(index) {
    if (index === 0) {
      rowArr.value = [null];
    } else {
      rowArr.value.splice(index, 1);
    }
  }
  function getrowArr() {
    let arr = [];
    if (seatsList.value && seatsList.value.length) {
      let arrIndex = 0;
      seatsList.value.forEach((item, i) => {
        if (i > 0 && seatsList.value[i - 1].position_x !== item.position_x) {
          arr.push(i - arrIndex);
          arrIndex = i;
        }
      });
      // 最后一排或者只有一排的时候
      arr.push(seatsList.value.length - arrIndex);
    } else {
      arr = [''];
    }
    rowArr.value = arr;
  }
  onMounted(() => {
    if (seatsList.value && seatsList.value.length > 0) {
      getrowArr();
    }
  });
</script>

<style scoped lang="less">
  .room-wrap {
    position: relative;
    margin-bottom: 100px;
    .del-ico {
      color: #ea5252;
      font-size: 16px;
      cursor: pointer;
    }
    .room-lef-last {
      cursor: pointer;
    }
    .add-ico {
      color: #ffbc46;
      font-size: 16px;
      vertical-align: middle;
      margin-right: 4px;
    }
    .room-box {
      position: absolute;
      overflow-x: scroll;
      background: #fff;
      left: 300px;
      top: -80px;
      border: 1px solid #dedede;
      min-width: 450px;
      max-width: 652px;
      padding: 0 25px;
      text-align: center;
    }
    .room-box-head {
      margin-bottom: 13px;
      min-width: 100%;
    }
    .room-box-con {
      min-height: 80px;
      margin: 0 auto;
      .seat-row {
        height: 40px;
        text-align: center;
        margin: 0 auto;
      }
      .seat-num {
        display: inline-block;
        width: 32px;
        height: 32px;
        line-height: 32px;
        margin: 0 8px 8px 0;
        border-radius: 4px;
        border: 1px solid #dedede;
        vertical-align: middle;
        &.is-choose {
          color: #fff;
          font-size: 16px;
          line-height: 30px;
          background: #23c6d5;
          border-color: #23c6d5;
        }
        &.is-used {
          color: #fff;
          line-height: 30px;
          font-size: 16px;
          background: #ca2e53;
          border-color: #ca2e53;
        }
      }
    }
  }
  .room-lef-li {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .room-input {
      width: 100px !important;
      margin-left: 25px;
      margin-right: 10px;
    }
  }
  .no-left {
    margin-bottom: 0;
    .room-box {
      padding-top: 10px;
      position: relative;
      left: auto;
      top: auto;
    }
    .room-box-head {
      padding-top: 20px;
    }
  }
  .room-top-rig {
    position: absolute;
    right: 15px;
    top: 5px;
    display: flex;
    justify-items: center;
    align-items: center;
    font-size: 12px;
    .not-sit {
      width: 19px;
      height: 19px;
      border-radius: 5px;
      border: 1px solid #bbb;
      margin-right: 10px;
    }
    .is-sit {
      margin-left: 15px;
      margin-right: 10px;
      width: 20px;
      height: 20px;
      border-radius: 5px;
      background: #ca2e53;
      color: #fff;
      line-height: 20px;
      text-align: center;
    }
    .seat-num {
      cursor: pointer;
    }
  }
</style>
