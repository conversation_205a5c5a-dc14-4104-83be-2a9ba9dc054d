// eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require('path');

module.exports = {
  root: true,
  parser: 'vue-eslint-parser',
  parserOptions: {
    // Parser that checks the content of the <script> tag
    parser: '@typescript-eslint/parser',
    sourceType: 'module',
    ecmaVersion: 2020,
    ecmaFeatures: {
      jsx: true,
    },
  },
  env: {
    'browser': true,
    'node': true,
    'vue/setup-compiler-macros': true,
  },
  plugins: ['@typescript-eslint', 'prettier'],
  extends: [
    // "airbnb": 这是由 Airbnb 团队维护的一组 JavaScript 代码规范，
    // 它提供了一套严格的、可读性高的代码规则，用于保持代码质量和一致性
    'airbnb-base',
    // 这个规则集合由 @typescript-eslint/eslint-plugin 提供，它包含一些用于检查和约束 TypeScript 代码的规则
    'plugin:@typescript-eslint/recommended',
    // ESLint官方提供的Vue插件，可以检查 .vue文件中的语法错误
    'plugin:vue/vue3-recommended',
    // 这个插件的作用是禁用所有与格式相关的eslint规则，也就是说把所有格式相关的校验都交给 prettier 处理
    'plugin:prettier/recommended',
    './.eslintrc-auto-import.json'
  ],
  settings: {
    'import/resolver': {
      typescript: {
        project: path.resolve(__dirname, './tsconfig.json'),
      },
    },
  },
  rules: {
    // Vue: Recommended rules to be closed or modify
    'vue/require-default-prop': 0,
    'vue/singleline-html-element-content-newline': 0,
    'vue/max-attributes-per-line': 0,
    // Vue: Add extra rules
    'vue/custom-event-name-casing': [2, 'camelCase'],
    'vue/no-v-text': 1,
    'vue/padding-line-between-blocks': 1,
    'vue/require-direct-export': 1,
    'vue/multi-word-component-names': 0,
    // Allow @ts-ignore comment
    '@typescript-eslint/ban-ts-comment': 0,
    '@typescript-eslint/no-unused-vars': 1,
    '@typescript-eslint/no-empty-function': 1,
    '@typescript-eslint/no-explicit-any': 0,
    'import/extensions': [
      2,
      'ignorePackages',
      {
        js: 'never',
        jsx: 'never',
        ts: 'never',
        tsx: 'never',
      },
    ],
    'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,
    'no-console': 0,
    'no-param-reassign': 0,
    'no-nested-ternary': 0, // 嵌套的三元表达式
    'prefer-regex-literals': 0,
    'import/no-extraneous-dependencies': 0,
    // 'prettier/prettier': 1,
    'prettier/prettier': [
      'error',
      {
        vueIndentScriptAndStyle: true,
        htmlWhitespaceSensitivity: 'ignore',
        bracketSameLine: true // 关键配置：让 `>` 保持在同一行
      }
    ],
    'camelcase': 0, // 后端返回非驼峰，关闭强制变量驼峰命名
    '@typescript-eslint/camelcase': 0, // 后端返回非驼峰，关闭强制变量驼峰命名
    
  },
};
