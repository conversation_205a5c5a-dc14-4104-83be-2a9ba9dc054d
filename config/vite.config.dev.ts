import { mergeConfig } from 'vite';
import eslint from 'vite-plugin-eslint';
import baseConfig from './vite.config.base';

export default mergeConfig(
  {
    mode: 'development',
    server: {
      host: 'fe.rocketbird.cn',
      open: true,
      hmr: {
        // 禁用开发服务器错误的屏蔽弹窗
        overlay: false,
      },
      fs: {
        strict: true,
      },
      proxy: {
        '/api': {
          target: 'https://beta.rocketbird.cn',
          // target: 'https://sim.rocketbird.cn',
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
    plugins: [
      eslint({
        failOnError: false, // 如果有任何错误，将导致模块构建失败，基于emitError。
        cache: false,
        fix: true, // 自动修复源代码
        include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
        exclude: ['node_modules'],
      }),
    ],
  },
  baseConfig
);
