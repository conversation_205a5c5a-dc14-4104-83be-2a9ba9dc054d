# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Development server**: `npm run dev` - Starts Vite dev server with custom domain `fe.rocketbird.cn`
- **Build for production**: `npm run build` - Builds using production config
- **Build with CDN**: `npm run cdn` - Builds using CDN configuration  
- **Build with bundle analyzer**: `npm run report` - Builds with bundle size analysis (set REPORT=true env var)
- **Type checking**: `npm run type:check` - Runs Vue TypeScript compiler checks
- **Preview production build**: `npm run preview` - Builds and serves locally
- **Lint staged files**: `npm run lint-staged` - Runs linting on staged git files

The project uses <PERSON>sky for git hooks and lint-staged for pre-commit linting with ESLint and Prettier.

## Architecture Overview

This is a Vue 3 + TypeScript SaaS management system built with:

- **Frontend Framework**: Vue 3 with Composition API
- **Build Tool**: Vite with custom configurations for dev/prod/CDN
- **UI Library**: Arco Design Vue with custom Rocketbird theme
- **State Management**: Pinia stores (app, user, admin-info, bus-info, card, pay, websocket, booking)
- **Routing**: Vue Router with route guards and permission system
- **HTTP Client**: Axios with custom request/response interceptors and VueUse integration

### Key Directory Structure

- `src/api/` - API layer with service modules for different business domains
- `src/views/` - Page components organized by business modules (admin, member, finance, etc.)
- `src/components/` - Reusable UI components with global registration
- `src/store/modules/` - Pinia state management stores
- `src/router/routes/modules/` - Route definitions organized by feature
- `src/utils/` - Utility functions and helpers
- `src/request/` - HTTP request handling and interceptors
- `config/` - Vite build configurations and plugins

### Business Modules

The application manages multiple business domains including:
- Member management and followup
- Card and package management  
- Class scheduling and booking
- Payment and finance tracking
- Staff and coach management
- Marketing and coupons
- Statistics and reporting
- Hardware integration (YPS devices)

### API Architecture

- **Base URL**: Configured via `getBaseUrl()` with environment-specific proxy to backend
- **Request Interceptors**: 
  - Automatic UUID generation for request tracing (`requestId` header)
  - Pagination parameter transformation (`current`/`pageSize` → `page_no`/`page_size`)
  - Dynamic timeout adjustment (50s for large datasets >50 records)
- **Response Interceptors**: Centralized error handling with Arco Message components
- **Request Pattern**: Uses VueUse `useAxios` wrapper for reactive request handling
- **Content Types**: Supports both form-encoded and JSON requests with automatic transformation

### Environment Configuration

- **Development**: `fe.rocketbird.cn` with proxy to `https://beta.rocketbird.cn`
- **Multi-tenant**: Brand site detection via `window.IS_BRAND_SITE` global variable
- **WebSocket**: Environment-specific WebSocket URLs for real-time features
- **CDN Build**: Uses `https://brandcdn.rocketbird.cn/` for CDN-optimized builds

### Mobile Support

- **Mobile Views**: Dedicated mobile page architecture in `src/views/mobile-page/`
- **PostCSS**: px-to-viewport conversion for mobile layouts (750px design width)
- **QR Integration**: WeChat SDK and @zxing/library for QR code scanning
- **Responsive**: Mobile-first design with conditional viewport conversion

### Development Features

- **Auto-imports**: Vue, Vue Router, and Pinia APIs automatically imported
- **Hot Module Replacement**: Custom HMR configuration with overlay disabled
- **ESLint**: Auto-fix on save with Airbnb-based configuration
- **Component Registration**: Arco Design components with tree-shaking via unplugin
- **SVG Loader**: Direct SVG imports as Vue components
- **Less Preprocessing**: Theme variables and responsive breakpoints

### State Management Patterns

- **Modular Stores**: Business domain-specific Pinia stores in `src/store/modules/`
- **Server-driven Menus**: Dynamic menu configuration with icon mapping
- **Real-time State**: WebSocket integration for live updates
- **Multi-tenant State**: Brand vs. merchant portal state differentiation

### Build Optimization

- **Manual Chunking**: Separate bundles for `arco`, `chart`, and `vue` libraries
- **Tree Shaking**: ECharts and Arco Design components loaded on-demand
- **Bundle Analysis**: Rollup visualizer for bundle size optimization
- **CDN Support**: Separate CDN configuration for production deployments

### Testing

- **Cypress E2E**: Permission-based rendering tests and product setting workflows
- **Test Files**: Located in `cypress/e2e/` with environment-specific configurations
- **Mocking**: Brand site globals and permission testing support

### Development Patterns

- **Request Handling**: Use `src/request/index.ts` for all API calls with VueUse integration
- **Error Handling**: Centralized error messages via `showError` configuration in request config
- **Pagination**: Always use `current`/`pageSize` - automatic conversion to backend format
- **Component Style**: Follow Arco Design patterns with custom Rocketbird theme
- **Mobile Development**: Use PostCSS viewport conversion only for mobile-page views